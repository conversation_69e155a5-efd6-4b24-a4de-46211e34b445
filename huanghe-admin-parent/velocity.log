2023-07-06 16:36:02,995 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2023-07-06 16:36:02,996 - Initializing Velocity, Calling init()...
2023-07-06 16:36:02,996 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2023-07-06 16:36:02,996 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2023-07-06 16:36:02,997 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2023-07-06 16:36:02,997 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2023-07-06 16:36:02,998 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2023-07-06 16:36:02,998 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2023-07-06 16:36:03,016 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,047 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2023-07-06 16:36:03,057 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2023-07-06 16:36:03,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2023-07-06 16:36:03,065 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2023-07-06 16:36:03,068 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2023-07-06 16:36:03,073 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2023-07-06 16:36:03,074 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2023-07-06 16:36:03,079 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2023-07-06 16:36:03,080 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2023-07-06 16:36:03,081 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2023-07-06 16:36:03,198 - Created '20' parsers.
2023-07-06 16:36:03,216 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2023-07-06 16:36:03,256 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2023-07-06 16:36:03,256 - Velocimacro : Default library not found.
2023-07-06 16:36:03,256 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2023-07-06 16:36:03,256 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2023-07-06 16:36:03,256 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2023-07-06 16:36:03,256 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2023-07-06 16:36:03,401 - ResourceManager : found vm/java/domain.java.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,474 - Null reference [template 'vm/java/domain.java.vm', line 32, column 9] : $column.columnComment cannot be resolved.
2023-07-06 16:36:03,513 - ResourceManager : found vm/java/mapper.java.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,520 - ResourceManager : found vm/java/service.java.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,539 - ResourceManager : found vm/java/serviceImpl.java.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,562 - ResourceManager : found vm/java/controller.java.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,585 - ResourceManager : found vm/xml/mapper.xml.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,615 - ResourceManager : found vm/sql/sql.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,626 - ResourceManager : found vm/js/api.js.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,698 - ResourceManager : found vm/vue/index.vue.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2023-07-06 16:36:03,706 - RHS of #set statement is null. Context will not be modified. vm/vue/index.vue.vm[line 105, column 1]
2023-07-06 16:36:03,712 - RHS of #set statement is null. Context will not be modified. vm/vue/index.vue.vm[line 109, column 1]
2023-07-06 16:36:03,715 - RHS of #set statement is null. Context will not be modified. vm/vue/index.vue.vm[line 275, column 1]
2023-07-06 16:36:03,715 - RHS of #set statement is null. Context will not be modified. vm/vue/index.vue.vm[line 279, column 1]
2023-07-06 16:36:03,717 - Null reference [template 'vm/vue/index.vue.vm', line 444, column 12] : $confirm cannot be resolved.
2023-07-06 16:36:03,718 - Null reference [template 'vm/vue/index.vue.vm', line 458, column 12] : $confirm cannot be resolved.
