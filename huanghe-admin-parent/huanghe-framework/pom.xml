<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>huanghe</artifactId>
        <groupId>cn.guliandigital</groupId>
        <version>3.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>huanghe-framework</artifactId>

    <description>
        framework框架核心
    </description>

    <dependencies>

        <!-- SpringBoot Web容器 -->
         <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot 拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- 阿里数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 获取系统信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
		<!--引入Knife4j的官方start包-->
		<dependency>
		    <groupId>com.github.xiaoymin</groupId>
		    <!--使用Swagger2-->
		    <artifactId>knife4j-spring-boot-starter</artifactId>
		    <version>2.0.1</version>
		     <exclusions>
		        <exclusion>
		            <groupId>org.springframework.plugin</groupId>
		            <artifactId>spring-plugin-core</artifactId>
		        </exclusion>
		        <exclusion>
		            <groupId>org.springframework.plugin</groupId>
		            <artifactId>spring-plugin-metadata</artifactId>
		        </exclusion>
		    </exclusions>

		</dependency>
		 <dependency>
            <groupId>org.springframework.plugin</groupId>
            <artifactId>spring-plugin-core</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>
		<dependency>
			<groupId>org.springframework.plugin</groupId>
			<artifactId>spring-plugin-metadata</artifactId>
			<version>2.0.0.RELEASE</version>
		</dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
        </dependency>

        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
        </dependency>

        <!-- 系统模块-->
        <dependency>
            <groupId>cn.guliandigital</groupId>
            <artifactId>huanghe-system</artifactId>
        </dependency>

    </dependencies>

</project>