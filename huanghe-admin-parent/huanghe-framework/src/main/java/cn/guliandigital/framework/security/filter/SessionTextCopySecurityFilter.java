package cn.guliandigital.framework.security.filter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * session过滤器 验证session有效性
 * 
 * <AUTHOR>
 */

@Slf4j
@Component
public class SessionTextCopySecurityFilter extends OncePerRequestFilter
{
    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;
    
    @Autowired
	private RedisCache redisCache;

    // 包含检查的url
 	private List<String> includeListCopy = Lists.newArrayList();

 	
 	
 	protected static final long MILLIS_SECOND = 1000;
 	
 	private static final long MINUTE_MILLIS = 60 * MILLIS_SECOND;
	

	private static final int copyIntevalSec = 6;//复制行为间隔6秒
	private static final int copyMaxWords = 400;//单次复制不能超过400字
	
	
	
 	@Override
	public void initFilterBean() throws ServletException {
		log.info("==>初始化配置参数 [minuteMaxCount][showCodeUrl][minuteLimitCount]");
		//复制链接
		includeListCopy.add("/wapi/product/menu/decryptdetail");
		includeListCopy.add("/wapi/product/page/decryptdetail");
		
	}
 	
 	
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		
		String requestUri = request.getRequestURI();
		//log.info("==>请求的url是 {}  method={}", request.getRequestURI(), request.getMethod());
		
		
		//log.info("==>开始校验复制次数...");
		boolean includeCopy = false;
		for (String url : includeListCopy) {
			if (StringUtil.contains(requestUri, url)) {
				includeCopy = true;
				log.info("==>匹配结果，监控 {}", url);
				break;
			}
		}
		if (!includeCopy) {
			filterChain.doFilter(request, response);
		} else {
			String sessionId = null;
			TPlatUser loginUser = null;
			try {
				loginUser = securityPlatuserUtils.getUser(request);
			}catch(Exception e) {
				
			}
	        if (StringUtil.isNotNull(loginUser)) {
	           sessionId = loginUser.getId();      	
	           //log.info("loginUser={}",loginUser.toString());
	        }
	        //没有找到会话，按ip设置
	        if(Strings.isNullOrEmpty(sessionId)) {        	
	        	sessionId = IpUtils.getIpAddr(request);
	        }
			//复制判断 每次间隔6秒，否则弹出提示
			String copyKey = "redis:security:session:copy:flag:"+sessionId;
			//如果没有值，则说明不在6秒内
			Object copyObj = redisCache.getCacheObject(copyKey);
			if( null == copyObj ) {
				//设置6秒自动消失
				redisCache.setCacheObject(copyKey, "exist");
				redisCache.expire(copyKey, copyIntevalSec, TimeUnit.SECONDS);
				
			}else {
				//response.setStatus(400);
				//response.getWriter().write("您的复制时间过短，请稍后再试。");
				ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, "您的复制时间过短，请稍后再试。")));
				return;
			}
			
			//校验字数	
			
			filterChain.doFilter(request, response);
		}
    
        
	}
	
	
	
}
