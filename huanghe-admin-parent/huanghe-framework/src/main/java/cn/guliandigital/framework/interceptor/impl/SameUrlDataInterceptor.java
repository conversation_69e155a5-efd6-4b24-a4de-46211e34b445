package cn.guliandigital.framework.interceptor.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;

import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.filter.repeated.RepeatedlyRequestWrapper;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.http.HttpHelper;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.framework.interceptor.RepeatSubmitInterceptor;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 判断请求url和数据是否和上一次相同，
 * 如果和上次相同，则是重复提交表单。 有效时间为10秒内。
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SameUrlDataInterceptor extends RepeatSubmitInterceptor
{
    public final String REPEAT_PARAMS = "repeatParams";

    public final String REPEAT_TIME = "repeatTime";

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;

    /**
     * 间隔时间，单位:秒 默认3秒
     * 
     * 两次相同参数的请求，如果间隔时间大于该参数，系统不会认定为重复提交的数据
     */
    private int intervalTimeFront = 100;
    private int intervalTimeBackup = 3000;

//    public void setIntervalTime(int intervalTime)
//    {
//        this.intervalTime = intervalTime;
//    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean isRepeatSubmit(HttpServletRequest request,String checkType, String tokenId)
    {
        String nowParams = "";
        if (request instanceof RepeatedlyRequestWrapper)
        {
            RepeatedlyRequestWrapper repeatedlyRequest = (RepeatedlyRequestWrapper) request;
            nowParams = HttpHelper.getBodyString(repeatedlyRequest);
        }

        // body参数为空，获取Parameter的数据
        if (StringUtil.isEmpty(nowParams))
        {
            nowParams = JSONObject.toJSONString(request.getParameterMap());
        }
        Map<String, Object> nowDataMap = new HashMap<String, Object>();
        nowDataMap.put(REPEAT_PARAMS, nowParams);
        nowDataMap.put(REPEAT_TIME, System.currentTimeMillis());
        String cache_repeat_key = null;
        // 请求地址（作为存放cache的key值）
        String url = request.getRequestURI();
        if(StringUtil.equals(checkType, "backon")) {
	        String headerstr = request.getHeader(header);	        
	        // 唯一标识（指定key + 消息头）
	        cache_repeat_key = Constants.REPEAT_SUBMIT_KEY + headerstr+tokenId;
	        //log.info("==>重复提交校验：{} ",cache_repeat_key);
	        Object sessionObj = redisCache.getCacheObject(cache_repeat_key);
	        if (sessionObj != null)
	        {
	            Map<String, Object> sessionMap = (Map<String, Object>) sessionObj;
	            if (sessionMap.containsKey(url))
	            {
	                Map<String, Object> preDataMap = (Map<String, Object>) sessionMap.get(url);
	                if (compareParams(nowDataMap, preDataMap) && compareTime(nowDataMap, preDataMap))
	                {
	                    return true;
	                }
	            }
	        }
	        Map<String, Object> cacheMap = new HashMap<String, Object>();
	        cacheMap.put(url, nowDataMap);
	        //log.info("==>重复提交校验：{}  = {}",cache_repeat_key, cacheMap);
	        redisCache.setCacheObject(cache_repeat_key, cacheMap, intervalTimeBackup, TimeUnit.MILLISECONDS);
	        return false;
        }else {
        	//前台
        	String headerstr = null;
        	try {
        		TPlatUser user = securityPlatuserUtils.getUser(request);
        		if(user != null) {
        			headerstr = user.getToken();
        		}
        	}catch(Exception e) {
        		log.error("获取用户信息失败...");
        	}
	        String ip = IpUtils.getIpAddr(request);
	        if(Strings.isNullOrEmpty(headerstr)) {
	        	headerstr = ip;
	        }
	        
	        // 唯一标识（指定key + 消息头）
	        cache_repeat_key = Constants.REPEAT_SUBMIT_KEY + headerstr+tokenId;
	        //log.info("==>重复提交校验：{} ",cache_repeat_key);
	        Object sessionObj = redisCache.getCacheObject(cache_repeat_key);
	        if (sessionObj != null)
	        {
	            Map<String, Object> sessionMap = (Map<String, Object>) sessionObj;
	            if (sessionMap.containsKey(url))
	            {
	                Map<String, Object> preDataMap = (Map<String, Object>) sessionMap.get(url);
	                if (compareTimeFront(nowDataMap, preDataMap))
	                {
	                    return true;
	                }
	            }
	        }
	        
	        Map<String, Object> cacheMap = new HashMap<String, Object>();
	        cacheMap.put(url, nowDataMap);
	        //log.info("==>重复提交校验：{}  = {}",cache_repeat_key, cacheMap);
	        redisCache.setCacheObject(cache_repeat_key, cacheMap, intervalTimeFront, TimeUnit.MILLISECONDS);
	        return false;
        }
        
    }
    
    
    
    @Override
	public boolean removeData(HttpServletRequest request, String checkType, String tokenId) {
		
    	String cache_repeat_key = null;
   		if(StringUtils.equals(checkType, "backon")) {
	    	String headerstr = request.getHeader(header);	        
	    	cache_repeat_key = Constants.REPEAT_SUBMIT_KEY + headerstr + tokenId;	    	
    	
   		}else {
   			String headerstr = null;
        	try {
        		TPlatUser user = securityPlatuserUtils.getUser(request);
        		if(user != null) {
        			headerstr = user.getToken();
        		}
        	}catch(Exception e) {
        		log.error("获取用户信息失败...");
        	}
	        String ip = IpUtils.getIpAddr(request);
	        if(Strings.isNullOrEmpty(headerstr)) {
	        	headerstr = ip;
	        }
	        cache_repeat_key = Constants.REPEAT_SUBMIT_KEY + headerstr + tokenId;
   		}
   		
   		log.info("==>删除重复提交rediskey：{} ",cache_repeat_key);  
                 
    	redisCache.deleteObject(cache_repeat_key);
		
		return true;
	}

    /**
     * 判断参数是否相同
     */
    private boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap)
    {
        String nowParams = (String) nowMap.get(REPEAT_PARAMS);
        String preParams = (String) preMap.get(REPEAT_PARAMS);
        return nowParams.equals(preParams);
    }

    /**
     * 判断两次间隔时间
     */
    private boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap)
    {
        long time1 = (Long) nowMap.get(REPEAT_TIME);
        long time2 = (Long) preMap.get(REPEAT_TIME);
        return (time1 - time2) < (this.intervalTimeBackup);
    }
    private boolean compareTimeFront(Map<String, Object> nowMap, Map<String, Object> preMap)
    {
        long time1 = (Long) nowMap.get(REPEAT_TIME);
        long time2 = (Long) preMap.get(REPEAT_TIME);
        return (time1 - time2) < (this.intervalTimeFront);
    }
}
