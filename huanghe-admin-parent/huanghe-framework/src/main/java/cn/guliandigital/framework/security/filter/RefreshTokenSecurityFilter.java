package cn.guliandigital.framework.security.filter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * session过滤器 验证session有效性
 * 
 * <AUTHOR>
 */

@Slf4j
@Component
public class RefreshTokenSecurityFilter extends OncePerRequestFilter
{
    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;
    
    @Autowired
	private RedisCache redisCache;

    // 排除检查的url
 	private List<String> excludeListView = Lists.newArrayList();
 	
 		
	
 	@Override
	public void initFilterBean() throws ServletException {
		log.info("==>初始化配置参数 [用户token刷新]");
		excludeListView.add("/wapi/common/picio");
	}
 	
 	
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		
		String requestUri = request.getRequestURI();
		//log.info("==>请求的url是 {}  method={}", request.getRequestURI(), request.getMethod());
		boolean excludeView = false;
		for (String url : excludeListView) {
			if (StringUtil.contains(requestUri, url)) {
				excludeView = true;
				//log.info("==>匹配结果，过滤 {}", url);
				break;
			}
		}
		if (excludeView) {		
			filterChain.doFilter(request, response);
		}else {
		
			String token = request.getHeader("userToken");
	        if(StrUtil.isBlank(token)){
	        	token = request.getParameter("userToken");
	        }
	        if(!Strings.isNullOrEmpty(token)) {
	        	try {
		        	TPlatUser user = securityPlatuserUtils.getUser(request);
		        	if(user == null) {
		        		log.error("user is null");
		        		ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.FORBIDDEN, "会话已过期")));
						return;
		        	}
		        	redisCache.expire(RedisConstants.LOGIN_USER_TOKEN+token, RedisConstants.TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);
		            redisCache.expire(RedisConstants.LOGIN_USER_INFO+user.getId(), RedisConstants.TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);
		            //log.info("==>刷新token......");
	        	}catch(CustomException e) {
	        		//log.error("CustomException,", e.getMessage());
	        	}catch(Exception e) {
	        		//ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.FORBIDDEN, "会话已过期")));
					log.error("error", e);
	        	}
	        }		
			
			filterChain.doFilter(request, response);		
		}
	}
	
	
	
}
