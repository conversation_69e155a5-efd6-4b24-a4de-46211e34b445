package cn.guliandigital.framework.security.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import cn.guliandigital.common.core.domain.model.LoginUser;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.framework.web.service.TokenService;

/**
 * token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
 @Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter
{
	// 令牌自定义标识
    @Value("${token.enabled}")
    private boolean enabled;
	 
    @Autowired
    private TokenService tokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException,IOException {
    	if(enabled) {
    		LoginUser loginUser = tokenService.getLoginUser(request);
    		if (StringUtil.isNotNull(loginUser) && StringUtil.isNull(SecurityUtils.getAuthentication()))
    		{
    			tokenService.verifyToken(loginUser);
    			UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
    			authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
    			SecurityContextHolder.getContext().setAuthentication(authenticationToken);
    		}
    	}
        chain.doFilter(request, response);
    }
}
