package cn.guliandigital.framework.security.filter;

import java.io.IOException;
import java.util.List;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * ip安全控制过滤器
 * 
 * 1小时内，单个ip访问200次，需要输入验证码继续访问 
 * 1小时内，单个ip访问400次，封停账户
 * 
 * <AUTHOR>
 */

@Slf4j
//@Component
public class IpSecurityFilter extends OncePerRequestFilter {

	@Autowired
	private RedisCache redisCache;

	protected static final long MILLIS_SECOND = 1000;
	
	private static final long HOUR_MILLIS = 60 * 60 * MILLIS_SECOND;

	private static final Integer hourMaxCount = 200;// 每小时限制N次
	private static final String showCodeUrl = "http://www.123.com/code";// 验证码页面url

	private static final Integer hourLimitCount = 400;
	private static final String userAlert = "您的ip存在大量非法访问。";

	// 排除检查的url
	private List<String> excludeList = Lists.newArrayList();

	@Override
	public void initFilterBean() throws ServletException {
		log.info("==>初始化配置参数 [hourMaxCount][showCodeUrl][hourLimitCount]");

		excludeList.add("/wapi/product/readhistory");		
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		log.info("==>请求的url是 {}  method={}", request.getRequestURI(), request.getMethod());
		String requestUri = request.getRequestURI();
		boolean hasExclude = false;
		for (String url : excludeList) {
			if (StringUtil.contains(requestUri, url)) {
				hasExclude = true;
				log.info("==>匹配结果，放弃监控 {}", url);
				break;
			}
		}
		if (hasExclude) {
			filterChain.doFilter(request, response);
		} else {
			String ip = IpUtils.getIpAddr(request);

			// ip访问量增加1			
			redisCache.incr("redis:security:ip:count:" + ip, 1);
			
			log.info("==>当前ip访问次数：{}  {}", ip, redisCache.getCacheObject("redis:security:ip:count:" + ip));
			// 记录起始时间
			Object stObj = redisCache.getCacheObject("redis:security:ip:starttime:" + ip);
			long st = 0L;
			if (ObjectUtils.isEmpty(stObj)) {
				st = System.currentTimeMillis();
				redisCache.setCacheObject("redis:security:ip:starttime:" + ip, st);
			} else {
				st = Long.parseLong(stObj.toString());
			}
			String type = null;// 触发类型 code-验证码 lock-封停
			long et = System.currentTimeMillis();
			long ms = et - st;
			// 时间在1小时内
			if (ms <= HOUR_MILLIS) {
				// 触发
				int count = redisCache.getCacheObject("redis:security:ip:count:" + ip);
				if (count > hourLimitCount) {
					type = "lock";
				} else if (count > hourMaxCount) {
					type = "code";
				}
			} else {
				// 重置数据
				resetData(ip);
			}

			if (StringUtil.equals(type, "code")) {
				log.info("==>超出限制，需要弹出验证码");
				// 重定向到输入验证码页面
				response.sendRedirect(showCodeUrl);
			} else if (StringUtil.equals(type, "lock")) {
				log.info("==>超出最大限制，锁定");
//				response.setStatus(403);
//				response.getWriter().write("request forbidden" + userAlert);
				ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED, userAlert)));
				
				// 重置数据
				resetData(ip);
			} else {
				filterChain.doFilter(request, response);
			}
		}

	}

	/**
	 * 重置ip的相关数据
	 * 
	 * @param ip
	 */
	public void resetData(String ip) {

		redisCache.deleteObject("redis:security:ip:count:" + ip);
		redisCache.deleteObject("redis:security:ip:starttime:" + ip);
		log.info("==>删除redis {}", "redis:security:ip:count:" + ip);
		log.info("==>删除redis {}", "redis:security:ip:starttime:" + ip);
	}

}
