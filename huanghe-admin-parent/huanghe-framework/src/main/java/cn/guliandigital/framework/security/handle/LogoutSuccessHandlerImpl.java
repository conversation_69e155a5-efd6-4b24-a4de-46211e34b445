package cn.guliandigital.framework.security.handle;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.codecs.MySQLCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import com.alibaba.fastjson.JSON;

import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.model.LoginUser;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.framework.manager.AsyncManager;
import cn.guliandigital.framework.manager.factory.AsyncFactory;
import cn.guliandigital.framework.web.service.TokenService;

/**
 * 自定义退出处理类 返回成功
 * 
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler
{
    @Autowired
    private TokenService tokenService;

    /**
     * 退出处理
     * 
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws ServletException
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtil.isNotNull(loginUser))
        {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            userName = ESAPI.encoder().encodeForSQL(new MySQLCodec(MySQLCodec.Mode.STANDARD),userName);
            
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
        }
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.SUCCESS, "退出成功")));
    }
}
