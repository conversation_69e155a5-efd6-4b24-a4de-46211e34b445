package cn.guliandigital.framework.interceptor;

import java.lang.reflect.Method;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSONObject;

import cn.guliandigital.common.annotation.FrontRepeatSubmit;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 防止重复提交拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class RepeatSubmitInterceptor extends HandlerInterceptorAdapter {
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		if (handler instanceof HandlerMethod) {
			//log.info("==>进入防重复提交程序......");
			HandlerMethod handlerMethod = (HandlerMethod) handler;
			Method method = handlerMethod.getMethod();
			// 获取类名
            String className = handlerMethod.getBeanType().getSimpleName();
            // 获取方法名
            String methodName = handlerMethod.getMethod().getName();
            String tokenId = className + "-" + methodName;
            
			RepeatSubmit backupannotation = method.getAnnotation(RepeatSubmit.class);
			FrontRepeatSubmit frontannotation = method.getAnnotation(FrontRepeatSubmit.class);
			
			if (frontannotation != null) {
				if (this.isRepeatSubmit(request, "front", tokenId)) {
					AjaxResult ajaxResult = AjaxResult.error("您的手速太快，请稍后再试!");
					//log.info("==>应答：{}", JSONObject.toJSONString(ajaxResult));

					ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
					return false;
				}
			}
			
			if (backupannotation != null) {
				if (this.isRepeatSubmit(request, "backon", tokenId)) {
					AjaxResult ajaxResult = AjaxResult.error("您的手速太快，请稍后再试!");
					//log.info("==>应答：{}", JSONObject.toJSONString(ajaxResult));

					ServletUtils.renderString(response, JSONObject.toJSONString(ajaxResult));
					return false;
				}
			}
			
			return true;
		} else {
			return super.preHandle(request, response, handler);
		}
	}

	
	@Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
		if (handler instanceof HandlerMethod) {
			//log.info("==>进入防重复提交程序......");
			HandlerMethod handlerMethod = (HandlerMethod) handler;
			Method method = handlerMethod.getMethod();			
			// 获取类名
            String className = handlerMethod.getBeanType().getSimpleName();
            // 获取方法名
            String methodName = handlerMethod.getMethod().getName();
            
            String tokenId = className + "-" + methodName;
            
			RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
			FrontRepeatSubmit frontannotation = method.getAnnotation(FrontRepeatSubmit.class);
			if (frontannotation != null) {
				this.removeData(request, "front", tokenId);
			}
			if (annotation != null) {
				this.removeData(request, "backon", tokenId);
			}
		}
    }
	
	/**
	 * 验证是否重复提交由子类实现具体的防重复提交的规则
	 *
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public abstract boolean isRepeatSubmit(HttpServletRequest request, String checkType, String tokenId);
	
	public abstract boolean removeData(HttpServletRequest request, String checkType, String tokenId);
}
