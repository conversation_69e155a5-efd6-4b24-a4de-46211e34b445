package cn.guliandigital.framework.security.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
public class AddResponseHeaderFilter extends OncePerRequestFilter{

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {		
		response.addHeader("X-Content-Type-Options", "nosniff");
		response.addHeader("Referrer-Policy", "origin");
		response.addHeader("X-Download-Options", "noopen");
		response.addHeader("Strict-Transport-Security", "max-age=63072000; includeSubdomains; preload");
		response.addHeader("X-Permitted-Cross-Domain-Policies", "master-only");
		//response.addHeader("X-Frame-Options", "SAMEORIGIN");
		response.addHeader("Content-Security-Policy", "default-src 'self'");
		response.addHeader("X-XSS-Protection", "1; mode=block");
		
		filterChain.doFilter(request, response);
	    
	}

}
