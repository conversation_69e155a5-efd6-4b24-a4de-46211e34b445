package cn.guliandigital.framework.security.filter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * session过滤器 验证session有效性
 * 
 * <AUTHOR>
 */

@Slf4j
//@Component
public class SessionSecurityFilter extends OncePerRequestFilter
{
    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;
    
    @Autowired
	private RedisCache redisCache;

    // 包含检查的url
 	private List<String> includeListView = Lists.newArrayList();
 	
 	
 	protected static final long MILLIS_SECOND = 1000;
 	
 	private static final long MINUTE_MILLIS = 60 * MILLIS_SECOND;

	private static final int minuteMaxCount = 5;// 1分钟5次页面请求
	private static final String showCodeUrl = "http://www.123.com/code";// 验证码页面url
	
	
	
	
 	@Override
	public void initFilterBean() throws ServletException {
		log.info("==>初始化配置参数 [minuteMaxCount][showCodeUrl][minuteLimitCount]");
				
		//正文阅读 图文阅读
		includeListView.add("/wapi/product/menu/menudetail");	
		includeListView.add("/wapi/product/page/getPicConten");	
	}
 	
 	
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		
		String requestUri = request.getRequestURI();
		log.info("==>请求的url是 {}  method={}", request.getRequestURI(), request.getMethod());
		String sessionId = null;
		
		boolean includeView = false;
		for (String url : includeListView) {
			if (StringUtil.contains(requestUri, url)) {
				includeView = true;
				log.info("==>阅读页匹配结果，监控 {}", url);
				break;
			}
		}
		if (!includeView) {
			filterChain.doFilter(request, response);
		} else {
			TPlatUser loginUser = null;
			try {
				loginUser = securityPlatuserUtils.getUser(request);
			}catch(Exception e) {
				
			}
	        if (StringUtil.isNotNull(loginUser)) {
	           sessionId = loginUser.getId();      	
	           //log.info("loginUser={}",loginUser.toString());
	        }
	        //没有找到会话，按ip设置
	        if(Strings.isNullOrEmpty(sessionId)) {        	
	        	sessionId = IpUtils.getIpAddr(request);
	        }
			//逻辑判断
			String countKey = "redis:security:session:count:" +requestUri+":"+ sessionId;
			String timeKey = "redis:security:session:starttime:" +requestUri+":"+ sessionId;
			// ip访问量增加1			
			redisCache.incr(countKey, 1);
			//仅仅为了测试，自动解锁，生产环境需要注释掉
			redisCache.expire(countKey, MINUTE_MILLIS, TimeUnit.SECONDS);
			redisCache.expire(timeKey, MINUTE_MILLIS, TimeUnit.SECONDS);
			
			log.info("==>当前session访问次数：{}  {}",countKey, redisCache.getCacheObject(countKey));
			// 记录起始时间
			Object stObj = redisCache.getCacheObject(timeKey);
			long st = 0L;
			if (ObjectUtils.isEmpty(stObj)) {
				st = System.currentTimeMillis();
				redisCache.setCacheObject(timeKey, st);
			} else {
				st = Long.parseLong(stObj.toString());
			}
			String type = null;// 触发类型 code-验证码 lock-封停
			long et = System.currentTimeMillis();
			long ms = et - st;
			// 时间在1分钟内
			if (ms <= MINUTE_MILLIS) {
				// 触发
				int count = redisCache.getCacheObject(countKey);
				if (count > minuteMaxCount) {
					type = "code";
				}
			} else {
				// 重置数据
				resetData(sessionId,requestUri);
			}
	
			if (StringUtil.equals(type, "code")) {
				log.info("==>超出限制，需要弹出验证码");
				// 重定向到输入验证码页面
				response.sendRedirect(showCodeUrl);
			} else {
				filterChain.doFilter(request, response);
			}
		}		
		
        
	}
	
	
	/**
	 * 重置ip的相关数据
	 * 
	 * @param ip
	 */
	public void resetData(String sessionId,String requestUri) {

		String countKey = "redis:security:session:count:" +requestUri+":"+ sessionId;
		String timeKey = "redis:security:session:starttime:" +requestUri+":"+ sessionId;
		
		redisCache.deleteObject(countKey);
		redisCache.deleteObject(timeKey);
		log.info("==>删除redis {}", countKey);
		log.info("==>删除redis {}", timeKey);
	}
	
}
