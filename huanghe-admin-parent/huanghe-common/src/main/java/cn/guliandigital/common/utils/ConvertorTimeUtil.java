package cn.guliandigital.common.utils;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ConvertorTimeUtil {

	static NumberFormat nf = new  DecimalFormat("0.00");
	
	public static void main(String[] args) {
        int seconds = 17854, msec = 360000;// 秒，毫秒
        System.out.println(secToTime(seconds));
        System.out.println(misSecToTimeFix(120));
        System.out.println(msec + "毫秒转换格式时间：\t" + msecToTime(msec));
        System.out.println("当前时间（时:分：秒.毫秒）\t" + new SimpleDateFormat("HH:mm:ss.SSS").format(new Date()));
    }
 
    /**
     * 秒转换小时-分-秒analytics/util/DateUtil.java
     *
     * @param seconds 秒为单位 比如..600秒
     * @return 比如...2小时3分钟52秒
     */
    public static String secToTime(int seconds) {
        int hour = seconds / 3600;
        int minute = (seconds - hour * 3600) / 60;
        int second = (seconds - hour * 3600 - minute * 60);
 
        StringBuffer sb = new StringBuffer();
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        if (second == 0) {
            sb.append("0秒");
        }
        return sb.toString();
    }
 
    /**
     * 秒转换小时-分-秒analytics/util/DateUtil.java
     *
     * @param seconds 秒为单位 比如..600秒
     * @return 比如...2小时3分钟52秒
     */
    public static String misSecToTime(int misseconds) {
    	int seconds = misseconds /1000;
    	int day = seconds / 24 / 3600;
        int hour = (seconds - day * 24 * 3600) / 3600;
        int minute = (seconds - day * 24 * 3600 - hour * 3600) / 60;
        int second = (seconds - day * 24 * 3600 - hour * 3600 - minute * 60);
 
        StringBuffer sb = new StringBuffer();
        if (day > 0) {
            sb.append(day + "天");
        }
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute  > 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        if (second == 0) {
        	double misd = misseconds /1000.0;
        	String mis = nf.format(misd);
            sb.append(mis+"秒");
        }
        return sb.toString();
    }
    
    /**
     * 秒转换小时-分-秒analytics/util/DateUtil.java
     *
     * @param seconds 秒为单位 比如..600秒
     * @return 比如...2小时3分钟52秒
     */
    public static String misSecToTimeFix(int misseconds) {
    	int seconds = misseconds /1000;
    	int day = seconds / 24 / 3600;
        int hour = (seconds - day * 24 * 3600) / 3600;
        int minute = (seconds - day * 24 * 3600 - hour * 3600) / 60;
        int second = (seconds - day * 24 * 3600 - hour * 3600 - minute * 60);
 
        StringBuffer sb = new StringBuffer();
        if (day >= 0) {
            sb.append(day + "天");
        }
        if (hour >= 0) {
            sb.append(hour + "小时");
        }
        if (minute >= 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        if (second == 0) {
        	//double mis = misseconds /1000.0;
        	int mis = 0;
            sb.append(mis+"秒");
        }
        return sb.toString();
    }
    
    /**
     * 将int类型数字转换成时分秒毫秒的格式数据
     *
     * @param time long类型的数据
     * @return HH:mm:ss.SSS
     * <AUTHOR> 2019/04/11
     */
    public static String msecToTime(int time) {
        String timeStr = null;
        int hour = 0;
        int minute = 0;
        int second = 0;
        int millisecond = 0;
        if (time <= 0)
            return "00:00:00.000";
        else {
            second = time / 1000;
            minute = second / 60;
            millisecond = time % 1000;
            if (second < 60) {
                timeStr = "00:00:" + unitFormat(second) + "." + unitFormat2(millisecond);
            } else if (minute < 60) {
                second = second % 60;
                timeStr = "00:" + unitFormat(minute) + ":" + unitFormat(second) + "." + unitFormat2(millisecond);
            } else {// 数字>=3600 000的时候
                hour = minute / 60;
                minute = minute % 60;
                second = second - hour * 3600 - minute * 60;
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second) + "."
                    + unitFormat2(millisecond);
            }
        }
        return timeStr;
    }
 
    public static String unitFormat(int i) {// 时分秒的格式转换
        String retStr = null;
        if (i >= 0 && i < 10)
            retStr = "0" + Integer.toString(i);
        else
            retStr = "" + i;
        return retStr;
    }
 
    public static String unitFormat2(int i) {// 毫秒的格式转换
        String retStr = null;
        if (i >= 0 && i < 10)
            retStr = "00" + Integer.toString(i);
        else if (i >= 10 && i < 100) {
            retStr = "0" + Integer.toString(i);
        } else
            retStr = "" + i;
        return retStr;
    }
}
