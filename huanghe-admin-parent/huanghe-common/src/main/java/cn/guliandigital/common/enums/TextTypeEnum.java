package cn.guliandigital.common.enums;

/**
 * 待处理文本类型
 * 
 * <AUTHOR>
public enum TextTypeEnum
{
    TEXT("text", "普通文本"),
    XML("xml", "XML文本");

    private final String code;
    private final String info;

    TextTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
