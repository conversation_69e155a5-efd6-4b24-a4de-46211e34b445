package cn.guliandigital.common.utils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ImgConvert {

	public static void main(String[] args) throws Exception {
		ImgConvert imgConvert = new ImgConvert();
		String filePath="C:\\Users\\<USER>\\Desktop\\ZSK49207 陳文紀\\pageimages\\ZSK49207-000001-L000001.tif";
		imgConvert.tiffTurnJpg(filePath,"C:\\\\Users\\\\<USER>\\\\Desktop\\test.jpg");

	}
	
	/** * tiff 图片 转 JPG 图片 * @param filePath tiff文件路径 
	 * @throws Exception */
	public static void tiffTurnJpg(String inputPath,String outPath) throws Exception{ 
		
		try { 		
			ThumbnailatorUtils.compressPicScale(inputPath, outPath, 1, 1);			
		} catch (Exception e) { 	
			log.error("==>第一次tif转jpg失败{}->{},inputPath:"+inputPath+",outPath:"+outPath,e);
			//tif2jpgJAI(inputPath, outPath);
		} 
		
	}
	
	
//	private static void tif2jpgJAI(String inputPath,String outPath) throws Exception{
//		try { 
//			RenderedOp file = JAI.create("fileload", inputPath);//读取tiff图片文件
//			OutputStream ops = null;
//			
//			ops = new FileOutputStream(outPath); //文件存储输出流
//	//		JPEGEncodeParam param = new JPEGEncodeParam();
//	//		ImageEncoder image = ImageCodec.createImageEncoder("JPEG", ops, param);//指定输出格式 
//	//		//解析输出流进行输出
//	//		image.encode(file);
//			ImageIO.write(file, "jpeg", ops);
//			//关闭流 
//			ops.close(); 
//			log.info("==>tif转jpg成功{}->{}",inputPath,outPath);
//		} catch (Exception e) { 	
//			log.error("==>tif转jpg失败{}->{},inputPath===>"+inputPath,e);		
//		} 
//	}
	
	
}
