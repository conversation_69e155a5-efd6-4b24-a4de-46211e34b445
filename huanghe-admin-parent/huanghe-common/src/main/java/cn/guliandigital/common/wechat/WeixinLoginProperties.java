package cn.guliandigital.common.wechat;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName WeixinLoginProperties
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/21 18:35
 */
@Component
@ConfigurationProperties(prefix="weixinconfig")
public class WeixinLoginProperties {

    private String weixinappID; // 商户appid

    private String weixinappSecret; // 私钥 pkcs8格式的

    public String getRedirect_uri() {
        return redirect_uri;
    }

    public void setRedirect_uri(String redirect_uri) {
        this.redirect_uri = redirect_uri;
    }

    private String redirect_uri;

    public String getWeixinappID() {
        return weixinappID;
    }

    public void setWeixinappID(String weixinappID) {
        this.weixinappID = weixinappID;
    }

    public String getWeixinappSecret() {
        return weixinappSecret;
    }

    public void setWeixinappSecret(String weixinappSecret) {
        this.weixinappSecret = weixinappSecret;
    }

}