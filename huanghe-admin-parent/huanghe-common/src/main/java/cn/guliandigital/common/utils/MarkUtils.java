package cn.guliandigital.common.utils;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Stack;
import java.util.stream.Stream;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Document.OutputSettings;
import org.springframework.stereotype.Component;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.ext.DefaultHandler2;

import lombok.extern.slf4j.Slf4j;

/**
 * XML/HTML 文本匹配标记
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class MarkUtils {

//	public static void main(String[] args) throws Exception {
//
//		// inpput
//		// search ，我試作中國
//		// output <span class="zhuanming">民國</span>十三年<y>，我試作</y><span
//		// class="shuming"><y>中國</y>禪學史</span>稿
//
////		Path path = Paths.get("C:\\Users\\<USER>\\Documents\\Tencent Files\\929351993\\FileRecv", "无标题.txt");
////		Path path = Paths.get("D:\\usr\\tmp\\2021\\05\\nbs", "9ca703e41363411ab78769a0df44e857.txt");
////		String readFileToString = FileUtils.readFileToString(path.toFile());
//		String input = "<a class=\"page\" id=\"ZSK12804-000356-L00338\" onclick=\"openReader('ZSK12804-000356-L00338')\">P338</a><div class=\"biaoti2\">《清代版刻真赏》序<a class=\"zhuhao\" id=\"377\" href=\"#378\">（1）</a></div><p><span class=\"jiaozhu-zhu\">\r\n"
//				+ "<a class=\"zhuhao\" id=\"378\" href=\"#377\">（1）</a>本篇录自《学苑与书林》，上海书店出版社，2006年。</span></p><p>韦力先生藏旧刻善本著称于世，然间亦有心留存清刻残册。近利用此残册百数十种，本昔日文学山房江静澜澄波父子编集《明刻集锦》之成法，辑得《清代版刻真赏》若干部，事竣征序于永年。永年以为我国古籍版本之学，贵在目验，此前贤刊刻留真谱、影印书影之暇，尚寄情于宋元残叶，潘景郑、顾起潜二先生出版《明代版本图录》之后，江氏复有《集锦》之举也。《集锦》辑成于1953年，先师顾颉刚先生尝撰序以张之，且“甚愿江君父子更进一步搜罗清代名刻之残者，同例分集装行，盖木刻之风，兹已届歇绝之期，必有此书，而后得以揽其后期之全貌，因以详察版刻终始递嬗之迹”。今去《集锦》之成正五十载，朱明旧本之稀见几同宋元，喜好版本言收藏者多追求清刻，缘此永年尝与贾二强君撰集《清代版刻图录》以事引导。而韦力先生成此《清代版刻真赏》，且使好此者获睹原物，知不特可偿先师颉刚先生之夙愿，于古籍版本学界水平之提高亦必更多裨益，实大有功于我国古老文化之传承发扬，四大发明中印刷术之光彩将得此而益彰已！2004年立秋日江阴黄永年谨序。</p><p><span class=\"luokuan\">（2004年8月7日）</span></p>";
////		String input = "王存字<red>正</red><red>仲</red>，<red>丹</red><red>陽</red>（今江蘇丹陽縣）人";
////		String input = "王存字<red>正</red><red>仲</red>，<red>丹</red><red>陽</red>（今江蘇丹陽縣）人";
////		String input = " 刑十九四八使<red>元</red>徒之类转相仿效刑十九四九一等<red>元</red>图小人新刑五下路里<red>元</red>治中“<red>元</red>”均应作“无”。“无粘带”者，谓无侵欺粘带不了事件，“解由”犹今公文，解由体式，";
//
////		String input = "<span class=\"zhuanming\">民國</span>十三年，我試作<span class=\"shuming\">中國<br/>禪學史</span>稿，我試作<span class=\"shuming\">中國</span>我試";
////		String search = "，我試作中國";
////		String search = "前言 隋 唐是中國封建社會的盛世，政治上相對統一安定，社會經濟和文化高度發達，居於亞洲乃至世界的先進國家行列。在宗教方面，從印度傳來的佛教經過六百年的傳播普及，已與中國傳統文化、習俗密切結合，形成了許多富有民族特色的佛教宗派。這不僅標誌着佛教中國化歷程的基本完成，也標誌着中國已成爲以大乘佛教爲主要形態的北傳佛教的中心。佛教交流是當時亞洲各國文化交流的重要環節，中國佛教源源不斷地傳入朝鮮、日本，後來發展成爲這些國家的重要的民族宗教。  在隋唐的佛教宗";
////		String search = "前言 隋 唐是中國封建社會的盛世，政治上相對統一安定，社會經濟和文化高度發達，居於亞洲乃至世界的先進國家行列。在宗教方面，從印度傳來的佛教經過六百年的傳播普及，已與中國傳統文化、習俗密切結合，形成了許多富有民族特色的佛教宗派。這不僅標誌着佛教中國化歷程的基本完成，也標誌着中國已成爲以大乘佛教爲主要形態的北傳佛教的中心。佛教交流是當時亞洲各國文化交流的重要環節，中國佛教源源不斷地傳入朝鮮、日本，後來發展成爲這些國家的重要的民族宗教。 在隋唐的佛教宗派中，";
////		String search = "楊曾文 一九九○年六月二十八日於 北京 東三環北路寓所";
////		String search = "又有不成年号，而沈刻《元典章》有之者： 吏八八";
////		String search = "元年号有“至大”，有“大德”，而沈刻《元典章》有“至大德”，其中必有一字系衍文： 吏二三一 近睹至大德二年 “德”字衍。 兵三廿六 既系至大德二年 “德”字衍。 “正统”为明朝年号，而沈刻《元典章》有正统：";
////		String search = "《校勘学释例》本名《元典章校补释例》";
////		String search = "会影响对原书的理解";
////		String search = "“有”";
////		String search = "正仲，丹陽";
////		String search = "“元”";
////		String search = "「使」";
////		String search = "論語義疏第十經一千二百二十三字　注一千一百七十五字";
////		String search = "劉餗，字鼎卿，史學家劉知幾的兒子，附見兩唐書劉子玄傳。傳中説他著有國朝傳記，新唐書藝文志乙部雜傳記類曾著録，三卷；丙部小説家類又著録傳記三卷，應該就是國朝傳記的簡稱，原注：“一作國史異纂。”李肇國史補序説：“昔劉餗集小説，涉南北朝至開元，著爲傳記。";
//		String search = "韦力先生";
//
//		String element = "search";
////		String attrs = "color=\"red\"";
////		String element = "red";
//		String attrs = "";
//
//		System.out.println("\n -- input -- \n");
//		System.out.println(input);
//		System.out.println("\n -- search -- \n");
//		System.out.println(search);
//		System.out.println("\n -- result -- \n");
////
////		String mark = mark(input, search, element, attrs, true);
////		System.out.println(mark);
////
////		String jsoupMark = jsoupMark(input, search, element, attrs);
////		System.out.println(jsoupMark);
//
////		String singleSqueezeBubblesMark = characterRepairSqueezeBubblesMark(input, search, "red", attrs, true);
////		System.out.println(singleSqueezeBubblesMark);
//
////		String jsoupSqueezeBubblesMark = jsoupSqueezeBubblesMark(input, search, element, attrs);
////		System.out.println(jsoupSqueezeBubblesMark);
//
//		String jsoupSqueezeBubblesMark = jsoupSqueezeBubblesMark(input, search, element, attrs, 2);
//
//		Path out = Paths.get("D:\\usr\\tmp\\2021\\05\\nbs", "out.txt");
//		FileUtils.write(out.toFile(), jsoupSqueezeBubblesMark);
//
//	}

	/**
	 * 使用 Jsoup 闭合标签
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	public static String jsoupMark(String input, String search, String element, String attrs) {
		Document document = Jsoup.parse(input);
		document.outputSettings(new OutputSettings().syntax(OutputSettings.Syntax.xml));// 格式化为XML
		final String parseInput = document.body().html();
		return mark(parseInput, search, element, attrs, true);
	}

	/**
	 * 标记查询内容
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @param flag    是否需要包裹
	 * @return
	 */
	public static String mark(String input, String search, String element, String attrs, boolean flag) {
		if (flag) {
			final String parseInput = "<root>" + input + "</root>";
			return mark(parseInput, search, element, attrs);
		}
		return mark(input, search, element, attrs);
	}

	/**
	 * 标记查询内容
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	private static String mark(String input, String search, String element, String attrs) {
		try {
			Map<String, String> result = new HashMap<String, String>();
			final String query = search.replaceAll("\\s+", " ");
			SAXParserFactory factory = SAXParserFactory.newInstance();
			// 2.从解析工厂获取解析器
			SAXParser parse = factory.newSAXParser();
			// 3.得到解读器
			XMLReader reader = parse.getXMLReader();
			// 4.设置内容处理器
			reader.setContentHandler(new DefaultHandler2() {

				Map<Integer // 文本索引
				, Integer // 全文索引
				> mapping = new LinkedHashMap<Integer, Integer>();

				int deep = 0;// 记录标签深度

				String fullContent = ""; // 全文内容带标签

				String textContent = ""; // 文本内容无标签

				int textIndex = 0; // 文本字符索引

				int fullIndex = 0; // 全文字符索引

				boolean suffixSpaxe = false;

				@Override
				public void startDocument() throws SAXException {
				}

				@Override
				public void startElement(String uri, String localName, String qName, Attributes attributes)
						throws SAXException {
					if (deep != 0) { // 忽略根标签
						List<String> array = new ArrayList<String>();
						for (int i = 0; i < attributes.getLength(); i++) {
							String attr = String.format("%s=\"%s\"", attributes.getQName(i), attributes.getValue(i));
							array.add(attr);
						}
						String content = "<" + qName + ">";
						if (null != array && !array.isEmpty()) {
							content = "<" + qName + " " + StringUtil.join(array, " ") + ">";
						}
						fullContent += content;
						fullIndex += content.length();
					}

					// 打印标签
//				int space = deep + content.length();
//				String format = String.format("%" + space + "s", content);
//				System.out.println(format);
					deep++;
				}

				@Override
				public void characters(char[] ch, int start, int length) throws SAXException {
					String content = new String(ch, start, length);
					content = content.replaceAll("\r|\n", " ");
					content = content.replaceAll("\\s+", " ");
					if (suffixSpaxe && content.indexOf(" ") == 0) {
						content = content.substring(1);
					}
					if (content.lastIndexOf(" ") == content.length() - 1) {
						suffixSpaxe = true;
					} else {
						suffixSpaxe = false;
					}
//					String peek = stack.peek();
//					if (StringUtil.equalsAnyIgnoreCase("P", peek)) {
//						content += " ";
//					}
					fullContent += content;
					textContent += content;
					for (int i = 0; i < content.length(); i++) {
						mapping.put(textIndex++, fullIndex++);
					}
				}

				@Override
				public void endElement(String uri, String localName, String qName) throws SAXException {
					deep--;
					if (deep != 0) { // 忽略根标签
						String content = "</" + qName + ">";
						fullContent += content;
						fullIndex += content.length();
					}
					// 打印标签
//				int space = deep + content.length();
//				String format = String.format("%" + space + "s", content);
//				System.out.println(format);
				}

				@Override
				public void endDocument() throws SAXException {
					Map<Integer, Integer> mark = new LinkedHashMap<Integer, Integer>();// 记录标记位置 key-开始位置, value-结束位置
					int fromIndex = 0;
					while (true) {
						int indexOf = textContent.indexOf(query, fromIndex);
						fromIndex = indexOf + query.length();
						if (indexOf == -1) {
							break;
						}
						int start = indexOf;
						int end = indexOf + query.length() - 1;
						int prev = -1;
						int snew = -1;
						for (int i = start; i <= end; i++) {
							if (i != start) {
								prev = mapping.get(i - 1);
							}
							Integer curr = mapping.get(i);// 获取全文对应索引
							if (prev != -1 && curr - prev == 1) {
								if (snew != -1) {
									mark.put(snew, curr);// 连续的值 后面覆盖前面
								}
							} else {
								// 新的开始
								snew = curr;
								mark.put(snew, curr);// 连续的值 后面覆盖前面
							}
						}
					}
					int offset = 0; // 偏移量
					String tagStart = String.format("<%s>", element);
					if (StringUtil.isNotBlank(attrs)) {
						tagStart = String.format("<%s %s>", element, attrs);
					}
					String tagEnd = String.format("</%s>", element);
					StringBuilder stringBuilder = new StringBuilder(fullContent);
					for (Entry<Integer, Integer> entry : mark.entrySet()) {
						Integer key = entry.getKey();
						Integer value = entry.getValue();
						stringBuilder.insert(key + offset, tagStart);
						offset += tagStart.length();
						stringBuilder.insert(value + 1 + offset, tagEnd);
						offset += tagEnd.length();
					}
					result.put("result", stringBuilder.toString());
					//log.info(textContent);
				}
			});
			// 5.读取xml的文档内容
			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(input.toString().getBytes("UTF-8"));
			InputStreamReader inputReader = new InputStreamReader(byteArrayInputStream, "UTF-8");
			InputSource inputSource = new InputSource();
			inputSource.setCharacterStream(inputReader);
			reader.parse(inputSource);
			return result.get("result");
		} catch (Exception e) {
			log.error("》》》出现异常：",e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 使用 Jsoup 闭合标签
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	public String jsoupSqueezeBubblesMark(String input, String search, String element, String attrs) {
		Document document = Jsoup.parse(input);
		document.outputSettings(new OutputSettings().syntax(OutputSettings.Syntax.xml));// 格式化为XML
		final String parseInput = document.body().html();
		//log.info("parseInput={}",parseInput);
		return squeezeBubblesMark(parseInput, search, element, attrs, true);
	}

	/**
	 * 标记查询内容
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @param flag    是否需要包裹
	 * @return
	 */
	public String squeezeBubblesMark(String input, String search, String element, String attrs, boolean flag) {
		if (flag) {
			final String parseInput = "<root>" + input + "</root>";
			//log.info("parseInput={}",parseInput);
			return squeezeBubblesMark(parseInput, search, element, attrs);
		}
		return squeezeBubblesMark(input, search, element, attrs);
	}

	final static Character[] IGNORE_CHARACTER_ARRAY = {};// 需要忽略的字符

	/**
	 * 标记查询内容, 对空格进行处理
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	private static String squeezeBubblesMark(String input, String search, String element, String attrs) {
		try {
			Map<String, String> result = new HashMap<String, String>();
			SAXParserFactory factory = SAXParserFactory.newInstance();
			// 2.从解析工厂获取解析器
			SAXParser parse = factory.newSAXParser();
			// 3.得到解读器
			XMLReader reader = parse.getXMLReader();
			// 4.设置内容处理器
			reader.setContentHandler(new DefaultHandler2() {

				Map<Integer // 文本索引
				, Integer // 全文索引
				> mapping = new LinkedHashMap<Integer, Integer>();

				int deep = 0;// 记录标签深度

				Stack<String> tagStack = new Stack<String>();// 记录标签 tagStack.size() == deep

				Stack<Attributes> attrStack = new Stack<Attributes>();

				String fullContent = ""; // 全文内容带标签

				String textContent = ""; // 文本内容无标签

				int textIndex = 0; // 文本字符索引

				int fullIndex = 0; // 全文字符索引

				Stack<String> clip = new Stack<String>();

				boolean fired = false;// clip 使用状态

				Integer firstFired = null;// 记录每一轮首次命中

				Map<Integer, Integer> textMark = new LinkedHashMap<Integer, Integer>();// 记录文本标记位置 key-开始位置, value-结束位置

				private void reloadClip() {
					// 重新装填
					clip.clear();
					for (int i = search.length() - 1; i >= 0; i--) {
						char charAt = search.charAt(i);
						if (Character.isWhitespace(charAt) || ArrayUtils.contains(IGNORE_CHARACTER_ARRAY, charAt)) {
							continue;
						}
						clip.push(String.valueOf(charAt));// 倒序装填, 栈顶元素为第一个字符
					}
					fired = false;// 重置为未使用
					firstFired = null;
				}

				@Override
				public void startDocument() throws SAXException {
					reloadClip();// 初始化
				}

				@Override
				public void startElement(String uri, String localName, String qName, Attributes attributes)
						throws SAXException {
					if (deep != 0) { // 忽略根标签
						List<String> array = new ArrayList<String>();
						for (int i = 0; i < attributes.getLength(); i++) {
							String attr = String.format("%s=\"%s\"", attributes.getQName(i), attributes.getValue(i));
							array.add(attr);
						}
						String content = "<" + qName + ">";
						if (null != array && !array.isEmpty()) {
							content = "<" + qName + " " + StringUtil.join(array, " ") + ">";
						}
						fullContent += content;
						fullIndex += content.length();
					}

					// 打印标签>
//					String tag = "<" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
					tagStack.push(qName);
					attrStack.push(attributes);
					deep++;
				}

				@Override
				public void characters(char[] ch, int start, int length) throws SAXException {
					String content = new String(ch, start, length);
					fullContent += content;
					textContent += content;
					for (int i = 0; i < content.length(); i++) {
						mapping.put(textIndex++, fullIndex++);
						// squeeze bubbles
						if (clip.isEmpty()) {
							continue; // 无查询内容
						}
						String target = String.valueOf(content.charAt(i));
						String bullet = clip.peek();// 获取栈顶元素
						// 忽略命中处理>
						if (StringUtil.isBlank(target)) {// 跳过空字符, 目前是空格
							continue;
						}
						if (StringUtil.equals(tagStack.peek(), "a")
								&& StringUtil.equals(attrStack.peek().getValue("class"), "page")) {
							continue;
						}
						if (ArrayUtils.isNotEmpty(IGNORE_CHARACTER_ARRAY)) {// 忽略某些字符, 需要配置IGNORE_CHARACTER_ARRAY
							String[] array = Stream.of(IGNORE_CHARACTER_ARRAY).map(item -> String.valueOf(item))
									.toArray(String[]::new);
							if (StringUtil.equalsAny(target, array)) {
								continue;
							}
						}
						// <忽略命中处理
						if (StringUtil.equals(target, bullet)) {
							if (!fired) {// 第一次命中
								firstFired = textIndex - 1;// textIndex已自增 减1为当前
							}
							String pop = clip.pop();// 弹出
							fired = true;// 记录命中状态
							if (clip.isEmpty() && fired) {
								// 记录索引
								textMark.put(firstFired, textIndex - 1);
								// 全部命中, 重新装填
								reloadClip();
							}
						} else {
							// 未匹配
							if (fired) {
								// 被命中过, 重新装填
								reloadClip();
							}
						}
					}
				}

				@Override
				public void endElement(String uri, String localName, String qName) throws SAXException {
					tagStack.pop();
					attrStack.pop();
					deep--;
					if (deep != 0) { // 忽略根标签
						String content = "</" + qName + ">";
						fullContent += content;
						fullIndex += content.length();
					}
					// 打印标签>
//					String tag = "</" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
				}

				@Override
				public void endDocument() throws SAXException {
					Map<Integer, Integer> fullMark = new LinkedHashMap<Integer, Integer>();// 记录全文标记位置 key-开始位置,
																							// value-结束位置
					for (Entry<Integer, Integer> entry : textMark.entrySet()) {
						Integer start = entry.getKey();
						Integer end = entry.getValue();
						int prev = -1;
						int snew = -1;
						for (int i = start; i <= end; i++) {
							if (i != start) {
								prev = mapping.get(i - 1);
							}
							Integer curr = mapping.get(i);// 获取全文对应索引
							if (prev != -1 && curr - prev == 1) {
								if (snew != -1) {
									fullMark.put(snew, curr);// 连续的值 后面覆盖前面
								}
							} else {
								// 新的开始
								snew = curr;
								fullMark.put(snew, curr);// 连续的值 后面覆盖前面
							}
						}
					}
					int offset = 0; // 偏移量
					String tagStart = String.format("<%s>", element);
					if (StringUtil.isNotBlank(attrs)) {
						tagStart = String.format("<%s %s>", element, attrs);
					}
					String tagEnd = String.format("</%s>", element);
					StringBuilder stringBuilder = new StringBuilder(fullContent);
					for (Entry<Integer, Integer> entry : fullMark.entrySet()) {
						Integer key = entry.getKey();
						Integer value = entry.getValue();
						stringBuilder.insert(key + offset, tagStart);
						offset += tagStart.length();
						stringBuilder.insert(value + 1 + offset, tagEnd);
						offset += tagEnd.length();
					}
					result.put("result", stringBuilder.toString());
					//log.info("fullContent=>"+fullContent);
				}
			});
			// 5.读取xml的文档内容
			//log.info("parseInput1={}",input);
			//log.info("parseInput2={}",new String(input.getBytes("UTF-8"),"GBK"));
			
			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(input.toString().getBytes("UTF-8"));
			InputStreamReader inputReader = new InputStreamReader(byteArrayInputStream, "UTF-8");
			InputSource inputSource = new InputSource();
			inputSource.setCharacterStream(inputReader);
			reader.parse(inputSource);
			//log.info("==>result={}",result.get("result"));
			return result.get("result");
		} catch (Exception e) {
			log.error("》》》出现异常：",e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 使用 Jsoup 闭合标签
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	public static String jsoupSqueezeBubblesMark(String input, String search, String element, String attrs, int matchFirst) {
		Document document = Jsoup.parse(input);
		document.outputSettings(new OutputSettings().syntax(OutputSettings.Syntax.xml));// 格式化为XML
		final String parseInput = document.body().html();
		
		return squeezeBubblesMark(parseInput, search, element, attrs, matchFirst, true);
	}

	/**
	 * 标记查询内容
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @param flag    是否需要包裹
	 * @return
	 */
	public static String squeezeBubblesMark(String input, String search, String element, String attrs, int matchFirst,
			boolean flag) {
		if (flag) {
			final String parseInput = "<root>" + input + "</root>";
			return squeezeBubblesMark(parseInput, search, element, attrs, matchFirst);
		}
		return squeezeBubblesMark(input, search, element, attrs, matchFirst);
	}

	/**
	 * 标记查询内容, 对空格进行处理
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	private static String squeezeBubblesMark(String inputStr, String searchs, String element, String attrs,
			int matchFirst) {
		try {
			Map<String, String> result = new HashMap<String, String>();
			SAXParserFactory factory = SAXParserFactory.newInstance();
			// 2.从解析工厂获取解析器
			SAXParser parse = factory.newSAXParser();
			// 3.得到解读器
			XMLReader reader = parse.getXMLReader();
			// 4.设置内容处理器
			reader.setContentHandler(new DefaultHandler2() {

				Map<Integer // 文本索引
				, Integer // 全文索引
				> mapping = new LinkedHashMap<Integer, Integer>();

				int deep = 0;// 记录标签深度

				Stack<String> tagStack = new Stack<String>();// 记录标签 tagStack.size() == deep

				Stack<Attributes> attrStack = new Stack<Attributes>();

				String fullContent = ""; // 全文内容带标签

				String textContent = ""; // 文本内容无标签

				int textIndex = 0; // 文本字符索引

				int fullIndex = 0; // 全文字符索引

				Stack<String> clip = new Stack<String>();

				boolean fired = false;// clip 使用状态

				Integer firstFired = null;// 记录每一轮首次命中

				Map<Integer, Integer> textMark = new LinkedHashMap<Integer, Integer>();// 记录文本标记位置 key-开始位置, value-结束位置

				int matched = 0;

				private void reloadClip() {
					// 重新装填
					clip.clear();
					for (int i = searchs.length() - 1; i >= 0; i--) {
						char charAt = searchs.charAt(i);
						if (Character.isWhitespace(charAt) || ArrayUtils.contains(IGNORE_CHARACTER_ARRAY, charAt)) {
							continue;
						}
						clip.push(String.valueOf(charAt));// 倒序装填, 栈顶元素为第一个字符
					}
					fired = false;// 重置为未使用
					firstFired = null;
				}

				@Override
				public void startDocument() throws SAXException {
					reloadClip();// 初始化
				}

				@Override
				public void startElement(String uri, String localName, String qName, Attributes attributes)
						throws SAXException {
					if (deep != 0) { // 忽略根标签
						List<String> array = new ArrayList<String>();
						for (int i = 0; i < attributes.getLength(); i++) {
							String attr = String.format("%s=\"%s\"", attributes.getQName(i), attributes.getValue(i));
							array.add(attr);
						}
						String content = "<" + qName + ">";
						if (null != array && !array.isEmpty()) {
							content = "<" + qName + " " + StringUtil.join(array, " ") + ">";
						}
						fullContent += content;
						fullIndex += content.length();
					}

					// 打印标签>
//					String tag = "<" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
					tagStack.push(qName);
					attrStack.push(attributes);
					deep++;
				}

				@Override
				public void characters(char[] ch, int start, int length) throws SAXException {
					String content = new String(ch, start, length);
					fullContent += content;
					textContent += content;
					for (int i = 0; i < content.length(); i++) {
						mapping.put(textIndex++, fullIndex++);
						// squeeze bubbles
						if (clip.isEmpty()) {
							continue; // 无查询内容
						}
						String target = String.valueOf(content.charAt(i));
						String bullet = clip.peek();// 获取栈顶元素
						// 忽略命中处理>
						if (StringUtil.isBlank(target)) {// 跳过空字符, 目前是空格
							continue;
						}
						if (StringUtil.equals(tagStack.peek(), "a")
								&& StringUtil.equals(attrStack.peek().getValue("class"), "page")) {
							continue;
						}
						if (ArrayUtils.isNotEmpty(IGNORE_CHARACTER_ARRAY)) {// 忽略某些字符, 需要配置IGNORE_CHARACTER_ARRAY
							String[] array = Stream.of(IGNORE_CHARACTER_ARRAY).map(item -> String.valueOf(item))
									.toArray(String[]::new);
							if (StringUtil.equalsAny(target, array)) {
								continue;
							}
						}
						// <忽略命中处理
						if (StringUtil.equals(target, bullet)) {
							if (!fired) {// 第一次命中
								firstFired = textIndex - 1;// textIndex已自增 减1为当前
								matched++;
								if (matched != matchFirst) {
									continue;
								}
							}
							String pop = clip.pop();// 弹出
							fired = true;// 记录命中状态
							if (clip.isEmpty() && fired) {
								// 记录索引
								textMark.put(firstFired, textIndex - 1);
								// 全部命中, 重新装填
								reloadClip();
							}
						} else {
							// 未匹配
							if (fired) {
								// 被命中过, 重新装填
								reloadClip();
							}
						}
					}
				}

				@Override
				public void endElement(String uri, String localName, String qName) throws SAXException {
					tagStack.pop();
					attrStack.pop();
					deep--;
					if (deep != 0) { // 忽略根标签
						String content = "</" + qName + ">";
						fullContent += content;
						fullIndex += content.length();
					}
					// 打印标签>
//					String tag = "</" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
				}

				@Override
				public void endDocument() throws SAXException {
					Map<Integer, Integer> fullMark = new LinkedHashMap<Integer, Integer>();// 记录全文标记位置 key-开始位置,
					// value-结束位置
					for (Entry<Integer, Integer> entry : textMark.entrySet()) {
						Integer start = entry.getKey();
						Integer end = entry.getValue();
						int prev = -1;
						int snew = -1;
						for (int i = start; i <= end; i++) {
							if (i != start) {
								prev = mapping.get(i - 1);
							}
							Integer curr = mapping.get(i);// 获取全文对应索引
							if (prev != -1 && curr - prev == 1) {
								if (snew != -1) {
									fullMark.put(snew, curr);// 连续的值 后面覆盖前面
								}
							} else {
								// 新的开始
								snew = curr;
								fullMark.put(snew, curr);// 连续的值 后面覆盖前面
							}
						}
					}
					int offset = 0; // 偏移量
					String tagStart = String.format("<%s>", element);
					if (StringUtil.isNotBlank(attrs)) {
						tagStart = String.format("<%s %s>", element, attrs);
					}
					String tagEnd = String.format("</%s>", element);
					StringBuilder stringBuilder = new StringBuilder(fullContent);
					for (Entry<Integer, Integer> entry : fullMark.entrySet()) {
						Integer key = entry.getKey();
						Integer value = entry.getValue();
						stringBuilder.insert(key + offset, tagStart);
						offset += tagStart.length();
						stringBuilder.insert(value + 1 + offset, tagEnd);
						offset += tagEnd.length();
					}
					result.put("result", stringBuilder.toString());
				}
			});
			// 5.读取xml的文档内容
			//log.info("inputStr1={}",inputStr);
			//log.info("inputStr2={}",inputStr.toString().getBytes("GBK"));
			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(inputStr.toString().getBytes("UTF-8"));
			InputStreamReader inputReader = new InputStreamReader(byteArrayInputStream, "UTF-8");
			InputSource inputSource = new InputSource();
			inputSource.setCharacterStream(inputReader);
			reader.parse(inputSource);
			return result.get("result");
		} catch (Exception e) {
			log.error("》》》出现异常：",e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 修补标记查询内容, 标记缺失的每个字符
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @param flag    是否需要包裹
	 * @return
	 */
	public static String characterRepairSqueezeBubblesMark(String input, String search, String element, String attrs,
			boolean flag) {
		if (flag) {
			final String parseInput = "<root>" + input + "</root>";
			return characterRepairSqueezeBubblesMark(parseInput, search, element, attrs);
		}
		return characterRepairSqueezeBubblesMark(input, search, element, attrs);
	}

	/**
	 * 标记查询内容, 对空格进行处理
	 * 
	 * @param input   输入内容
	 * @param search  查询内容
	 * @param element 元素名称
	 * @param attrs   属性
	 * @return
	 */
	private static String characterRepairSqueezeBubblesMark(String input, String search, String element, String attrs) {
		try {
			Map<String, String> result = new HashMap<String, String>();
			SAXParserFactory factory = SAXParserFactory.newInstance();
			// 2.从解析工厂获取解析器
			SAXParser parse = factory.newSAXParser();
			// 3.得到解读器
			XMLReader reader = parse.getXMLReader();
			// 4.设置内容处理器
			reader.setContentHandler(new DefaultHandler2() {

				Map<Integer // 文本索引
				, Integer // 全文索引
				> mapping = new LinkedHashMap<Integer, Integer>();

				int deep = 0;// 记录标签深度

				Stack<String> tagStack = new Stack<String>();// 记录标签 tagStack.size() == deep

				Stack<Attributes> attrStack = new Stack<Attributes>();

				String fullContent = ""; // 全文内容带标签

				String textContent = ""; // 文本内容无标签

				int textIndex = 0; // 文本字符索引

				int fullIndex = 0; // 全文字符索引

				Stack<String> clip = new Stack<String>();

				boolean fired = false;// clip 使用状态

				Integer firstFired = null;// 记录每一轮首次命中

				Map<Integer, Integer> textMark = new LinkedHashMap<Integer, Integer>();// 记录文本标记位置 key-开始位置, value-结束位置

				Map<Integer, String> tagsMark = new LinkedHashMap<Integer, String>();// 记录索引位置上字符所属标签

				private void reloadClip() {
					// 重新装填
					clip.clear();
					for (int i = search.length() - 1; i >= 0; i--) {
						char charAt = search.charAt(i);
						if (Character.isWhitespace(charAt) || ArrayUtils.contains(IGNORE_CHARACTER_ARRAY, charAt)) {
							continue;
						}
						clip.push(String.valueOf(charAt));// 倒序装填, 栈顶元素为第一个字符
					}
					fired = false;// 重置为未使用
					firstFired = null;
				}

				@Override
				public void startDocument() throws SAXException {
					reloadClip();// 初始化
				}

				@Override
				public void startElement(String uri, String localName, String qName, Attributes attributes)
						throws SAXException {
					if (deep != 0) { // 忽略根标签
						List<String> array = new ArrayList<String>();
						for (int i = 0; i < attributes.getLength(); i++) {
							String attr = String.format("%s=\"%s\"", attributes.getQName(i), attributes.getValue(i));
							array.add(attr);
						}
						String content = "<" + qName + ">";
						if (null != array && !array.isEmpty()) {
							content = "<" + qName + " " + StringUtil.join(array, " ") + ">";
						}
						fullContent += content;
						fullIndex += content.length();
					}

					// 打印标签>
//					String tag = "<" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
					tagStack.push(qName);
					attrStack.push(attributes);
					deep++;
				}

				@Override
				public void characters(char[] ch, int start, int length) throws SAXException {
					String content = new String(ch, start, length);
					fullContent += content;
					textContent += content;
					for (int i = 0; i < content.length(); i++) {
						mapping.put(textIndex++, fullIndex++);
						// squeeze bubbles
						if (clip.isEmpty()) {
							continue; // 无查询内容
						}
						String target = String.valueOf(content.charAt(i));
						String bullet = clip.peek();// 获取栈顶元素
						// 忽略命中处理>
						if (StringUtil.isBlank(target)) {// 跳过空字符, 目前是空格
							continue;
						}
						if (StringUtil.equals(tagStack.peek(), "a")
								&& StringUtil.equals(attrStack.peek().getValue("class"), "page")) {
							continue;
						}
						if (ArrayUtils.isNotEmpty(IGNORE_CHARACTER_ARRAY)) {// 忽略某些字符, 需要配置IGNORE_CHARACTER_ARRAY
							String[] array = Stream.of(IGNORE_CHARACTER_ARRAY).map(item -> String.valueOf(item))
									.toArray(String[]::new);
							if (StringUtil.equalsAny(target, array)) {
								continue;
							}
						}
						// <忽略命中处理
						if (StringUtil.equals(target, bullet)) {
							if (!fired) {// 第一次命中
								firstFired = textIndex - 1;// textIndex已自增 减1为当前
							}
							String pop = clip.pop();// 弹出
							fired = true;// 记录命中状态
							tagsMark.put(fullIndex - 1, tagStack.peek());// 记录命中字符所属标签
							if (clip.isEmpty() && fired) {
								// 记录索引
								textMark.put(firstFired, textIndex - 1);
								// 全部命中, 重新装填
								reloadClip();
							}
						} else {
							// 未匹配
							if (fired) {
								// 被命中过, 重新装填
								reloadClip();
							}
						}
					}
				}

				@Override
				public void endElement(String uri, String localName, String qName) throws SAXException {
					tagStack.pop();
					attrStack.pop();
					deep--;
					if (deep != 0) { // 忽略根标签
						String content = "</" + qName + ">";
						fullContent += content;
						fullIndex += content.length();
					}
					// 打印标签>
//					String tag = "</" + qName + ">";
//					int space = deep + tag.length();
//					String format = String.format("%" + space + "s", tag);
//					System.out.println(format);
					// <打印标签
				}

				@Override
				public void endDocument() throws SAXException {
					Map<Integer, Integer> fullMark = new LinkedHashMap<Integer, Integer>();// 记录全文标记位置 key-开始位置,
					// value-结束位置
					for (Entry<Integer, Integer> entry : textMark.entrySet()) {
						Integer start = entry.getKey();
						Integer end = entry.getValue();
						int prev = -1;
						int snew = -1;
						for (int i = start; i <= end; i++) {
							if (i != start) {
								prev = mapping.get(i - 1);
							}
							Integer curr = mapping.get(i);// 获取全文对应索引
							if (prev != -1 && curr - prev == 1) {
								if (snew != -1) {
									fullMark.put(snew, curr);// 连续的值 后面覆盖前面
								}
							} else {
								// 新的开始
								snew = curr;
								fullMark.put(snew, curr);// 连续的值 后面覆盖前面
							}
						}
					}
					List<Integer> singleMark = new ArrayList<Integer>();// 记录字符全文索引
					for (Entry<Integer, Integer> entry : fullMark.entrySet()) {
						Integer key = entry.getKey();
						Integer value = entry.getValue();
						for (int i = key; i <= value; i++) {
							String tag = tagsMark.get(i);
							if (!StringUtil.equals(tag, element)) {
								// 跳过已经属于需要添加标签的字符
								singleMark.add(i);
							}
						}
					}
					int offset = 0; // 偏移量
					String tagStart = String.format("<%s>", element);
					if (StringUtil.isNotBlank(attrs)) {
						tagStart = String.format("<%s %s>", element, attrs);
					}
					String tagEnd = String.format("</%s>", element);
					StringBuilder stringBuilder = new StringBuilder(fullContent);
					// 对单个字符添加标签
					for (Integer single : singleMark) {
						stringBuilder.insert(single + offset, tagStart);
						offset += tagStart.length();
						stringBuilder.insert(single + 1 + offset, tagEnd);
						offset += tagEnd.length();
					}
					result.put("result", stringBuilder.toString());
				}
			});
			// 5.读取xml的文档内容
			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(input.toString().getBytes("UTF-8"));
			InputStreamReader inputReader = new InputStreamReader(byteArrayInputStream, "UTF-8");
			InputSource inputSource = new InputSource();
			inputSource.setCharacterStream(inputReader);
			reader.parse(inputSource);
			return result.get("result");
		} catch (Exception e) {
			log.error("》》》出现异常：",e);
			throw new RuntimeException(e);
		}
	}
}
