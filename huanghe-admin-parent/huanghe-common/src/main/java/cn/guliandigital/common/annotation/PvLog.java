package cn.guliandigital.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import cn.guliandigital.common.enums.ApiBusinessType;
import cn.guliandigital.common.enums.OperatorType;
import cn.guliandigital.common.enums.UserSource;

/**
 * 前台pv访问日志
 * 
 * <AUTHOR>
 *
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PvLog
{
    /**
     * 模块 
     */
    String title() default "";

    /**
     * 功能
     */
    ApiBusinessType businessType() default ApiBusinessType.VIEW;
    
    UserSource userSource() default UserSource.PC;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.FRONT;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default false;
}
