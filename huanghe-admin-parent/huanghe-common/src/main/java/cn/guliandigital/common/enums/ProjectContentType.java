package cn.guliandigital.common.enums;

/**
 * 项目工程类型
 *
 * <AUTHOR>
 */
public enum ProjectContentType {
    INTRODUCCE(0, "介绍"),
    EDITORIALCOMMITTEE(1,"编委会"),
    CLASSICS(2,"典籍篇"),
    RESEARCH(3,"研究篇"),
    HISTORICALRECORDS(4,"史志篇"),
    CHARACTERS(5,"人物篇"),
    CULTURALRELICS(6,"文物篇"),
    LITERATUREANDART(7,"文学艺术篇"),
    LANDSCAPE(8,"山水篇"),
    TECHNOLOGY(9,"科技篇"),
    REDLITERATURE(10,"红色文献篇");



    private final int code;
    private final String info;


    ProjectContentType(int code, String info) {
        this.code = code;
        this.info = info;
    }


    public int getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
