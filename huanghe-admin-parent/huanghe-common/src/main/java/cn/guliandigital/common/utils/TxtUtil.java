package cn.guliandigital.common.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.InputStreamReader;

import cn.guliandigital.common.exception.ServiceException;
import cn.hutool.core.io.BOMInputStream;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class TxtUtil {

	/**
	 * 功能：Java读取txt文件的内容 步骤：1：先获得文件句柄 2：获得文件句柄当做是输入一个字节码流，需要对这个输入流进行读取
	 * 3：读取到输入流后，需要读取生成字节流 4：一行一行的输出。readline()。 备注：需要考虑的是异常情况
	 * 
	 * @param filePath
	 */
	public static String readTxtFile(String filePath) throws Exception{
		FileInputStream fis = null;
		InputStreamReader read = null;
		BufferedReader bufferedReader = null;
		BOMInputStream bomIn = null;
		try {
			String encoding = "UTF-8";
			StringBuffer str = new StringBuffer();
			File file = new File(filePath);
			if (file.isFile() && file.exists()) { // 判断文件是否存在
				
			   fis = new FileInputStream(file);
			   //可检测多种类型，并剔除bom
			   bomIn = new BOMInputStream(fis);
			   
			   //若检测到bom，则使用bom对应的编码			   
			   String _encoding = bomIn.getCharset();
			   
			   if(_encoding.equals(encoding)) {
				   read = new InputStreamReader(new FileInputStream(file), encoding);// 考虑到编码格式 
			   }else{
				   read = new InputStreamReader(bomIn, _encoding);				
			   }
//				InputStreamReader read = new InputStreamReader(
//						new FileInputStream(file), encoding);// 考虑到编码格式
				bufferedReader = new BufferedReader(read);
				String lineTxt = null;
				while ((lineTxt = bufferedReader.readLine()) != null) {
					str.append(lineTxt).append("\r\n");
				}
				read.close();
			} else {
				log.info("找不到指定的文件");
			}
			return str.toString();
		} catch (Exception e) {
			log.info("出错",e);
			throw e;
		}finally {
			       	
        	if(bomIn != null) {
        		bomIn.close();
        	}
        	if(fis != null) {
				fis.close();
        	} 
        	if(bufferedReader != null) {
        		bufferedReader.close();
        	}
        	if(read != null) {
        		read.close();
        	}
        	
		}
		
	}

	

	public static void contentToTxt(String filePath, String content) throws Exception{
		String str = null; // 原有txt内容
		StringBuilder s1 = new StringBuilder();// 内容更新
		BufferedReader input = null;
		BufferedWriter output = null;
		try {
			File f = new File(filePath);
			if (f.exists()) {
				//System.out.print("文件存在");
			} else {
				log.info("文件不存在{}",f.getAbsolutePath());
				boolean iscreate = f.createNewFile();// 不存在则创建
				if(!iscreate) {
					throw new ServiceException("文件不存在");
				}
			}
			input = new BufferedReader(new FileReader(f));

			while ((str = input.readLine()) != null) {
				s1.append(str).append("\r\n");
			}
			//log.info(s1);
			input.close();
			s1.append(content);

			output = new BufferedWriter(new FileWriter(f));
			output.write(s1.toString());
			output.close();
		} catch (Exception e) {
			log.info("出错",e);
			throw e;
		}finally {
			if(input != null) {
				input.close();
        	}        	
        	if(output != null) {
        		output.close();
        	}
		}
	}

//	public static void appendFile(String filePath, String content) {
//		try {
//			File f = new File(filePath);
//			if (f.exists()) {
//				//System.out.print("文件存在");
//			} else {
//				System.out.print("文件不存在");
//				f.createNewFile();// 不存在则创建
//			}
//			// 打开一个随机访问文件流，按读写方式
//			RandomAccessFile randomFile = new RandomAccessFile(filePath, "rw");
//			// 文件长度，字节数
//			long fileLength = randomFile.length();
//			// 将写文件指针移到文件尾。
//			randomFile.seek(fileLength);
//			randomFile.writeBytes((content.getBytes("UTF-8")+"\r\n").toString());
//			randomFile.close();
//		} catch (Exception e) {			
//			log.info("读取文件内容出错",e);
//		}
//	}
	
	public static String txt2String(File file) throws Exception{
        StringBuilder content = new StringBuilder();
        InputStreamReader reader = null;
        BufferedReader br = null;
    	try{
    		reader = new InputStreamReader(new FileInputStream(file), "UTF-8"); // 建立一个输入流对象reader  
    		br = new BufferedReader(reader);//构造一个BufferedReader类来读取文件
    		String line = "";  
            line = br.readLine();
            while (line != null) {
            	content.append(line).append("\r");
                line = br.readLine(); // 一次读入一行数据  
            }
            br.close();
    	}catch(Exception e){
    		log.info("出错",e);
    		throw e;
        }finally {
        	if(br != null) {
        		br.close();
        	}        	
        	if(reader != null) {
        		reader.close();
        	}
        	
        }
    	return content.toString();
    }
	

	

	public static String charArrayToString(char[] charArray) {
		StringBuffer sb = new StringBuffer();
		for (char ch : charArray) {
			sb.append(ch);
		}
		return sb.toString();
	}
	
	
}
