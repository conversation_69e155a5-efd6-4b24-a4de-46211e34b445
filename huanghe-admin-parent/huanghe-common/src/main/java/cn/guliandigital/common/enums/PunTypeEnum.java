package cn.guliandigital.common.enums;

/**
 * 自动标点类型
 * 
 * <AUTHOR>
public enum PunTypeEnum
{
    PUN_TYPE_1(1, "标点"),
    PUN_TYPE_2(2, "标线"),
    PUN_TYPE_3(3, "标点标线"),
    PUN_TYPE_7(7, "按段落标点");

    private final Integer code;
    private final String info;

    PunTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
