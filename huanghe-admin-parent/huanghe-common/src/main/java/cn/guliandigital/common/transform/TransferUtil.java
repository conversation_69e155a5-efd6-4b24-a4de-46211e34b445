package cn.guliandigital.common.transform;

import java.util.Map;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.UnicodeUtils;

public class TransferUtil {

	
	public static String transfer(String queryKeys, Map<String, String> mapping) {
        String u4 = UnicodeUtils.string2Unicode(queryKeys);
        String[] u4arry = u4.split("\\\\u");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < u4arry.length; i++) {
            String code = u4arry[i];
            if (Strings.isNullOrEmpty(code)) {
                continue;
            }
            String unicode = "\\u" + code;
            String word = UnicodeUtils.unicode2String(unicode);
            // 找出对应关系
            if (mapping.containsKey(word)) {
                result.append(mapping.get(word));
            } else {
                result.append(word);
            }
        }

        return result.toString();
    }
}
