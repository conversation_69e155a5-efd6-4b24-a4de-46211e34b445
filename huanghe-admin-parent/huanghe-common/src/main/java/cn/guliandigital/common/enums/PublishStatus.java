package cn.guliandigital.common.enums;

/**
 * 资讯发布状态
 * 
 * <AUTHOR>
 */
public enum PublishStatus
{
    NOT("0", "未发布"),
    DONE("1", "已发布"),
    CANCEL("2", "已撤销");

    private final String code;
    private final String info;

    PublishStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
