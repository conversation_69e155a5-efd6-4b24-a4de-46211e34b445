package cn.guliandigital.common.utils;

import java.util.Stack;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

public class CharxUtils {

	/**
	 * 字符串转换unicode
	 */
	public static String string2Unicode(String string) {

		StringBuffer unicode = new StringBuffer();

		for (int i = 0; i < string.length(); i++) {
			// 取出每一个字符
			char c = string.charAt(i);
			// 转换为unicode, 不足4位用0补足4位
			unicode.append("\\u" + StringUtil.leftPad(Integer.toHexString(c), 4, "0"));
		}

		return unicode.toString();
	}

	/**
	 * unicode转换字符串
	 */
	public static String unicode2String(String unicode) {

		StringBuffer string = new StringBuffer();

		String[] hex = unicode.split("\\\\u");

		for (int i = 1; i < hex.length; i++) {
			int data = Integer.parseInt(hex[i], 16);
			string.append((char) data);
		}

		return string.toString();
	}

	public static String str2Hex(String word) {
		String result = null;
		int codePoint = word.codePointAt(0);
		result = toHex(codePoint);
		return result;
	}

	private static String toHex(int codePoint) {
		return new StringBuilder().append("&#x").append(Integer.toHexString(codePoint)).append(";").toString();
	}

	/**
	 * 辅助平面的字符位共有 220220 个，因此表示这些字符至少需要 20 个二进制位。 UTF-16 将这 20 个二进制位分成两半，前 10 位映射在
	 * U+D800 到 U+DBFF，称为高位（H）， 后 10 位映射在 U+DC00 到 U+DFFF，称为低位（L）。
	 * 这意味着，一个辅助平面的字符，被拆成两个基本平面的字符表示。
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isDoubleChinese(String u4) {
		boolean isCh = false;

		int a = u4.compareTo("d800");
		int b = u4.compareTo("dbff");
		// log.info(a+"-->"+b);
		if (a >= 0 && b <= 0) {// 在此区间
			isCh = true;
		} else {
			int a1 = u4.compareTo("dc00");
			int b1 = u4.compareTo("dfff");
			if (a1 >= 0 && b1 <= 0) {// 在此区间
				isCh = true;
			}
		}
		return isCh;
	}

	private static String fontFontType(String unicode) {
		if (unicode.startsWith("&#xf")) {
			return "15";
		} else if (unicode.startsWith("&#x2")) {
			return "02";
		}
		return "00";
	}

	public static String formatFont(String word) {
		String result = word;
		try {
			if (!Strings.isNullOrEmpty(word)) {
				String u4 = string2Unicode(word);
				String[] u4arry = u4.split("\\\\u");
				StringBuilder usb = new StringBuilder();
				for (int i = 0; i < u4arry.length; i++) {
					String u4_str = u4arry[i];
					if (!Strings.isNullOrEmpty(u4_str)) {
						if (isDoubleChinese(u4_str)) {
							u4_str = "\\u" + u4_str + "\\u" + u4arry[++i];
							String bigword = word.substring(0, 2);
							word = word.substring(2);
							String ft = fontFontType(str2Hex(bigword));
							if ("02".equals(ft)) {
								usb.append("<字体 type=\"超大字2\">").append(bigword).append("</字体>");
							} else if ("15".equals(ft)) {
								usb.append("<字体 type=\"超大字3\">").append(bigword).append("</字体>");
							}
						} else {
							String uword = unicode2String("\\u" + u4_str);
							word = word.substring(1);
							usb.append(uword);
						}
					}
				}
				result = usb.toString();
			}
			return result;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static Stack<String> toStack(String text) {
		Stack<String> stack = new Stack<String>();
		try {
			if (!Strings.isNullOrEmpty(text)) {
				String u4 = string2Unicode(text);
				String[] u4arry = u4.split("\\\\u");
				for (int i = 0; i < u4arry.length; i++) {
					String u4_str = u4arry[i];
					if (!Strings.isNullOrEmpty(u4_str)) {
						if (isDoubleChinese(u4_str)) {
							u4_str = "\\u" + u4_str + "\\u" + u4arry[++i];
							String chars = text.substring(0, 2);
							text = text.substring(2);
							stack.add(0, chars);
						} else {
							String chars = unicode2String("\\u" + u4_str);
							text = text.substring(1);
							stack.add(0, chars);
						}
					}
				}
			}
			return stack;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static int length(String text) {
		int length = 0;
		try {
			if (!Strings.isNullOrEmpty(text)) {
				String u4 = string2Unicode(text);
				String[] u4arry = u4.split("\\\\u");
				for (int i = 0; i < u4arry.length; i++) {
					String u4_str = u4arry[i];
					if (!Strings.isNullOrEmpty(u4_str)) {
						if (isDoubleChinese(u4_str)) {
							u4_str = "\\u" + u4_str + "\\u" + u4arry[++i];
//							String chars = text.substring(0, 2);
//							text = text.substring(2);
							length++;
						} else {
//							String chars = unicode2String("\\u" + u4_str);
//							text = text.substring(1);
							length++;
						}
					}
				}
			}
			return length;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) {

//		String text = "<正文>羊𠊨𠊨</正文>";
		String text = "羊𠊨𠊨";
//		String word = format2XML(test);
//		System.out.println(word);
//		Stack<String> stack = toStack(text);
//		System.out.println(stack);
//		while (!stack.isEmpty()) {
//			System.out.println(stack.pop());
//		}
		int length = length(text);
		System.out.println(length);
	}

}
