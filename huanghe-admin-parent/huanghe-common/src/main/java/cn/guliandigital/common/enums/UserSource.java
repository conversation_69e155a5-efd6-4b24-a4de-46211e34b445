package cn.guliandigital.common.enums;

/**
 * 用户来源
 * 
 * <AUTHOR>
 */
public enum UserSource
{
    PC("P", "PC"), APP("A", "APP"),ZONG("Z","总平台");

    private final String code;
    private final String info;

    UserSource(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
