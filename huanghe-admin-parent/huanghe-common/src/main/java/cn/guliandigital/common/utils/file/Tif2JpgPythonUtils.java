package cn.guliandigital.common.utils.file;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.utils.StringUtil;
import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

@Slf4j
public class Tif2JpgPythonUtils {

	//private static String httpUrl = "http://192.168.10.150:18607/tif2jpg/v2";

	public static void tif2jpgHandler(String inputPath, String outPath, boolean checkPicType) throws Exception{
		//log.info("==>开始转换");
		File inputFile = new File(inputPath);

		if (!FileUtil.exist(inputFile)) {
			log.info("==>输入文件不存在 {}", inputPath);
			return;
		}
		if(checkPicType) {
			if(StringUtil.equalsIgnoreCase(FilenameUtils.getExtension(inputFile.getName()),"jpg")) {
				log.info("==>输入文件是jpg,不需要转换  {}", inputPath);
				return;
			}
		}

		log.info("==>转换tif图片大小：{}",inputFile.length());
		long st = System.currentTimeMillis();
		String base64 = img2base64(inputPath);

		long et = System.currentTimeMillis();
		log.info("==>img2base64花费时间："+(et-st));


		Map<String, Object> param = Maps.newHashMap();
		param.put("basestr", base64);
		param.put("filename", inputFile.getName());
		String httpUrl = HuangHeConfig.getImgTransformHttp();
		//log.info(httpUrl);
		if(Strings.isNullOrEmpty(httpUrl)) {
			httpUrl = "http://192.168.10.150:18607/tif2jpg/v2";
		}
		//log.info(httpUrl);
		Map<String, Object> result = HttpUtils.doPost(httpUrl, param);
		//log.info("==>{}", result);
		long et1 = System.currentTimeMillis();
		log.info("==>http花费时间："+(et1-et));

		if(result.containsKey("base64img")) {

			String baseimgdata = (String)result.get("base64img");

			boolean transresult = base642img(baseimgdata, outPath);

			long et2 = System.currentTimeMillis();
			log.info("==>base642img花费时间："+(et2-et1));
			//log.info("==>转换结果：{}", transresult);
			if(!transresult) {
				throw new Exception("图片转换异常");
			}
		}
	}

	/**
	 * 图片转化成base64字符串
	 *
	 * @param imgPath
	 * @return
	 */
	private static String img2base64(String imgPath) throws Exception{// 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
		String imgFile = imgPath;// 待处理的图片
		InputStream in = null;
		byte[] data = null;
		String encode = null; // 返回Base64编码过的字节数组字符串
		// 对字节数组Base64编码
		BASE64Encoder encoder = new BASE64Encoder();
		try {
			// 读取图片字节数组
			in = new FileInputStream(imgFile);
			data = new byte[in.available()];
			in.read(data);
			encode = encoder.encode(data);
		} catch (IOException e) {
			log.error("", e);
			throw e;
		} finally {
			try {
				if(in != null) {
					in.close();
				}
			} catch (Exception e) {
				log.error("", e);
			}
		}
		return encode;
	}

	/**
	 * base64字符串还原图片
	 * @param imgData
	 * @param inputPath
	 * @return
	 */
	public static boolean base642img(String imgData, String inputPath) throws Exception{ // 对字节数组字符串进行Base64解码并生成图片
		if (imgData == null) // 图像数据为空
			return false;

		BASE64Decoder decoder = new BASE64Decoder();
		OutputStream out = null;
		try {
			out = new FileOutputStream(inputPath);
			// Base64解码
			byte[] b = decoder.decodeBuffer(imgData);
			for (int i = 0; i < b.length; ++i) {
				if (b[i] < 0) {// 调整异常数据
					b[i] += 256;
				}
			}
			out.write(b);
		} catch (Exception e) {
			log.error("", e);
			throw e;
		} finally {
			try {
				if(out != null) {
					out.flush();
				}
			} catch (IOException e) {
				log.error("", e);
			}
			try {
				if(out != null) {
					out.close();
				}
			} catch (IOException e) {
				log.error("", e);
			}
		}

		return true;
	}

}
