package cn.guliandigital.common.utils.pinyin;

import java.util.Arrays;

import org.apache.commons.lang3.ArrayUtils;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 拼音转换类
 * 
 * <AUTHOR>
 *
 */

@Slf4j
public class PingYinUtil {

	 private static final String[] ALL_PINYIN =
         {"a", "ai", "an", "ang", "ao", "ba", "bai", "ban", "bang", "bao", "bei", "ben",
                 "beng", "bi", "bian", "biao", "bie", "bin", "bing", "bo", "bu", "ca", "cai",
                 "can", "cang", "cao", "ce", "cen", "ceng", "cha", "chai", "chan", "chang",
                 "chao", "che", "chen", "cheng", "chi", "chong", "chou", "chu", "chua",
                 "chuai", "chuan", "chuang", "chui", "chun", "chuo", "ci", "cong", "cou",
                 "cu", "cuan", "cui", "cun", "cuo", "da", "dai", "dan", "dang", "dao", "de",
                 "dei", "den", "deng", "di", "dia", "dian", "diao", "die", "ding", "diu",
                 "dong", "dou", "du", "duan", "dui", "dun", "duo", "e", "ei", "en", "eng",
                 "er", "fa", "fan", "fang", "fei", "fen", "feng", "fiao", "fo", "fou", "fu",
                 "ga", "gai", "gan", "gang", "gao", "ge", "gei", "gen", "geng", "gong",
                 "gou", "gu", "gua", "guai", "guan", "guang", "gui", "gun", "guo", "ha",
                 "hai", "han", "hang", "hao", "he", "hei", "hen", "heng", "hm", "hng",
                 "hong", "hou", "hu", "hua", "huai", "huan", "huang", "hui", "hun", "huo",
                 "ji", "jia", "jian", "jiang", "jiao", "jie", "jin", "jing", "jiong", "jiu",
                 "ju", "juan", "jue", "jun", "ka", "kai", "kan", "kang", "kao", "ke", "kei",
                 "ken", "keng", "kong", "kou", "ku", "kua", "kuai", "kuan", "kuang", "kui",
                 "kun", "kuo", "la", "lai", "lan", "lang", "lao", "le", "lei", "leng", "li",
                 "lia", "lian", "liang", "liao", "lie", "lin", "ling", "liu", "lo", "long",
                 "lou", "lu", "luan", "lue", "lun", "luo", "lv", "m", "ma", "mai", "man",
                 "mang", "mao", "me", "mei", "men", "meng", "mi", "mian", "miao", "mie",
                 "min", "ming", "miu", "mo", "mou", "mu", "n", "na", "nai", "nan", "nang",
                 "nao", "ne", "nei", "nen", "neng", "ng", "ni", "nian", "niang", "niao",
                 "nie", "nin", "ning", "niu", "nong", "nou", "nu", "nuan", "nue", "nuo",
                 "nv", "o", "ou", "pa", "pai", "pan", "pang", "pao", "pei", "pen", "peng",
                 "pi", "pian", "piao", "pie", "pin", "ping", "po", "pou", "pu", "qi", "qia",
                 "qian", "qiang", "qiao", "qie", "qin", "qing", "qiong", "qiu", "qu", "quan",
                 "que", "qun", "ran", "rang", "rao", "re", "ren", "reng", "ri", "rong",
                 "rou", "ru", "ruan", "rui", "run", "ruo", "sa", "sai", "san", "sang", "sao",
                 "se", "sen", "seng", "sha", "shai", "shan", "shang", "shao", "she", "shei",
                 "shen", "sheng", "shi", "shou", "shu", "shua", "shuai", "shuan", "shuang",
                 "shui", "shun", "shuo", "si", "song", "sou", "su", "suan", "sui", "sun",
                 "suo", "ta", "tai", "tan", "tang", "tao", "te", "tei", "teng", "ti", "tian",
                 "tiao", "tie", "ting", "tong", "tou", "tu", "tuan", "tui", "tun", "tuo",
                 "wa", "wai", "wan", "wang", "wei", "wen", "weng", "wo", "wu", "xi", "xia",
                 "xian", "xiang", "xiao", "xie", "xin", "xing", "xiong", "xiu", "xu", "xuan",
                 "xue", "xun", "ya", "yan", "yang", "yao", "ye", "yi", "yin", "ying", "yo",
                 "yong", "you", "yu", "yuan", "yue", "yun", "za", "zai", "zan", "zang",
                 "zao", "ze", "zei", "zen", "zeng", "zha", "zhai", "zhan", "zhang", "zhao",
                 "zhe", "zhei", "zhen", "zheng", "zhi", "zhong", "zhou", "zhu", "zhua",
                 "zhuai", "zhuan", "zhuang", "zhui", "zhun", "zhuo", "zi", "zong", "zou",
                 "zu", "zuan", "zui", "zun", "zuo"};

 private static final String[] HEAD_PART_PINYIN =
         {"b", "be", "bia", "c", "ch", "cho", "chon", "co", "con", "cua", "d", "din", "do",
                 "don", "dua", "f", "fe", "fi", "fia", "g", "go", "gon", "h", "hn", "ho", "hon",
                 "j", "jio", "jion", "jua", "k", "ko", "kon", "l", "len", "lon", "lua", "mia",
                 "nia", "no", "non", "nua", "p", "pe", "pia", "q", "qio", "qion", "qua", "r",
                 "ra", "ro", "ron", "rua", "s", "sh", "sho", "so", "son", "sua", "t", "ten",
                 "tia", "tin", "to", "ton", "tua", "w", "we", "x", "xio", "xion", "xua", "y",
                 "yon", "yua", "z", "zh", "zho", "zhon", "zo", "zon", "zua"};

 	/**
	 * 判断是否是汉字
	 * 
	 * @param inputString
	 * @return
	 */
	public static boolean isChinese(String inputString) {

		try {
			if (Strings.isNullOrEmpty(inputString)) {
				return false;
			}
			if (inputString.matches("[\\u4E00-\\u9FA5]+")) {
				return true;
			}

		} catch (Exception e) {
			log.error("error,", e);
		}
		return false;
	}

	/**
	 * 是否含有拼音
	 * 
	 * @param inputString
	 * @return
	 */
	public static boolean isPinyin(String pinyin, boolean canBeHeadPart) {
        pinyin = pinyin.toLowerCase();
        if (canBeHeadPart && Arrays.binarySearch(HEAD_PART_PINYIN, pinyin) >= 0) {
            return true;
        }
        return Arrays.binarySearch(ALL_PINYIN, pinyin) >= 0;
    }

    public static boolean isPinyin(String pinyin) {
    	if(StringUtil.containsAnyIgnoreCase(pinyin, HEAD_PART_PINYIN)){
    		return true;
    	}
    	
        return StringUtil.containsAnyIgnoreCase(pinyin, ALL_PINYIN);
    }

	public static String[] pinyinSuffixs(String[] split) {
		String[] arr = {};
		for (String string : split) {
			if(isPinyin(string)) {
				if(ArrayUtils.contains(arr, ".pinyin")) {
					continue;
				}
				arr = ArrayUtils.add(arr, ".pinyin");
			} else {
				if(ArrayUtils.contains(arr, "")) {
					continue;
				}
				arr = ArrayUtils.add(arr, "");
			}
		}
		return arr;
	}


//	/**
//	 * 将字符串中的中文转化为拼音,其他字符不变
//	 * 
//	 * @param inputString
//	 * @return
//	 */
//	public static String getPingYin(String inputString) {
//		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
//		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
//		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
//		format.setVCharType(HanyuPinyinVCharType.WITH_V);
//
//		char[] input = inputString.trim().toCharArray();
//		String output = "";
//
//		try {
//			for (int i = 0; i < input.length; i++) {
//				if (java.lang.Character.toString(input[i]).matches("[\\u4E00-\\u9FA5]+")) {
//					String[] temp = PinyinHelper.toHanyuPinyinStringArray(input[i], format);
//					output += temp[0];
//				} else
//					output += java.lang.Character.toString(input[i]);
//			}
//		} catch (BadHanyuPinyinOutputFormatCombination e) {
//			log.error("error,", e);
//		}
//		return output;
//	}
//
//	/**
//	 * 获取汉字串拼音首字母，英文字符不变
//	 * 
//	 * @param chinese
//	 *            汉字串
//	 * @return 汉语拼音首字母
//	 */
//	public static String getFirstSpell(String chinese) {
//		StringBuffer pybf = new StringBuffer();
//		char[] arr = chinese.toCharArray();
//		HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
//		defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
//		defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
//		for (int i = 0; i < arr.length; i++) {
//			if (arr[i] > 128) {
//				try {
//					String[] temp = PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat);
//					if (temp != null) {
//						pybf.append(temp[0].charAt(0));
//					}
//				} catch (BadHanyuPinyinOutputFormatCombination e) {
//					log.error("error,", e);
//				}
//			} else {
//				pybf.append(arr[i]);
//			}
//		}
//		return pybf.toString().replaceAll("\\W", "").trim();
//	}
//
//	/**
//	 * 获取汉字串拼音，英文字符不变
//	 * 
//	 * @param chinese
//	 *            汉字串
//	 * @return 汉语拼音
//	 */
//	public static String getFullSpell(String chinese) {
//		StringBuffer pybf = new StringBuffer();
//		char[] arr = chinese.toCharArray();
//		HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
//		defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
//		defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
//		for (int i = 0; i < arr.length; i++) {
//			if (arr[i] > 128) {
//				try {
//					pybf.append(PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)[0]);
//				} catch (BadHanyuPinyinOutputFormatCombination e) {
//					log.error("error,", e);
//				}
//			} else {
//				pybf.append(arr[i]);
//			}
//		}
//		return pybf.toString();
//	}
}
