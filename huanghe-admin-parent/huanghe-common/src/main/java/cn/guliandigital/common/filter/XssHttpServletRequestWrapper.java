package cn.guliandigital.common.filter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpHeaders;
import org.springframework.http.MediaType;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.html.EscapeUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * XSS过滤处理
 * 
 * <AUTHOR>
 */

@Slf4j
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper
{
    /**
     * @param request
     */
    public XssHttpServletRequestWrapper(HttpServletRequest request)
    {
        super(request);
    }

    @Override
    public String getHeader(String name) {
        String value = super.getHeader(name);       
    	//log.info(name+"  =  "+value);
    	if(Strings.isNullOrEmpty(value)) {
    		return null;
    	}
        // 防xss攻击和过滤前后空格
    	//value = SQLFilter.sqlInject(value);
        return XssUtil.cleanXSS(value);
    }
    
    //重写getParameter
    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        if(Strings.isNullOrEmpty(value)) {
    		return null;
    	}
        //value = SQLFilter.sqlInject(value);    	
        return XssUtil.cleanXSS(value);
    }
    
    @Override
    public String[] getParameterValues(String name)
    {
        String[] values = super.getParameterValues(name);
        if (values != null)
        {
            int length = values.length;
            String[] escapseValues = new String[length];
            for (int i = 0; i < length; i++)
            {
            	
            	String _value = values[i];
            	//log.info(name+"  =  "+_value);
            	if(Strings.isNullOrEmpty(_value)) {
            		continue;
            	}
                // 防xss攻击和过滤前后空格
            	//_value = SQLFilter.sqlInject(_value);
            	
                escapseValues[i] = XssUtil.cleanXSS(_value).trim();
                //log.info("XssUtil.cleanXSS=>"+escapseValues[i]);
            }
            return escapseValues;
        }
        return super.getParameterValues(name);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException
    {
        // 非json类型，直接返回
        if (!isJsonRequest())
        {
            return super.getInputStream();
        }

        // 为空，直接返回
        String json = IOUtils.toString(super.getInputStream(), "utf-8");
        if (StringUtil.isEmpty(json))
        {
            return super.getInputStream();
        }

        // xss过滤
        json = EscapeUtil.clean(json).trim();
        final ByteArrayInputStream bis = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
        return new ServletInputStream()
        {
            @Override
            public boolean isFinished()
            {
                return true;
            }

            @Override
            public boolean isReady()
            {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener)
            {
            }

            @Override
            public int read() {
                return bis.read();
            }
        };
    }

    /**
     * 是否是Json请求
     * 
     * @param request
     */
    public boolean isJsonRequest()
    {
        String header = super.getHeader(HttpHeaders.CONTENT_TYPE);
        return MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(header)
                || MediaType.APPLICATION_JSON_UTF8_VALUE.equalsIgnoreCase(header);
    }
}