package cn.guliandigital.common.enums;

/**
 * 反馈处理状态
 * 
 * <AUTHOR>
 */
public enum HandleStatus
{
    NOT(0, "未处理"), DONE(1, "已处理");

    private final int code;
    private final String info;

    HandleStatus(int code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public int getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
