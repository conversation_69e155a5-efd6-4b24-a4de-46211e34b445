package cn.guliandigital.common.enums;

/**
 * 是否忽略成分差异
 * 
 * <AUTHOR>
public enum PunIgnoreEnum
{
    PUN_TYPE_0(0, "否"),
    PUN_TYPE_1(1, "是");


    private final Integer code;
    private final String info;

    PunIgnoreEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
