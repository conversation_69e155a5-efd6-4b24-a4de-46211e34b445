package cn.guliandigital.common.core.domain.model;

import java.util.List;

/**
 * @version V1.0
 * @Description:记录意见反馈未查看数量的实体
 * @Auther: BOHANZHANG
 * @date 2020-10-19 21:47
 * @Title: FeedBackCount.java
 * @Package: cn.guliandigital.common.core.domain.model
 */

public class FeedBackIsLookCount {

    private static final long serialVersionUID = 1L;

    //未查看意见的主键id
    private List<String> feedBackId;
    //未查看的条数
    private int notCount;

    public int getNotCount() {
        return notCount;
    }

    public void setNotCount(int notCount) {
        this.notCount = notCount;
    }

    public List<String> getFeedBackId() {
        return feedBackId;
    }

    public void setFeedBackId(List<String> feedBackId) {
        this.feedBackId = feedBackId;
    }
}
