package cn.guliandigital.common.enums;

public enum DataStatusEnum {

	// 0-准备 1-解析中  2-解析成功 3-解析失败
	READY("0","准备"),    
    ING("1","解析中"),    
    SUCCESS("2","解析完成") ,
    LOST("3","解析失败"),
    WARNING("4","部分成功");


    private final String code;
    private final String msg;

    DataStatusEnum(String code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }

    public String getCode()
    {
        return code;
    }

    public String getMsg()
    {
        return msg;
    }
    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(String code) {
        DataStatusEnum[] imagetextTypeEnums = values();
        for (DataStatusEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getCode().equals(code)) {
                return imagetextTypeEnum.getMsg();
            }
        }
        return null;
    }

    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static String getCode(String message) {
        DataStatusEnum[] imagetextTypeEnums = values();
        for (DataStatusEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getMsg().equals(message)) {
                return imagetextTypeEnum.getCode();
            }
        }
        return null;
    }
}
