package cn.guliandigital.common.enums;

/**
 * 订单授权方式
 * 
 * <AUTHOR>
 */
public enum OrderType
{
    LIMIT ("LIMIT", "限时"),
    LONG("LONG", "长久");

    private final String code;
    private final String info;

    OrderType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
