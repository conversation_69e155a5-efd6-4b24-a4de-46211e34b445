package cn.guliandigital.common.utils;


import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.io.FilenameUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.google.common.collect.Lists;

import cn.guliandigital.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;


/**
 * 解析xml工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class XproUtils {


	public static void main(String[] args){
		List<String> list= Collections.synchronizedList(new ArrayList<String>());
		try {
			parseMainXpro4Meta("C:\\Users\\<USER>\\Desktop\\v2\\main.xpro");
		    System.out.println(list);
		} catch (Exception exception) {
			exception.printStackTrace();
		}

//		String deletePath=FilenameUtils.normalize(apiPath+ File.separator + insert.getId() ,true);
//		try {
//			FileUtils.deleteDirectory(Paths.get("D:\\wenzhou\\uploadPath\\31d62a80487646128a42d94e83528ba5").toFile());
//		} catch (IOException ioException) {
//			ioException.printStackTrace();
//		}

	}


	/**
	 * 解析工程文件
	 * @param xproPath
	 * @throws Exception
	 */
	public static List<String> parseMainXpro4Meta(String xproPath) throws Exception {
		try {
			log.info("xpro工程文件路径："+xproPath);
			File xmlfile = new File(xproPath);
			if(!xmlfile.exists()) {
				throw new ServiceException("main.xpro工程文件不存在！");
			}
			SAXReader reader = new SAXReader();
			Document document = reader.read(xmlfile);
			Element root = document.getRootElement();//获取根元素
			Element bookE = root.element("书籍");
			if(bookE==null){
				throw new ServiceException("书籍信息不存在！");
			}
			Element files = bookE.element("文件目录");
			if(files==null){
				throw new ServiceException("文件目录不存在！");
			}
			Element bufen = files.element("部分");
			if(bufen==null){
				throw new ServiceException("部分不存在！");
			}
			
			List<String> list = Lists.newArrayList();
			List<Element> fs = bufen.elements();
			for (Element fe : fs) {
				if(fe!=null){
					String xmlpath = fe.attributeValue("路径");
					list.add(FilenameUtils.normalize(xmlpath, true));
				}
			}
			List<Element> _fs = files.elements();
			for (Element fe : _fs) {
				List<Element> _fss = fe.elements();
				for (Element _fe : _fss) {
					String xmlpath = _fe.attributeValue("路径");
					// log.info(xmlpath);
					list.add(FilenameUtils.normalize(xmlpath, true));
				}
			}
			list = list.stream().distinct().collect(Collectors.toList());
			return list;
		}catch(Exception e) {
			log.error("error,",e);
			throw e;
		}
	}	

	
	public static String  removePage(String content) {
		//String patstr = "(P[0-9]*)";
		//String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		String patstr = "(<原书页面>[0-9A-Za-z-]{1,}</原书页面>)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(content);		    
	    while(m.find()){	 
	    	content = m.replaceAll("");	    	
	    }
	    return content;
	}


}
