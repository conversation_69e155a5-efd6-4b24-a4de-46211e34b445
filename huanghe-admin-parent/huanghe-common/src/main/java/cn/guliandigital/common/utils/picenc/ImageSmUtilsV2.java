package cn.guliandigital.common.utils.picenc;

import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.imageio.ImageIO;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ThumbnailatorUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ImageSmUtilsV2 {
	
	//static DecimalFormat format = new DecimalFormat("0");

	int rows = 5;
	int cols = 5;
	
	int w = 1;
	int h = 1;
	
	/**
     * 切割图片
     *
     * @throws Exception
     */
    public LinkedHashMap<Integer,List<Integer>> splitImage(String inputpath,String outpath,int height,int width) throws Exception {
    	
    	//long st = System.currentTimeMillis();
    	//log.info("==>inputpath:{}",inputpath);
    	File file = new File(inputpath);
        //log.info("File Path : {}", inputpath);
        ImageIO.setUseCache(false);
        FileInputStream fis = new FileInputStream(file);
        BufferedImage bufferimage = ImageIO.read(fis);
        //log.info("Image Height : {}", image.getHeight());
        //log.info("Image Width : {} ", image.getWidth());
        
        int _rows = divi(height);
        int _cols = divi(width);
               
        log.info("==>要拆分的行列{}:{}",_rows,_cols);
        rows = _rows;
        cols = _cols;
        
        w = width;
        h = height;
        
        int chunks = rows * cols;

        int chunkWidth = bufferimage.getWidth() / cols;
        int chunkHeight = bufferimage.getHeight() / rows;

        int count = 0;
        BufferedImage[] imgs = new BufferedImage[chunks];
       
        for (int x = 0; x < rows; x++) {        	
            for (int y = 0; y < cols; y++) {
                imgs[count] = new BufferedImage(chunkWidth, chunkHeight, bufferimage.getType());

                Graphics2D gr = imgs[count++].createGraphics();
                gr.drawImage(bufferimage, 0, 0, chunkWidth, chunkHeight,
                        chunkWidth * y, chunkHeight * x,
                        chunkWidth * y + chunkWidth, chunkHeight * x + chunkHeight, null);
                gr.dispose();               
            }          
        }
        
        if(fis != null) {
        	fis.close();
        }
        for (int i = 0; i < imgs.length; i++) {
            ImageIO.write(imgs[i], "jpg", new File(outpath+"/" + i + ".jpg"));
        }
        //行转列,用于按列展示
        LinkedHashMap<Integer,List<Integer>> map = new LinkedHashMap<Integer,List<Integer>>();
        List<Integer> list = null;
        for (int n = 0; n < cols; n++) {
        	list = Lists.newArrayList();  
        	for (int m = 0; m < rows; m++) {        		
        		list.add(m*cols+n);
        	}        	
        	map.put(n, list);
        }        
              
       
        //long et = System.currentTimeMillis();
        //log.info("==>拆解图片需要时间 {} ms",et-st);
        return map;
    }

    /**
     * 合并图片
     * 输出顺序
     * @throws Exception
     */
    public Map<String,Object> mergeImage(LinkedHashMap<Integer,List<Integer>> normalMap,
    		String input,String outputpath,String baseUrl,String storeType,
    		String id,int image_height,int image_width,String extend) throws Exception {
    	//log.info("==>要合并的行列{}:{}",rows,cols);	
    	//long st = System.currentTimeMillis();
    	Map<String,Object> result = new HashMap<String,Object>();
    	
        int chunks = rows * cols;
        ImageIO.setUseCache(false);
        int chunkWidth, chunkHeight;
        int type;

        File[] imgFiles = new File[chunks];

        BufferedImage[] buffImages = new BufferedImage[chunks];
        List<Integer>  indexList = Lists.newArrayList();
        for (int i = 0; i < chunks; i++) {      
        	imgFiles[i] = new File(input+"/" + i + ".jpg");
            buffImages[i] = ImageIO.read(imgFiles[i]);
            indexList.add(i);
        }
        //打乱顺序 优先对列打乱
        int size = normalMap.size();
        List<Integer> collist = Lists.newArrayList();
        for(int m = 0 ; m < size; m++){
        	collist.add(m);        	
        }
        //随机打乱列顺序
        Collections.shuffle(collist);
       
        type = buffImages[0].getType();
        chunkWidth = buffImages[0].getWidth();
        chunkHeight = buffImages[0].getHeight();

        BufferedImage finalImg = new BufferedImage(chunkWidth * cols, chunkHeight * rows, type);
        LinkedHashMap<Integer,LinkedHashMap<Integer,Integer>> correct_map_rows = new LinkedHashMap<Integer,LinkedHashMap<Integer,Integer>>();
        LinkedHashMap<Integer,List<Integer>> shuff_list_Map = new LinkedHashMap<Integer,List<Integer>>();
        for (int j = 0; j < cols; j++) {
        	LinkedHashMap<Integer,Integer> correct_rows = new LinkedHashMap<Integer,Integer>();
        	//打乱后重新排序
        	int c_idx = collist.get(j);
        	List<Integer> _rowlist = normalMap.get(c_idx);
        	List<Integer> temp_sorts = Lists.newArrayList();
        	temp_sorts.addAll(_rowlist);
        	for (int i = 0; i < rows; i++) {    
        		correct_rows.put(temp_sorts.get(i), i);
        	}
        	correct_map_rows.put(j, correct_rows);
        	
        	List<Integer> rowlist = Lists.newArrayList();
        	rowlist.addAll(_rowlist);
        	
        	Collections.shuffle(rowlist);
        	//记录列新顺序 **
        	shuff_list_Map.put(j, rowlist);
        	
        	for (int i = 0; i < rows; i++) {       		
        		int _idx = rowlist.get(i);
        		//correct_rows.put(bi_idx, i);
        		//System.out.println("==>合并图片行顺序："+_idx);
            	finalImg.createGraphics().drawImage(buffImages[_idx], chunkWidth * j, chunkHeight * i, null);               
            }
        }
          
        //合并后的图
        ImageIO.write(finalImg, "jpeg", new File(outputpath));
        log.info("==>生成的图片是{}", outputpath);
        //
        String baseName = FilenameUtils.getBaseName(outputpath);
        baseName = baseName.replace("_E", "");
        String compressOutPath = FilenameUtils.getFullPath(outputpath) + baseName +"_C."+FilenameUtils.getExtension(outputpath);
        compressOutPath = FilenameUtils.normalize(compressOutPath,true);
        String imgSrc = FilenameUtils.getName(compressOutPath);
        
        //组成链接        
        if("L".equals(storeType)){
        	imgSrc = new StringBuilder().append(baseUrl).append("?picname=").append(id).append("/").append(FilenameUtils.getBaseName(compressOutPath)+".jpx&st="+System.currentTimeMillis()).toString();;
        }else{
        	imgSrc = new StringBuilder().append(baseUrl).append("?picname=").append(id).append("/").append(FilenameUtils.getBaseName(compressOutPath)+"."+extend+"&st="+System.currentTimeMillis()).toString();;
        }
        
        //int total_width = changeImgSize(outputpath,compressOutPath,image_height, image_width, image_height);
        FileUtils.copyFile(new File(outputpath), new File(compressOutPath));
        
        
        int height = image_height / rows;
        int width = image_width / cols;
        int divHeight = image_height;
        /*
         * 生成div层
         */
        StringBuffer div = new StringBuffer(3000);
        div.append("<div id='myImgage' class='kongtiaojifang' style=\"width: "+(image_width)+"px; height: "+(divHeight)+"px; overflow: hidden;transform: scale(1); padding:0; \"  ref=\"kongtiao\"  >");
        div.append("<div md5='' change='0' style=\"width: "+image_width+"px; height: "+divHeight+"px; writing-mode:horizontal-tb; -webkit-writing-mode:horizontal-tb; -ms-writing-mode:lr-tb; padding:0; \" >");
        
        for (int j = 0; j < cols; j++) {
        	
        	div.append("<div style=\"display:inline-block;width:"+width+"px;height:"+divHeight+"px;\">   ");
        	//打乱后重新排序        	
        	int c_idx = getIdx(collist,j);        	
        	List<Integer> rowlist = shuff_list_Map.get(c_idx);

            LinkedHashMap<Integer,Integer> correct_rows = correct_map_rows.get(c_idx);
            //System.out.println(correct_rows);
            //重点计算
    		int margin_left = 0-c_idx*width;
    		
        	for (int i = 0; i < rows; i++) { 		
        		
        		//这个位置比较绕
        		int img_idx = getZX(correct_rows,i);
        		//取下标        		    
        		int _idx = getIdx(rowlist,img_idx);        		
        		
        		int margin_top = 0-_idx*height;
        		//logger.info("==>正确位置："+_idx+",margin_top:"+margin_top);
        		div.append("<div style=\"overflow:hidden;width:"+width+"px;height:"+height+"px;\">");
        		div.append("<img style=\"margin-left:"+margin_left+"px;margin-top:"+margin_top+"px;\" width=\""+image_width+"\" src=\""+imgSrc+"\" class=\"lazy\" />");
        		div.append("</div>");
        		
            }
        	div.append("</div>");
        }
        
        div.append("</div>");
        div.append("</div>");
        
        //进行加密
        String divstr = div.toString();
        //divstr = divstr.replace("md5=''", "md5='"+Md5Utils.hash(divstr)+"'");
        divstr = divstr.replace("md5=''", "md5='"+IdUtils.simpleUUID()+"'");
       // log.info(divstr);
        result.put("divCss", divstr);
        result.put("encPicPath", compressOutPath);
        //result.put("totalHeight", image_height);
        //result.put("totalWidth", image_width);
        //删除临时文件
        for (int i = 0; i < imgFiles.length; i++) {
        	boolean isdelete = imgFiles[i].delete();
            if(!isdelete) {
            	log.info("文件{}删除失败",imgFiles[i].getAbsolutePath());
            }
            //logger.info("==>删除成功");
        }
        File dir = new File(input);
        if(dir.exists()){
        	boolean isdelete = dir.delete();
        	if(!isdelete) {
            	log.info("文件夹{}删除失败",dir.getAbsolutePath());
            }
        }
        //long et = System.currentTimeMillis();
       // log.info("==>拼接图片需要时间 {} ms",et-st);
        return result;       
    }
    
    /**
     * 
     * @param map
     * @param val
     * @return 得到顺序
     */
    public int getZX(Map<Integer,Integer> map,int val){
    	int result = 0;
    	for(Entry<Integer,Integer> entry:map.entrySet()){
    		int key = entry.getKey();
    		int value = entry.getValue();
    		if(val == value){
    			result = key;
    			break;
    		}
    	}
    	return result;
    }
    
    /**
     * 返回下标
     * @param list
     * @param val
     * @return
     */
    public int getIdx(List<Integer> list,int val){
    	int result = 0;
    	for(int i=0;i<list.size();i++){
    		if(val == list.get(i)){
    			result = i;
    			break;
    		}
    	}
    	return result;
    }
 
    
   public int getScreenWidth(int image_width,int image_height, int height) {
	   int width = 0;
	   double ratio = (double)image_height/height;
       BigDecimal  b = new BigDecimal(ratio);  
       ratio   =   b.setScale(1,   BigDecimal.ROUND_HALF_UP).doubleValue(); 
       width = (int)(image_width / ratio);
       
//       log.info("==>image_height:{}",image_height);
//       log.info("==>image_width:{}",image_width);
       log.info("==>ratio:{}",ratio);
       log.info("==>height:{}",height);
       log.info("==>width:{}",width);
      
       return width;
   }
       
    /**
     * 压缩图像
     * @param inputPath
     * @param outPath
     * @param height
     * @return
     */
    public int changeImgSize(String inputPath,String outputPath,int height,int image_width,int image_height){
    	int width = 0;
    	try{
    		//long st = System.currentTimeMillis();
	    	//读取图片
//	         BufferedInputStream in = new BufferedInputStream(new FileInputStream(inputPath));
//	         //字节流转图片对象
//	         Image bi =ImageIO.read(in);
//	         //获取图像的高度，宽度
//	         int image_height = bi.getHeight(null);
//	         int image_width = bi.getWidth(null);
//	         double ratio = (double)image_height/height;
//	         BigDecimal  b = new BigDecimal(ratio);  
//	         ratio   =   b.setScale(1,   BigDecimal.ROUND_HALF_UP).doubleValue(); 
//	         width = (int)(image_width / ratio);
//	         
//	         log.info("==>image_height:{}",image_height);
//	         log.info("==>image_width:{}",image_width);
//	         log.info("==>ratio:{}",ratio);
//	         log.info("==>height:{}",height);
//	         log.info("==>计算后width:{}",width);
	        
	         
	        //构建图片流
//	         BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//	         //绘制改变尺寸后的图
//	         tag.getGraphics().drawImage(bi, 0, 0,width, height, null);  
//	         //输出流
//	         BufferedOutputStream out = new BufferedOutputStream(new FileOutputStream(outputPath));
//	         //JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
//	         //encoder.encode(tag);
//	         ImageIO.write(tag, "jpeg",out);
//	         in.close();
//	         out.close();
	         
    		 width = getScreenWidth(w, h, height);
    		
	         //log.info("==>inputPath:{}",inputPath);
	         //log.info("==>outputPath:{}",outputPath);
	         ThumbnailatorUtils.compressPic(inputPath, outputPath, height, width);
	         //TifCompressPythonUtils.tifCompressHandler(inputPath, outputPath, height, width);
	         //long et = System.currentTimeMillis();
	         //log.info("==>压缩图片需要时间{}ms",et-st);
    	}catch(Exception e){
    		log.error("",e);
    	}
    	return width;
    }
    
    /**
     * 压缩图片
     * @param inputPath
     * @param outputPath
     * @param height
     * @param width
     * @return
     */
    public static int compressPicSize(String inputPath,String outputPath,int height, int width){
    	
    	try{
    		//long st = System.currentTimeMillis();
//	    	//读取图片
//	         BufferedInputStream in = new BufferedInputStream(new FileInputStream(inputPath));
//	         //字节流转图片对象
//	         Image bi =ImageIO.read(in);
//	         ImageIO.setUseCache(false);
//	         //获取图像的高度，宽度
//	         int image_height = bi.getHeight(null);
//	         int image_width = bi.getWidth(null);
//	         double ratio = (double)image_height/height;
//	         BigDecimal  b = new BigDecimal(ratio);  
//	         ratio   =   b.setScale(1,   BigDecimal.ROUND_HALF_UP).doubleValue(); 
//	         width = (int)(image_width / ratio);
//	         
//	         log.info("==>image_height:{}",image_height);
//	         log.info("==>image_width:{}",image_width);
//	         log.info("==>ratio:{}",ratio);
//	         log.info("==>width:{}",width);
	        
	         
//	        //构建图片流
//	         BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//	         //绘制改变尺寸后的图
//	         tag.getGraphics().drawImage(bi, 0, 0,width, height, null);  
//	         //输出流
//	         BufferedOutputStream out = new BufferedOutputStream(new FileOutputStream(outputPath));
//	         //JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
//	         //encoder.encode(tag);
//	         ImageIO.write(tag, "jpeg",out);
//	         in.close();
//	         out.close();
	         //TifCompressPythonUtils.tifCompressHandler(inputPath, outputPath, height, width);
    		ThumbnailatorUtils.compressPic(inputPath, outputPath, height, width);
	         //log.info("==>inputPath:{}",inputPath);
	         //log.info("==>outputPath:{}",outputPath);	 
	         ////long et = System.currentTimeMillis();
	         //log.info("==>预压缩图片需要时间{}ms",et-st);
    	}catch(Exception e){
    		log.error("",e);
    	}
    	return width;
    }
    
    
    //判断一个数被谁整除
    public int divi(int a) {
    	log.info("==>待计算拆分的值：{}",a);
    	int result = 5;
    	for(int i = 2;i < 10;i++) {
    		if(a % i == 0) {
    			result = i;
    			break;
    		}
    	}   
    	
    	return result;
    }
    
    /**
     * 推导出高度
     * @param platHeight
     * @param picHeight
     * @param picWidth
     * @return
     */
    public static Map<String,Integer> deduceHeight(int divHeight,int picHeight,int picWidth) {
    	int compressH = 0;
    	int compressW = 0;
    	Map<String,Integer> map = Maps.newHashMap();
    	//数据预处理
    	//log.info("==>数据预处理开始，H:{}：W:{}",picHeight, picWidth);
    	if(divHeight % 2 == 1) {
    		divHeight = divHeight + 1;
    	}
    	for(int m = picHeight; m>0; m--) {
        	if(m % 200 ==0) {
        		compressH = m;
        		break;
        	}
        }       
        for(int n = picWidth; n>0; n--) {
        	if(n % 200 ==0) {
        		compressW = n;
        		break;
        	}
        }
        //log.info("==>数据预处理结束，H:{}：W:{}",compressH, compressW);
        
        map.put("width", compressW);
		map.put("height", compressH);
		map.put("platWidth", 0);
		map.put("platHeight", divHeight);
		
		return map;       
    }
    
   
    public static void getHeight(int h,int w,List<Integer> list) {
    	    	
    	for(int x = 100; x < h ; x++) {
    		int y = h % x;
    		int y1 = 0;
    		if( y == 0) {
    			y1 = h / x;
    		}else {
    			continue;
    		}
    		int y2 = w % y1;
    		if(y2 == 0) {
    			list.add(x);
    		}
    	}
    }
    
    public static void main(String[] args) {
        try {
//        	String path = "C:\\Users\\<USER>\\Desktop\\测试文件加密\\";
//        	String originalImg = path+"a078e6fe6be4456eaed268883a263afa.jpg";
//        	
//        	//String outpath_size = changeImgSize(originalImg,260);
//        	String outpath_size = originalImg;
//        	LinkedHashMap<Integer,List<Integer>> map = splitImage(outpath_size,path,500);
//        	System.out.println(map);
//        	
//            String outpath = path+"finally.jpg";
//            Map<String,Object> result = mergeImage(map,path,outpath,1000,"","L");
//            
//            log.info("==>图片展示顺序："+result);
//                    
//            
//            log.info("==>生成新图片成功<==");
//        	int image_height = 3132;
//        	int height = 639;
//        	double ratio = (double)(image_height) / height;
//        	ratio = Double.parseDouble(format.format(ratio));
//        	System.out.println(ratio);
//        	
//            int image_width = 2150;
//            int width = (int)(image_width / ratio);
//            System.out.println(width);
        	
        	
//        	log.info(""+deduceHeight(651, 4398, 3158));
//        	
//        	List<Integer> list = Lists.newArrayList();
//        	getGreatestCommonDivisor(4200,3000 ,list);
//        	log.info("==>size:"+list.size());
//        	log.info("==>"+StringUtil.join(list,","));
        	List<Integer> list = Lists.newArrayList();
        	getHeight(4700, 3400 ,list);
        	log.info("==>size:"+list.size());
        	log.info("==>"+StringUtil.join(list,","));
        } catch (Exception e) {
        	log.error("error,", e);
        }
    }
    
    
}
