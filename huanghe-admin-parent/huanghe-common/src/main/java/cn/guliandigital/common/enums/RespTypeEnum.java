package cn.guliandigital.common.enums;

/**
 * 待处理文本类型
 * 
 * <AUTHOR>
public enum RespTypeEnum
{
    TEXT("text", "文本"),
    JSON("json", "json"),
    ARRAY("array", "数组");

    private final String code;
    private final String info;

    RespTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
