package cn.guliandigital.common.enums;

/**
 * 订单状态
 * 
 * <AUTHOR>
 */
public enum OrderStatus
{
    NOT("0", "未开通"),
    DONE("1", "已开通");

    private final String code;
    private final String info;

    OrderStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
