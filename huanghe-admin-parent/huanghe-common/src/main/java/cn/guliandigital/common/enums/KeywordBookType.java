package cn.guliandigital.common.enums;

import cn.guliandigital.common.utils.StringUtil;

/**
 * 检索类型
 * 
 */
public enum KeywordBookType {

	ALL("all", "综合", new String[] { "bookDesc", "bookName" }), //
	BOOK_NAME("bookName", "题名", new String[] { "bookName" }), //
	MAIN_RESPONSIBILITY("mainResponsibility", "作者", new String[] { "mainResponsibility" }), //
	PUBLISHER("publisher", "出版者", new String[] {});

	private final String code;
	private final String info;
	private final String[] fields;

	KeywordBookType(String code, String info, String[] fields) {
		this.code = code;
		this.info = info;
		this.fields = fields;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}

	public String[] getFields() {
		return fields;
	}

	public static KeywordBookType getByCode(String code) {
		if (StringUtil.isBlank(code)) {
			return KeywordBookType.ALL;
		}
		KeywordBookType[] values = values();
		for (KeywordBookType keywordType : values) {
			if (StringUtil.equals(keywordType.getCode(), code)) {
				return keywordType;
			}
		}
		return KeywordBookType.ALL;
	}
}
