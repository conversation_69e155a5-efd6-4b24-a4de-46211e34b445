package cn.guliandigital.common.utils;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.io.FileUtil;
import org.apache.commons.io.FilenameUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * base64Utils
 *
 * <AUTHOR>
 * @since 2024-11-05 11:11
 */
public class Base64Utils {



    /**
     * 将base64转为路径
     * @param content
     * @param httpUrl
     * @return
     */
    public static String base64ToPng(String content,String httpUrl,String downloadPath){
        Document doc = Jsoup.parse(content);
        Elements strong = doc.select("img");
        List<String> list=new ArrayList<>();
        for (Element element:strong){
            list.add(element.getElementsByAttribute("src").attr("src"));
        }
        for (String base:list){
            if (base.contains("base64,")){
                String b = base.split(",")[1];
                // base64字符串 转 文件
                byte[] decode = Base64Decoder.decode(b);
                String normalize = FilenameUtils.normalize(HuangHeConfig.getUploadPath() + File.separator + "base64" + File.separator);
                new File(normalize).mkdirs();
                String path=FilenameUtils.normalize(normalize+ IdUtils.simpleUUID()+".png");
                File file = FileUtil.writeBytes(decode,path);
                //替换图片链接
                String replace = path.replace(FilenameUtils.normalize(HuangHeConfig.getProfile()), FilenameUtils.normalize(Constants.RESOURCE_PREFIX));
                replace=replace.replace("\\","/");
                String url=httpUrl+downloadPath+replace;
                content=content.replace(base,url);
            }
        }
        return content;
    }
}
