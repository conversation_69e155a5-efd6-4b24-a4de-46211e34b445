package cn.guliandigital.common.core.domain.model;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @version V1.0
 * @Description:
 * @Auther: BOHANZHANG
 * @date 2020-11-21 16:18
 * @Title: TransformVo.java
 * @Package: cn.guliandigital.dev.entity
 */

@Data
public class TransformVo {

    private static final long serialVersionUID = 1L;

    /*原文*/
    private String content;

    /*操作类型，1-转简体，2-转繁体，3-筛简体，4-筛繁体*/
    private String type;


    /*繁简转换数据id*/
    private String id;

    /*满意度*/
    private String number;

    /*繁简转换任务id*/
    private String taskId;

    /*下拉选选中值数组*/
    private String[] selectArray;

    /*带标签的文本*/
    private String tagContent;

    /*邮箱*/
    private String mailAddress;

    /*上传文件*/
    private MultipartFile file;


}
