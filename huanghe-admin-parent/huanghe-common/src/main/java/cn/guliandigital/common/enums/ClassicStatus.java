package cn.guliandigital.common.enums;

public enum ClassicStatus {

    DOWN("0", "下架"),
    UP("1", "上架");

    private final String code;
    private final String info;

    ClassicStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
