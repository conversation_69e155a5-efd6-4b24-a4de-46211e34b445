package cn.guliandigital.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConfigurationProperties(prefix = "huanghe")
public class HuangHeConfig
{
        
    /** 项目名称 */
    private String name;

    /** 版本 */
    private String version;

    /** 版权年份 */
    private String copyrightYear;

    /** 实例演示开关 */
    private boolean demoEnabled;

    /** 上传路径 */
    private static String profile;

    /** 获取地址开关 */
    private static boolean addressEnabled;
    
    private static boolean sslEnabled;
     
    /** 系统访问url 解析xml数据时使用  */
    private static String platHttpUrl;
    
    /** 图片加密连接 **/
   // private String picEncPaths;
    
    private static String tif2jpghttp;
    
    private static String tifcompresshttp;


    private static String pdfWordUrl;//word转pdf

    private static String pdfMenuUrl;//获取pdf目录树

    private static String pdfContentUrl;//获取pdf每页内容

    private static String pdfApiKey;//pdf接口key


    public static String getPdfApiKey() {
        return pdfApiKey;
    }

    public void setPdfApiKey(String pdfApiKey) {
        HuangHeConfig.pdfApiKey = pdfApiKey;
    }

    public static String getPdfWordUrl() {
        return pdfWordUrl;
    }

    public void setPdfWordUrl(String pdfWordUrl) {
        HuangHeConfig.pdfWordUrl = pdfWordUrl;
    }

    public static String getPdfMenuUrl() {
        return pdfMenuUrl;
    }

    public void setPdfMenuUrl(String pdfMenuUrl) {
        HuangHeConfig.pdfMenuUrl = pdfMenuUrl;
    }

    public static String getPdfContentUrl() {
        return pdfContentUrl;
    }

    public void setPdfContentUrl(String pdfContentUrl) {
        HuangHeConfig.pdfContentUrl = pdfContentUrl;
    }


    
	public static String getImgCompressHttp() {
    	return tifcompresshttp;
    }
    public static String getImgTransformHttp() {
    	return tif2jpghttp;
    }
    
    public String getTif2jpghttp() {
		return tif2jpghttp;
	}

	public void setTif2jpghttp(String tif2jpghttp) {
		this.tif2jpghttp = tif2jpghttp;
	}
	public String getTifcompresshttp() {
		return tifcompresshttp;
	}

	public void setTifcompresshttp(String tifcompresshttp) {
		this.tifcompresshttp = tifcompresshttp;
	}
    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getVersion()
    {
        return version;
    }

    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getCopyrightYear()
    {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear)
    {
        this.copyrightYear = copyrightYear;
    }

    public boolean isDemoEnabled()
    {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled)
    {
        this.demoEnabled = demoEnabled;
    }

    public static String getProfile()
    {
        return profile;
    }

    public void setProfile(String profile)
    {
        HuangHeConfig.profile = profile;
        log.info("==>上传路径是：{}", HuangHeConfig.profile);
    }

    public static boolean isAddressEnabled()
    {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled)
    {
        HuangHeConfig.addressEnabled = addressEnabled;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath()
    {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath()
    {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath()
    {
        return getProfile() + "/upload";
    }

	public static String getPlatHttpUrl() {
		return HuangHeConfig.platHttpUrl;
	}

	public void setPlatHttpUrl(String platHttpUrl) {
		HuangHeConfig.platHttpUrl = platHttpUrl;
		log.info("==>platHttpUrl是：{}",HuangHeConfig.platHttpUrl);
	}
	public static boolean isSslEnabled() {
		return sslEnabled;
	}
	public static void setSslEnabled(boolean sslEnabled) {
		HuangHeConfig.sslEnabled = sslEnabled;
		//log.info("==>sslEnabled：{}",HuangHeConfig.sslEnabled);
	}

//	public String getPicEncPath() {
//		return picEncPath;
//	}
//
//	public void setPicEncPath(String picEncPath) {
//		this.picEncPath = picEncPath;
//	}
}
