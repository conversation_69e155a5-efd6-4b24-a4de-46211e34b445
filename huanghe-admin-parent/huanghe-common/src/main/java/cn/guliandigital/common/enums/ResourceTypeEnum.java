package cn.guliandigital.common.enums;

public enum ResourceTypeEnum {

	T("T","全文"),
	PDF("PDF","pdf");



    private final String code;
    private final String info;

    ResourceTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(String code) {
        ResourceTypeEnum[] imagetextTypeEnums = values();
        for (ResourceTypeEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getCode().equals(code)) {
                return imagetextTypeEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static String getCode(String message) {
        ResourceTypeEnum[] imagetextTypeEnums = values();
        for (ResourceTypeEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getInfo().equals(message)) {
                return imagetextTypeEnum.getCode();
            }
        }
        return null;
    }
}
