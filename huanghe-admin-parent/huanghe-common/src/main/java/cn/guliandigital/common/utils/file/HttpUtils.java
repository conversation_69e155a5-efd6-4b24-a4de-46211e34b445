package cn.guliandigital.common.utils.file;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.nio.charset.Charset;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.http.Consts;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.google.common.base.Strings;

import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * Created by 陈淑瑞 on 2018/5/13.
 */

@Slf4j
public class HttpUtils {

	public static final PoolingHttpClientConnectionManager cm;
	public static final CloseableHttpClient httpClient;

	static{
		cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(200);
		cm.setDefaultMaxPerRoute(100);
		RequestConfig config = RequestConfig.custom()
					.setConnectTimeout(10000)
					.setSocketTimeout(10000)
					.build();
		httpClient = HttpClientBuilder.create()
					.setConnectionManager(cm)
					.setDefaultRequestConfig(config)
					.build();
	}


	/**
     * post方式
     * @param url
     * @param param
     * @return
     */
    public static Map<String, Object> doPost(String url, Map<String, Object> param) {

    	Map<String, Object> data = null;
        CloseableHttpResponse response = null;
        log.info("==>请求连接：{}", url);
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);

            httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");

            // 创建参数列表
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (Entry<String,Object> entry : param.entrySet()) {
                	String key = entry.getKey();
                	Object obj = entry.getValue();
                	if(obj instanceof String){
                		paramList.add(new BasicNameValuePair(key, obj.toString()));
                	}else {
                		paramList.add(new BasicNameValuePair(key, obj.toString()));
                	}
                }
                // 模拟表单
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList,"UTF-8");
                httpPost.setEntity(entity);
            }
            //log.info("==>开始发送http请求：{}", httpPost.getAllHeaders());
            // 执行http请求
            response = httpClient.execute(httpPost);
            String resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            //System.out.println(resultString);
            //log.info(resultString);
            if(!Strings.isNullOrEmpty(resultString)){
            	JSONObject json = JSONObject.fromObject(resultString);
            	data = parseJSON2Map(json);
            }
        } catch (Exception e) {
            log.error("",e);
        } finally {
            try {
            	if(response != null) {
            		response.close();
            	}
            } catch (IOException e) {
            	log.error("",e);
            }
        }

        return data;
    }

    public static Map<String, Object> parseJSON2Map(JSONObject json) {
        Map<String, Object> map = new HashMap<String, Object>();
        // 最外层解析
        for (Object k : json.keySet()) {
            Object v = json.get(k);
            // 如果内层还是json数组的话，继续解析
            if (v instanceof JSONArray) {
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                Iterator<JSONObject> it = ((JSONArray) v).iterator();
                while (it.hasNext()) {
                    JSONObject json2 = it.next();
                    list.add(parseJSON2Map(json2));
                }
                map.put(k.toString(), list);
            } else if (v instanceof JSONObject) {
                // 如果内层是json对象的话，继续解析
                map.put(k.toString(), parseJSON2Map((JSONObject) v));
            } else {
		// 如果内层是普通对象的话，直接放入map中
                map.put(k.toString(), v);
            }
        }
        return map;
    }
   /**
     * post提交
     * @param url
     * @param params
     * @param file
     * @return
     */
    public static Map<String, Object> doPostFile(String url, Map<String, Object> params) {
    	Map<String, Object> data = null;
        try {
            HttpPost post = new HttpPost(url);
            MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
    		entityBuilder.setCharset(Charset.forName("UTF-8"));
    		entityBuilder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
    		ContentType contentType = ContentType.create("text/plain", "UTF-8");
    		if (!Objects.isNull(params)) {
    			 for (Entry<String,Object> entry : params.entrySet()) {
                 	String key = entry.getKey();
                 	Object obj = entry.getValue();
                	//Object obj = params.get(key);
                	if(obj instanceof File){ //参数是File 类型
        				File _file = (File) obj;
        				entityBuilder.addPart(key, new FileBody(_file));
        			}else if(obj instanceof String){ //参数是String 类型
        				String value = (String) obj;
        				entityBuilder.addPart(key, new StringBody(value,contentType));
        			}
                }
            }

    		post.setEntity(entityBuilder.build());

			CloseableHttpResponse response = httpClient.execute(post);
			HttpEntity entity = response.getEntity();
			if(entity!=null){
				String resultString =  EntityUtils.toString(entity, "UTF-8");
				if(!Strings.isNullOrEmpty(resultString)){
					JSONObject json = JSONObject.fromObject(resultString);
	            	data =parseJSON2Map(json);
	            }
			}
			EntityUtils.consume(entity);
			response.close();

        } catch (Exception ex) {
        	log.error("",ex);
        }
        return data;
    }


    /**
     * 下载文件
     *
     * @param url
     *            http://www.xxx.com/img/333.jpg
     * @param destFileName
     *            xxx.jpg/xxx.png/xxx.txt
     * @throws ClientProtocolException
     * @throws IOException
     */
    public static void getFile(String url, String destFileName)
            throws ClientProtocolException, IOException {
        // 生成一个httpclient对象
        //CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGet httpget = new HttpGet(url);
        HttpResponse response = httpClient.execute(httpget);
        HttpEntity entity = response.getEntity();
        InputStream in = entity.getContent();
        File file = new File(destFileName);
        try {
            FileOutputStream fout = new FileOutputStream(file);
            int l = -1;
            byte[] tmp = new byte[1024];
            while ((l = in.read(tmp)) != -1) {
                fout.write(tmp, 0, l);
                // 注意这里如果用OutputStream.write(buff)的话，图片会失真，大家可以试试
            }
            fout.flush();
            fout.close();
        } finally {
            // 关闭低层流。
            in.close();
        }
        //httpClient.close();
    }


    public static final ContentType TEXT_PLAIN_UTF_8 = ContentType.create(ContentType.TEXT_PLAIN.getMimeType(),
			Consts.UTF_8);

	public static Entry<Boolean, String> post(String url, String text) {
		StringEntity stringEntity = new StringEntity(text, Consts.UTF_8);
		return post(url, stringEntity);
	}

	public static Entry<Boolean, String> post(String url, String text, ContentType contentType) {
		StringEntity stringEntity = new StringEntity(text, contentType);
		return post(url, stringEntity);
	}

	public static Entry<Boolean, String> post(String url, String text, ContentType contentType, Header... headers) {
		StringEntity stringEntity = new StringEntity(text, contentType);
		return post(url, stringEntity, headers);
	}

	public static Entry<Boolean, String> post(String url, StringEntity stringEntity, Header... headers) {
		try {
			CloseableHttpClient httpClient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(stringEntity);
			if(ArrayUtils.isNotEmpty(headers)) {
				httpPost.setHeaders(headers);
			}
			CloseableHttpResponse response = httpClient.execute(httpPost);
			HttpEntity entity = response.getEntity();
			boolean isSuccess = false;
			String result = EntityUtils.toString(entity, "UTF-8");
			if (response.getStatusLine().getStatusCode() == 200) {
				isSuccess = true;
			}
			Entry<Boolean, String> entry = new AbstractMap.SimpleEntry<>(isSuccess, result);
			response.close();
			httpClient.close();
			return entry;
		} catch (Exception e) {
			log.error("",e);
			Entry<Boolean, String> entry = new AbstractMap.SimpleEntry<>(false, e.getMessage());
			return entry;
		}
	}
	
	
	public static Entry<Integer, String> get(String url) {
		try {
			HttpGet httpGet = new HttpGet(url);
			CloseableHttpResponse response = httpClient.execute(httpGet);
			HttpEntity entity = response.getEntity();
			String result = EntityUtils.toString(entity, "UTF-8");
			int statusCode = response.getStatusLine().getStatusCode();
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(statusCode, result);
			response.close();
			httpClient.close();
			return entry;
		} catch (Exception e) {
			log.info("异常 -> {}", e.getMessage());
			log.error("异常 -> ", e);
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(-1, "请求异常！");
			if (e instanceof SocketTimeoutException) {
				entry.setValue("请求超时！");
			}
			return entry;
		}
	}
	
	public static Entry<Integer, String> get1(String url) {
		try {
			CloseableHttpClient httpClient = HttpClients.createDefault();
			HttpGet httpGet = new HttpGet(url);
			RequestConfig requestConfig = RequestConfig.custom() //
					.setSocketTimeout(60 * 1000) //
					.setConnectTimeout(60 * 1000).build();// 设置请求和传输超时间
			httpGet.setConfig(requestConfig);

			CloseableHttpResponse response = httpClient.execute(httpGet);
			HttpEntity entity = response.getEntity();
			String result = EntityUtils.toString(entity, "UTF-8");
			int statusCode = response.getStatusLine().getStatusCode();
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(statusCode, result);
			response.close();
			httpClient.close();
			return entry;
		} catch (Exception e) {
			log.info("异常 -> {}", e.getMessage());
			log.error("异常 -> ", e);
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(-1, "接口异常未识别出结果！");
			if(e instanceof SocketTimeoutException) {
				entry.setValue("识别超时，请重试！");
			}
			return entry;
		}
	}
	
	public static Entry<Integer, String> post1(String url, String text, ContentType contentType) {
		StringEntity stringEntity = new StringEntity(text, contentType);
		return post1(url, stringEntity);
	}
	
	public static Entry<Integer, String> post1(String url, HttpEntity httpEntity, Header... headers) {
		try {
			CloseableHttpClient httpClient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(httpEntity);
			if (ArrayUtils.isNotEmpty(headers)) {
				httpPost.setHeaders(headers);
			}

			RequestConfig requestConfig = RequestConfig.custom() //
					.setSocketTimeout(3 * 60 * 1000) //
					.setConnectTimeout(3 * 60 * 1000).build();// 设置请求和传输超时间
			httpPost.setConfig(requestConfig);

			CloseableHttpResponse response = httpClient.execute(httpPost);
			HttpEntity entity = response.getEntity();
			String result = EntityUtils.toString(entity, "UTF-8");
			int statusCode = response.getStatusLine().getStatusCode();
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(statusCode, result);
			response.close();
			httpClient.close();
			return entry;
		} catch (Exception e) {
			log.info("异常 -> {}", e.getMessage());
			log.error("异常 -> ", e);
			Entry<Integer, String> entry = new AbstractMap.SimpleEntry<>(-1, "接口异常未识别出结果！");
			if (e instanceof SocketTimeoutException) {
				entry.setValue("识别超时，请重试！");
			}
			return entry;
		}
	}
}

