package cn.guliandigital.common.utils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.map.LinkedMap;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BigwordUtil {

	/**
	 * 是否含有标点符号
	 * @param str
	 * @return
	 */
	public static boolean isP(String str) {
		
		boolean result = false;
		Pattern pattern = Pattern.compile("\\pP");// 匹配标点符号

		Matcher matcher = pattern.matcher(str);

		if (matcher.find()) {

			result = true;
		}
		return result;
	}

	/**
	 * 
	 * @param word
	 *            原始字数
	 * @param searchResult
	 *            检索结果
	 * @return
	 */
	public static List<String> getHightlight(String word, String searchResult) {
		char[] chars = word.toCharArray();
		Pattern p = Pattern.compile(
				"[\uFF01]|[\uFF0C-\uFF0E]|[\uFF1A-\uFF1B]|[\uFF1F]|[\uFF08-\uFF09]|[\u3001-\u3002]|[\u3010-\u3011]|[\u201C-\u201D]|[\u2013-\u2014]|"
						+ "[\u2018-\u2019]|[\u2026]|[\u3008-\u300F]|[\u3014-\u3015]|[\u00A0]|[\u0020]|[\u3000]");
		Matcher m = null;
		Map<Integer, String> map = new LinkedMap<Integer, String>();
		List<Integer> indexList = new LinkedList<Integer>();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < chars.length; i++) {
			m = p.matcher(String.valueOf(chars[i]));
			if (m.find()) {
				map.put(i, String.valueOf(chars[i]));
				indexList.add(i);
			} else {
				sb.append(String.valueOf(chars[i]));
			}
		}
		// searchResult = MarkUtils.characterRepairSqueezeBubblesMark(searchResult,
		// word, "red", "", true);
		List<String> list = Lists.newArrayList();
		int len = sb.length();
		// log.info(""+word.length());
		Document doc = Jsoup.parse(searchResult);
		// log.info(doc.html());
		Elements elements = doc.getElementsByTag("red");
		Iterator<Element> its = elements.iterator();
		StringBuilder buf = new StringBuilder();
		while (its.hasNext()) {
			Element el = its.next();
			String _word = el.text();
			// log.info("==>_word:"+_word);
			buf.append(_word);
		}
		// 进行切分放到list中
		String res = buf.toString();
		int realLen = res.length();
		int count = realLen / len;
		Map<String, String> uniqueMap = Maps.newHashMap();
		for (int i = 0; i < count; i++) {
			String _word = StringUtil.substring(res, i * len, (i + 1) * len);
			// list.add(_word);
			uniqueMap.put(_word, _word);
		}
		list = uniqueMap.keySet().stream().collect(Collectors.toList());

		StringBuffer str = new StringBuffer();
		List<String> returnList = new ArrayList<String>();
		for (String sss : list) {
			str.setLength(0);
			str = new StringBuffer(sss);
			str.setLength(word.length());
			for (Integer index : indexList) {
				str.insert(index, map.get(index));
			}
			returnList.add(str.toString().trim());
			log.info("====>搜索内容{},{}", word.toString().length(), word.toString());
			log.info("====>处理内容{},{}", str.toString().trim().length(), str.toString().trim());
		}
		return returnList;

	}

	public static String formatWord2Desc(String word, Map<String, String> newMapping) {

		if (Strings.isNullOrEmpty(word)) {
			return word;
		}
		// 正文转密文
		StringBuilder descBuff = new StringBuilder();

		descBuff.setLength(0);
		String u4 = UnicodeUtils.string2Unicode(word);
		String[] u4arry = u4.split("\\\\u");

		for (int i = 0; i < u4arry.length; i++) {
			String u4_str = u4arry[i];
			// log.info(u4_str);
			String mapKey = "";
			if (!Strings.isNullOrEmpty(u4_str)) {
				// 此处可以去掉判断，为了以后冗余
				if (BigwordUtil.isDoubleChinese(u4_str)) {
					i = i + 1;
					u4_str = "\\u" + u4_str + "\\u" + u4arry[i];
					mapKey = u4_str;
				} else {
					mapKey = "\\u" + u4_str;
				}
				String newWord = newMapping.get(mapKey);
				if (Strings.isNullOrEmpty(newWord)) {
					newWord = UnicodeUtils.unicode2String(mapKey);
				}

				descBuff.append(newWord);
			}
		}

		return descBuff.toString();
	}
	
	
	public  static String tranferBigwordSpan(String word){
		//log.info(word);
		String u4str = word;
		try {
			if(!Strings.isNullOrEmpty(word)){				
				word = Pattern.compile("\\u3000").matcher(word).replaceAll("&#12288;");
				word = Pattern.compile("\\s").matcher(word).replaceAll("&nbsp;");
				//替换&#x22c7f;								
				String u4 = UnicodeUtils.string2Unicode(word);								
				String[] u4arry = u4.split("\\\\u");
				StringBuilder usb = new StringBuilder();
				for(int i =0; i< u4arry.length;i++){
					String u4_str = u4arry[i];
					//log.info(u4_str);	
					if(!Strings.isNullOrEmpty(u4_str)){
						//此处可以去掉判断，为了以后冗余
						if(isDoubleChinese(u4_str)){							
							//log.info(u4_str);	
							i = i+1;
							u4_str = "\\u"+u4_str + "\\u"+u4arry[i];							
							String bigword = word.substring(0, 2);	
							word = word.replace(bigword, "");
							String ueu = str2Hex(bigword);							
							usb.append(ueu);
								
						}else {
							String uword = UnicodeUtils.unicode2String("\\u"+u4_str);
							
							word = word.replace(uword, "");							
							usb.append(uword);
						}
					}
				}
				u4str = usb.toString();												
			}	
			if(!Strings.isNullOrEmpty(u4str)) {
				return replaceSpan2(u4str);
			}
		} catch (Exception e) {			
			log.error("error,", e);
			log.info(word);
		}
		return word;
	}

	
	public static String replaceSpan2(String str) {
		Map<String,String> map = Maps.newHashMap();
		String patstr = "(&#x[0-9a-z;]{6})";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);
	    //String uc6 = null;
	    StringBuilder wordstr = new StringBuilder();
	    String result = str;
	    while( m.find()){	      
	       String waizi = m.group();
	       if(map.containsKey(waizi)) {
	    	   continue;
	       }else {
	    	   map.put(waizi, waizi);
	       }
	       waizi =waizi.toLowerCase();
	       String ft = fontType(waizi);
	       if("02".equals(ft)) {
	    	   wordstr.append("<span class=\"big02\">").append(waizi).append("</span>");
	       }else if("15".equals(ft)){
	    	   wordstr.append("<span class=\"big15\">").append(waizi).append("</span>");
	       }
	       result = StringUtils.replace(result, waizi, wordstr.toString());
	       wordstr.setLength(0);	       	       	      
	    }
	    //log.info("==>处理结果：{}",result);
		return result;
	}
	
	public static String tranferBigwords(String word) {
		String u4str = word;
		try {
			if (!Strings.isNullOrEmpty(word)) {
				// word = Pattern.compile("\\u3000").matcher(word).replaceAll("&#12288;");
				// word = Pattern.compile("\\s").matcher(word).replaceAll("&nbsp;");
				// 替换&#x22c7f;
				String u4 = UnicodeUtils.string2Unicode(word);
				String[] u4arry = u4.split("\\\\u");
				StringBuilder usb = new StringBuilder();
				for (int i = 0; i < u4arry.length; i++) {
					String u4_str = u4arry[i];
					// log.info(u4_str);
					if (!Strings.isNullOrEmpty(u4_str)) {
						// 此处可以去掉判断，为了以后冗余
						if (BigwordUtil.isDoubleChinese(u4_str)) {
							i = i + 1;
							u4_str = "\\u" + u4_str + "\\u" + u4arry[i];
							String bigword = word.substring(0, 2);
							word = StringUtil.replaceOnce(word, bigword, "");
							String ueu = BigwordUtil.hexs2words(bigword);
							usb.append(ueu);
							//usb.append(bigword);

						} else {
							String uword = UnicodeUtils.unicode2String("\\u" + u4_str);

							//word = word.replace(uword, "");
							word = StringUtil.replaceOnce(word, uword, "");
							usb.append(uword);
						}
					}
				}
				u4str = usb.toString();
			}

		} catch (Exception e) {
			log.error("", e);
			log.error("文字：{}",word);
		}
		return u4str;
	}
	
	
//	public static String tranferBigwordHex(String word) {
//		String u4str = word;
//		try {
//			if (!Strings.isNullOrEmpty(word)) {
//				// word = Pattern.compile("\\u3000").matcher(word).replaceAll("&#12288;");
//				// word = Pattern.compile("\\s").matcher(word).replaceAll("&nbsp;");
//				// 替换&#x22c7f;
//				String u4 = UnicodeUtils.string2Unicode(word);
//				String[] u4arry = u4.split("\\\\u");
//				StringBuilder usb = new StringBuilder();
//				for (int i = 0; i < u4arry.length; i++) {
//					String u4_str = u4arry[i];
//					// log.info(u4_str);
//					if (!Strings.isNullOrEmpty(u4_str)) {
//						// 此处可以去掉判断，为了以后冗余
//						if (BigwordUtil.isDoubleChinese(u4_str)) {
//							i = i + 1;
//							u4_str = "\\u" + u4_str + "\\u" + u4arry[i];
//							String bigword = word.substring(0, 2);
//							word = StringUtil.replaceOnce(word, bigword, "");
//							String ueu = str2Hex(bigword);
//							usb.append(ueu);
//							//usb.append(bigword);
//
//						} else {
//							String uword = UnicodeUtils.unicode2String("\\u" + u4_str);
//
//							//word = word.replace(uword, "");
//							word = StringUtil.replaceOnce(word, uword, "");
//							usb.append(uword);
//						}
//					}
//				}
//				u4str = usb.toString();
//			}
//
//		} catch (Exception e) {
//			log.error("", e);
//			log.error("文字：{}",word);
//		}
//		return u4str;
//	}
//	
	
	/*
	 * 转成&#x格式
	 */
	public static String toHex(int codePoint) {
		return new StringBuilder().append("&#x").append(Integer.toHexString(codePoint)).append(";").toString();
	}

	/**
	 * 字符串转为&#x格式
	 * 
	 * @param bigword
	 * @return
	 */
	public static String str2Hex(String bigword) {
		int codePoint = bigword.codePointAt(0);
		return toHex(codePoint);
	}

	/**
	 * 判断是否是大字
	 * 
	 * @param word
	 * @return true:是 false:不是大字
	 */
	public static boolean isBigword(String word) {
		String u4 = string2Unicode(word);
		String[] u4arry = u4.split("\\\\u");
		// StringBuilder usb = new StringBuilder();
		for (int i = 0; i < u4arry.length; i++) {
			String u4_str = u4arry[i];
			// log.info(u4_str);
			if (!Strings.isNullOrEmpty(u4_str)) {
				// 此处可以去掉判断，为了以后冗余
				if (isDoubleChinese(u4_str)) {
					log.info("======word:含有大字符：" + word + "======");
					return true;
				}
			}
		}
		return false;
	}

	public static List<String> getBigword(String oxstr) {
		List<String> list = Lists.newArrayList();
		String patstr = "(&#x[0-9a-z;]{6})";
		Pattern p = Pattern.compile(patstr);
		Matcher m = p.matcher(oxstr);

		while (m.find()) {
			String waizi = m.group();
			list.add(waizi);
		}
		return list;
	}

	/**
	 * 辅助平面的字符位共有 220220 个，因此表示这些字符至少需要 20 个二进制位。 UTF-16 将这 20 个二进制位分成两半，前 10 位映射在
	 * U+D800 到 U+DBFF，称为高位（H）， 后 10 位映射在 U+DC00 到 U+DFFF，称为低位（L）。
	 * 这意味着，一个辅助平面的字符，被拆成两个基本平面的字符表示。
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isDoubleChinese(String u4) {
		boolean isCh = false;

		int a = u4.compareTo("d800");
		int b = u4.compareTo("dbff");
		// log.info(a+"-->"+b);
		if (a >= 0 && b <= 0) {// 在此区间
			isCh = true;
		} else {
			int a1 = u4.compareTo("dc00");
			int b1 = u4.compareTo("dfff");
			if (a1 >= 0 && b1 <= 0) {// 在此区间
				isCh = true;
			}
		}
		return isCh;
	}

	/**
	 * 字符串转换unicode
	 */
	public static String string2Unicode(String string) {

		StringBuffer unicode = new StringBuffer();

		for (int i = 0; i < string.length(); i++) {
			// 取出每一个字符
			char c = string.charAt(i);
			// 转换为unicode
			unicode.append("\\u" + Integer.toHexString(c));
		}

		return unicode.toString();
	}

	/**
	 * 将 &#x ; 转为unicode码
	 * 
	 * @param hexStr
	 * @return
	 */
	public static String point2unicode(String hexStr) {
		String codepointhex = hexStr.replace("&#x", "").replace(";", "");
		// 16进制转为10进制
		BigInteger codepointint = new BigInteger(codepointhex, 16);
		int codePoint = codepointint.intValue();
		// 转为文字
		String word = new String(Character.toChars(codePoint));
		// log.info("word==>"+word);
		// 文字转unicode
		String uicode = string2Unicode(word);
		// log.info("uicode==>"+uicode);

		return uicode;
	}

	/**
	 * 
	 * @param hex
	 *            &#xf2456 ;&#xf930e;
	 * @return 文字
	 */
	public static String hex2word(String hex) {

		 String word = hex;
	 	 if(StringUtil.startsWith(hex, "&#x")) {       
             Integer code = Integer.valueOf(Integer.parseInt(hex.replace("&#x", "").replace(";", ""), 16));
             char[] chars = Character.toChars(code.intValue());
             word = new String(chars);
	 	 }         
         return word;		 
	}
	
	
	public static String hexs2words(String wordStr) {

		String patstr = "(&#x[0-9a-z;]{6})";
		Pattern p = Pattern.compile(patstr);
		Matcher m = p.matcher(wordStr);

		while (m.find()) {
			String waizi = m.group();
			String hw = hex2word(waizi);
			wordStr = wordStr.replace(waizi, hw);
		}
        return wordStr;		 
	}

	
	
	public static List<String> words2list(String word) {
		List<String> list = Lists.newArrayList();
		
		try {
			if (!Strings.isNullOrEmpty(word)) {				
				String u4 = UnicodeUtils.string2Unicode(word);
				String[] u4arry = u4.split("\\\\u");				
				for (int i = 0; i < u4arry.length; i++) {
					String u4_str = u4arry[i];
					// log.info(u4_str);
					if (!Strings.isNullOrEmpty(u4_str)) {
						// 此处可以去掉判断，为了以后冗余
						if (BigwordUtil.isDoubleChinese(u4_str)) {
							i = i + 1;
							u4_str = "\\u" + u4_str + "\\u" + u4arry[i];
							String bigword = word.substring(0, 2);
							word = StringUtil.replaceOnce(word, bigword, "");
							list.add(bigword);
						} else {
							String uword = UnicodeUtils.unicode2String("\\u" + u4_str);
							word = StringUtil.replaceOnce(word, uword, "");
							list.add(uword);
						}
					}
				}				
			}

		} catch (Exception e) {
			log.error("", e);
			log.error("文字：{}",word);
		}
		return list;
	}
	
	
	private static String replaceSpan(String str) {
		Map<String,String> map = Maps.newHashMap();
		String patstr = "(&#x[0-9a-z;]{6})";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);
	    //String uc6 = null;
	    StringBuilder wordstr = new StringBuilder();
	    String result = str;
	    while( m.find()){	      
	       String waizi = m.group();
	       if(map.containsKey(waizi)) {
	    	   continue;
	       }else {
	    	   map.put(waizi, waizi);
	       }
	       waizi =waizi.toLowerCase();
	       String ft = fontType(waizi);
	       
	       //外字转为大字，不是码
	       String bigword = BigwordUtil.hex2word(waizi);
	       if("02".equals(ft)) {
	    	   wordstr.append("<span class=\"big02\">").append(bigword).append("</span>");
	       }else if("15".equals(ft)){
	    	   wordstr.append("<span class=\"big15\">").append(bigword).append("</span>");
	       }
	       result = StringUtil.replace(result, waizi, wordstr.toString());
	       wordstr.setLength(0);	       	       	      
	    }
	    //log.info("==>处理结果：{}",result);
		return result;
	}
	
	private static String fontType(String unicode) {
		if(unicode.startsWith("&#xf")) {
			return "15";
		}else if(unicode.startsWith("&#x2")){
			return "02";
		}
		return "00";
	}
	
	public static boolean isDouble(String text) {
		if (text.length() == 2) {
			return isDoubleChinese(Integer.toHexString(text.charAt(0)));
		}
		return false;
	}
	
	public static void main(String[] args) {
//		 String hexstr = "&#x25874;";
//		 log.info("==>原始16进制数："+hexstr);
//		 String u4 = point2unicode(hexstr);
//		 log.info("==>转换后unicode："+u4);
//		
//		 String uword = UnicodeUtils.unicode2String(u4);
//		 log.info("==>转换后uword："+uword);
		//
		// String tword = tranferBigword("要𥡴查");
		// log.info("==>转换后tword："+tword);

		String sss = "￥";
		sss="　";
		//int codePoint = sss.codePointAt(0);
		//System.out.println(codePoint);
		
		System.out.println(UnicodeUtils.string2Unicode(sss));
//		
//		
//		System.out.println(str2Hex("𭃡"));
		//String str = "𰥍";
		String str = "□";
		System.out.println(string2Unicode(str));
		
		System.out.println(str2Hex(str));
	}
}
