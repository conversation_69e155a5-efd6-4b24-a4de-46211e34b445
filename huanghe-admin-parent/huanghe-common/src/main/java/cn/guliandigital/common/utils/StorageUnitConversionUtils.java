package cn.guliandigital.common.utils;

import java.math.BigDecimal;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 存储单位换算工具类
 * <AUTHOR>
 *
 */
public class StorageUnitConversionUtils {
	
	public enum Unit{
		// 单位大小请按顺序书写,并保证进制相同, 如 1B = 8BIT无法与下列1024进制相同,所以无此单位转换方法
		B,KB,MB,GB,TB,PB//可以继续添加 ,TB,PB,EB
	}
	
	public static Unit getUnit(String text) {
		Unit[] values = Unit.values();
		for (Unit u : values) {
			if(u.toString().equalsIgnoreCase(text)) {
				return u;
			}
		}
		return null;
	}
	
	public static String getBestString(BigDecimal value, String separator) {
		return String.join(separator, getBestStrings(value));
	}
	
	public static String getBestString(String value, String separator) {
		if(StringUtil.isBlank(value) || !StringUtil.isNumeric(value)) {
			return String.join(separator, new String[] {"-", ""});
		}
		BigDecimal decimal = new BigDecimal(value);
		return String.join(separator, getBestStrings(decimal));
	}
	
	public static String getBestString(BigDecimal value) {
		return String.join(" ", getBestStrings(value));
	}
	
	public static String[] getBestStrings(String value) {
		if(StringUtil.isBlank(value) || !StringUtil.isNumeric(value)) {
			return new String[] {"-", ""};
		}
		BigDecimal decimal = new BigDecimal(value);
		return getBestStrings(decimal);
	}
	
	public static String[] getBestStrings(String value, Integer scale) {
		if(StringUtil.isBlank(value) || !StringUtil.isNumeric(value)) {
			return new String[] {"-", ""};
		}
		BigDecimal decimal = new BigDecimal(value);
		return getBestStrings(decimal, scale);
	}
	
	public static String[] getBestStrings(BigDecimal value) {
		String[] strings = new String[2];
		Unit bestUnit = StorageUnitConversionUtils.getBestUnit(value);
		strings[1] = bestUnit.toString();
		BigDecimal bestValue = StorageUnitConversionUtils.conversion(value, Unit.B, bestUnit, 0);
		strings[0] = bestValue.toString();
		return strings;
	}
	
	public static String[] getBestStrings(BigDecimal value, Integer scale) {
		String[] strings = new String[2];
		Unit bestUnit = StorageUnitConversionUtils.getBestUnit(value);
		strings[1] = bestUnit.toString();
		BigDecimal bestValue = StorageUnitConversionUtils.conversion(value, Unit.B, bestUnit, scale);
		strings[0] = bestValue.toString();
		return strings;
	}
	
	/**
	 * 获取数字最佳匹配单位
	 * @param value 以B为单位的数据
	 * @return
	 */
	public static Unit getBestUnit(BigDecimal value) {
		Unit[] values = Unit.values();
		if(ArrayUtils.isNotEmpty(values)) {
			for (int i = 0; i < values.length; i++) {
				Unit u = values[i];
				int pow = (u.ordinal() + 1) * 10;
				BigDecimal standard = new BigDecimal(Math.pow(2, pow));
				if((value.compareTo(standard)) == -1) {
					return u;
				}
				// 使用最大单位
				if ((i + 1) == values.length) {
					return u;
				}
			}
		}
		return Unit.B;
	}
	
	/***
	 * 
	 * 换算方法
	 * @param value 当前值
	 * @param currentUnit 当前单位
	 * @param targetUnit 目标单位
	 * @param scale 保留小数位数
	 * @return
	 */
	public static BigDecimal conversion(BigDecimal value, Unit currentUnit, Unit targetUnit, Integer scale) {
		if (currentUnit.ordinal() > targetUnit.ordinal()) {
			// 当前单位大于目标单位
			for (int i = currentUnit.ordinal(); i > targetUnit.ordinal(); i--) {
				value = value.multiply(new BigDecimal(1024));
			}
			return value;
		} else if (currentUnit.ordinal() < targetUnit.ordinal()) {
			// 当前单位小于目标单位
			for (int i = currentUnit.ordinal(); i < targetUnit.ordinal(); i++) {
				value = value.divide(new BigDecimal(1024), scale, BigDecimal.ROUND_HALF_UP);
			}
			return value;
		} else {
			// 当前单位等于目标单位
			return value;
		}
	}

	public static BigDecimal getByteUnit(BigDecimal value, String unit){
		BigDecimal resultSize = null;
		switch (unit){
			case "GB":
				resultSize = conversion(value,StorageUnitConversionUtils.Unit.GB,StorageUnitConversionUtils.Unit.B,0);
				break;
			case "TB":
				resultSize = conversion(value,StorageUnitConversionUtils.Unit.TB,StorageUnitConversionUtils.Unit.B,0);
				break;
			case "PB":
				resultSize = conversion(value,StorageUnitConversionUtils.Unit.PB,StorageUnitConversionUtils.Unit.B,0);
				break;
		}
		return resultSize;
	}
	
	public static void main(String[] args) {
		/*
		 * BigDecimal conversion = conversion(new BigDecimal(1),
		 * StorageUnitConversionUtils.Unit.GB, StorageUnitConversionUtils.Unit.B, 16);
		 * System.out.println(conversion);
		 */
		/*BigDecimal value = new BigDecimal("1231231231231");
		Unit bestUnit = getBestUnit(value);
		BigDecimal conversion = conversion(value, Unit.B, bestUnit, 0);
		System.out.println(conversion.toString() + " " + bestUnit.toString());
		
		String[] bestShow = getBestStrings(value);
		System.out.println(String.join(" ", bestShow));*/
		
		//Unit[] values = Unit.values();
		//for (Unit unit2 : values) {
		//	System.out.println(unit2);
		//}
		/*BigDecimal b = new BigDecimal(1000);
		BigInteger gb = getByteUnit(2L, "GB");
		System.out.println(gb);

		BigInteger tb = getByteUnit(2L, "TB");
		System.out.println(tb);

		BigInteger pb = getByteUnit(1L, "PB");
		System.out.println(pb);*/

		String[] bestStrings = getBestStrings("1785643134976", 2);
		System.out.println(bestStrings[0]+bestStrings[1]);
	}

}
