package cn.guliandigital.common.utils;

import java.util.HashMap;
import java.util.Map;

public class FanJianUtils {  
	 private static final String shortChar = "勾咤哪吕吴啊咒呙努讻咔呗员唣启哑问念吟忤啖吃哲丧单岩衔乔咱唤哟吗唝啬唢呛呜啼啧哗叹呕尝喽哔慨唠哒哓啖喷恶啴咴呒叽嗈当哕哝嗳哙啸咛哜吓尝啮噜向咙咽冁亸嘤严喾嗫啭嚣嚣呓冁啰苏啮嘱囡国函国囵篅围园圆团图坯座坍垧附丘垛坝执垭垩野坚埚采坎报塍尧碱阶场涂茔冢埘埙垲坞块尘垫堑砖圩塔场硗坟坠堕坛墙垱垦堧压埙圹垒垄垄坏垆坛坝壮壶婿壸寿梦伙夹奂奥奁夺奖奋姹妆妒侄妊奸娱妩娅娄娲淫妇偷姻妈媪愧袅妪娆娴娴婵妫妩娇媭婳嫱嫒袅嫔奶婴婶懒娘娈马冯驭驮驯驰驲驳驼驻驵驶驷驸驹骀驽驾驳骇骈骂骃骆骍骋骏骎骐骑骒䯄验骓骗鬃骙骛骞骟草骝腾驺骚骘骜骅蓦驱骠骡骢骖骁骄骣骡惊驿验骕骤骥驴骧骦骊骉髹鬓发松胡须鬓斗闹哄阋斗阄郁鬲魉魇鱼鱽鱾鲂鲀鲁鲧鲆鲄鲅鲇鲉鲊鲋鲌鲍鲐鲛鲜鲝鲞鲑鲒鲔鲕鲖鲦鲨鲩鲞鳀鲠鲤鲧鲪鲸鲭鲮鲯鲰鲲鲳鲴鲶鲵鲷鲻鳅鳊鲽鲾鳀鲗鳃鳂鳄鳅鳆鳊鳇鳈鲫鳑鳒鳍鲢渔鲥鳎鳏鳁鳐鲦鳙鳖鳌鳓鲣鳔鳗鳘鳛鳚鳉鳝鳞鳟鲼鲟鳜鳝鲟鳣鳡鳢鲙鲎鲚鲿鳠鳄鲈鲡鸟凫鸠鸢鸣凤鸤鸩雁鸦鸨鸵鸪鸭鸮鸯鸰鸳鸱鸲鸿䴔鸸䴕鸽鸹鸺鹈鹁鹃鹆鹄鹅鹓鹑鹒鹉䴖鸦鹊鸫鹌鹍鹎鹏雕鹚鹍鹕䴗鹖鹗鹘鹙鹜鹡鹢鹣莺鹤鹝鹞鸡鸧鹟䴘鹠鹧鸷鸥鹥鹨鹫燕鹩鹇鹪鹬鸶㶉鹯鹰鹭鹮鹔鹲鹱莺鸬鹴鹦鹳鸾鹂卤鹾咸硷盐碱粗狍麟丽麇獐粗麦麸面曲麸曲面么黄黉糜点党鬒霉黪黡黩黾鼋鼌蛙鳖鳌鼍冬貂鼹衄齄齐斋赍齑齿龀龁龂龃龄出龅龆咬啮龈龉龊腭龋龌龙庞龚龛和龟兹珏珍佩玡现珐管雕琅珲玳玚玮莹珏玛琏琐玱琉琎玑珰环瑷玙玺璇琼璃珑璎瓒什产苏宁氓圳亩毕异留画当畴迭匹疏痱恫痉酸淤痖痹喑疟疡愈痪疯瘗疮瘘疭痨疗痫瘅疠痒瘪痴疖症癞疬瘿癣瘾痈瘫癫发皂皓皑疱皲皱齇杯碗盗盏监尽盘卢荡视昚视眦众眷睐睁眯瞒眍了睑蒙瞰瞩矜矫碇炮朱砗硖硁砚棋埼砧硕砀确确码硙盘碛砖硁碜硗䃅矶础碍矿矾砺礌砾砻炮只秘算佑蜡祸禄祯祎祃禅御礼祷祢籼年粳粳秆禀棱糯秸称种稿谷缜糜糠积颖稣䅟稚穑秽秾获稳稆阱窗窝洼窑窑穷窥窭窎窜窍窦灶窃伫并俟竖竞慷恟匆耻恒怪旨匆怅恶德凄闷恽蠢惬惬恻爱茕恼恿悫栗恺愠怆忾殷态庆悫惭惭悭戚忧虑欲憩恸怂慑惨惯宪凭怜愤慭悯愦惮怃惫忆懔应怿恳懑蒙恹恹懵怼惩怀懒悬忏慑欢惧忡恋戆钺戋戬戗戏战戏厄擀扦钳曳抛掸拼拿着挲救挟卷碰挂挜㨄挨㧏扪抻采挣舍抡淘扫挥拣扬掩捶换榨扇榷构拓损掏抢捣捂挚抟抠扯揸搂掴挝捴折掺掼捞挞挠掸撂撑扑掸㧑揿抚挢携捻拨挦拥击挡据掳择捡举担掰拧摈挤抬捣搁拟扩掷撵撷扰摅擞摆拢撄拦搀撺捃摄携挛捃摊攒挡搅揽考掂勃败叙叙掇扬敌驱数毙敛斓斩断㔉于旆旗幡并幺干辖乱亘亚忪伫畲布占做并来仑芯腧侠侣俣系局仿仓幸伥徕剚俩们个睬伦契逼侦侧咱伟家效备徭伧伞杰佣债仅传伛佥倾偻伤偬童雇侥偾仙仆伪侨俊亿仪价当侬傻俭侩傧侪俦尽储优偿傩俪罗攒傥俨凶儿兖两冉幂冻凛凯凭劫劫剁克拉刭则黥刬刚剐剥剀创铲札划刿剧剑刽刘剂㔉效敕劲敕务动劳勋胜绩势戮剿勚劢勋励劝掬匦汇匮奁椟区协恤却崖厍庞厕历厌厉厣参迭睿丛娩孙学孽孪冗宫冤浸宁寝实宽审写宠宝宝克专将寻对导菽鲜鲜尤尴尴届尸屃屉屡层屟屦属屃冈峡岘岛峰崃嵩岗昆峥崟仑岩嵛岚崭岖嵝峣嵚峤峱峄崄屿岙嵘岭岳岿峦巅岩卮匝纸帅师裙帐带裈帧帏币帻帼帜帮帱㡡干几庋库厢厕寓厦庼荫厨厮广庙厂庑废廪庐厅回吊韬张强别弹强弥弯汇彟雕佛殉后径徕从遍复彷彻征泛污泛沉冱冲决况溯泄汹浃泾莅凉泪凌涞凄浅涡净沦浙渌浑凑洇减浈测汤餐渊涣沨涌荥沟涟灭湿涢温沧准涤浉熘浐浒滚沪渍汉满渐沤滞卤溇溆颍渔渗涨浆涝沄洁浇潜涩润溃澄涠沩滗舄泼浔噀浣渑浓泽浊浍泶滪淀泞滨济蒙涛滥沵浚湿浕涩潍渖泻滢渎潴滤溅泺浏泷潆濑沥濒泸潇澜潋弥漓滠沣滩洒漤灏湾滦滟滟伙灾为灺照乌淬爇无茕辉炸炼烟烦暖炀暖焕煅炜荧烨热颎烫炽炖磷烧焖焰灯营灿烛毁焘熏耀烬燮烨烁炉烂争为爷尔丬床墙笺闸牍抵牵犁荦牦犊牺状徇狭狈狰狾犹猿呆狮狲狱貘奖狷独猃狯狞狝获犷兽猎獭献猕缸瓷砖瓯瓮罂针钉钊钌焊扣钏钐钓钒钗钫钘巨钝钞钤钣钧钑钮钯铊铉铋钰钲钳钴钵钹钺钻钾钿铃铁铂铅钩铆刨铍铰铳铏铐铑铒铚铁铜铨铫铢铣铦缿衔铭铬银锐锑锒铓铺铻铗铘销焊锄镯锊锉铤锈镅锋锓锔锭锫锩锬铔错锜钱钢锞锟锡锢锅铮锤锥锦锯录锱镀镃锲锚炼针锴铡钖锷锾锹锸钟锻锼锽锤键辖镕镑镐镒镰蓥镇链镈镉耨镋锁铠枪铩钨镏镜铲镝镛镞旋铧镆錾铿镖戚镗镂镘镠镪锵铴钟镦铙镡镣镤镌镫镰镭铁铛铎镮镯锈镔铸鉴鉴镬矿镳刨铄锧镴炉镶钥镵镊銮锣钻凿镢长门闩闪闬闭闵闶闰开闲闳间闲闹闸阂关闺闽合阀阁阅阆阃闾阏阇阈阉阊阌阍阎阔暗阑阒板阕闱阙闯阖阗阘闿窥关闼阚阓阐阛辟坑厄址啊垝阵陕陉峭升陆陈阴队阶堤阳陨坞际随险隐陇隶只隽虽杂离鸡双雏难云电沾灵溜霡雾雱霁叇霭雳灵叆靓静腼靥韧鼗巩鼗绱秋鞟靴鞑鞒缰千鞯韦韧韨韩韪鞴韫韬袜韭韵响页顶顷顸项顺须颃顼顽顿颂颁颀预领颇颏颉颌俯颐头颊颈频颒颔颋颓悴颗额颜题颙腮颚颛类颠愿颡颟顾颢憔颤颥显颦颅颞颧风飒飐飑台刮飓扬飔飕飖飗飘飙飞餐饤饥饦飧饨饪饫饬饭饮养饰饱饴饲饳饺饼饵饪饷饽饾馁余饿馂馆饯馄肴喂馅糊饧喂馉馊糇馐糕馌馎饩馈馏馑馒膳饶馓馈馔饥飨餍馍馋肮鲠髅脏体髌髋篪笋笔䇲笕箸笮筒钳笺篪个筝棰帚筅箧范节蓑笃筑筼筱筛箬簰筘彗箦篓筚博篑箪帘筜虡签檐箫筹篮藤笼箨籁篯箓钥奁签篱笾簖箩籝吁糁秕妆粽模粪糁粮粝团粝糵籴粜纟纠纡纣红纥约纨纪纫纹纺纭纮纯纰纱纳纷纴纸级纾纼纽纻弦绊绀绁扎绂组绅细䌷䌹终绐绋绌绍绞统绒结绔绖绁给绦纴绗绛络绚绝丝绨绑绠经绡捆绢绥绤绣综绽绾绻绫绪紧绮线绯绰网纲绲绶彩纶维绵绺绷绸缗绹缀绿缁缔编缂绁缃练缄缅缇缈缉缌缓缎缏褓绵线缑缒纬缘缞缟缡缢缣萦缜缚缛致缙县缊绦绦鞶缝绉演缩纤绩絷缥缕缦缧绷总纵缪襁缫织缮缯橤绕伞穗缭缋翻茧系缰绳缱缲绎缳绘缴绣缤继纩缠颣缬续累攘缨纤才缵缆钵瓶䓨樽坛瓮罂垆坛罚罚骂罢罴罗羁绒羡义群膻翅习翚玩翘翙端锄耧耢聃圣馘闻声聪耸联职聂聩聍聋听肇肃臆肯胳胚疣脢修脉膀脆胁唇胫吻胀肾肠脚肿脑肷䒿嗉腽肤腘胶腻腊脓脸脍胆膑脐膘腊胭胪裸脏脔臜卧临皋台与举兴旧铺馆舱鸼舣橹舰橹舻艰艹刍苎莓答华荇豆荚茎苋庄烟苌着抱莱庵果莴萍荤叶万菹参苇荭药莅席盖莲莳苍茜莜搜荪莼菱麻莼蒂苘蒌荜茑葱苁卜蒋荫荡藕芸荙荛蒇蕊蒉荬芜荞莸荨芗荐蔷姜莶荟蓟蓣萧苧荠借蓝熏荩萨䓖薯艺薮药茏蔼蕊萚荨苈苹芦蔺蕲苏蕴兰蔹藓檗蓠橤萝蔂处号虏亏虬蛇蛔蚺蛔螂蛱蚬蚋蝶蜗蚀霓䗖虻蠕猬虾虱萤蚂蠹蚁蛳蛰䗖蝼蝈螀蛲虫蝉虮蚁虿蛏蝇蝎玭蛴蚝蝾蛎蜡蜂蟏蛊蚕蛮众脉蔑炫术胡冲卫卫只衮茵夹补夹里袅装裉制褎裈复袆缡裤䌹裢褒亵褛幞禅杂袯裆袄褴袜摆衬袭襕褶核升昺时晋晢昼晰晖晕暖旸皓畅昵晔暂昙晓历向暧旷叠昽晒书会胧么朵圬勺柂桠东柿锨舵拐楠桴台按契刊柏枻筏条拶䇲杆枧捆枭栀碇碗弃桊枨桠栋栖极栈㭎枣梨梅桢业杨楦棕矩枫搒槁荣构橐杩杠干梿梅桤枪盘梁椁样桩桦椠枢标楂楼镘乐枞桨桡树横丛朴桥槜机椭檩舣檠柽樯槚档检桧柠槟苘台梼柜槛棹凳槠橱椟橥榈栉栎橹橼榇栊柝枥栌榉棂樱栏权栾椤攒把榄棂喝款钦叹欧欻敛欤啸欢岁历归夭残殒殇殚僵殓殡歼杀壳殴医毗绒球牦毵毡氇气氲见视规觅觇眺觋睹觍亲觎觏觊觐觑觑觉览觌观斤粗抵觞觯触计订讣讦吁讨讧讪托讫训讯记讱访讵讶讷讼讻许讹欣设诀注咏评证诂诃诅诇诊诈诉诋诒词诎诐诏诧该详诓诔试诗诖诘夸诙诚诠诛诜话诟诡诣询讻诩说诫悖志诬语诮误诰诱诲诞诳诵诶认谊谅谆谇谈请诸诹诼诽课訚诤谂论诿谁谀调谄喧谛谙谚咨谜诨谝诺谋谌谍谏谐谑谒谓谔谖谕谥讽讳谞谤谥谦谧讲谎谡谣谢誊诌谪哗谟谨讴谩谬识谱嘻嘲谭谮讹谯证谲撰讥议谴噪译誉谵谪诪护审谫读詟燕雠变让谰谶谗赞谠谳溪岂竖丰艳豚猪豮貊狸猰猫㺄獾贝贞负贡财责贪贬贫货贩贯贮贰贲贳贴贵贶买贷贸贻费贺资赅贼贾贿赀赁赂宾赇赈赊赔赓赕赋账卖赌赉贤贱赏赐质赒赖赗赆赛赚购赙剩赘贽赜赟赠赞赢赡赃赑赆赎赝赣赧赪赪趁赵赶趑趋趱刖踮迹跺跺局蜷践剕逾踊跄蹄跖迹蹒跸踪跷蹶趸跻踌跃蹰踯跹跞踬蹑躏耽躲躬裸躯亸车轧军轨轩轪轫轭软轱轲轴轵轸轶轺较轼载轾辁辀辂辄辅轻挽辌辇辆辉辈辋辊轮辍辎辏辐软辑输辖毂辕辒舆辗辘转辙辚轿轰轹辔轳罪辞办辞辫辩农迤逃乃回这连乃径逖过进周游运达违溯远沓递逊适遁绕迁辽遗迟选迈还迩边逦逻哪合郏郄鄹邮兒郓乡郧邬邹墉邻郑郸邓邺郐邝郦酂鸩酬腌酝丑医酱酰酦酬燕酿醾酾衅酽释厘㑳兀喞㬺㩳䎱𦡦䱷凉說说";
	 private static final String longChar =  "句吒呐呂吳呵呪咼呶哅哢唄員唕啓啞問喎唸唫啎啗喫喆喪單喦啣喬喒喚喲嗎嗊嗇嗩嗆嗚嗁嘖嘩嘆嘔嘗嘍嗶嘅嘮噠嘵噉噴噁嘽噅嘸嘰噰噹噦噥噯噲嘯嚀嚌嚇嚐嚙嚕嚮嚨嚥辴嚲嚶嚴嚳囁囀嚻囂囈囅囉囌囓囑囝囯圅國圇圌圍園圓團圖坏坐㘱坰坿坵垜垻執埡堊埜堅堝埰埳報堘堯堿堦場塗塋塚塒塤塏塢塊塵墊塹塼墟墖塲墝墳墜墮壇墻壋墾壖壓壎壙壘壠壟壞壚壜壩壯壺壻壼壽夢夥夾奐奧奩奪奬奮奼妝妬姪姙姦娛娬婭婁媧婬婦媮婣媽媼媿嫋嫗嬈嫻嫺嬋嬀嫵嬌嬃嫿嬙嬡嬝嬪嬭嬰嬸嬾孃孌馬馮馭馱馴馳馹駁駝駐駔駛駟駙駒駘駑駕駮駭駢駡駰駱騂騁駿駸騏騎騍騧騐騅騙騣騤騖騫騸騲騮騰騶騷騭驁驊驀驅驃騾驄驂驍驕驏驘驚驛驗驌驟驥驢驤驦驪驫髤髩髮鬆鬍鬚鬢鬥鬧鬨鬩鬭鬮鬱䰛魎魘魚魛魢魴魨魯鮌鮃魺鮁鮎鮋鮓鮒鮊鮣鮑鮐鮫鮮鮺鮝鮭鮚鮪鮞鮦鯈鯊鯇鯗鮷鯁鯉鯀鮶鯨鯖鯪鯕鯫鯤鯧鯝鯰鯢鯛鯔鰌鯿鰆鰈鰏鯷鰂鰓鰃鰐鰍鰒鯾鰉鰁鯽鰟鰜鰭鰱䱷鰣鰨鰥鰮鰩鰷鱅鱉鰲鰳鰹鰾鰻鰵鰼䲁鱂鱔鱗鱒鱝鱏鱖鱓鱘鱣鱤鱧鱠鱟鱭鱨鱯鱷鱸鱺鳥鳧鳩鳶鳴鳳鳲鴆鴈鴉鴇鴕鴣鴨鴞鴦鴒鴛鴟鴝鴻鵁鴯鴷鴿鴰鵂鵜鵓鵑鵒鵠鵝鵷鶉鶊鵡鶄鵶鵲鶇鵪鵾鵯鵬鵰鷀鶤鶘鶪鶡鶚鶻鶖鶩鶺鷁鶼鶯鶴鷊鷂鷄鶬鶲鷉鶹鷓鷙鷗鷖鷚鷲鷰鷯鷳鷦鷸鷥鸂鸇鷹鷺䴉鷫鸏鸌鸎鸕鸘鸚鸛鸞鸝鹵鹺鹹鹻鹽鹼麁麅麐麗麕麞麤麥麩麪麯䴸麴麵麽黃黌𪎭點黨黰黴黲黶黷黽黿鼂鼃鼈鼇鼉鼕鼦鼴䶊齇齊齋齎齏齒齔齕齗齟齡齣齙齠齩齧齦齬齪齶齲齷龍龐龑龔龕龢龜玆玨珎珮琊現琺琯琱瑯琿瑇瑒瑋瑩瑴瑪璉瑣瑲瑠璡璣璫環璦璵璽璿瓊瓈瓏瓔瓚甚産甦甯甿甽畝畢異畱畫當疇疊疋疎疿痌痙痠瘀瘂痺瘖瘧瘍瘉瘓瘋瘞瘡瘻瘲癆療癇癉癘癢癟癡癤癥癩癧癭癬癮癰癱癲發皁皜皚皰皸皺皻盃盌盜盞監盡盤盧盪眎眘眡眥眾睠睞睜瞇瞞瞘瞭瞼矇矙矚𥎊矯矴砲硃硨硤硜硯碁碕碪碩碭碻確碼磑磐磧磚䃘磣磽磾磯礎礙礦礬礪礧礫礱礮祇祕祘祐䄍禍祿禎禕禡禪禦禮禱禰秈秊秔稉稈稟稜稬稭稱種稾穀稹穈穅積穎穌穇穉穡穢穠穫穩穭穽窓窩窪窯窰窮窺窶窵竄竅竇竈竊竚竝竢竪競忼忷怱恥恆恠恉悤悵惡惪悽悶惲惷愜㥦惻愛惸惱慂愨慄愷慍愴愾慇態慶慤慚慙慳慼憂慮慾憇慟慫慴慘慣憲憑憐憤憖憫憒憚憮憊憶懍應懌懇懣懞懨懕懜懟懲懷懶懸懺懾懽懼𢥞戀戇戉戔戩戧戯戰戲戹扞扡拑抴拋担拚拏招挱捄挾捲掽掛掗掫捱掆捫捵採掙捨掄掏掃揮揀揚揜搥換搾搧搉搆搨損搯搶搗摀摯摶摳撦摣摟摑撾摠摺摻摜撈撻撓撢撩撐撲撣撝撳撫撟擕撚撥撏擁擊擋據擄擇撿擧擔擘擰擯擠擡擣擱擬擴擲攆擷擾攄擻擺攏攖攔攙攛𢹲攝攜㩳攣攟攤攢攩攪攬攷敁㪍敗敍敘敠敭敵敺數斃斂斕斬斷斸於斾旂旛並么乾舝亂亙亞伀佇佘佈佔作併來侖信俞俠侶俁係侷倣倉倖倀倈倳倆們個倸倫偰偪偵側偺偉傢傚備傜傖傘傑傭債僅傳傴僉傾僂傷傯僮僱僥僨僊僕僞僑儁億儀價儅儂儍儉儈儐儕儔儘儲優償儺儷儸儹儻儼兇兒兗兩冄冪凍凜凱凴刧刦刴剋剌剄則剠剗剛剮剝剴創剷劄劃劌劇劍劊劉劑劚効勅勁勑務動勞勛勝勣勢勠勦勩勱勳勵勸匊匭匯匱匳匵區協卹卻厓厙厖厠厤厭厲厴參叠叡叢㝃孫學孼孿宂宮寃寖寧寢實寬審寫寵寳寶尅專將尋對導尗尠尟尢尲尷屆屍屓屜屢層屧屨屬屭岡峽峴島峯崍崧崗崑崢崯崙嵒崳嵐嶄嶇嶁嶢嶔嶠嶩嶧嶮嶼嶴嶸嶺嶽巋巒巔巖巵帀帋帥師帬帳帶㡓幀幃幣幘幗幟幫幬幮幹幾庪庫廂廁庽廈廎廕廚廝廣廟廠廡廢廩廬廳廻弔弢張強彆彈彊彌彎彙彠彫彿徇後徑徠從徧復徬徹徵氾汙汎沈沍沖決況泝洩洶浹涇涖涼淚淩淶淒淺渦淨淪淛淥渾湊湮減湞測湯湌淵渙渢湧滎溝漣滅溼溳溫滄準滌溮溜滻滸滾滬漬漢滿漸漚滯滷漊漵潁漁滲漲漿澇澐潔澾澆潛澁潤潰澂潿潙潷潟潑潯潠澣澠濃澤濁澮澩澦澱濘濱濟濛濤濫濔濬濕濜澀濰瀋瀉瀅瀆瀦濾濺濼瀏瀧瀠瀨瀝瀕瀘瀟瀾瀲瀰灕灄灃灘灑灠灝灣灤灧灩火災為炧炤烏焠焫無煢煇煠煉煙煩煗煬煖煥煆煒熒燁熱熲燙熾燉燐燒燜燄燈營燦燭燬燾燻燿燼爕爗爍爐爛爭爲爺爾爿牀牆牋牐牘牴牽犂犖犛犢犧狀狥狹狽猙猘猶猨獃獅猻獄獏獎獧獨獫獪獰獮獲獷獸獵獺獻獼𤭛甆甎甌甕甖針釘釗釕釬釦釧釤釣釩釵鈁鈃鉅鈍鈔鈐鈑鈞鈒鈕鈀鉈鉉鉍鈺鉦鉗鈷鉢鈸鉞鉆鉀鈿鈴鉄鉑鉛鉤鉚鉋鈹鉸銃鉶銬銠鉺銍銕銅銓銚銖銑銛銗銜銘鉻銀鋭銻鋃鋩鋪鋙鋏鋣銷銲鋤鋜鋝銼鋌銹鋂鋒鋟鋦錠錇錈錟錏錯錡錢鋼錁錕錫錮鍋錚錘錐錦鋸錄錙鍍鎡鍥錨鍊鍼鍇鍘鍚鍔鍰鍬鍤鍾鍛鎪鍠鎚鍵鎋鎔鎊鎬鎰鎌鎣鎮鏈鎛鎘鎒鎲鎖鎧鎗鎩鎢鎦鏡鏟鏑鏞鏃鏇鏵鏌鏨鏗鏢鏚鏜鏤鏝鏐鏹鏘鐋鐘鐓鐃鐔鐐鏷鐫鐙鐮鐳鐵鐺鐸鐶鐲鏽鑌鑄鑑鑒鑊鑛鑣鑤鑠鑕鑞鑪鑲鑰鑱鑷鑾鑼鑽鑿钁長門閂閃閈閉閔閌閏開閑閎間閒閙閘閡関閨閩閤閥閣閲閬閫閭閼闍閾閹閶閿閽閻闊闇闌闃闆闋闈闕闖闔闐闒闓闚關闥闞闠闡闤闢阬阨阯阿陒陣陝陘陗陞陸陳陰隊階隄陽隕隖際隨險隱隴隸隻雋雖雜離雞雙雛難雲電霑霛霤霢霧霶霽靆靄靂靈靉靚靜靦靨靭鞀鞏鞉鞝鞦鞹鞾韃鞽韁韆韉韋韌韍韓韙韝韞韜韤韮韻響頁頂頃頇項順須頏頊頑頓頌頒頎預領頗頦頡頜頫頤頭頰頸頻頮頷頲頽顇顆額顔題顒顋顎顓類顛願顙顢顧顥顦顫顬顯顰顱顳顴風颯颭颮颱颳颶颺颸颼颻飀飄飆飛飡飣飢飥飱飩飪飫飭飯飲養飾飽飴飼飿餃餅餌餁餉餑餖餒餘餓餕館餞餛餚餧餡餬餳餵餶餿餱饈餻饁餺餼餽餾饉饅饍饒饊饋饌饑饗饜饝饞骯骾髏髒體髕髖竾筍筆筴筧筯筰筩箝箋箎箇箏箠箒箲篋範節簑篤築篔篠篩篛𥱼簆篲簀簍篳簙簣簞簾簹簴簽簷簫籌籃籐籠籜籟籛籙籥籢籤籬籩籪籮籯籲籸粃粧糉糢糞糝糧䊪糰糲糱糴糶糸糾紆紂紅紇約紈紀紉紋紡紜紘純紕紗納紛紝紙級紓紖紐紵絃絆紺紲紮紱組紳細紬絅終紿紼絀紹絞統絨結絝絰絏給絛絍絎絳絡絢絶絲綈綁綆經綃綑絹綏綌綉綜綻綰綣綾緒緊綺綫緋綽網綱緄綬綵綸維綿綹綳綢緍綯綴綠緇締編緙緤緗練緘緬緹緲緝緦緩緞緶緥緜線緱縋緯緣縗縞縭縊縑縈縝縛縟緻縉縣縕縚縧縏縫縐縯縮縴績縶縹縷縵縲繃總縱繆繈繅織繕繒繠繞繖繐繚繢繙繭繫繮繩繾繰繹繯繪繳繡繽繼纊纏纇纈續纍纕纓纖纔纘纜缽缾罃罇罈罋罌罏罎罰罸罵罷羆羅羈羢羨義羣羶翄習翬翫翹翽耑耡耬耮耼聖聝聞聲聰聳聯職聶聵聹聾聽肈肅肊肎肐肧肬脄脩脈胮脃脅脣脛脗脹腎腸腳腫腦膁膋膆膃膞膚膕膠膩臈膿臉膾膽臏臍臕臘臙臚臝臟臠臢臥臨臯臺與舉興舊舖舘艙鵃艤艣艦艪艫艱艸芻苧苺荅華莕荳莢莖莧莊菸萇著菢萊菴菓萵蓱葷葉萬葅葠葦葒葯蒞蓆蓋蓮蒔蒼蒨蓧蒐蓀蒓蔆蔴蓴蔕䔛蔞蓽蔦蔥蓯蔔蔣蔭蕩蕅蕓薘蕘蕆蕋蕢蕒蕪蕎蕕蕁薌薦薔薑薟薈薊蕷蕭薴薺藉藍薰藎薩藭藷藝藪藥蘢藹蘂蘀䕭藶蘋蘆藺蘄蘇蘊蘭蘞蘚蘗蘺𧄜蘿虆處號虜虧虯虵蚘蚦蛕蜋蛺蜆蜹蜨蝸蝕蜺蝃蝱蝡蝟蝦蝨螢螞螙螘螄蟄螮螻蟈螿蟯蟲蟬蟣蟻蠆蟶蠅蠍蠙蠐蠔蠑蠣蠟蠭蠨蠱蠶蠻衆衇衊衒術衚衝衛衞衹袞裀袷補裌裏裊裝褃製褏褌複褘褵褲褧褳襃褻褸襆襌襍襏襠襖襤襪襬襯襲襴襵覈昇昞時晉晣晝晳暉暈㬉暘暠暢暱曄暫曇曉曆曏曖曠曡曨曬書會朧末朶杇杓杝枒東柹杴柁柺柟枹枱案栔栞栢栧栰條桚梜桿梘梱梟梔椗椀棄棬棖椏棟棲極棧棡棗棃楳楨業楊楥椶榘楓榜槀榮構槖榪槓榦槤槑榿槍槃樑槨樣樁樺槧樞標樝樓槾樂樅槳橈樹橫樷樸橋檇機橢檁檥㯳檉檣檟檔檢檜檸檳檾檯檮櫃檻櫂櫈櫧櫥櫝櫫櫚櫛櫟櫓櫞櫬櫳𣟄櫪櫨櫸櫺櫻欄權欒欏欑欛欖欞欱欵欽歎歐歘歛歟歗歡歲歷歸殀殘殞殤殫殭殮殯殲殺殼毆毉毘毧毬氂毿氈氌氣氳見視規覓覘覜覡覩覥親覦覯覬覲覷覰覺覽覿觀觔觕觝觴觶觸計訂訃訐訏討訌訕託訖訓訊記訒訪詎訝訥訟訩許訛訢設訣註詠評証詁訶詛詗診詐訴詆詒詞詘詖詔詫該詳誆誄試詩詿詰誇詼誠詮誅詵話詬詭詣詢詾詡説誡誖誌誣語誚誤誥誘誨誕誑誦誒認誼諒諄誶談請諸諏諑誹課誾諍諗論諉誰諛調諂諠諦諳諺諮謎諢諞諾謀諶諜諫諧謔謁謂諤諼諭諡諷諱諝謗謚謙謐講謊謖謡謝謄謅謫譁謨謹謳謾謬識譜譆謿譚譖譌譙證譎譔譏議譴譟譯譽譫讁譸護讅譾讀讋讌讎變讓讕讖讒讚讜讞谿豈豎豐豔豘豬豶貉貍䝟貓貐貛貝貞負貢財責貪貶貧貨販貫貯貳賁貰貼貴貺買貸貿貽費賀資賅賊賈賄貲賃賂賓賕賑賒賠賡賧賦賬賣賭賚賢賤賞賜質賙賴賵賮賽賺購賻賸贅贄賾贇贈贊贏贍贓贔贐贖贗贛𧹞䞓赬趂趙趕趦趨趲跀跕跡跢跥跼踡踐䠊踰踴蹌蹏蹠蹟蹣蹕蹤蹺蹷躉躋躊躍躕躑躚躒躓躡躪躭躱躳躶軀軃車軋軍軌軒軑軔軛軟軲軻軸軹軫軼軺較軾載輊輇輈輅輒輔輕輓輬輦輛輝輩輞輥輪輟輜輳輻輭輯輸轄轂轅轀輿輾轆轉轍轔轎轟轢轡轤辠辤辦辭辮辯農迆迯迺迴這連逎逕逷過進週遊運達違遡遠遝遞遜適遯遶遷遼遺遲選邁還邇邊邐邏那郃郟郤郰郵郳鄆鄉鄖鄔鄒鄘鄰鄭鄲鄧鄴鄶鄺酈酇酖酧醃醞醜醫醬醯醱醻醼釀醿釃釁釅釋釐兀唧幐𦡂袡凉説說";
	   
	 private static Map<Integer,Integer> shortMap = null;
	 private static Map<Integer,Integer> longMap = null;
	 
	 private static final int unicodeMaxCodePoint = 65535;
	 
	 static {
		 initMap();
	 }

	  
	//执行  变量flag  是来判断进行 哪种形式的转换  
	  
	 public static String change(String str, boolean flag) {  
	  if (flag == true) {  
	   return toFan(str);  
	  } else {  
	   return toJian(str);  
	  }  
	 }  
	   
	 //简转繁体  
	 public static String toFan(String str) {  
	  StringBuffer outputString = new StringBuffer(); 
	  if (str!=null)
	  {
		  for (int i = 0; i < str.length(); i++) {
			  int codePoint = str.codePointAt(i);
			  if (shortMap.get(codePoint)!=null)
			  {
				  char[] c = Character.toChars(shortMap.get(codePoint).intValue());
				  outputString.append(new String(c));
			  }
			  //超大字
			  else if (codePoint>unicodeMaxCodePoint)
			  {
				  outputString.append(str.substring(i,i+2));
				  i++;
			  }
			  else
			  {
				  outputString.append((char)codePoint);
			  }
		  }  
	  }
	  return outputString.toString();  
	 }  
	      
	 //繁体转简  
	 public static String toJian(String str) {  
		 StringBuffer outputString = new StringBuffer(); 
		  if (str!=null)
		  {
			  for (int i = 0; i < str.length(); i++) {
				  int codePoint = str.codePointAt(i);
				  if (longMap.get(codePoint)!=null)
				  {
					  char[] c = Character.toChars(longMap.get(codePoint).intValue());
					  outputString.append(new String(c));
				  }
				  else if (codePoint>unicodeMaxCodePoint)
				  {
					  outputString.append(str.substring(i,i+2));
					  i++;
				  }
				  else
					  outputString.append((char)codePoint);
			  }  
		  }
		  return outputString.toString();  
	 } 
	 

	 
	 private static void initMap()
	 {
		 //if (shortMap==null)
		 //{
			 shortMap = new HashMap<Integer, Integer>();
			 for (int i=0,j=0;i<shortChar.length();i++,j++)
			 {
				 int a = shortChar.codePointAt(i);
				 int b = longChar.codePointAt(j);
				 if (a>unicodeMaxCodePoint)
					 i++;
				 if (b>unicodeMaxCodePoint)
					 j++;
				 if (shortMap.get(a)==null)
					 shortMap.put(a, b);
			 }
		// }
//		 if (longMap==null)
//		 {
			 longMap = new HashMap<Integer, Integer>();
			 for (int i=0,j=0;i<longChar.length();i++,j++)
			 {
				 int a = longChar.codePointAt(i);
				 int b = shortChar.codePointAt(j);
				 if (a>unicodeMaxCodePoint)
					 i++;
				 if (b>unicodeMaxCodePoint)
					 j++;
				 if (longMap.get(a)==null)
					 longMap.put(a, b);
			 }
		 //}
	 }
	 
//	 private static void export(String path) throws IOException
//	 {
//		 Workbook wb = new XSSFWorkbook();
//		 Sheet sheet = wb.createSheet();
//		 for (int i=0,j=0;i<shortChar.length();i++,j++)
//		 {
//			 Row row = sheet.createRow(i);
//			 Integer shortCode = shortChar.codePointAt(i);
//			 char[] c = Character.toChars(shortCode);
//			 row.createCell(0).setCellValue(new String(c));
//			 Integer longCode = longChar.codePointAt(j);
//			 c = Character.toChars(longCode);
//			 row.createCell(1).setCellValue(new String(c));
//			 if (shortCode>65535)
//				 i++;
//			 if (longCode>65535)
//				 j++;
//		 }
//		 OutputStream out = new FileOutputStream(path);
//		 wb.write(out);
//		 out.close();
//		 
//	 }
	 
	 /*public static void main(String[] args) throws IOException {
			export("D:\\temp\\辞源.xlsx");

	}*/
}
