package cn.guliandigital.common.utils;

import java.io.File;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.springframework.util.ObjectUtils;

import com.google.common.base.Strings;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TransferUtils {

	public static void main(String[] args) throws Exception {
		//String enc="<p>下叙和滁<img src=\"http://njxadmin.njcbs.cn/jinling/wapi/common/download?filename=/dfb52b0f92e5432aba623bd779ddf51e/zt/Z-ZSK75499-000038-L00022-4-7.png\" class=\"zitu\"></img>此</p>";
		//enc = "<div class=\"biaoti1\">荽剐诉橹榢俕闕</div><p><span class=\"luokuan\">㪅廇㻫埬閁诰</span></p>";
		String enc = FileUtils.readFileToString(new File("d://qilu.txt"), "UTF-8");
		
		enc = StringUtil.replaceAll(enc, "div", "span");	
		 if (StringUtil.contains(enc, "class=\"zitu\">") || StringUtil.contains(enc, "class=\"waizi\">")) {
             enc = StringUtil.replace(enc, "class=\"zitu\">", "class=\"zitu\"/>");
             enc = StringUtil.replace(enc, "class=\"waizi\">", "class=\"waizi\"/>");
         }

//         enc = XmlToolUtils.removeBigwordStr(enc, "big01");
//         enc = XmlToolUtils.removeBigwordStr(enc, "big02");
//         enc = XmlToolUtils.removeBigwordStr(enc, "big03");
         
		String result = getResult(enc);
		
		System.out.println(result);
	}

	/**
	 * 标准化数据，全部加标签
	 * @param content
	 * @return
	 * @throws DocumentException
	 */
	public static String getResult(String content) throws Exception {
		if(Strings.isNullOrEmpty(content)) {
			return content;
		}
		//log.info("==>开始标准化标签......"+content);
		content = "<root>" + content + "</root>";
		Document document = DocumentHelper.parseText(content);
		Element rootElement = document.getRootElement();
		List<Node> selectNodes = rootElement.selectNodes("/root/p|span");// 查找所有文本节点
		StringBuilder sb = new StringBuilder();
		for (Node node : selectNodes) {			
			Element _ele = (Element)node;
			Attribute _attri = _ele.attribute("class");
			if(ObjectUtils.isEmpty(_attri)) {
				sb.append("<p>");
			}else {
				if(StringUtils.contains(_attri.getValue(), "biaoti")) {
					sb.append("<p class=\""+_attri.getValue()+"\">");	
				}
				if(StringUtils.equals(_attri.getValue(), "bt")) {
					sb.append("<p class=\"bt\">");	
				}else {
					sb.append("<p>");		
				}
			}
			getNextData((Element)node,  sb);
			
			sb.append("</p>");
		}
		return sb.toString();
	}

	private static void getNextData(Element element, StringBuilder buffers) {
		List nodes = element.content();
		
		Iterator iterator = nodes.iterator();
		
		while(iterator.hasNext()){
			Node node = (Node)iterator.next();
			switch (node.getNodeType()) {
			case Node.ELEMENT_NODE:
				if (StringUtil.equalsIgnoreCase("span", node.getName())) {
					getNextData((Element) node, buffers);
				} else {
					if (StringUtil.equalsIgnoreCase(node.getName(), "img")) {// 继承父标签为span的属性
						//System.out.println("=====>img");
						Element img = DocumentHelper.createElement("img");
						img.addText("");
						Element nn = (Element)node;
						img.setAttributes(nn.attributes());
						Element span = DocumentHelper.createElement("span");
						span.add(img);
						buffers.append(span.asXML());		
					}
				}
				break;
			case Node.TEXT_NODE:
				String text = node.getText();
			
				for (Character character : text.toCharArray()) {
					String valueOf = String.valueOf(character);

					Element span = DocumentHelper.createElement("span");
					List<Attribute> lista = node.getParent().attributes();
					span.setAttributes(lista);
					span.addText(valueOf);
					buffers.append(span.asXML());
				}
				break;
			}
		}
	}

}
