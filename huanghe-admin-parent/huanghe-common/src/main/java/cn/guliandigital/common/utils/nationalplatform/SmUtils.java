package cn.guliandigital.common.utils.nationalplatform;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import org.apache.commons.lang.StringUtils;

import java.util.concurrent.TimeUnit;

public class SmUtils {

    private final String secretKey;// secretKey;

    //指明加密算法和秘钥
    private final SymmetricCrypto sm4;

    public SmUtils(String secretKey) {
        this.secretKey = secretKey;
        this.sm4 = new SymmetricCrypto("SM4/ECB/PKCS5Padding", secretKey.getBytes());
    }

    //校验时间戳是否在5分钟之内
    public static boolean checkTime(String clientTime){
        if (StringUtils.isEmpty(clientTime)){
            return false;
        }
        long clientMinute = TimeUnit.MILLISECONDS.toMinutes(Long.parseLong(clientTime));
        long minute = TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis());
        long l = minute - clientMinute;
        return l <= 5 && l >= -5;
    }

    //SM4加密为16进制，也可以加密成base64/字节数组
    public  String encryptSm4(String plaintext) {
        return sm4.encryptHex(plaintext);
    }

    //SM4解密
    public  String decryptSm4(String ciphertext) {
        return sm4.decryptStr(ciphertext);
    }

    //SM3加盐再加密
    public  String encryptSm3hash(String plain,String clientTime) {
        if (StringUtils.isEmpty(plain)||StringUtils.isEmpty(clientTime)){
            return null;
        }
        plain= plain + clientTime + secretKey;//数据加盐
        return SmUtil.sm3(plain);
    }

}
