package cn.guliandigital.common.enums;

/**
 * 数据来源
 * 
 * <AUTHOR>
 */
public enum DataForm
{
    P("P", "平台"), A("A", "小程序");

    private final String code;
    private final String info;

    DataForm(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
