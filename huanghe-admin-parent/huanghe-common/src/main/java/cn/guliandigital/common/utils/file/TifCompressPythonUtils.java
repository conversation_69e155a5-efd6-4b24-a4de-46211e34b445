package cn.guliandigital.common.utils.file;

import java.io.File;
import java.util.Map;

import cn.guliandigital.common.config.HuangHeConfig;
import com.google.common.collect.Maps;

import cn.guliandigital.common.utils.StringUtil;
import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TifCompressPythonUtils {

	//private static String httpUrl = "http://192.168.10.162:18608/tifcompress/v2";

	public static void tifCompressHandler(String inputPath, String outputPath, int height, int width) throws Exception{
		//log.info("==>开始转换");
		File inputFile = new File(inputPath);
		
		if (!FileUtil.exist(inputFile)) {
			log.info("==>输入文件不存在 {}", inputPath);
			return;
		}
		
//		if(StringUtil.equalsIgnoreCase(FilenameUtils.getExtension(inputFile.getName()),"jpg")) {
//			log.info("==>输入文件是jpg,不需要转换  {}", inputPath);
//			return;
//		}
		
		
		//log.info("==>图片大小：{}",inputFile.length());
		//long st = System.currentTimeMillis();
		
				
		Map<String, Object> param = Maps.newHashMap();
		param.put("srcpath", inputPath);
		param.put("targetpath", outputPath);
		param.put("height", "");
		param.put("width", "");
		Map<String, Object> result = HttpUtils.doPost(HuangHeConfig.getImgCompressHttp(), param);
		//log.info("==>{}", result);
		//long et1 = System.currentTimeMillis();
		//log.info("==>http花费时间："+(et1-st));
		
		if(StringUtil.equals(result.get("code").toString(),"00")) {
			
			Object costtime = result.get("costtime");					
			//long et2 = System.currentTimeMillis();
			log.info("==>模型花费时间：{} s",costtime);			
		}
	}

	
}
