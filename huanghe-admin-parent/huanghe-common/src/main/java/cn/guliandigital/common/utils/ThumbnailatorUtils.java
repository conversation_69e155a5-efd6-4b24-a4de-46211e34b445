package cn.guliandigital.common.utils;

import java.io.File;
import java.io.IOException;

import org.apache.commons.io.FilenameUtils;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

@Slf4j
public class ThumbnailatorUtils {

	/**
	 * 指定大小进行缩放
	 * 
	 * @throws IOException
	 */
	public static void compressPic(String inputPath, String outputPath, int height, int width) {
		/*
		 * size(width,height) 若图片横比200小，高比300小，不变 若图片横比200小，高比300大，高缩小到300，图片比例不变
		 * 若图片横比200大，高比300小，横缩小到200，图片比例不变 若图片横比200大，高比300大，图片按比例缩小，横为200或高为300
		 */
		try {
			Thumbnails.of(inputPath).size(width, height).keepAspectRatio(false).toFile(outputPath);
		} catch (Exception e) {
			log.error("压缩图片异常", e);
		}
	}

	/**
	 * 
	 * @param inputPath
	 * @param outputPath
	 * @param scale      比例
	 * @param quality    质量
	 * @throws Exception 
	 */
	public static void compressPicScale(String inputPath, String outputPath, double scale, float quality) throws Exception {
		/*
		 * size(width,height) 若图片横比200小，高比300小，不变 若图片横比200小，高比300大，高缩小到300，图片比例不变
		 * 若图片横比200大，高比300小，横缩小到200，图片比例不变 若图片横比200大，高比300大，图片按比例缩小，横为200或高为300
		 */
		try {
			Thumbnails.of(inputPath).scale(scale).outputQuality(quality).toFile(outputPath);
		} catch (Exception e) {
			log.error("压缩图片异常", e);
			throw e;
		}
	}

	public static void main(String args[]) throws Exception {
//		String input = "C:\\\\Users\\\\<USER>\\\\Desktop\\\\ZSK86442-000007-L00003_E.jpg";
//		String output = "C:\\\\Users\\\\<USER>\\\\Desktop\\\\ZSK86442-000007-L00003_C.jpg";
//		File out = new File(output);
//		if(out.exists()) {
//			out.delete();
//		}
//		//Thread.sleep(18000);
//		float f = 1/6;
//		System.out.println(f);
//		compressPicScale(input, output, 0.4, 1);
		
		String path = "D:\\stand_pic-result";
		String outbasepath = path + File.separator +"tmp";
		File obp = new File(outbasepath);
		if(!obp.exists()) {
			boolean ismk = obp.mkdirs();
			if(!ismk) {
            	log.info("文件夹{}创建失败",obp.getAbsolutePath());
            }
		}
		File file = new File(path);
		File[] fs = file.listFiles();
		for(File f : fs) {
			if(f.isFile()) {
				String outputPath = outbasepath+File.separator+f.getName();
			
				Thumbnails.of(f).scale(0.2).outputQuality(1).toFile(outputPath);
			}
		}
	}
}
