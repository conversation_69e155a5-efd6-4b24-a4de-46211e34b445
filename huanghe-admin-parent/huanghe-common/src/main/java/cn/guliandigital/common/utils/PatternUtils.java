package cn.guliandigital.common.utils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

public class PatternUtils {
	
	public static final String FILE_NUMBER_SUFFIX_REGEX = "\\s*\\((\\d+)\\)\\..*";
	
	public static final String DRIVE_LETTER_REGEX = "^[a-zA-Z]:.*$";
	
	public static final String DIRECTORY_NUMBER_SUFFIX_REGEX = "\\s*\\((\\d+)\\)";
	
	public static final String FILE_NUMBER_REGEX_EXTENSION_FORMAT = "%s\\s*\\((\\d+)\\)\\.%s";
	
	public static final String DIRECTORY_NUMBER_REGEX_EXTENSION_FORMAT = "%s\\s*\\((\\d+)\\)";
	
	public static final Pattern linePattern = Pattern.compile("_(\\w)");
	
	public static String escapeValue(String value) {
		if (StringUtil.isNotBlank(value)) {
			String[] escapeChars = { "\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|" };
			for (String key : escapeChars) {
				if (value.contains(key)) {
					value = value.replace(key, "\\" + key);
				}
			}
		}
		return value;
	}
	
	public static Integer matcherMax(String regex, List<String> list) {
		Integer max = 1;
		for (String content : list) {
			if (Pattern.matches(regex, content)) {
				// 获取重复文件编号
				String matcher = getMatcher(regex, content);
				if (StringUtil.isNumeric(matcher)) {
					Integer valueOf = Integer.valueOf(matcher);
					if (valueOf != null && valueOf >= max) {
						max = valueOf + 1;
					}
				}
			}
		}
		return max;
	}
	
	public static String getMatcher(String regex, String content) {
		String result = "";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(content);
		while (matcher.find()) {
			result = matcher.group(1);
		}
		return result;
	}
	
	public static String getMatcher(String regex, String content, int index) {
		String result = "";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(content);
		while (matcher.find()) {
			result = matcher.group(index);
		}
		return result;
	}
	
	/**
	 * 获取文件名称序号
	 * @param filename
	 * @param content
	 * @return
	 */
	public static String getFileNumberMatcher(String filename, String content) {
		String baseName = FilenameUtils.getBaseName(filename);
		String extension = FilenameUtils.getExtension(filename);
		String regex = String.format(FILE_NUMBER_REGEX_EXTENSION_FORMAT, escapeValue(baseName), extension);
		return getMatcher(regex, content);
	}
	
	public static String getDirectoryNumberMatcher(String directoryName, String content) {
		String regex = String.format(DIRECTORY_NUMBER_REGEX_EXTENSION_FORMAT, escapeValue(directoryName));
		return getMatcher(regex, content);
	}
	
	public static String buildRegex(String tpl, String...contents) {
		for (int i = 0; i < contents.length; i++) {
			contents[i] = escapeValue(contents[i]);
		}
		return String.format(tpl, contents);
	}
	
	/** 下划线转驼峰 */
	public static String lineToHump(String str) {
		str = str.toLowerCase();
		Matcher matcher = linePattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		while (matcher.find()) {
			matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/** 驼峰转下划线(简单写法，效率低于{@link #humpToLine2(String)}) */
	public static String humpToLine(String str) {
		return str.replaceAll("[A-Z]", "_$0").toLowerCase();
	}

	private static Pattern humpPattern = Pattern.compile("[A-Z]");

	/** 驼峰转下划线,效率比上面高 */
	public static String humpToLine2(String str) {
		Matcher matcher = humpPattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		while (matcher.find()) {
			matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
		}
		matcher.appendTail(sb);
		return sb.toString();
	}
	
	public static void main(String[] args) {
		//log.info(FilenameUtils.getBaseName("2 (2).jpg"));
//		boolean matches = Pattern.matches("2 \\(2\\)" + "(\\(\\d+\\))\\..*", "2 (2)(1)(2).jpg");
//		System.out.println(matches);
//		boolean matches1 = Pattern.matches("2" + "\\s*(\\(\\d+\\))\\..*", "2(1).jpg");
//		System.out.println(matches1);
//		String matcher = DiskUtils.getMatcher("2 \\(2\\)" + "\\s*\\((\\d+)\\)\\.xml", "2 (2)(3).jpg");
//		System.out.println(matcher);
//		boolean matches2 = Pattern.matches("(\\d+)(\\w+)", "11KB");
//		System.out.println(matches2);
//		String matcher2 = PatternUtils.getMatcher("(\\d+)(\\w+)", "11KB", 1);
//		System.out.println(matcher2);
//		String matcher3 = PatternUtils.getMatcher("(\\d+)(\\w+)", "11KB", 2);
//		System.out.println(matcher3);
		//System.out.println(String.format(FILE_NUMBER_REGEX_EXTENSION_FORMAT, "2 (2)", "jpg"));
		//String matcher4 = getMatcher(String.format(FILE_NUMBER_REGEX_EXTENSION_FORMAT, escapeValue("2"), "jpg"), "2 (2)(3).jpg");
//		String fileNumberMatcher = getFileNumberMatcher("2.jpg", "2(2).jpg");
//		System.out.println(fileNumberMatcher);
//		String directoryNumberMatcher = getDirectoryNumberMatcher("2", "2(2)");
//		System.out.println(directoryNumberMatcher);
//		
//		String buildRegex = buildRegex(FILE_NUMBER_REGEX_EXTENSION_FORMAT, "123", "jpg");
//		System.out.println(buildRegex);
		
		String lineToHump = lineToHump("f_parent_no_leader");
	    System.out.println(lineToHump);// fParentNoLeader
	    System.out.println(humpToLine(lineToHump));// f_parent_no_leader
	    System.out.println(humpToLine2(lineToHump));// f_parent_no_leader
		
	}
}
