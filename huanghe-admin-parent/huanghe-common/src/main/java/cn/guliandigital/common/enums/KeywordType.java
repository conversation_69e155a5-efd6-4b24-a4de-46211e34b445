package cn.guliandigital.common.enums;

import cn.guliandigital.common.utils.StringUtil;

/**
 * 检索类型
 * 
 */
public enum KeywordType {

	ALL("all", "综合", new String[] { "fullText", "menuName" }, new String[] { "fullText", "menuName" }), //
	BOOK_NAME("bookName", "题名", new String[] { "bookName" }, new String[] { "bookName" }), //
	MAIN_RESPONSIBILITY("mainResponsibility", "作者", new String[] { "mainResponsibility" },
			new String[] { "mainResponsibility" }), //
	PUBLISHER("publisher", "出版者", new String[] { "publisher.biaodian" }, new String[] { "publisher" }),

	ALL_AND_BOOK_NAME_AND_MAIN_RESPONSIBILITY("allAndBookNameAndMainResponsibility", "全文+作者+题名",
			new String[] { "fullText", "menuName","bookName","mainResponsibility" },
			new String[] { "fullText", "menuName","bookName","mainResponsibility" }); //


	private final String code;
	private final String info;
	private final String[] fields;
	private final String[] ignoreFields;

	KeywordType(String code, String info, String[] fields, String[] ignoreFields) {
		this.code = code;
		this.info = info;
		this.fields = fields;
		this.ignoreFields = ignoreFields;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}

	public String[] getFields() {
		return fields;
	}
	
	public String[] getIgnoreFields() {
		return ignoreFields;
	}

	public static KeywordType getByCode(String code) {
		if (StringUtil.isBlank(code)) {
			return KeywordType.ALL;
		}
		KeywordType[] values = values();
		for (KeywordType keywordType : values) {
			if (StringUtil.equals(keywordType.getCode(), code)) {
				return keywordType;
			}
		}
		return KeywordType.ALL;
	}
}
