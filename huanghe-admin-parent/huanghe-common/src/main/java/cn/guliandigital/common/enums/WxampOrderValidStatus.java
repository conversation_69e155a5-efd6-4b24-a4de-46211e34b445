package cn.guliandigital.common.enums;

import cn.guliandigital.common.utils.StringUtil;

// 订单生效状态
public enum WxampOrderValidStatus {

	OFF("0", "未生效"), //
	ON("1", "已生效"); //

	private final String code;
	private final String info;

	WxampOrderValidStatus(String code, String info) {
		this.code = code;
		this.info = info;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}
	
	/**
     * 根据key获取enum
     *
     * @param code
     * @return
     */
    public static WxampOrderValidStatus getEnum(String code) {
    	WxampOrderValidStatus[] wxampSelectStatusArray = values();
        for (WxampOrderValidStatus wxampSelectStatus : wxampSelectStatusArray) {
			if (StringUtil.equals(wxampSelectStatus.getCode(), code)) {
                return wxampSelectStatus;
            }
        }
        return null;
    }
}
