package cn.guliandigital.common.utils.http;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * @ClassName HttpClientUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/23 11:21
 */
public class HttpClientUtils {
    // 日志记录
    private static Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    private static RequestConfig requestConfig = null;

    static {
        // 设置请求和传输超时时间
        requestConfig = RequestConfig.custom().setSocketTimeout(2000).setConnectTimeout(2000).build();
    }

    /**
     * post请求传输json参数
     *
     * @param url       url地址
     * @param jsonParam 参数
     * @return
     */
//    public static JSONObject httpPost(String url, JSONObject jsonParam) {
//        // post请求返回结果
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        JSONObject jsonResult = null;
//        HttpPost httpPost = new HttpPost(url);
//        // 设置请求和传输超时时间
//        httpPost.setConfig(requestConfig);
//        try {
//            if (null != jsonParam) {
//                // 解决中文乱码问题
//                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
//                entity.setContentEncoding("UTF-8");
//                entity.setContentType("application/json");
//                httpPost.setEntity(entity);
//            }
//            CloseableHttpResponse result = httpClient.execute(httpPost);
//            // 请求发送成功，并得到响应
//            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                String str = "";
//                try {
//                    // 读取服务器返回过来的json字符串数据
//                    str = EntityUtils.toString(result.getEntity(), "utf-8");
//                    // 把json字符串转换成json对象
//                    jsonResult = JSONObject.parseObject(str);
//                } catch (Exception e) {
//                    logger.error("post请求提交失败:" + url, e);
//                }
//            }
//        } catch (IOException e) {
//            logger.error("post请求提交失败:" + url, e);
//        } finally {
//            httpPost.releaseConnection();
//            try {
//				httpClient.close();
//			} catch (IOException e) {
//				Log.error("",e);
//			}
//        }
//        return jsonResult;
//    }

    /**
     * post请求传输String参数 例如：name=Jack&sex=1&type=2
     * Content-type:application/x-www-form-urlencoded
     *
     * @param url      url地址
     * @param strParam 参数
     * @return
     */
//    public static JSONObject httpPost(String url, String strParam) {
//        // post请求返回结果
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        JSONObject jsonResult = null;
//        HttpPost httpPost = new HttpPost(url);
//        httpPost.setConfig(requestConfig);
//        try {
//            if (null != strParam) {
//                // 解决中文乱码问题
//                StringEntity entity = new StringEntity(strParam, "utf-8");
//                entity.setContentEncoding("UTF-8");
//                entity.setContentType("application/x-www-form-urlencoded");
//                httpPost.setEntity(entity);
//            }
//            CloseableHttpResponse result = httpClient.execute(httpPost);
//            // 请求发送成功，并得到响应
//            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                String str = "";
//                try {
//                    // 读取服务器返回过来的json字符串数据
//                    str = EntityUtils.toString(result.getEntity(), "utf-8");
//                    // 把json字符串转换成json对象
//                    jsonResult = JSONObject.parseObject(str);
//                } catch (Exception e) {
//                    logger.error("post请求提交失败:" + url, e);
//                }
//            }
//        } catch (IOException e) {
//            logger.error("post请求提交失败:" + url, e);
//        } finally {
//            httpPost.releaseConnection();
//            try {
//				httpClient.close();
//			} catch (IOException e) {
//				Log.error("",e);
//			}
//        }
//        return jsonResult;
//    }

    /**
     * 发送get请求
     *
     * @param url 路径
     * @return
     */
    public static JSONObject httpGet(String url) throws Exception {
        // get请求返回结果
        JSONObject jsonResult = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 发送get请求
        HttpGet request = new HttpGet(url);
        request.setConfig(requestConfig);
        try {
            CloseableHttpResponse response = httpClient.execute(request);

            // 请求发送成功，并得到响应
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数据
                HttpEntity entity = response.getEntity();
                String strResult = EntityUtils.toString(entity, "utf-8");
                // 把json字符串转换成json对象
                jsonResult = JSONObject.parseObject(strResult);
            } else {
                logger.error("get请求提交失败:" + url);
            }
        } catch (Exception e) {
            logger.error("get请求提交失败:" + url, e);
            throw e;
        } finally {
            request.releaseConnection();
            try {
				httpClient.close();
			} catch (IOException e) {
				logger.error("",e);
			}
        }
        return jsonResult;
    }

    public static JSONObject post(String url, Map<String, Object> request, Map<String, String> headers)
            throws Exception {
        JSONObject jsonResult = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(JSON.toJSONString(request), "UTF-8"));
        httpPost.addHeader("Content-Type", "application/json");
        for (Entry<String, String> entry : headers.entrySet()) {
            httpPost.addHeader(entry.getKey(), entry.getValue());
        }

        CloseableHttpResponse result = httpClient.execute(httpPost);
        // 请求发送成功，并得到响应
        if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            String str = "";
            try {
                // 读取服务器返回过来的json字符串数据
                str = EntityUtils.toString(result.getEntity(), "utf-8");
                // 把json字符串转换成json对象
                jsonResult = JSONObject.parseObject(str);
            } catch (Exception e) {
                logger.error("post请求提交失败:" + url, e);

            } finally {
                httpPost.releaseConnection();
                try {
    				httpClient.close();
    			} catch (IOException e) {
    				logger.error("",e);
    			}
            }

        }
        return jsonResult;
    }

    public static byte[] get(String url, Map<String, String> headers)
            throws Exception {
        //JSONObject jsonResult = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("Content-Type", "application/json");
        for (Entry<String, String> entry : headers.entrySet()) {
            httpGet.addHeader(entry.getKey(), entry.getValue());
        }
        CloseableHttpResponse result = httpClient.execute(httpGet);
        // 请求发送成功，并得到响应
        if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            try {
                HttpEntity entity = result.getEntity();
                byte[] data = EntityUtils.toByteArray(entity);
                if(data == null) {
                	return null;
                }
                String fileName = result.getHeaders("Content-Disposition")[0].getValue().split("filename=")[1];
                logger.info("文件名为" + fileName);

                FileOutputStream fos = new FileOutputStream(fileName);
                fos.write(data);
                fos.close();
                if(data!=null){
                    return data;
                }

            } catch (Exception e) {
                logger.error("post请求提交失败:" + url, e);

            } finally {
                httpGet.releaseConnection();
                try {
    				httpClient.close();
    			} catch (IOException e) {
    				logger.error("",e);
    			}
            }

        }
        return null;
    }

}
