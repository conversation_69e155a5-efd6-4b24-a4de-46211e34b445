package cn.guliandigital.common.utils;

import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class RandomUtil {
    private static final Random random = new Random();
    // 预定义200个常用汉字（示例，可扩展）
    private static final char[] COMMON_CHINESE = {
            '王','李','张','刘','陈','杨','黄','赵','周','吴','徐','孙','马','朱','胡','郭','何','高','林','罗',
            '爱','文','小','明','伟','芳','丽','强','秀','英','娜','敏','静','宇','飞','鹏','浩','海','波','平',
            '伟','涛','杰','鑫','欣','怡','婷','博','超','晨','雨','欣','瑶','思','嘉','俊','宇','轩','泽','瑞',
            '乐','天','一','子','辰','奕','然','梓','浩','轩','萱','琪','欣','悦','琳','彤','佳','心','诗','梦',
            '安','白','柏','宝','斌','冰','博','才','彩','灿','昌','超','辰','成','诚','川','春','聪','达','丹',
            '德','东','冬','凡','方','芳','飞','菲','芬','风','峰','刚','歌','光','国','海','豪','浩','和','鹤',
            '贺','亨','宏','华','欢','辉','慧','惠','吉','佳','家','嘉','建','健','江','杰','洁','金','瑾','菁',
            '静','娟','君','俊','凯','康','可','兰','乐','雷','磊','丽','利','良','林','琳','玲','龙','曼','美',
            '萌','梦','娜','楠','宁','培','佩','萍','琪','琦','倩','强','琴','清','晴','庆','荣','蓉','瑞','莎',
            '珊','善','尚','绍','生','胜','盛','诗','石','世','舒','淑','双','顺','思','松','泰','涛','天','田'
    };
    private static final int ENGLISH_LOWER_START = 97;  // a-z
    private static final int ENGLISH_UPPER_START = 65;  // A-Z


    public static String getUserName() {
        int length = 2 + random.nextInt(5); // 生成2-6位长度
        StringBuilder username = new StringBuilder();

        for (int i = 0; i < length; i++) {
            // 50%概率生成中文，50%概率生成英文
            if (random.nextDouble() < 0.5) {
                username.append(getRandomChinese());
            } else {
                username.append(getRandomEnglish());
            }
        }
        return username.toString();
    }

    // 从常用汉字数组中随机选取
    private static char getRandomChinese() {
        return COMMON_CHINESE[random.nextInt(COMMON_CHINESE.length)];
    }

    // 生成随机英文字母（大小写随机）
    private static char getRandomEnglish() {
        return random.nextBoolean() ?
                (char) (ENGLISH_LOWER_START + random.nextInt(26)) :
                (char) (ENGLISH_UPPER_START + random.nextInt(26));
    }

    /**
     * 获取指定区间随机数
     * @param min
     * @param max
     * @return
     */
    public static int randomNum(int min, int max){
        return ThreadLocalRandom.current().nextInt(min, max+1);
    }

    public static void main(String[] args) {
        //System.out.println(generateMixedUsername());

        //System.out.println(ThreadLocalRandom.current().nextInt(5, 26));
        String dateStr = "2010年10月10日";



    }


}