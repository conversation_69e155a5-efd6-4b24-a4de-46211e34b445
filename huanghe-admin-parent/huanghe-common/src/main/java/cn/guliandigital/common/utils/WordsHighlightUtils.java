package cn.guliandigital.common.utils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.seimicrawler.xpath.JXDocument;
import org.seimicrawler.xpath.JXNode;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.guliandigital.common.constant.EsConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 字符串工具类
 * 
 * <AUTHOR>
 */

@Slf4j
public class WordsHighlightUtils {

	/**
	 * 
	 * @param str
	 * @param search
	 * @param pos
	 *            出现的次数 比如第几次出现
	 */
	public static Map<String, Integer> findWordPos(String str, String search, int pos, String orginStr) {
		if (Strings.isNullOrEmpty(str) || Strings.isNullOrEmpty(search)) {
			return Maps.newHashMap();
		}
		StringBuffer buff = new StringBuffer("(");
		List<String> searchwords = BigwordUtil.words2list(search);
		for(String word : searchwords) {
			buff.append(word).append("[#]*");
		}
		buff = buff.delete(buff.length()-4, buff.length());
		buff.append(")");
		Map<String, Integer> _map = Maps.newHashMap();
		//Pattern p = Pattern.compile("(因[#]*为[#]*中[#]*国)");
		Pattern p = Pattern.compile(buff.toString());	
		//log.info("==>{}", str);
		Matcher m = p.matcher(str);
		int _pos = 0;
		int start = 0;
		int end = 0;
		while (m.find()) {
			
			start = m.start();
			end = m.end();			
			if (pos == _pos) {
				break;
			}
			_pos++;
		}

		// 对结果进行处理
		
		for (String searchword : searchwords) {			
			int count = 0;
			for (int i = 0; i < orginStr.length(); i++) {
				char currentChar = orginStr.charAt(i);
				if (currentChar == searchword.charAt(0)) {
					count++;
					if (i >= start && i <= end) {
						 log.info("["+searchword+"]出现的次数："+count);						 
						_map.put(searchword+"_"+count, count);						
					}					
				}
				if(i > end) {
					break;
				}
			}
		}
		return _map;
	}
	/**
	 *专名词专用
	 * @param str
	 * @param search
	 * @param pos
	 *            出现的次数 比如第几次出现
	 */
	public static Map<String, Integer> findWordPosZmc(String str, String search, String orginStr) {
		if (Strings.isNullOrEmpty(str) || Strings.isNullOrEmpty(search)) {
			return Maps.newHashMap();
		}
		StringBuffer buff = new StringBuffer("(");
		List<String> searchwords = BigwordUtil.words2list(search);
		for(String word : searchwords) {
			buff.append(word).append("[#]*");
		}
		buff = buff.delete(buff.length()-4, buff.length());
		buff.append(")");
		Map<String, Integer> _map = Maps.newHashMap();
		//Pattern p = Pattern.compile("(因[#]*为[#]*中[#]*国)");
		Pattern p = Pattern.compile(buff.toString());
		//log.info("==>{}", str);
		Matcher m = p.matcher(str);
		int index=0;
		while (m.find()) {
			int start = m.start();
			int end = m.end();
			Boolean isTrue=false;
			// 对结果进行处理
			for (String searchword : searchwords) {
				int count = 0;
				for (int i = 0; i < orginStr.length(); i++) {
					char currentChar = orginStr.charAt(i);
					if (currentChar == searchword.charAt(0)) {
						count++;
						if (i >= start && i <= end) {
							isTrue=true;
							log.info("["+searchword+"]出现的次数："+count);
							log.info("["+searchword+"]出现的序号："+index);
							_map.put(searchword+"_"+count, index);
						}
					}
					if(i > end) {
						break;
					}
				}
			}
			if (isTrue){
				index++;
			}
		}
		return _map;
	}

	

	/**
	 *
	 * @param str
	 *            原文
	 * @param searchpos
	 *            字坐标对应关系
	 *
	 * @return
	 */
	public static String hightlightWords(String str, Map<String, Integer> searchpos) {
		String result = str;
		for (Entry<String, Integer> entry : searchpos.entrySet()) {
			String _word = entry.getKey();
			String[]  words = _word.split("_");
			String word = words[0];

			Integer pos = entry.getValue();

			Pattern p = Pattern.compile(word);
			Matcher m = p.matcher(result);
			int _pos = 0;

			while (m.find()) {
				_pos++;
				String groupstr = m.group();
				if (pos == _pos) {
					result = StringUtil.substring(result, 0, m.start()) + EsConstants.HIGHTLIGH_START + groupstr
							+ EsConstants.HIGHTLIGH_END + StringUtil.substring(result, m.start() + 1, result.length());

					break;
				}
			}
		}
		return result;
	}

	/**
	 * 
	 * @param targetstr 本章节的全部html内容
	 * 
	 * @return
	 * @throws Exception
	 */
	public static String completeLostHtmlTag(String targetstr) {
		String result = "";
			
		//本身
		List<String> closetags = lastUncloseTags(targetstr);
		//log.info(StringUtil.join(closetags, "|||||"));
		StringBuilder buff = new StringBuilder();
		Collections.sort(closetags, Collections.reverseOrder());
		
		for(String tag : closetags) {
			if(StringUtil.startsWith(tag, "<span")) {
				buff.append("</span>");
			}
			if(StringUtil.startsWith(tag, "<div")) {
				buff.append("</div>");
			}	
			if(StringUtil.startsWith(tag, "<a")) {
				buff.append("</a>");
			}
			if(StringUtil.startsWith(tag, "<p")) {
				buff.append("</p>");
			}
		}
		//String lostTags = StringUtil.join(closetags, "");
			
		result =  result + targetstr + buff.toString();		
		
		return result;
	}
	
	/**
	 * 
	 * @param htmlStr
	 * @return
	 */
	private static List<String> lastUncloseTags(String htmlStr){
		List<String> list = Lists.newCopyOnWriteArrayList();
		Stack<String> statck = new Stack<String>();
		
		//Pattern p = Pattern.compile("<\\/[a-zA-Z]*>");
		//Pattern p = Pattern.compile("<p>|<span class=\"[a-zA-Z0-9]*\"[^<>]*>|</span>|</p>");
		//Pattern TAG_PATTERN = Pattern.compile("<\\s*(\\w+)(?:\\s|>|\\s+[^>])*");
		Pattern TAG_PATTERN = Pattern.compile("<\\s*(\\w+)(?:\\s|>|\\s+[^>])*|(</\\w+){1}");
		Matcher m = TAG_PATTERN.matcher(htmlStr);		
		// 先找出不配对得标签，记录位置下表
		while (m.find()) {
			String tag = m.group();
			//System.out.println(tag);
			if(StringUtil.startsWith(tag, "<")&&!StringUtil.startsWith(tag, "</")) {
				statck.add(tag);
			}
			if(StringUtil.startsWith(tag, "</")) {	
				if(!statck.empty()) {
					String tags = statck.pop();
					boolean isdelete = list.remove(tags);
					if(!isdelete) {
						list.add(tag);
					}
				}else {
					list.add(tag);
				}
			}else {
				list.add(tag);
			}
		}
		return list;
	}
	
	public static String  removePage(String content) {
		//String patstr = "(P[0-9]*)";
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(content);		    
	    while(m.find()){	 
	    	content = m.replaceAll("");	    	
	    }
	    return content;
	}
	
	
	
	/**
	 * 去掉注释之类的标签
	 * @param html
	 * @return
	 */
	public static String removeHtmlTag(String html) {
		String result = html;
		Document doc = (Document) Jsoup.parse(html);
		JXDocument jxDocument = JXDocument.create(doc);
		
		List<JXNode> elementsToRemove = jxDocument.selN("//span[@class]");
		for(JXNode node : elementsToRemove) {			
			String span = node.toString();
			String sss = span.replaceAll("<{1}[^<>]*>{1}", "");
			log.info(span);
			int len = StringUtils.length(sss);
			String padStr = StringUtils.leftPad("", len, "#");
			result = StringUtils.replace(result, node.toString(),padStr);			
		}
		
		return result;
	}
	
	
	public static String removeZITUtag(String html) {
		String result = html;
		Document doc = (Document) Jsoup.parse(html);
		JXDocument jxDocument = JXDocument.create(doc);
		
		List<JXNode> elementsToRemove = jxDocument.selN("//img[@src]");
		for(JXNode node : elementsToRemove) {			
			String span = node.toString();			
			log.info(span);
		
			result = StringUtils.replace(result, node.toString(), "★");			
		}
		
		return result;
	}
	
	/**
	 * 
	 * @param fullstr 全文  带标签 
	 * @param notagchars 去掉标签的全文
	 * @param limit 限制字数
	 * @return
	 */
	public static String getLimitChars(String fullstr, int limit) {
		String result  = null;
		if(Strings.isNullOrEmpty(fullstr)) {
			return fullstr;
		}
	         
		String notagchars = WordsHighlightUtils.removePage(fullstr);
		notagchars = notagchars.replaceAll("<{1}[^<>]*>{1}", "");
		notagchars = StringUtil.replaceAllBlank(notagchars);
        
		if(StringUtils.length(notagchars) < limit) {
			limit = StringUtils.length(notagchars);
		}
		String limitcontent = StringUtil.substring(notagchars, 0, limit);
		String lastChar = StringUtils.substring(limitcontent, limitcontent.length()-1, limitcontent.length());
		log.info(lastChar);
		int lastcharIdx = StringUtils.lastIndexOf(limitcontent, lastChar);
		log.info(" lastcharIdx = {}",lastcharIdx);
		Pattern p = Pattern.compile(lastChar);
		Matcher m = p.matcher(limitcontent);
		int _pos = 0;
		while (m.find()) {
			_pos++;
		}
		log.info("相对位置：第{}个位置",_pos);
		
		//在原文中找出最后一个字的绝对位置
		Pattern p2 = Pattern.compile(lastChar);
		Matcher m2 = p2.matcher(fullstr);
		int _pos2 = 0;
		int end = 0;
		while (m2.find()) {
			_pos2++;
			if(_pos2 == _pos) {
				end = m2.end();
			}
		}
		log.info("绝对位置：{}",end);
		result = StringUtils.substring(fullstr, 0, end);
		log.info("提取字符串：{}",result);
		result = completeLostHtmlTag(result);
		log.info("补全后：{}",result);
		return result;		
	}
	
	public static void main(String[] args) throws Exception {

		// String sss = "我们中国人在中国生活,中国真好";
//		String search = "因为中国";
//		
//		String sssss = "<p><span class=\"zhu\">整体来说，</span>因为<span class=\"zhu\">我们</span>中国人在中国</p>";
//		String _sssss = removeHtmlTag(sssss);
//		System.out.println("_sssss：" + _sssss);
//		String sss = _sssss.replaceAll("<{1}[^<>]*>{1}", "");
//		sss = StringUtil.replaceAllBlank(sss);
//
//		Map<String, Integer> _map = findWordPos(sss, search, 1, sss);
//		System.out.println("出现的次数：" + _map);
//
//		String result = hightlightWords(sssss, _map);
//		System.out.println("高亮结果：" + result);
		
//		String sssss = "<p>嬹䥕隖脜舻嬹<img src=\"%domain/a15358d5e85f4bb3bc1ae5e091783f03/zt/字图.png\" class=\"zitu\"></img>圉䣝</p>";
//		sssss = StringUtils.remove(sssss, "</img>");
//		String _sssss = removeZITUtag(sssss);
//		System.out.println("_sssss：" + _sssss);
		
		String fullstr = "<a class=\"page\" id=\"JY20220017-000215-L00215\" onclick=\"openReader('JY20220017-000215-L00215')\">P215</a><div class=\"biaoti2\">饒州府<span class=\"zhu\">四</span></div>";
		//String limitcontent = "饒州府四";
		
		getLimitChars(fullstr, 2);
		
	}
}