package cn.guliandigital.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 自动标点相关配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConfigurationProperties(prefix = "t2s")
@Data
public class T2sConfig
{

    /** 繁简转换地址 */
    private String t2sAddress;

    /** key */
    private String appKey;

    /** appSecrect */
    private String appSecrect;



	
	
}
