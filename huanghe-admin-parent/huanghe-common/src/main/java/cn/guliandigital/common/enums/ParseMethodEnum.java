package cn.guliandigital.common.enums;

public enum ParseMethodEnum {

    M("M","手动"),    
    S("S","推送")    
    ;


    private final String code;
    private final String msg;

    ParseMethodEnum(String code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }

    public String getCode()
    {
        return code;
    }

    public String getMsg()
    {
        return msg;
    }
    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(String code) {
        ParseMethodEnum[] imagetextTypeEnums = values();
        for (ParseMethodEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getCode().equals(code)) {
                return imagetextTypeEnum.getMsg();
            }
        }
        return null;
    }

    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static String getCode(String message) {
        ParseMethodEnum[] imagetextTypeEnums = values();
        for (ParseMethodEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getMsg().equals(message)) {
                return imagetextTypeEnum.getCode();
            }
        }
        return null;
    }
}
