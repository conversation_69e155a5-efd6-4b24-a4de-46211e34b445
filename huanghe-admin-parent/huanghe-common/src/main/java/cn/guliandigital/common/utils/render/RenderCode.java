package cn.guliandigital.common.utils.render;

import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Node;

import java.util.List;

public class RenderCode {

    /**
     * 清空标签
     *
     * @param content
     * @return
     * @throws Exception
     */
    public static String clearTag(String content) throws Exception {
        try {
            Document document = DocumentHelper.parseText("<root>" + content + "</root>");
            List<Node> selectNodes = document.selectNodes("//text()");
            StringBuilder sb = new StringBuilder();
            for (Node node : selectNodes) {
                String text = node.asXML();
                sb.append(text);
            }
            return sb.toString();
        } catch (DocumentException e) {
            throw e;
        }
    }
}
