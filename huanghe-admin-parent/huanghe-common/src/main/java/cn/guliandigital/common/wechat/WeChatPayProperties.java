package cn.guliandigital.common.wechat;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * @ClassNameWeixinLoginProperties
 * @DescriptionTODO
 * @Authorzyx @Date2020/10/2118:35
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat.pay")
public class WeChatPayProperties {

	// APPID
	private String appId;
	// mchid
	private String merchantId;
	// 商户API私钥
	private String privateKey;
	// 商户证书序列号
	private String merchantSerialNumber;
	// 商户APIv3密钥
	private String apiV3Key;
	// 支付通知地址
	private String payNotifyUrl;
	//退款通知
	private String refundNotifyUrl;

}