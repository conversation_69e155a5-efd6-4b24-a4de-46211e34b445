package cn.guliandigital.common.enums;

public enum ProStatusEnum {

    OFFLINE(0, "下架"),
    ONLINE(1, "上架");

    private final Integer code;
    private final String info;

    ProStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
