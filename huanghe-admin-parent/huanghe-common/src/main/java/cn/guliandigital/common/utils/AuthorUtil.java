package cn.guliandigital.common.utils;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;

public class AuthorUtil {

	/**
	 * 获取标准作者
	 * @param authorStr 作者字段
	 * @return
	 */
	public static String getFormalAuthor(String authorStr) {
		
		if(Strings.isNullOrEmpty(authorStr)) {
			return authorStr;
		}
		/*
		 *  F是责任方式
		 *	@是朝代
		 *	#是作者名称
		 *	C代表同一个责任方式出现了两次或两次以上
		 */
		//@清#吕燕昭F修@清#姚鼐F纂 = [清]吕燕昭修、[清]姚鼐纂
		StringBuffer buff = new StringBuffer();
		//String authorStr = "@清#吕燕昭F修@清#姚鼐F纂";
		if(StringUtil.contains(authorStr, "@")) {
			String[] authors = StringUtil.split(authorStr, "@");
			for(String author : authors) {
				//System.out.println("==>"+author);				
				author = StringUtil.replace(author, "F", " ");
				author = StringUtil.replace(author, "C", " ");
				if(StringUtil.indexOf(author, "#") != -1) {
					String[] authornames = StringUtil.split(author, "#");
					for(int i = 0; i < authornames.length ; i++) {
						if( i == 0) {
							buff.append("[" + authornames[i] +"]");
						}else {
							buff.append(authornames[i]+",");
						}
					}
				}else {
					buff.append(author);
				}
				//System.out.println("==>"+buff.toString());			
			}
		}else {
			authorStr = StringUtil.replace(authorStr, "F", " ");
			authorStr = StringUtil.replace(authorStr, "C", " ");
			if(StringUtil.indexOf(authorStr, "#") != -1) {
				String[] authornames = StringUtil.split(authorStr, "#");
				for(int i = 0; i < authornames.length ; i++) {					
					buff.append(authornames[i]+",");					
				}
			}else {
				buff.append(authorStr);
			}
		}
			
		if(buff.lastIndexOf(",") != -1) {
			buff = buff.delete(buff.length()-1, buff.length());
		}
		return buff.toString();
	}
	
	
	public static void main(String[] args) {
		
		String author = "#佚名F撰";
		//author = "@清#吕燕昭F修@清#姚鼐F纂";
		String sss = AuthorUtil.getFormalAuthor(author);
		System.out.println(sss);
	}
}
