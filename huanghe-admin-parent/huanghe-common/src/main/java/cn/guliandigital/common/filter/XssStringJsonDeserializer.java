package cn.guliandigital.common.filter;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import cn.hutool.core.util.EscapeUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class XssStringJsonDeserializer extends JsonDeserializer<String>{

	
	@Override
    public Class<String> handledType() {
        return String.class;
    }

    //对入参转义
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        String value = jsonParser.getText();
        //log.info("==>原始：{}",value);
        if (value != null) {
        	//value = SQLFilter.sqlInject(value);
        	String _val = XssUtil.cleanXSS(value);
        	//log.info("==>原始：{}  转换后：{}",value, _val);
            return _val;
        }

        return value;
    }
}
