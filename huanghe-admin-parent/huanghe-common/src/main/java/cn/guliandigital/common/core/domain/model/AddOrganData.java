package cn.guliandigital.common.core.domain.model;

import com.alibaba.fastjson.JSONArray;

import java.util.Map;

/**
 * @version V1.0
 * @Description:新增机构页面表单
 * @Auther: BOHANZHANG
 * @date 2020-9-16 17:01
 * @Title: AddOrganData.java
 * @Package: cn.guliandigital.common.core.domain.model
 */

public class AddOrganData {

    private static final long serialVersionUID = 1L;

    /** 机构编号 */
    private String orgCode;

    /** 机构名称 */
    private String orgName;

    /** 机构类型 */
    private String orgType;

    /** 地址 */
    private String orgAddress;

    /*ip地址列表*/
    private JSONArray orgIps;

    /** 登录名 */
    private String loginName;

    /** 姓名 */
    private String userName;

    /** 登录密码 */
    private String loginPass;

    /** 电话 */
    private String userTel;

    /** 邮箱 */
    private String userEmail;

    /** 用户类型  S-机构管理员  P-普通用户 */
    private String userType;

    /** 用户备注 */
    private String remark;

    private String orgStatus;

    public String getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(String orgStatus) {
        this.orgStatus = orgStatus;
    }

    public JSONArray getOrgIps() {
        return orgIps;
    }

    public void setOrgIps(JSONArray orgIps) {
        this.orgIps = orgIps;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgAddress() {
        return orgAddress;
    }

    public void setOrgAddress(String orgAddress) {
        this.orgAddress = orgAddress;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLoginPass() {
        return loginPass;
    }

    public void setLoginPass(String loginPass) {
        this.loginPass = loginPass;
    }

    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
