package cn.guliandigital.common.enums;

/**
 * 自动标点类型
 * 
 * <AUTHOR>
public enum PushStatusEnum
{
    PUSH_STATUS_1("1", "解析中"),
    PUSH_STATUS_2("2", "解析成功"),
    PUSH_STATUS_3("3", "解析失败");

    private final String code;
    private final String info;

    PushStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
