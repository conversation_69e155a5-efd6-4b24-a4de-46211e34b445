package cn.guliandigital.common.core.domain.model;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * @version V1.0
 * @Description:机构详情实体
 * @Auther: BOHANZHANG
 * @date 2020-10-11 15:49
 * @Title: OrganDetailData.java
 * @Package: cn.guliandigital.common.core.domain.model
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganDetailData {

    private static final long serialVersionUID = 1L;

    /** 机构名称 */
    private String orgName;

    /** 机构类型 */
    private String orgType;

    /*ip地址列表*/
    private JSONArray orgIps;

    /** 授权方式 */
    private String authMethod;

    /** 授权开始时间 */
    private String authStartTime;

    /** 授权结束时间 */
    private String authEndTime;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public JSONArray getOrgIps() {
        return orgIps;
    }

    public void setOrgIps(JSONArray orgIps) {
        this.orgIps = orgIps;
    }

    public String getAuthMethod() {
        return authMethod;
    }

    public void setAuthMethod(String authMethod) {
        this.authMethod = authMethod;
    }

    public String getAuthStartTime() {
        return authStartTime;
    }

    public void setAuthStartTime(String authStartTime) {
        this.authStartTime = authStartTime;
    }

    public String getAuthEndTime() {
        return authEndTime;
    }

    public void setAuthEndTime(String authEndTime) {
        this.authEndTime = authEndTime;
    }
}
