package cn.guliandigital.common.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.guliandigital.common.constant.EsConstants;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

@Slf4j
public class HighlightUtils {

	/**
	 * 
	 * @param hlmstr
	 * @return
	 */
	public static String formatField(String hlmstr) {
		int totalidx = hlmstr.length();
		int startidx = hlmstr.indexOf(EsConstants.ES_PRE_TAGS);
		int endidx = hlmstr.lastIndexOf(EsConstants.ES_POST_TAGS);
		
		int fsidx = 0;
		if(startidx > EsConstants.ES_HIGHLIGHT_TEXT) {
			fsidx = startidx - EsConstants.ES_HIGHLIGHT_TEXT;
		}else {
			fsidx = startidx;
		}
		int feidx = 0;
		if(totalidx - endidx > EsConstants.ES_HIGHLIGHT_TEXT) {
			feidx = totalidx - EsConstants.ES_HIGHLIGHT_TEXT;
		}else {
			feidx = totalidx;
		}
		hlmstr = hlmstr.substring(fsidx,feidx);
		return hlmstr;
	}
	
	/**
	 * 
	 * @param fieldName
	 * @return
	 */
	public static String parSetName(String fieldName) {
		if (null == fieldName || "".equals(fieldName)) {
			return null;
		}
		int startIndex = 0;
		if (fieldName.charAt(0) == '_')
			startIndex = 1;
		return "set" + fieldName.substring(startIndex, startIndex + 1).toUpperCase()
				+ fieldName.substring(startIndex + 1);
	}
	
	/**
	 * 格式化	
	 * @param hightStr
	 * @return
	 */
	public static String standardHightLight(String hightStr)  {
		hightStr = StringUtil.remove(hightStr, EsConstants.ES_PRE_TAGS + EsConstants.ES_POST_TAGS);
		StringBuilder result = new StringBuilder();
		
		String patstr = "([&#xa-z0-9]{0,3}"+EsConstants.ES_PRE_TAGS+"[x]?[0-9a-z]{1,6}"+EsConstants.ES_POST_TAGS+"[0-9a-z]{1,6}[;]?)";
		Pattern p = Pattern.compile(patstr);
	    Matcher m = p.matcher(hightStr);
	   
	    StringBuilder buff = new StringBuilder();
	   
	    int startIdx = 0;
	    int endIdx = 0;
	    while( m.find()){	      
	    	
	       String waiziStr = m.group();
	       log.info("匹配外字："+waiziStr);	       
	      
	       //截取除外字外的标签位置
	       endIdx = hightStr.indexOf(waiziStr,startIdx);
	       
	       String otherStr = hightStr.substring(startIdx,endIdx);		       
	       result.append(otherStr);		       
	       String waizire = waiziStr.replace(EsConstants.ES_PRE_TAGS, "").replace(EsConstants.ES_POST_TAGS, "");
	       result.append(EsConstants.ES_PRE_TAGS + waizire + EsConstants.ES_POST_TAGS);
		   		       
	       buff.append(otherStr);
	       buff.append(waiziStr);	       
	       //计算下一个的开始坐标
	       startIdx = endIdx + waiziStr.length();
	    }
	    String last_temp = hightStr.substring(buff.length(), hightStr.length());
	    result.append(last_temp);
	   		
	    return result.toString();
	}
	
	public static String redHightLight(String hightStr)  {
		hightStr = StringUtil.remove(hightStr, "<red>" + "</red>");
		StringBuilder result = new StringBuilder();
		
		String patstr = "([&#x]{0,3}"+"<red>"+"[x]?[0-9a-z]{1,6}"+"</red>"+"[0-9a-z]{1,6}[;]?)";
		Pattern p = Pattern.compile(patstr);
		Matcher m = p.matcher(hightStr);
		
		StringBuilder buff = new StringBuilder();
		
		int startIdx = 0;
		int endIdx = 0;
		while( m.find()){	      
			
			String waiziStr = m.group();
			log.info("匹配外字："+waiziStr);	       
			
			//截取除外字外的标签位置
			endIdx = hightStr.indexOf(waiziStr,startIdx);
			
			String otherStr = hightStr.substring(startIdx,endIdx);		       
			result.append(otherStr);		       
			String waizire = waiziStr.replace("<red>", "").replace("</red>", "");
			result.append("<red>" + waizire + "</red>");
			
			buff.append(otherStr);
			buff.append(waiziStr);	       
			//计算下一个的开始坐标
			startIdx = endIdx + waiziStr.length();
		}
		String last_temp = hightStr.substring(buff.length(), hightStr.length());
		result.append(last_temp);
		
		return result.toString();
	}

	/**
	 * 删除开头和结尾不符合规则的大字
	 * @param hightStr
	 * @return
	 */
	public static String formatBeginBigword(String hightStr) {
		String result = hightStr;
		//去掉最开始的
		String patstr = "^([#x]{0,2}[0-9a-z;]{6})";		
	    result = StringUtil.replacePattern(result, patstr, "");				
		
		//去掉最后的
		String patstrend = "([#x]{0,2}[0-9a-z;]{0,6})$";		
	    result = StringUtil.replacePattern(result, patstrend, "");
				
		return result;
	}
	public static String higNumber(String text, String keyword){
		String join="";
		Document doc = Jsoup.parse(text);
		Elements strong = doc.select("font");
		List<Integer> list=new ArrayList<>();
		for (Element element:strong){
			String el = element.text();
			if (StringUtils.isEmpty(el)){
				//标签里面没有内容，记录标签出现的次数
				int index = element.elementSiblingIndex();
				list.add(index);
			}
		}
		if (!list.isEmpty()){
			String sthong="<font>";
			String replace = text.replace(EsConstants.ES_PRE_TAGS + EsConstants.ES_POST_TAGS, sthong);
			String[] split = replace.split(sthong);
			for (int i=0;i<list.size();i++){
				if (i+1<split.length){
					String sp = split[i + 1];
					String key = sp.substring(0, 1);
					String keyed = sp.substring(1);
					key = EsConstants.ES_PRE_TAGS + key + EsConstants.ES_POST_TAGS;
					split[i + 1]=key+keyed;
				}
			}
			join = ArrayUtil.join(split, "");
		}else {
			join=text;
		}
		//如果是数字第一个高亮不上
		if (Character.isDigit(join.charAt(0))){
			//是数字是否需要高亮
			String[] keys = keyword.split("");
			String s1 = join.substring(0, 1);
			for(String key:keys){
				if (key.equals(s1)){
					s1=EsConstants.ES_PRE_TAGS +s1+ EsConstants.ES_POST_TAGS;
				}
			}
			join=s1+join.substring(1);
		}
		return join;
	}
	/**
	 * 高亮过滤掉其他标签
	 * @return
	 */
	public static  String higLable(String fieldFormat,String htmlstr,Object data) throws Exception {
		if (!fieldFormat.equals("fullText")&&!fieldFormat.equals("bookDesc")){
			Field field = data.getClass().getDeclaredField(fieldFormat);
			field.setAccessible(true);
			Object invoke = field.get(data);
			if (StringUtil.isNotNull(invoke)){
				String str = invoke.toString();
				Document doc = Jsoup.parse(htmlstr);
				Elements strong = doc.select("strong");
				List<String> list=new ArrayList<>();
				for (Element element:strong){
					list.add(element.text());
				}
				for (String higth:list){
					str= StringUtil.replace(str, higth, EsConstants.ES_PRE_TAGS + higth + EsConstants.ES_POST_TAGS);
				}
				htmlstr=str;
			}
		}else {
			int i = htmlstr.indexOf(EsConstants.ES_PRE_TAGS);
			if (i>5){
				htmlstr=htmlstr.replace(htmlstr.substring(0,i-5),"");
			}
		}
		return htmlstr;
	}


	public static String higQuickNumber(String text, String keyword){
		String join="";
		Document doc = Jsoup.parse(text);
		Elements strong = doc.select("em");
		List<Integer> list=new ArrayList<>();
		for (Element element:strong){
			String el = element.text();
			if (StringUtils.isEmpty(el)){
				//标签里面没有内容，记录标签出现的次数
				int index = element.elementSiblingIndex();
				list.add(index);
			}
		}
		if (!list.isEmpty()){
			String sthong="<em>";
			String replace = text.replace(EsConstants.NATIONAL_ES_PRE_TAGS + EsConstants.NATIONAL_ES_POST_TAGS, sthong);
			String[] split = replace.split(sthong);
			for (int i=0;i<list.size();i++){
				if (i+1<split.length){
					String sp = split[i + 1];
					String key = sp.substring(0, 1);
					String keyed = sp.substring(1);
					key = EsConstants.NATIONAL_ES_PRE_TAGS + key + EsConstants.NATIONAL_ES_POST_TAGS;
					split[i + 1]=key+keyed;
				}
			}
			join = ArrayUtil.join(split, "");
		}else {
			join=text;
		}
		//如果是数字第一个高亮不上
		if (Character.isDigit(join.charAt(0))){
			//是数字是否需要高亮
			String[] keys = keyword.split("");
			String s1 = join.substring(0, 1);
			for(String key:keys){
				if (key.equals(s1)){
					s1=EsConstants.NATIONAL_ES_PRE_TAGS +s1+ EsConstants.NATIONAL_ES_POST_TAGS;
				}
			}
			join=s1+join.substring(1);
		}
		return join;
	}

	public static  String higQuickLable(String fieldFormat,String htmlstr,Object data) throws Exception {
		if (!fieldFormat.equals("fullText")&&!fieldFormat.equals("bookDesc")){
			Field field = data.getClass().getDeclaredField(fieldFormat);
			field.setAccessible(true);
			Object invoke = field.get(data);
			if (StringUtil.isNotNull(invoke)){
				String str = invoke.toString();
				Document doc = Jsoup.parse(htmlstr);
				Elements strong = doc.select("em");
				List<String> list=new ArrayList<>();
				for (Element element:strong){
					list.add(element.text());
				}
				for (String higth:list){
					str= StringUtil.replace(str, higth, EsConstants.NATIONAL_ES_PRE_TAGS + higth + EsConstants.NATIONAL_ES_POST_TAGS);
				}
				htmlstr=str;
			}
		}else {
			int i = htmlstr.indexOf(EsConstants.NATIONAL_ES_PRE_TAGS);
			if (i>5){
				htmlstr=htmlstr.replace(htmlstr.substring(0,i-5),"");
			}
		}
		if(htmlstr.length()>200){
			htmlstr=htmlstr.substring(0,200);
		}
		return htmlstr;
	}

	public static void main(String[] args) {

		//String test="耳何言乎虚懸趙甲有田而開與錢乙錢乙復開與孫丙孫丙復開與李丁李丁復開與趙甲李丁有開趙甲<strong style='color:red' id='top'>不</strong><strong style='color:red' id='top'>&#x2d8e3;</strong>則併田與粮而没之矣然飛洒者損人以#x2d8e3;田而<strong style='color:red' id='top'>不</strong><strong style='color:red' id='top'>&#x2d8e3;</strong>粮&#";
		String test = "<strong style='color:red' id='top'>採</strong><strong style='color:red' id='top'>訪</strong><strong style='color:red' id='top'>余</strong><strong style='color:red' id='top'>支</strong><strong style='color:red' id='top'>湖</strong><strong style='color:red' id='top'>繪</strong><strong style='color:red' id='top'>圖</strong>&#<strong style='color:red' id='top'>x</strong>2<strong style='color:red' id='top'></strong>0<strong style='color:red' id='top'></strong>2<strong style='color:red' id='top'></strong>4<strong style='color:red' id='top'></strong>4;<strong style='color:red' id='top'>工</strong><strong style='color:red' id='top'>食</strong><strong style='color:red' id='top'>用</strong><strong style='color:red' id='top'>雜</strong><strong style='color:red' id='top'>項</strong><strong style='color:red' id='top'>洋</strong><strong style='color:red' id='top'>玖</strong><strong style='color:red' id='top'>拾</strong><strong style='color:red' id='top'>元</strong>陳德星堂";
		System.out.println(test);
		// test = StringUtil.remove(test, EsConstants.ES_PRE_TAGS + EsConstants.ES_POST_TAGS);
		String sss = HighlightUtils.formatBeginBigword(test);
		System.out.println("sssss="+sss);
		
		String result2 = HighlightUtils.standardHightLight(sss);
		System.out.println("ttttt="+result2);
	}
}
