package cn.guliandigital.common.core.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 前端用户登录对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value="用户登录实体",description="用户登录实体类")
public class WebLoginBody
{
    /**
     * 手机号
     */
    private String userTel;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 用户密码
     */
    private String password;
    //验证码
    private String code;

    //唯一码
    private String uuid;
    /**
     * 验证码
     */
    @ApiModelProperty(value = "手机验证码")
    private String phoneCode;

    /**
     * 图片验证码
     */
    @ApiModelProperty(value = "图片验证码")
    private String imageCode;
    /**
     * 登录类型  mc-手机验证码   mp-手机号密码
     */
    private String loginType;
    /**
     * 手机唯一码
     */
    @ApiModelProperty(value = "手机验证码uuid")
    private String phoneUuid;
    /**
     * 图片唯一码
     */
    @ApiModelProperty(value = "图片验证码uuid")
    private String imageUuid;



}
