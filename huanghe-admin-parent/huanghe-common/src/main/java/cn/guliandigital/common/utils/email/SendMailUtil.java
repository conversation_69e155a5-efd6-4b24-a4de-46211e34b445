//package cn.guliandigital.common.utils.email;
//
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
//import java.security.GeneralSecurityException;
//import java.util.Date;
//import java.util.Properties;
//
//import javax.mail.Authenticator;
//import javax.mail.Message;
//import javax.mail.MessagingException;
//import javax.mail.PasswordAuthentication;
//import javax.mail.Session;
//import javax.mail.Transport;
//import javax.mail.internet.InternetAddress;
//import javax.mail.internet.MimeBodyPart;
//import javax.mail.internet.MimeMessage;
//import javax.mail.internet.MimeMultipart;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import com.sun.mail.util.MailSSLSocketFactory;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//@Component
//public class SendMailUtil {
//	
//
//	@Value("${spring.mail.account}")
//    private String account;
//
//    @Value("${spring.mail.password}")
//    private String password;
//
//    @Value("${spring.mail.host}")
//    private String host;
//
//    @Value("${spring.mail.port}")
//    private String port;
//    
//    @Value("${spring.mail.protocol}")
//    private String protocol;
//    
//    @Value("${spring.mail.sender}")
//    private String sender;
//	
//	// 初始化参数
//	public Session initProperties() {
//		Properties properties = new Properties();
//		properties.setProperty("mail.transport.protocol", protocol);
//		properties.setProperty("mail.smtp.host", host);
//		properties.setProperty("mail.smtp.port", port);
//		// 使用smtp身份验证
//		properties.put("mail.smtp.auth", "true");
//		// 使用SSL,企业邮箱必需 start
//		// 开启安全协议
//		MailSSLSocketFactory mailSSLSocketFactory = null;
//		try {
//			mailSSLSocketFactory = new MailSSLSocketFactory();
//			mailSSLSocketFactory.setTrustAllHosts(true);
//		} catch (GeneralSecurityException e) {
//			e.printStackTrace();
//		}
//		properties.put("mail.smtp.enable", "true");
//		if(mailSSLSocketFactory != null) {
//			properties.put("mail.smtp.ssl.socketFactory", mailSSLSocketFactory);
//		}
//		properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
//		properties.put("mail.smtp.socketFactory.fallback", "false");
//		properties.put("mail.smtp.socketFactory.port", port);
//		Session session = Session.getDefaultInstance(properties, new Authenticator() {
//			@Override
//			protected PasswordAuthentication getPasswordAuthentication() {
//				return new PasswordAuthentication(account, password);
//			}
//		});
//		// 使用SSL,企业邮箱必需 end
//		// TODO 显示debug信息 正式环境注释掉
//		session.setDebug(true);
//		return session;
//	}
//
//	// @param subject 邮件主题
//	// @param content 邮件内容
//	// @param receiverList 接收者列表,多个接收者之间用","隔开
//	public Boolean send(String receiverList, String subject, String content) {
//		boolean resultMsg = false;
//		try {
//			Session session = initProperties();
//			MimeMessage mimeMessage = new MimeMessage(session);
//			// 获取配置文件参数
//			mimeMessage.setFrom(new InternetAddress(account, sender));// 发件人,可以设置发件人的别名
//			// 收件人,多人接收
//			InternetAddress[] internetAddressTo = new InternetAddress().parse(receiverList);
//			mimeMessage.setRecipients(Message.RecipientType.TO, internetAddressTo);
//			// 主题
//			mimeMessage.setSubject(subject);
//			// 时间
//			mimeMessage.setSentDate(new Date());
//			// 容器类 附件
//			MimeMultipart mimeMultipart = new MimeMultipart();
//			// 可以包装文本,图片,附件
//			MimeBodyPart bodyPart = new MimeBodyPart();
//			// 设置内容
//			bodyPart.setContent(content, "text/html; charset=UTF-8");
//			mimeMultipart.addBodyPart(bodyPart);
//			// 添加图片&附件
//			bodyPart = new MimeBodyPart();
//			// bodyPart.attachFile(fileSrc);
//			// mimeMultipart.addBodyPart(bodyPart);
//			mimeMessage.setContent(mimeMultipart, "text/html; charset=UTF-8");
//			mimeMessage.saveChanges();
//			Transport.send(mimeMessage);
//			resultMsg = true;
//		} catch (MessagingException e) {
//			log.error("",e);
//		} catch (UnsupportedEncodingException e) {
//			log.error("",e);
//		} catch (Exception e) {
//			log.error("",e);
//		}
//		return resultMsg;
//	}
//
//	public static void main(String[] args) throws GeneralSecurityException, UnsupportedEncodingException {
//
//		StringBuffer url = new StringBuffer();
//		url.append("http://locahost:8080");
//		url.append("noa");
//		url.append("/reportFindPassword/updatePassword.action?");
//		url.append("employeeCode=123456");
//		url.append("&employeeName=" + URLEncoder.encode("发送邮件测试", "UTF-8"));
//		url.append("&pemployeeCode=123456");
//		url.append("&pemployeeName=" + URLEncoder.encode("哈哈", "UTF-8"));
//		url.append("&email=*******@***.com");
//		url.append("&dateTime=20160418162538");
//		StringBuffer content = new StringBuffer();
//		content.append("<div><div style='margin-left:4%;'>");
//		content.append("<p style='color:red;'>");
//		content.append("啊啊啊(123456)您好：</p>");
//		content.append("<p style='text-indent: 2em;'>您正在使用密码找回功能，请点击下面的链接完成密码找回。</p>");
//		content.append("<p style='text-indent: 2em;display: block;word-break: break-all;'>");
//		content.append(
//				"链接地址：<a style='text-decoration: none;' href='" + url.toString() + "'>" + url.toString() + "</a></p>");
//		content.append("</div>");
//		content.append("<ul style='color: rgb(169, 169, 189);font-size: 18px;'>");
//		content.append("<li>为了保障您帐号的安全，该链接有效期为12小时。</li>");
//		content.append("<li>如果该链接无法点击，请直接复制以上网址到浏览器地址栏中访问。</li>");
//		content.append("<li>请您妥善保管，此邮件无需回复。</li>");
//		content.append("</ul>");
//		content.append("</div>");
//		//SendMailUtil.send("<EMAIL>,<EMAIL>,<EMAIL>", "重置密码", content.toString());
//		SendMailUtil util = new SendMailUtil();
//		//util.sendMail("<EMAIL>", "测试", content.toString());
//	}
//
//	/**
//	 * @param url               右键中的url链接
//	 * @param username          用户名
//	 * @param functionOperation 右键内容
//	 * @return
//	 * @throws UnsupportedEncodingException
//	 */
//	public String makeCodeMsg(String code,int validetime) {
//		// 您正在使用密码找回功能，请点击下面的链接完成密码找回。
//		StringBuffer content = new StringBuffer();
//		content.append("<div><div style='margin-left:4%;'>");
////		content.append("<p style='color:red;'>");
////		content.append("您好：</p>");
//		content.append("<p style='text-indent: 2em;'>您收到的"+code.length()+"位随机验证码是： " + code + "</p>");
//		content.append("<p style='text-indent: 2em;display: block;word-break: break-all;'>");
//		content.append("</div>");
//		content.append("<ul style='color: rgb(169, 169, 189);font-size: 18px;'>");
//		content.append("<li>该验证码"+validetime+"分钟内有效。</li>");
//		content.append("<li>请勿向任何人提供您收到的验证码。</li>");
//		content.append("<li>请您妥善保管，此邮件无需回复。</li>");
//		content.append("</ul>");
//		content.append("</div>");
//		return content.toString();
//	}
//
////	/**
////	 * @param url               右键中的url链接
////	 * @param username          用户名
////	 * @param functionOperation 右键内容
////	 * @return
////	 * @throws UnsupportedEncodingException
////	 */
////	public static String pwdNotice(String url, String username, String functionOperation)
////			throws UnsupportedEncodingException {
////		// 您正在使用密码找回功能，请点击下面的链接完成密码找回。
////		StringBuffer content = new StringBuffer();
////		content.append("<div><div style='margin-left:4%;'>");
////		content.append("<p style='color:red;'>");
////		content.append(username + "您好：</p>");
////		content.append("<p style='text-indent: 2em;'>" + functionOperation + "</p>");
////		content.append("<p style='text-indent: 2em;display: block;word-break: break-all;'>");
////		content.append(
////				"链接地址：<a style='text-decoration: none;' href='" + url.toString() + "'>" + url.toString() + "</a></p>");
////		content.append("</div>");
////		content.append("<ul style='color: rgb(169, 169, 189);font-size: 18px;'>");
////		content.append("<li>如果该链接无法点击，请直接复制以上网址到浏览器地址栏中访问。</li>");
////		content.append("<li>请您妥善保管，此邮件无需回复。</li>");
////		content.append("</ul>");
////		content.append("</div>");
////		return content.toString();
////	}
//}
