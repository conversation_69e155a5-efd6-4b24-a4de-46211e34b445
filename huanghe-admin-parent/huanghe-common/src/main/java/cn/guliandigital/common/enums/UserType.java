package cn.guliandigital.common.enums;

/**
 * 用户类型
 * 
 * <AUTHOR>
 */
public enum UserType
{
    S("S", "机构管理员"), P("P", "普通用户");

    private final String code;
    private final String info;

    UserType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
