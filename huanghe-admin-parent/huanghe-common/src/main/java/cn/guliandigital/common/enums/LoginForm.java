package cn.guliandigital.common.enums;

/**
 * 用户类型
 * 
 * <AUTHOR>
 */
public enum LoginForm
{
    U("U", "个人"), O("O", "机构");

    private final String code;
    private final String info;

    LoginForm(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
