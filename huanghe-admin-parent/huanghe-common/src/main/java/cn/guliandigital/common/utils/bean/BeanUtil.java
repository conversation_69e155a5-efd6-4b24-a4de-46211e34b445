package cn.guliandigital.common.utils.bean;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.DateUtil;

/**
 * Bean 工具类
 * 
 * <AUTHOR>
 */
public class BeanUtil extends org.springframework.beans.BeanUtils
{
	
	private static final Logger logger = LoggerFactory.getLogger(BeanUtil.class);
	
    /** Bean方法名中属性名开始的下标 */
    private static final int BEAN_METHOD_PROP_INDEX = 3;

    /** * 匹配getter方法的正则表达式 */
    private static final Pattern GET_PATTERN = Pattern.compile("get(\\p{javaUpperCase}\\w*)");

    /** * 匹配setter方法的正则表达式 */
    private static final Pattern SET_PATTERN = Pattern.compile("set(\\p{javaUpperCase}\\w*)");

    /**
     * Bean属性复制工具方法。
     * 
     * @param dest 目标对象
     * @param src 源对象
     */
    public static void copyBeanProp(Object dest, Object src) throws Exception
    {
        copyProperties(src, dest);       
    }

    /**
     * 获取对象的setter方法。
     * 
     * @param obj 对象
     * @return 对象的setter方法列表
     */
    public static List<Method> getSetterMethods(Object obj)
    {
        // setter方法列表
        List<Method> setterMethods = new ArrayList<Method>();

        // 获取所有方法
        Method[] methods = obj.getClass().getMethods();

        // 查找setter方法

        for (Method method : methods)
        {
            Matcher m = SET_PATTERN.matcher(method.getName());
            if (m.matches() && (method.getParameterTypes().length == 1))
            {
                setterMethods.add(method);
            }
        }
        // 返回setter方法列表
        return setterMethods;
    }

    /**
     * 获取对象的getter方法。
     * 
     * @param obj 对象
     * @return 对象的getter方法列表
     */

    public static List<Method> getGetterMethods(Object obj)
    {
        // getter方法列表
        List<Method> getterMethods = new ArrayList<Method>();
        // 获取所有方法
        Method[] methods = obj.getClass().getMethods();
        // 查找getter方法
        for (Method method : methods)
        {
            Matcher m = GET_PATTERN.matcher(method.getName());
            if (m.matches() && (method.getParameterTypes().length == 0))
            {
                getterMethods.add(method);
            }
        }
        // 返回getter方法列表
        return getterMethods;
    }

    /**
     * 检查Bean方法名中的属性名是否相等。<br>
     * 如getName()和setName()属性名一样，getName()和setAge()属性名不一样。
     * 
     * @param m1 方法名1
     * @param m2 方法名2
     * @return 属性名一样返回true，否则返回false
     */

    public static boolean isMethodPropEquals(String m1, String m2)
    {
        return m1.substring(BEAN_METHOD_PROP_INDEX).equals(m2.substring(BEAN_METHOD_PROP_INDEX));
    }
    
    
    /**
	 * 获取属性值
	 * @param bean
	 * @param fieldName
	 * @return
	 */
	public static Object getValue(Object bean, String fieldName) { 

	    Field[] fields = bean.getClass().getDeclaredFields();  
	    Field.setAccessible(fields, true);     
	    Object obj = null;  
	    for (int i = 0; i < fields.length; i++) {  
	        Field field = fields[i];  
	          
	        if (fieldName.equals(field.getName())) {  
	            try {  
	                obj = field.get(bean);  
	            } catch (IllegalArgumentException e) {  
	                //log.error(e.getMessage());  
	            } catch (IllegalAccessException e) {  
	                //log.error(e.getMessage());  
	            }  
	            break;
	        }  
	    }  
	    return obj;   
	}  
	
	
	/**********************************************************************************************
 	 * 存值
 	 * <AUTHOR> chensr
 	 * @since 创建时间 2016-06-11
 	 * @param object
 	 * @param cla
 	 * @param name
 	 * @param value
 	 * @return
	**********************************************************************************************/
	public static void setValue(Object object, Class cla, String name, String value) throws Exception{
		try {
			if(Strings.isNullOrEmpty(value) || Strings.isNullOrEmpty(value.trim()) || "null".equals(value))
				value = null;
			Field field = cla.getDeclaredField(name);
			String newStr = name.substring(0,1).toUpperCase()+name.replaceFirst("\\w","");
			Method ms = cla.getDeclaredMethod("set"+newStr, new Class[]{field.getType()});
			if(Integer.class.getName().equals(field.getType().getName()))
				ms.invoke(object, new Object[]{null == value ? Integer.valueOf("0") : Integer.valueOf(value)});
			else if(String.class.getName().equals(field.getType().getName()))
				ms.invoke(object, new Object[]{value});
			else if(Double.class.getName().equals(field.getType().getName()))
				ms.invoke(object, new Object[]{null == value ? value : Double.valueOf(value)});
			else if(BigDecimal.class.getName().equals(field.getType().getName()))
				ms.invoke(object, new Object[]{null == value ? value : new BigDecimal(Double.valueOf(value))});
			else if(Short.class.getName().equals(field.getType().getName()))
				ms.invoke(object, new Object[]{null == value ? Short.valueOf("0") : Short.valueOf(value)});
			else if (Date.class.getName().equals(field.getType().getName())){
				ms.invoke(object, new Object[]{null == value ? value : DateUtil.parseDate(value, "yyyy-MM-dd HH:ss:mm")});
			} else
				ms.invoke(object, new Object[]{value});
		} catch (Exception e) {
			logger.error("error,",e);
			throw e;
		}
	}
	
	/**
     * 配合beanutils设置对源对象中空的元素不进行复制，避免了目标对象某属性原本不为空，赋值后为空
     */
    public static String[] getNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for(java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null || Strings.isNullOrEmpty(srcValue.toString())) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
       
        return emptyNames.toArray(result);
    }
}
