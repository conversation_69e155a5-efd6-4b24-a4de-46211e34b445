package cn.guliandigital.common.enums;

/**
 * 转换模式
 */
public enum TransModeEnum {
    TRANS_MODE_1(1, "转简体"),
    TRANS_MODE_2(2, "转繁体"),
    TRANS_MODE_3(3, "筛简体"),
    TRANS_MODE_4(4, "筛繁体");

    private final Integer code;
    private final String info;

    TransModeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
