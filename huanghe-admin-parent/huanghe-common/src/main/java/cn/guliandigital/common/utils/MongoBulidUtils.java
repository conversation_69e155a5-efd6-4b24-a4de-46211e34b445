package cn.guliandigital.common.utils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import cn.guliandigital.common.utils.PatternUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MongoBulidUtils {

	public final static String LIKE_SUFFIX = "Like";

	public final static String NOT_LIKE_SUFFIX = "NotLike";

	public final static String IN_SUFFIX = "In";

	public final static String PREFIX_SUFFIX = "Prefix";

	public final static String GTE_SUFFIX = "Gte";

	public final static String LTE_SUFFIX = "Lte";
	
	public final static String NE_SUFFIX = "Ne";
	
	public final static String SPACE_SUFFIX = "";// 空串
	
	private final static String[] SUFFIXS = { 
			LIKE_SUFFIX, 
			NOT_LIKE_SUFFIX, 
			IN_SUFFIX, 
			PREFIX_SUFFIX, 
			GTE_SUFFIX, 
			LTE_SUFFIX,
			NE_SUFFIX,
			SPACE_SUFFIX};
	
	//public final Pattern pattern = Pattern.compile("^.*" + condition.get(entry.getValue()) + ".*$", Pattern.CASE_INSENSITIVE);
	
	public final static String REGEX_PREFIX = "^%s.*$";
	
	public final static String REGEX_LIKE = "^.*%s.*$";

	/**
	 * 处理对应实体类中以Like,NotLike,In,Prefix,Gte,Lte为后缀的条件,及无后缀的条件
	 * @param query
	 * @param condition
	 * @param clazz
	 */
	public static <T> void buildQuery(Query query, Map<String, Object> condition, Class<T> clazz) {
		for (String suffix : SUFFIXS) {
			// 查询参数名称列表
			Set<String> keySet = condition.keySet();
			// 获取字段名称列表
			List<String> fieldNames = FieldUtils.getAllFieldsList(clazz).stream().map(f -> f.getName())
					.collect(Collectors.toList());
			Map<String, String> extractKey = extractKey(fieldNames, keySet, suffix);
			if(extractKey.isEmpty()) {
				continue;
			}
			buildCriterias(query, condition, extractKey, suffix);
		}
	}
	
	/**
	 * 处理对应实体类中以Like,NotLike,In,Prefix,Gte,Lte为后缀的条件,及无后缀的条件
	 * @param query
	 * @param condition
	 * @param clazz
	 * @param check 是否打开字段类型校验
	 */
	public static <T> void buildQuery(Query query, Map<String, Object> condition, Class<T> clazz, boolean check) {
		if(!check) {
			buildQuery(query, condition, clazz);
		}
		Map<String, Class<?>> extractClass = extractClass(clazz);
		// 查询参数名称列表
		//Set<String> keySet = new HashSet<>(condition.keySet());
		Set<String> keySet = condition.keySet();// 在后续条件封装操作中会移除已经封装的键值对参数
		// 记录匹配的条件
		Set<String> matchs = new HashSet<>();
		for (String suffix : SUFFIXS) {
			// 获取字段名称列表
			List<String> fieldNames = FieldUtils.getAllFieldsList(clazz).stream().map(f -> f.getName())
					.collect(Collectors.toList());
			Map<String, String> extractKey = extractKey(fieldNames, keySet, suffix);
			Collection<String> values = extractKey.values();
			if(!values.isEmpty()) {
				matchs.addAll(values);
			}
			if(extractKey.isEmpty()) {
				continue;
			}
			for (Entry<String, String> entry : extractKey.entrySet()) {
				Class<?> clz = extractClass.get(entry.getKey());
				Object value = condition.get(entry.getValue());
				String valueClass = value.getClass().getName();
				String fieldClass = clz.getName();
				// 当前参数类型与字段类型不相同
				if(!valueClass.equals(fieldClass)) {
					// List中元素类型判断
					if(value instanceof Collection) {
						Collection<?> collection = (Collection<?>) value;
						if(collection.isEmpty()) {
							continue;
						}
						Optional<?> findFirst = collection.stream().findFirst();
						if(findFirst.isPresent()) {
							String itemClass = findFirst.get().getClass().getName();
							if(/*itemClass.indexOf(fieldClass) < 0 && */!itemClass.equals(fieldClass)) {
								// 条件值的元素类型与字段类型不同，可能导致Mongo查询结果错误！
								log.warn("Element class[{}] of collections in condition[{ {} : {} }] be different from field[{}] class[{}],"
										+ " May cause query result error in mongo!",
										itemClass,
										entry.getValue(),
										valueClass,
										entry.getKey(),
										fieldClass);
							}
						}
					}else if(value.getClass().isArray()) {// 数组判断
						if(ArrayUtils.isEmpty((Object[]) value)) {
							continue;
						}
						String itemClass = value.getClass().getComponentType().getName();
						if(/*itemClass.indexOf(fieldClass) < 0 && */!itemClass.equals(fieldClass)) {
							// 条件值的元素类型与字段类型不同，可能导致Mongo查询结果错误！
							log.warn("Element class[{}] of arrays in condition[{ {} : {} }] be different from field[{}] class[{}],"
									+ " May cause query result error in mongo!",
									itemClass,
									entry.getValue(),
									valueClass,
									entry.getKey(),
									fieldClass);
						}
					}else {
						String itemClass = value.getClass().getName();
						if(fieldClass.equals(String.class.getName())) {
							if(itemClass.equals(Integer.class.getName()) ||
									itemClass.equals(int.class.getName())) {
								// 处理参数为int/integer,属性为字符串情况下,处理参数为字符串
								log.debug("Attempt to convert condition[{ {} : {} }] value class [{}] to field class [{}]",
										entry.getValue(),
										value,
										valueClass,
										String.class.getName());
								condition.put(entry.getValue(), String.valueOf(value));
								break;
							}
						}
						// 条件值的类型与字段类型不同，可能导致Mongo查询结果错误！
						log.warn("Condition[{ {} : {} }] value class [{}] be different from field[{}] class [{}],"
								+ " May cause query result error in mongo!",
								entry.getValue(),
								value,
								valueClass,
								entry.getKey(),
								fieldClass);
					}
				}
			}
			buildCriterias(query, condition, extractKey, suffix);
		}
		keySet.removeAll(matchs);
		if(!keySet.isEmpty()) {
			// 未匹配的参数列表
			log.debug("Unmatched condition keys are {}", keySet);
		}
		
	}
	
	public static void buildCriterias(Query query, Map<String, Object> condition, Map<String, String> extractKey, String suffix) {
		for (Entry<String, String> entry : extractKey.entrySet()) {
			Object value = condition.get(entry.getValue());
			String key = entry.getKey();
			if (value != null) {
				buildCriteria(query, key, value, suffix);
			}
		}
	}
	
	/**
	 * 构建条件
	 * @param query
	 * @param key
	 * @param value
	 * @param suffix
	 */
	public static void buildCriteria(Query query, String key, Object value, String suffix) {
		Criteria criteria = null;
		switch (suffix) {
		case LIKE_SUFFIX:
			criteria = Criteria.where(key).is(buildPattern(REGEX_LIKE, value));
			break;
		case NOT_LIKE_SUFFIX:
			criteria = Criteria.where(key).not().regex(buildPattern(REGEX_LIKE, value));
			break;
		case IN_SUFFIX:
			// IN 查询
			if(value.getClass().isArray()) {
				Object[] array = (Object[]) value;
				criteria = Criteria.where(key).in(array);
			}else if(value instanceof Collection) {
				Collection<?> collection = (Collection<?>) value;
				criteria = Criteria.where(key).in(collection);
			}
			break;
		case PREFIX_SUFFIX:
			criteria = Criteria.where(key).is(buildPattern(REGEX_PREFIX, value));
			break;
		case GTE_SUFFIX:
			criteria = Criteria.where(key).gte(value);			
			break;
		case LTE_SUFFIX:
			criteria = Criteria.where(key).lte(value);
			break;
		case NE_SUFFIX:
			criteria = Criteria.where(key).ne(value);
			break;
		case SPACE_SUFFIX:
			criteria = Criteria.where(key).is(value);
			break;
		default:
			// 跳过
			// OTHER 非规则的手动处理OR在此添加 case xxx 处理
			break;
		}
		if(criteria == null) {
			return;
		}
		query.addCriteria(criteria);
	}
	
	/**
	 * 提取Key
	 * @param fields
	 * @param keySet
	 * @param suffix
	 * @return
	 */
	public static Map<String, String> extractKey(List<String> fields, Set<String> keySet, String suffix) {
		final String postfix = suffix;
		return fields.stream().filter(item -> keySet.contains(item + postfix)).collect(Collectors.toMap(item -> item, item -> item + postfix));
	}
	
	/**
	 * 提取字段类型
	 * @param clazz
	 * @param keySet
	 * @param suffix
	 * @return
	 */
	public static <T> Map<String, Class<?>> extractClass(Class<T> clazz) {
		List<Field> fields = FieldUtils.getAllFieldsList(clazz);
		return fields.stream().collect(Collectors.toMap(item -> item.getName(), item -> item.getType()));
	}
	
	/**
	 * 组合模板及值
	 * @param tpl 模板
	 * @param value 值
	 * @return
	 */
	public static Pattern buildPattern(String tpl, Object value) {
		// 转义
		value = PatternUtils.escapeValue(String.valueOf(value));
		String regex = String.format(tpl, value);
		return Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
	}
	
	/**
	 * 构建update对象
	 * @param update
	 * @param t
	 * @return true - 已设置更新参数; false - 未设置更新参数;
	 * @throws RuntimeException
	 */
	public static <T> void buildUpdate(Update update, T t) throws Exception {
		List<Field> allFieldsList = FieldUtils.getAllFieldsList(t.getClass());
		for (Field field : allFieldsList) {
			field.setAccessible(true);
			// 忽略空值 & Id注解 的属性
			if(null != field.get(t) && null == field.getAnnotation(Id.class) ) {
				update.set(field.getName(), field.get(t));
			}
		}
		if(MongoBulidUtils.isEmpty(update)) {
			log.warn("Updated data could not be found!");
		}
	}
	
	/**
	 * 构建update对象
	 * @param t
	 * @return null - 未设置更新参数
	 * @throws RuntimeException
	 */
	public static <T> void buildUpdate(T t) throws Exception {
		Update update = new Update();
		buildUpdate(update, t);
	}
	
	public static boolean isEmpty(Update update) {
		if(update == null) {
			return false;
		}
		return update.getUpdateObject().isEmpty();
	}
	
	/**
	 * 根据注解@Id 获取属性值
	 * @param t
	 * @return
	 * @throws Exception
	 */
	public static <T> Object getPrimaryKey(T t) throws Exception{
		Optional<Field> findFirst = FieldUtils.getAllFieldsList(t.getClass()).stream()
				.filter(item -> null != item.getAnnotation(Id.class)).findFirst();
		if(findFirst.isPresent()) {
			Field field = findFirst.get();
			field.setAccessible(true);
			return field.get(t);
		}
		return null;
	}
	
	public static void main(String[] args) throws Exception {
		//System.out.println("[Ljava.lang.Integer;".indexOf("java.lang.Integer"));
//		Map<String, Object> condition = ConditionUtils.condition()
//				//.put("id", "1")
//				//.prefix("resDir", "/1")
//				//.put("fileNamePrefix", 123)
//				//.in("fileType", "1", "2")
//				.put("extensionIn", new String[] {"3", "4"})
//				.in("versionNo", new ArrayList<Integer>(Arrays.asList(1, 2)))
//				//.in("versionNo", new ArrayList<String>(Arrays.asList("jpg", "png")))
//				.put("resHashCode", 1)
//				.like("saveFileName", "全文")
//				.getCondition();
//		Query query = new Query();
//		buildQuery(query, condition, DiskFileSystemFilesMongo.class, true);
//		//System.out.println(condition);
//		System.out.println(query);
//		//System.out.println(boolean.class.getName());
//		//System.out.println(ArrayUtils.EMPTY_OBJECT_ARRAY.getClass().getComponentType().getName());
//		String[] strs = new String[] {"3", "4"};
//		//System.out.println(strs.getClass().isArray());
//		
//		/*System.out.println(Integer.class.getClass().isPrimitive());
//		FileMongo fileMongo = new FileMongo();
//		//fileMongo.setId("123");
//		Update update = new Update();
//		buildUpdate(update, fileMongo);
//		System.out.println(isEmpty(update));
//		System.out.println(null == getPrimaryKey(fileMongo));*/
	}
}
