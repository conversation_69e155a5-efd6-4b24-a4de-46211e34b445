package cn.guliandigital.common.constant;

import java.util.LinkedHashMap;

import com.google.common.collect.Maps;

/**
 * @ClassName RedisConstants
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/14 11:43
 */
public class RedisConstants {
	
	
    public static String LOGIN_QQ_ExpiresIn="huanghe:login:qq:user:expiresIn:";
    public static String LOGIN_QQ_USER_TOKEN = "huanghe:login:qq:user:token:";
    //四部分类统计树
    //public static String REDIS_SUM_SIBU_PRIX = "huanghe:sum:sibu:id:";
    
    //资源分类统计树
    //public static String REDIS_SUM_CLASS_PRIX = "huanghe:sum:otherclass:id:";

    //用户登录token
    public static String LOGIN_USER_TOKEN = "huanghe:login:user:token:";
    public static String LOGIN_USER_INFO = "huanghe:login:user:id:token:";
    public static String TOKEN_EXPIRATION = "huanghe:token:expiration:";
    //是否添加到书架
    //public static String REDIS_BOOK_IN_SHELF = "huanghe:book:shelf:";

    public static String LOGIN_WX_USER_TOKEN = "huanghe:login:wx:user:token:";
    
    public static String REDIS_LOGINCODE_SMS = "huanghe:login:sms:code:";
    
    public static String REDIS_LOGINCODE_EMAIL = "huanghe:login:email:code:";

    //redis缓存登录错误次数
    public static String LOGIN_PASS_FAIL = "huanghe:login:pass:fail:";

    //redis记录项目工程阅读次数
    public static String HUANGHE_PROJECT_ID  = "huanghe:project:id:";

    //redis记录项目新闻阅读次数
    public static String HUANGHE_NEWS_ID  = "huanghe:news:id:";

    //redis记录章节复制字数
    public static String HUANGHE_MENUID_USER  = "huanghe:user:menu:id:";

    //3小时=3*60分钟
    public static int TOKEN_EXPIRE_MINUTES = 5*60;
    
    public static String HUANGHE_PAGEIMAGES_SPAN = "huanghe:book:pageimages:enc:span:";
    
    public static String HUANGHE_BOOK_PAGE_NO_LOG = "huanghe:book:pageno:log:";
    
    public static String HUANGHE_AUTO_COUNT = "huanghe:auto:count:";

    public static String HUANGHE_DISPLAY_MENU_BOOK_MAPPING = "huanghe:display:menu:book:mapping:";
    
    public static String HUANGHE_PICID_DISPLAY_MAPPING = "huanghe:picid:display:mapping:";
    public static String HUANGHE_MENUID_DISPLAY_MAPPING = "huanghe:menuid:display:mapping:";
    
    public static String HUANGHE_PICID_DISPLAY_IMAGES_MAPPING = "huanghe:picid:display:images:mapping:";
    //public static String HUANGHE_PICID_DISPLAY_IMAGES_MAPPING_NEXT = "huanghe:picid:display:images:mapping:next:";
    
    public static LinkedHashMap<String,String> HUANGHE_TEMP_MAPs = Maps.newLinkedHashMap();

    //用户锁定状态 1-锁10分钟，2-永远锁住
    public static String HUANGHE_LOGIN_LOCK = "huanghe:login:lock:";

    public static String HUANGHE_LOGIN_LOCK_COUNT = "huanghe:login:lock:count:";
    //错误次数
    public static String HUANGHE_PASS_ERROR_COUNT = "huanghe:pass:error:count:";
    //第一次错误开始日期
    public static String HUANGHE_PASS_ERROR_TIME = "huanghe:pass:error:time:";

}
