package cn.guliandigital.common.enums;

import cn.guliandigital.common.utils.StringUtil;

public enum WxampSelectStatus {

	OFF("0", "未选中"), //
	ON("1", "已选中"); //

	private final String code;
	private final String info;

	WxampSelectStatus(String code, String info) {
		this.code = code;
		this.info = info;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}
	
	/**
     * 根据key获取enum
     *
     * @param code
     * @return
     */
    public static WxampSelectStatus getEnum(String code) {
    	WxampSelectStatus[] wxampSelectStatusArray = values();
        for (WxampSelectStatus wxampSelectStatus : wxampSelectStatusArray) {
			if (StringUtil.equals(wxampSelectStatus.getCode(), code)) {
                return wxampSelectStatus;
            }
        }
        return null;
    }
}
