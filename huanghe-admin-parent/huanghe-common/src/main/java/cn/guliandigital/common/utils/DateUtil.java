/**
 * Copyright &copy; 2012-2013 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 */
package cn.guliandigital.common.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import com.google.common.base.Strings;





/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 * <AUTHOR>
 * @version 2013-3-15
 */
public class DateUtil extends org.apache.commons.lang3.time.DateUtils {
	
    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

	public static final String YYYY_MM_DD ="yyyy-MM-dd";
	public static final String YYYYMMDD = "yyyyMMdd";
	public static final String YYYYMM = "yyyyMM";
	public static final String YYYY_MM = "yyyy-MM";
	public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
	public static final String MM_DD_HH_MM = "MM-dd HH:mm";
	public static final String YYYYMMDDHHMM = "yyyyMMddHHmm";
	public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
	public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public static final String HHmmss = "HH:mm:ss";
	public static final String HHmm = "HH:mm";
	
	public static final int H24 = 24;
	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd）
	 */
	public static String getDate() {
		return getDate("yyyy-MM-dd");
	}
	
	 /**
     * 日期型字符串转化为日期 格式
     * @throws Exception 
     */
    public static Date parseDate(Object str) throws Exception {
        if (str == null) {
            return null;
        }
        
        return parseDate(str.toString(), parsePatterns);
       
    }

    
	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String getDate(String pattern) {
		return DateFormatUtils.format(new Date(), pattern);
	}
	
	/**
	 * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String formatDate(Date date, Object... pattern) {
		String formatDate = null;
		if (pattern != null && pattern.length > 0) {
			formatDate = DateFormatUtils.format(date, pattern[0].toString());
		} else {
			formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
		}
		return formatDate;
	}

	/**
	 * 得到当前时间字符串 格式（HH:mm:ss）
	 */
	public static String getTime() {
		return formatDate(new Date(), "HH:mm:ss");
	}

	/**
	 * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String getDateTime() {
		return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static String getYear() {
		return formatDate(new Date(), "yyyy");
	}

	/**
	 * 得到当前月份字符串 格式（MM）
	 */
	public static String getMonth() {
		return formatDate(new Date(), "MM");
	}

	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay() {
		return formatDate(new Date(), "dd");
	}

	/**
	 * 得到当前星期字符串 格式（E）星期几
	 */
	public static String getWeek() {
		return formatDate(new Date(), "E");
	}
	
	/**
	 * 日期型字符串转化为日期 格式（"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss" ）
	 */
	public static Date parseDate(String str) {
		try {
			return parseDate(str, parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获取过去的天数
	 * @param date
	 * @return
	 */
	public static long pastDays(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(24*60*60*1000);
	}
	
	
	public static String getCuurentDateStr() {
		return dateToStr(new Date(), YYYY_MM_DD);
	}
	
	public static Date getCuurentDate() {
		return new Date();
	}
	
	public static String getCuurentTime() {
		return dateToStr(new Date(), YYYY_MM_DD_HH_MM_SS);
	}
	
	public static String getCuurentHHMMStr() {
		return dateToStr(new Date(), YYYY_MM_DD_HH_MM);
	}
	
	public static Date strToDate(String dateStr) {
		return strToDate(dateStr,YYYY_MM_DD);
	}
	
	public static String format(Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
		return sdf.format(date);
	}
	
	public static String format(String dateStr) {
		if(Strings.isNullOrEmpty(dateStr)) {
			return dateStr;
		}
		return strToDate(dateStr).toString();
	}
	
	public static Date obj2Date(Object obj, String format) {
		if(obj == null) {
			return null;
		}
		return strToDate(obj.toString(), format);
	}
	
	public static Date str2date(String dateStr) {
		return strToDate(dateStr, YYYY_MM_DD);
	}

	/**
	 * 将字符串时间改成Date类型
	 */
	public static Date strToDate(String dateStr, String format) {
		
		Date date = null;

		try {
			if(!StringUtil.isEmpty(dateStr)){
				if(dateStr.indexOf("月")!=-1){
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
					date = simpleDateFormat.parse(dateStr);
				}else{
					SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
					date = simpleDateFormat.parse(dateStr);
				}
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return date;
	}

	/**
	 * 将Date时间转成字符串
	 */
	public static String dateToStr(Date date, String format) {
		if(date != null){
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			return simpleDateFormat.format(date);
		}
		return "";
	}
	
	public static String dateToStr(Date date) {
		if(date != null){
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
			return simpleDateFormat.format(date);
		}
		return "";
	}

	/**
	 * 获取2个字符日期的天数差
	 * 
	 * @return 天数差
	 * @throws Exception 
	 */
	public static long getDaysOfTowDiffDate(String p_startDate, String p_endDate) throws Exception {

		Date startDate = DateUtil.strToDate(p_startDate, DateUtil.YYYY_MM_DD);
		Date endDate = DateUtil.strToDate(p_endDate, DateUtil.YYYY_MM_DD);
		return getDaysOfTowDiffDate(startDate, endDate);
	}
	
	public static long getDaysOfTowDiffDate(Object p_startDate, Object p_endDate) throws Exception {
		return getDaysOfTowDiffDate(p_startDate.toString(), p_endDate.toString());
	}

	/**
	 * 获取2个日期的天数差
	 * 
	 * @param p_startDate
	 * @param p_endDate
	 * @return 天数差
	 */
	public static long getDaysOfTowDiffDate(Date startDate, Date endDate) {
		long startTime = startDate.getTime();
		long endTime = endDate.getTime();
		return (long) ((endTime - startTime) / (1000 * 60 * 60 * 24));
	}

	public static long getHoursOfTowDiffDate(Date startDate, Date endDate) {
		long startTime = startDate.getTime();
		long endTime = endDate.getTime();
		Long day = (endTime - startTime) / (1000 * 60 * 60 * 24);
		Long hour = (endTime - startTime - (day * 1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
		return hour;
	}


	/**
	 * 获取2个Date型日期的分钟数差值
	 * 
	 * @param p_startDate
	 * @param p_endDate
	 * @return 分钟数差值
	 */
	public static long getMinutesOfTowDiffDate(Date p_startDate, Date p_endDate) {
		long startTime = p_startDate.getTime();
		long endTime = p_endDate.getTime();
		return (long) ((endTime - startTime) / (1000 * 60));
	}

	
	/**
	 * @throws Exception 
	 * 
	* @Description: 给出日期添加一段时间后的日期
	* @param format
	* @param dateStr
	* @param plus
	* @return   
	* @Created:[2015年3月17日下午3:33:39] by chenshurui
	* @return String 
	* @throws
	 */
	public static String getPlusDays(String format, String dateStr, long plus) throws Exception {

		Date date = DateUtil.strToDate(dateStr,format);
		long time = date.getTime() + plus * 24 * 60 * 60 * 1000;

		return DateUtil.dateToStr(new Date(time), format);
	}

	/**
	 * 给出日期添加一段时间后的日期
	 */
	public static String getPlusDays(String format, Date date, long plus) {

		long time = date.getTime() + plus * 24 * 60 * 60 * 1000;

		return DateUtil.dateToStr(new Date(time), format);
	}
	
	

	/**
	 * 获取日期中的某数值。如获取月份
	 * 
	 * @param date
	 *            日期
	 * @param dateType
	 *            日期格式
	 * @return 数值
	 */
	private static int getInteger(Date date, int dateType) {
		int num = 0;
		Calendar calendar = Calendar.getInstance();
		if (date != null) {
			calendar.setTime(date);
			num = calendar.get(dateType);
		}
		return num;
	}

	/**
	 * 增加日期中某类型的某数值。如增加日期
	 * 
	 * @param date
	 *            日期
	 * @param dateType
	 *            类型
	 * @param amount
	 *            数值
	 * @return 计算后日期
	 */
	private static Date addInteger(Date date, int dateType, int amount) {
		Date myDate = null;
		if (date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(dateType, amount);
			myDate = calendar.getTime();
		}
		return myDate;
	}
	
	/** 
     * 增加日期的年份。失败返回null。 
     * @param date 日期 
     * @param yearAmount 增加数量。可为负数 
     * @return 增加年份后的日期 
     */  
    public static Date addYear(Date date, int yearAmount) {  
        return addInteger(date, Calendar.YEAR, yearAmount);  
    }  
    
    public static Date addYear(String date, int yearAmount) {  
        return addInteger(str2date(date), Calendar.YEAR, yearAmount);  
    } 
    
    /** 
     * 增加日期的月份。失败返回null。 
     * @param date 日期 
     * @param monthAmount 增加数量。可为负数 
     * @return 增加月份后的日期 
     */  
    public static Date addMonth(Date date, int monthAmount) {  
        return addInteger(date, Calendar.MONTH, monthAmount);  
    }  
    
    /** 
     * 增加日期的天数。失败返回null。 
     * @param date 日期 
     * @param dayAmount 增加数量。可为负数 
     * @return 增加天数后的日期 
     */  
    public static Date addDay(Date date, int dayAmount) {  
        return addInteger(date, Calendar.DATE, dayAmount);  
    }  
    
    public static Date addHour(Date date, int hourAmount) {  
        return addInteger(date, Calendar.HOUR, hourAmount);  
    }  
    
    public static String addOneDay(Date date) {
		return format(addDay(date, 1));
	}
    
    public static String addOneDay(String dateStr) {
		return addOneDay(str2date(dateStr));
	}
    
    
    public static String getLast2Year(){
    	Date date = addYear(new Date(),-2);
    	return format(date);
    }
    public static String getYear0101() {
    	Calendar calendar = Calendar.getInstance();
    	return calendar.get(Calendar.YEAR)+"-01-01";
    }
    
    public static String get(String format) {
    	return new SimpleDateFormat(format).format(new Date());
    }
    
    public static String getPastMinuteTime(int m) {
    	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
    	Calendar calendar = Calendar.getInstance();  
    	int now = calendar.get(Calendar.MINUTE);  
    	calendar.set(Calendar.MINUTE, now-m);  
    	return df.format(calendar.getTime());
    }
    //
    public static String getDateInURL(String date){
    	return date.replace(" ", "%20");
    }
    
    /**  
     * 判断当前时间是否是周末  
     * @param time  
     * @return  
     */
    public static boolean isWeekEnd(Date time) {  
         Calendar calendar = Calendar.getInstance();  
         calendar.setTime(time);  
         int week = calendar.get(Calendar.DAY_OF_WEEK);  
         if (Calendar.SUNDAY == week || Calendar.SATURDAY == week) {  
                return true;  
         } else {  
                return false;  
         }  
    }  
    
    public static boolean isDateEqual(String startDate, String endDate) {
    	return format(startDate).equals(format(endDate));
    }
    
    public static String miniteStr(Date startTime, Date endTime) {
    	return miniteStr(timeDiff(startTime, endTime));
    }
    
    public static String miniteStr(long minite) {
    	if(minite <= 0) {
    		return "0分钟";
    	}
    	StringBuilder sb = new StringBuilder();
    	if(minite/60 > 0) {
    		sb.append(minite/60).append("小时");
    	}
    	if(minite%60 > 0) {
    		sb.append(minite%60).append("分钟");
    	}
    	return sb.toString();
    }
    
    public static long timeDiff(String startTime, String endTime) {
    	if(endTime == null ) {
    		return timeDiff(strToDate(startTime, YYYY_MM_DD_HH_MM_SS), new Date());
    	}
    	return timeDiff(strToDate(startTime, YYYY_MM_DD_HH_MM_SS), strToDate(endTime, YYYY_MM_DD_HH_MM_SS));
    }
    
    public static long timeDiff(Date startTime, Date endTime) {
    	if(endTime == null) {
    		return (new Date().getTime() - startTime.getTime())/(60*1000);
    	}
    	return (endTime.getTime() - startTime.getTime())/(60*1000);
    }
    
    public static long timeDiffS(Date startTime, Date endTime) {
    	if(endTime == null) {
    		return (new Date().getTime() - startTime.getTime())/(60*1000);
    	}
    	return (endTime.getTime() - startTime.getTime())/(1000);
    }
    //计算两个日期之间天数差
    public static long dayDiff(Date startTime, Date endTime) {
    	if(endTime == null) {
    		return (new Date().getTime() - startTime.getTime())/(60*60*1000);
    	}
    	return (endTime.getTime() - startTime.getTime())/(24*60*60*1000);
    }
    
    public static String getYearFirst(){
    	//获取当前年的第一天
		 Calendar calendar=Calendar.getInstance();
		 int year = calendar.get(Calendar.YEAR);
		 Calendar calendar1=Calendar.getInstance();
		 calendar1.clear();
		 calendar1.set(Calendar.YEAR, year);
		 Date date = calendar1.getTime();
		 SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		 String format = dateFormat.format(date);
		 return format;
    }
    public static String getYearLast(){
    	 Calendar currCal=Calendar.getInstance();  
         int currentYear = currCal.get(Calendar.YEAR);
         Calendar calendar = Calendar.getInstance();
         calendar.clear();
         calendar.set(Calendar.YEAR, currentYear);
         calendar.roll(Calendar.DAY_OF_YEAR, -1);
         Date currYearLast = calendar.getTime();
         SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
         String sDate = f.format(currYearLast);
         return sDate;
    }
    public static  String getYearFirst(String time) throws Exception{
    	//获取莫一年的第一天
    	String formDate = formDate(time);
    	int year =Integer.parseInt(formDate);    	    	
		 Calendar calendar1=Calendar.getInstance();
		 calendar1.clear();
		 calendar1.set(Calendar.YEAR, year);
		 Date date = calendar1.getTime();
		 SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		 String format = dateFormat.format(date);
		 return format;
    }
    public static String getCurrentYear(){
    	  Calendar calendar = Calendar.getInstance();
    	  int i = calendar.get(Calendar.YEAR);
    	  String currentYear = String.valueOf(i);
    	  return currentYear;
    }
//    public static String getYearLast(String time){
//    	String formDate = formDate(time);
//    	int currentYear =Integer.valueOf(formDate);
//        Calendar calendar = Calendar.getInstance();
//        calendar.clear();
//        calendar.set(Calendar.YEAR, currentYear);
//        calendar.roll(Calendar.DAY_OF_YEAR, -1);
//        Date currYearLast = calendar.getTime();
//        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd");
//        String sDate = f.format(currYearLast);
//        return sDate;
//   }
    public static String formDate(String time) throws Exception{
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
    	SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy");
    	String format="";
    	Date date;
    	
		if(StringUtil.isEmpty(time)){
			return "";
		}else{
			date = sdf.parse(time);
			format = sdf2.format(date);
		}
    	
    	
    	return format;
    }
    
    /**
     * 比较是否在时间段内
     * @param today
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception 
     */
    public static boolean isBetweenDay(Date today,String startDate,String endDate) throws Exception{
    	
    	Date start_date = DateUtil.strToDate(startDate+" 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
		Date end_date = DateUtil.strToDate(endDate+" 23:59:59", DateUtil.YYYY_MM_DD_HH_MM_SS);
    	
		//Date today_date = DateUtils.strToDate(today, DateUtils.YYYY_MM_DD);
		
		if (today.after(start_date) && today.before(end_date)) {  
	        return true;  
	    }else if(today.compareTo(start_date)==0 || today.compareTo(end_date) == 0 ){  
	        return true;  
	    }else {  
	        return false;  
	    }  
    }
    
    public static boolean isBetweenHour(Date today,String startDate,String endDate) throws Exception{
    	
    	Date start_date = DateUtil.strToDate(startDate, DateUtil.YYYY_MM_DD_HH_MM);
		Date end_date = DateUtil.strToDate(endDate, DateUtil.YYYY_MM_DD_HH_MM);
    	
		if (today.after(start_date) && today.before(end_date)) {  
	        return true;  
	    }else if(today.compareTo(start_date)==0 || today.compareTo(end_date) == 0 ){  
	        return true;  
	    }else {  
	        return false;  
	    }  
    }
   
    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }
    
    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return formatDate(new Date(), format);
    }
    
    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }
    
    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60L;
        long nh = 1000 * 60 * 60L;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

	/**
	 * 计算指定时间跟当前时间的差值
	 * @param specifiedDate
	 * @return
	 */
	public static long timeChecker (Date specifiedDate){
		// 指定时间（示例）
		Date currentDate = new Date();

		// 计算时间差（毫秒）
		long diffMillis = Math.abs(currentDate.getTime() - specifiedDate.getTime());
		long diffMinutes = TimeUnit.MILLISECONDS.toMinutes(diffMillis);

		return diffMinutes;
	}
}
