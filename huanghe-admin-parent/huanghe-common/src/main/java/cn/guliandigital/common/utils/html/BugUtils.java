package cn.guliandigital.common.utils.html;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.StringUtil;

public class BugUtils {

	
	public static String removeCrlf(String str) {
		if(Strings.isNullOrEmpty(str)) {
			return str;
		}
		str = StringUtil.replace(str, "\t", "");
		str = StringUtil.replace(str, "\r", "");
		str = StringUtil.replace(str, "\n", "");
		str = StringUtil.replace(str, "\r\n", "");
		str = StringUtil.replace(str, " ", "");
		str = StringUtil.replace(str, "'", "");
		return str;
	}
}
