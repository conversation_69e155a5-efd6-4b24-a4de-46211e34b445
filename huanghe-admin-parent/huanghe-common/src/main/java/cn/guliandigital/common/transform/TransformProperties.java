package cn.guliandigital.common.transform;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName TransformProperties
 * @Description
 * <AUTHOR>
 * @Date 2020/12/1 18:35
 */
@Data
@Component
@ConfigurationProperties(prefix="transform")
public class TransformProperties {

    private String availableWordsUrl; // 查询可用字数接口

    private String raditionalAndSimplifiedUrl; // 繁简转换/筛查

    private String assessTransformUrl;//评价

    private String correctTransformUrl;//纠错

    private String downloadTextUrl;//文件下载

    private String uploadTextUrl;//上传文件


}