 package cn.guliandigital.common.config;

import org.springframework.stereotype.Component;

@Component
public class ThreadLocalConfig {

	// jdk建议将 ThreadLocal 定义为 private static ，这样就不会有弱引用，内存泄漏的问题了
    //用于计算时长
    private static ThreadLocal<Integer> pagenoThreadLocal = new ThreadLocal<>();

       
    //获取当前线程的存的变量
    public int getP() {
        return pagenoThreadLocal.get();
    }

    //设置当前线程的存的变量
    public void increment(int incr) {    	
        this.pagenoThreadLocal.set(getP()+incr);
    }
    
    public void setP(Integer p) {    	
        this.pagenoThreadLocal.set(p);
    }


    //移除当前线程的存的变量
    public void removeP() {
        this.pagenoThreadLocal.remove();
    }
}
