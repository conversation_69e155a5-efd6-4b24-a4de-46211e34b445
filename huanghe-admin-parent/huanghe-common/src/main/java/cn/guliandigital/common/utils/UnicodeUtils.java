package cn.guliandigital.common.utils;

import com.google.common.base.Strings;


public class UnicodeUtils {

	
	public static String transform(String content) {
		if(Strings.isNullOrEmpty(content)){
			return content;
		}
		content = content.replaceAll("&", "&amp;");
		content = content.replaceAll("<", "&lt;");
		content = content.replaceAll(" ", "&nbsp;");
		content = content.replaceAll(">", "&gt;");
		content = content.replaceAll("\"", "&quot;");
		//content = content.replaceAll("\n", "\n\r");
		return content;
	}
	
	
	public static  String transHtmlform(String content) {
		if(Strings.isNullOrEmpty(content)){
			return content;
		}
		content = content.replaceAll("&", "&amp;");
		content = content.replaceAll("<", "&lt;");
		content = content.replaceAll(">", "&gt;");
		content = content.replaceAll("&lt;INSTER STYLE=\"background:red;\"&gt;", "<INSTER STYLE=\"background:#FF00FF;\">");
		content = content.replaceAll("&lt;/INSTER&gt;", "</INSTER>");
		content = content.replaceAll("&lt;DEL STYLE=\"background:#FFE6E6;\"&gt;", "<DEL STYLE=\"background:	#FF8C00;\">");
		content = content.replaceAll("&lt;/DEL&gt;", "</DEL>");		
		content = content.replaceAll("&lt;insert_a", "<a");
		content = content.replaceAll("aaa&gt;", ">");
		content = content.replaceAll("&lt;/insert_a&gt;", "</a>");		
		content = content.replaceAll("&lt;insert_li&gt;", "<li>");	
		content = content.replaceAll("&lt;/insert_li&gt;", "</li>");
		/*content = content.replaceAll("&lt;div&gt;", "<div>");
		content = content.replaceAll("&lt;/div&gt;", "</div>");*/
		content = content.replaceAll("&lt;insert_span", "<span");
		content = content.replaceAll("aaa&gt;", ">");
		content = content.replaceAll("&lt;/insert_span&gt;", "</span>");
		content = content.replaceAll("&lt;delete_span", "<span");
		content = content.replaceAll("aaa&gt;", ">");
		content = content.replaceAll("&lt;/delete_span&gt;", "</span>");
		content = content.replaceAll("&lt;delete_li&gt;", "<li>");	
		content = content.replaceAll("&lt;/delete_li&gt;", "</li>");
		content = content.replaceAll("&lt;delete_a", "<a");
		content = content.replaceAll("aaa&gt;", ">");
		content = content.replaceAll("&lt;/delete_a&gt;", "</a>");	
		
		content = content.replaceAll("&lt;br&gt;", "<br />");
		content = content.replaceAll("\r\n", "<br />");
		return content;
	}
	
	
	/** 
     * 字符串转换unicode 
     */  
    public static String string2Unicode(String string) {  
       
        StringBuffer unicode = new StringBuffer();  
       
        for (int i = 0; i < string.length(); i++) {  
       
            // 取出每一个字符  
            char c = string.charAt(i);  
       
            // 转换为unicode  
            unicode.append("\\u" + Integer.toHexString(c));  
        }  
       
        return unicode.toString();  
    }  
    
      
    /** 
     * unicode 转字符串 
     */  
    public static String unicode2String(String unicode) {  
       
        StringBuffer string = new StringBuffer();  
       
        String[] hex = unicode.split("\\\\u");  
       
        for (int i = 1; i < hex.length; i++) {  
       
            // 转换出每一个代码点  
            int data = Integer.parseInt(hex[i], 16);  
       
            // 追加成string  
            string.append((char) data);  
        }  
       
        return string.toString();  
    }  
    
    public static void main(String[] args) {
    	String str = "退出";
    	System.out.println(string2Unicode(str));
    }
}
