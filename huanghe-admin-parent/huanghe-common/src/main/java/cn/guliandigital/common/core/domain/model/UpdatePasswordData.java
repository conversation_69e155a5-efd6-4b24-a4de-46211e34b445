package cn.guliandigital.common.core.domain.model;

import lombok.Data;

/**
 * @version V1.0
 * @Description:
 * @Auther: BOHANZHANG
 * @date 2020-10-28 18:28
 * @Title: UpdatePasswordData.java
 * @Package: cn.guliandigital.common.core.domain.model
 */

@Data
public class UpdatePasswordData {

    /**
     * 用户id
     */
    private String id;

    /**
     * 登录密码
     */
    private String loginPass;

    /**
     * 验证码id
     */
    private String uuid;

    /**
     * 验证码
     */
    private String code;

    /**
     * 手机号
     */
    public String userTel;

    /**
     * 邮箱
     */
    public String userEmail;
    
    /**
     * 旧密码
     */
    public String loginPassOld;
}
