package cn.guliandigital.common.core.domain.model;

/**
 * @version V1.0
 * @Description:记录意见反馈总数的实体
 * @Auther: BOHANZHANG
 * @date 2020-10-19 21:47
 * @Title: FeedBackCount.java
 * @Package: cn.guliandigital.common.core.domain.model
 */

public class FeedBackCount {

    private static final long serialVersionUID = 1L;

    //未处理条数
    private int notCount;
    //已处理条数
    private int doneCount;

    public int getNotCount() {
        return notCount;
    }

    public void setNotCount(int notCount) {
        this.notCount = notCount;
    }

    public int getDoneCount() {
        return doneCount;
    }

    public void setDoneCount(int doneCount) {
        this.doneCount = doneCount;
    }
}
