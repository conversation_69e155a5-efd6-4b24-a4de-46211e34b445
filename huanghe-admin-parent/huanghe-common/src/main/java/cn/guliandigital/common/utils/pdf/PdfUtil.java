package cn.guliandigital.common.utils.pdf;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.constant.EsConstants;
import cn.guliandigital.common.utils.StringUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileInputStream;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * word转pdf工具类
 */
@Slf4j
public class PdfUtil {

    /**
     * 读取PDF内容
     *
     * @param file
     * @return
     */
    public static List<PdfContent> getPdfContent(File file) {
        try {
            String extension = FilenameUtils.getExtension(file.getName());
            if (!extension.equalsIgnoreCase("pdf")) {
                return null;
            }
            //发送请求
            HttpPost httpPost = new HttpPost(HuangHeConfig.getPdfContentUrl());
            CloseableHttpClient client = HttpClients.createDefault();
            FileInputStream fileInputStream = new FileInputStream(file);
            HttpEntity entity = MultipartEntityBuilder.create()
                    .addBinaryBody("file", fileInputStream,
                            ContentType.APPLICATION_OCTET_STREAM, file.getName()).build();

            httpPost.setHeader(new BasicHeader("X-API-KEY", HuangHeConfig.getPdfApiKey()));
            httpPost.setEntity(entity);
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                String entityStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                JSONObject result = JSONObject.parseObject(entityStr);
                if (StringUtil.isNotNull(result.get("data"))) {
                    List<PdfContent> data = JSONObject.parseArray(result.get("data").toString(), PdfContent.class);
                    return data;
                }
            } else {
                throw new Exception(response.toString());
            }
        } catch (Exception e) {
            log.error("获取pdf内容错误===>", e);
            throw new RuntimeException("读取pdf文件失败");
        }
        return null;
    }

    /**
     * 读取PDF章节
     *
     * @param file
     * @return
     */
    public static List<PdfContent> getPdfMenu(File file) {
        try {
            String extension = FilenameUtils.getExtension(file.getName());
            if (!extension.equalsIgnoreCase("pdf")) {
                return null;
            }
            //发送请求
            HttpPost httpPost = new HttpPost(HuangHeConfig.getPdfMenuUrl());
            CloseableHttpClient client = HttpClients.createDefault();
            FileInputStream fileInputStream = new FileInputStream(file);
            HttpEntity entity = MultipartEntityBuilder.create()
                    .addBinaryBody("file", fileInputStream,
                            ContentType.APPLICATION_OCTET_STREAM, file.getName()).build();

            httpPost.setHeader(new BasicHeader("X-API-KEY", HuangHeConfig.getPdfApiKey()));
            httpPost.setEntity(entity);
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                String entityStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                JSONObject result = JSONObject.parseObject(entityStr);
                if (StringUtil.isNotNull(result.get("data"))) {
                    List<PdfContent> data = JSONObject.parseArray(result.get("data").toString(), PdfContent.class);
                    data.sort(Comparator.comparing(PdfContent::getPage));
                    return data;
                }
            } else {
                throw new Exception(response.toString());
            }
        } catch (Exception e) {
            log.error("获取pdf目录错误===>", e);
            throw new RuntimeException("读取pdf文件失败");
        }
        return null;
    }

    /**
     * 获取
     *
     * @param pdfFile
     * @return
     */

    public static String removePageNumber(String content) {
        if (StringUtil.isEmpty(content)){
            return "";
        }
        content = content.replace("　", "")
                .replace(" ", "")
                .replace("\n", "");
        String page = "\\d+";
        String[] removes={"—"+page +"—","第"+page+"页","－"+page+"－","-"+page+"-"};
        for (String reg:removes){
            Pattern pattern = Pattern.compile(reg);
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()){
                String group = matcher.group();
                if (content.startsWith(group)) {
                    content = content.substring(group.length());
                }
            }
        }
        content=content.replace("<fontstyle='color:red'id='top'>", EsConstants.ES_PRE_TAGS);
        return content;
    }
}


