package cn.guliandigital.sendmsg.service;

import com.alibaba.fastjson.JSONObject;
import cn.guliandigital.sendmsg.domain.SendMsgVo;

/**
 * @version V1.0
 * @Description:
 * @Auther: BOHANZHANG
 * @date 2020-10-23 10:51
 * @Title: SendMsgService.java
 * @Package: cn.guliandigital.sendmsg.service
 */

public interface SendMsgService {

    /**
     * 发送短信
     * @param to 手机号
     */
    public SendMsgVo sendSms(String to);


    public SendMsgVo sendSmsSubMail(String to);






    /**
     * 发送HTML邮件
     * @param to 收件人
     */
    //public SendMsgVo sendHtmlMail(String to);
}
