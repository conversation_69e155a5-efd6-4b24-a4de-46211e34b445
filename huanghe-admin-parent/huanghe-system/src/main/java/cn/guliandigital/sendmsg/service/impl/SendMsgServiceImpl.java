package cn.guliandigital.sendmsg.service.impl;

import static me.zhyd.oauth.utils.GlobalAuthUtils.getTimestamp;

import java.security.MessageDigest;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.google.common.collect.Maps;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.CaptchaUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.sendmsg.domain.SendMsgVo;
import cn.guliandigital.sendmsg.service.SendMsgService;
import lombok.extern.slf4j.Slf4j;

/**
 * @version V1.0
 * @Description:发送短信，邮件实现类
 * @Auther: BOHANZHANG
 * @date 2020-10-23 10:52
 * @Title: SendMsgServiceImpl.java
 * @Package: cn.guliandigital.sendmsg.service.impl
 */
@Slf4j
@Service
public class SendMsgServiceImpl implements SendMsgService {

    /**
     * Spring Boot 提供了一个发送邮件的简单抽象，使用的是下面这个接口，这里直接注入即可使用
     */
//    @Autowired
//    private JavaMailSender mailSender;

    @Autowired
    private RedisCache redisCache;
//
//    @Autowired
//    private SendMailUtil sendMailUtil;

    /**
     * 配置文件中我的qq邮箱
     */
//    @Value("${spring.mail.from}")
//    private String from;

    @Value("${aliyun.sendsms.regionId}")
    private String regionId;

    @Value("${aliyun.sendsms.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.sendsms.secret}")
    private String secret;

    @Value("${aliyun.sendsms.sysDomain}")
    private String sysDomain;

    @Value("${aliyun.sendsms.action}")
    private String action;

    @Value("${aliyun.sendsms.signName}")
    private String signName;

    @Value("${aliyun.sendsms.templateCode}")
    private String templateCode;

    @Value("${submail.appid}")
    private String appid;

    @Value("${submail.appkey}")
    private String appkey;

    @Value("${submail.projectId}")
    private String projectId;

    public static final String TIMESTAMP = "https://api-v4.mysubmail.com/service/timestamp";
    private static final String URL = "https://api-v4.mysubmail.com/sms/xsend";
    public static final String TYPE_MD5 = "md5";
    public static final String TYPE_SHA1 = "sha1";
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};


    private int invalidTime = 5;//2分钟

    /**
     * 发送短信
     *
     * @param to 手机号
     */
    @Override
    public SendMsgVo sendSms(String to) {
        SendMsgVo vo = new SendMsgVo();
        JSONObject content = new JSONObject();
        //获取6位随机验证码
        //String code = CaptchaUtil.getRandomCode();
        String code = CaptchaUtil.getRandomDigitCode();
        code = code.toLowerCase();
        content.put("code", code);
        //if(sendSmsPut(to,templateCode,content)){
        Map<String, Object> send_result = sendSmsPut(to, projectId, content);
        boolean success = (boolean) send_result.get("success");
        if (success) {
            // 将验证码与验证码对应的id存入到redis中，用来验证
            String uuid = IdUtils.simpleUUID();
            String redisKey = RedisConstants.REDIS_LOGINCODE_SMS + to + ":" + uuid;
            log.info("RedisKey=" + redisKey);
            //设置redis超时时间为3600s
            redisCache.setCacheObjectNormal(redisKey, code, invalidTime, TimeUnit.MINUTES);
            if (true) {
                vo.setUuid(uuid);
                return vo;
            }
        } else {
            String msg = (String) send_result.get("msg");
            vo.setErrorMsg(msg);
            return vo;
        }
        return null;
    }

    public Map<String, Object> sendSmsSubMailPut(String to, String project, JSONObject content) {

        Map<String, Object> result = Maps.newHashMap();
        result.put("success", false);
        result.put("msg", "发送失败");

        TreeMap<String, String> requestData = new TreeMap<String, String>();
        JSONObject vars = new JSONObject();

        String sign_type = "md5";
        String sign_version = "2";
        vars.put("code", content.getString("code"));
//        vars.put("name", "张三丰");

        //组合请求数据
        try {
            requestData.put("appid", appid);
            requestData.put("to", to);
            requestData.put("project", project);
            if (sign_type.equals(TYPE_MD5) || sign_type.equals(TYPE_SHA1)) {
                String timestamp = getTimestamp();
                requestData.put("timestamp", timestamp);
                requestData.put("sign_type", sign_type);
                requestData.put("sign_version", sign_version);
                String signStr = appid + appkey + formatRequest(requestData) + appid + appkey;
                //System.out.println(signStr);
                requestData.put("signature", encode(sign_type, signStr));
            } else {
                requestData.put("signature", appkey);
            }
            requestData.put("vars", vars.toJSONString());
            //post请求
            HttpPost httpPost = new HttpPost(URL);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-Type", "application/json");
            StringEntity entity = new StringEntity(JSONObject.toJSONString(requestData), "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build();
            HttpResponse response = closeableHttpClient.execute(httpPost);
            HttpEntity httpEntity = response.getEntity();
            log.info("++++++++++++++++++++++++++++++++++++++++++++++++++++++");
            log.info("=================closeableHttpClient" + closeableHttpClient);
            log.info("=================response" + response);
            log.info("=================httpEntity" + httpEntity);
            if (httpEntity != null) {
                String jsonStr = EntityUtils.toString(httpEntity, "UTF-8");
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                log.info("请求结果-------------"+jsonStr);
                if ("success".equals(jsonObject.getString("status"))) {
                    log.info("短信发送成功！");
                    result.put("success", true);
                    result.put("msg", "短信发送成功");
                    return result;

                } else if (StringUtil.equals("This phone number request frequency limit", jsonObject.getString("msg"))) {
                    result.put("success", false);
                    String message = jsonObject.getString("msg");
                    result.put("msg", "该手机号码请求超限!");
//                    //触发小时级流控Permits:5
//                    if (StringUtil.contains(message, ":10")) {
//                        result.put("msg", "同一账号一天只能获取十次");
//                    } else if (StringUtil.contains(message, ":5")) {
//                        result.put("msg", "同一账号一小时只能获取五次");
//                    } else if (StringUtil.contains(message, ":1")) {
//                        result.put("msg", "同一账号一分钟只能获取一次");
//                    }
                    return result;
                }

            }
        } catch (ClientProtocolException e) {
            log.error("短信发送异常！", e);
        } catch (Exception e) {
            log.error("短信发送异常！", e);
        }
        return result;
    }

    public static String encode(String algorithm, String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest messageDigest = MessageDigest.getInstance(algorithm);
            messageDigest.update(str.getBytes("UTF-8"));
            return getFormattedText(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        for (int j = 0; j < len; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }

    public static String formatRequest(Map<String, String> data) {
    	StringBuffer sb = new StringBuffer();
    	for(Entry<String, String> entry : data.entrySet()) {
    		String key = entry.getKey();
    		Object value = data.get(key);
            if (value instanceof String) {
                sb.append(key + "=" + value + "&");
            }
    	}
//        Set<String> keySet = data.keySet();
//        Iterator<String> it = keySet.iterator();
//        
//        while (it.hasNext()) {
//            String key = it.next();
//            
//        }
        if (sb.length() != 0) {
            log.info("sb.substring(0, sb.length() - 1) = " + sb.substring(0, sb.length() - 1));
            return sb.substring(0, sb.length() - 1);
        }
        return null;
    }


    @Override
    public SendMsgVo sendSmsSubMail(String to) {

        SendMsgVo vo = new SendMsgVo();
        JSONObject content = new JSONObject();
        //获取6位随机验证码
        //String code = CaptchaUtil.getRandomCode();
        String code = CaptchaUtil.getRandomDigitCode();
        code = code.toLowerCase();
        content.put("code", code);
        Map<String, Object> _result = sendSmsSubMailPut(to, projectId, content);
        boolean success = (boolean) _result.get("success");
        log.info("-----------"+success);
        if (success) {
            // 将验证码与验证码对应的id存入到redis中，用来验证
            String uuid = IdUtils.simpleUUID();
            String redisKey = RedisConstants.REDIS_LOGINCODE_SMS + to + ":" + uuid;
            log.info("RedisKey=" + redisKey);
            //设置redis超时时间为3600s
            redisCache.setCacheObjectNormal(redisKey, code, invalidTime, TimeUnit.MINUTES);
            if (true) {
                vo.setUuid(uuid);
                return vo;
            }
        } else {

            String msg = (String) _result.get("msg");
            vo.setErrorMsg(msg);
            return vo;
        }
        return null;
    }


    /**
     * http://web.c123.cn/admin/index.html#/api/hear/report
     * 150     citic2020
     *
     * @return
     */
//    public boolean sendSmsHttp(String to,String smscontent) {
//    	String url = "http://api.qirui.com:7891/mt?dc=8&un=2305520010&pw=6146a4b34f1cc33ce43c&sm=【国强图书】您的验证码是"+smscontent+"，有效期为2分钟。&da="+to+"&tf=3&rf=2&rd=0";
//
//    	JSONObject jsonResult = HttpClientUtils.httpGet(url);
//    	//{"success": true, "id":"152820588617618581"}
//    	boolean result = (boolean)jsonResult.get("success");
//    	return result;
//    }
    public Map<String, Object> sendSmsPut(String to, String templateCode, JSONObject content) {

        Map<String, Object> result = Maps.newHashMap();
        result.put("success", false);
        result.put("msg", "发送失败");

        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, secret);
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(sysDomain);
        request.setSysVersion("2017-05-25");
        request.setSysAction(action);
        request.putQueryParameter("PhoneNumbers", to);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", templateCode);
        request.putQueryParameter("TemplateParam", content.toJSONString());
        try {
            CommonResponse response = client.getCommonResponse(request);
            log.info("短信应答码：" + response.getData());
            JSONObject data = JSONObject.parseObject(response.getData());
            String code = data.getString("Code");
            if ("OK".equals(code)) {
                log.info("短信发送成功！");
                result.put("success", true);
                result.put("msg", "短信发送成功");
                return result;
            } else if (StringUtil.equals("isv.BUSINESS_LIMIT_CONTROL", code)) {
                result.put("success", false);
                String message = data.getString("Message");
                //触发小时级流控Permits:5
                if (StringUtil.contains(message, ":10")) {
                    result.put("msg", "同一账号一天只能获取十次");
                } else if (StringUtil.contains(message, ":5")) {
                    result.put("msg", "同一账号一小时只能获取五次");
                } else if (StringUtil.contains(message, ":1")) {
                    result.put("msg", "同一账号一分钟只能获取一次");
                }
                return result;
            } else {
                result.put("success", false);
                result.put("msg", data.getString("Message"));
                return result;
            }
        } catch (ServerException e) {
            log.error("短信发送异常！", e);
        } catch (ClientException e) {
            log.error("短信发送异常！", e);
        } catch (Exception e) {
            log.error("短信发送异常！", e);
        }
        return result;
    }

    /**
     * html邮件
     * @param to 收件人
     */
//    @Override
//    public SendMsgVo sendHtmlMail(String to) {
//        SendMsgVo vo = new SendMsgVo();
//        //获取6位随机验证码
//        String code = CaptchaUtil.getRandomCode();
//        code = code.toLowerCase();
//        String content = "您当前的验证码为："+code+"</br>验证码有效期为2分钟";
//        if(sendHtmlMailPut(to,"验证码",content)){
//            // 将验证码与验证码对应的id存入到redis中，用来验证
//            String uuid = IdUtils.simpleUUID();
//            String redisKey = RedisConstants.REDIS_LOGINCODE_EMAIL+ to+":"+uuid;
//            log.info("RedisKey="+redisKey);
//            //设置redis超时时间为60s
//            redisCache.setCacheObjectNormal(redisKey, code, invalidTime, TimeUnit.MINUTES);
//            //if(true){
//                vo.setUuid(uuid);
//                return vo;
//            //}
//        }
//        return null;
//    }

//    @Override
//    public SendMsgVo sendHtmlMail(String to) {
//        SendMsgVo vo = new SendMsgVo();
//        //获取6位随机验证码
//        //String code = CaptchaUtil.getRandomCode();
//        String code = CaptchaUtil.getRandomDigitCode();
//        code = code.toLowerCase();
//        String content = sendMailUtil.makeCodeMsg(code,invalidTime);
//        if(sendMailUtil.send(to,"验证码",content)){
//            // 将验证码与验证码对应的id存入到redis中，用来验证
//            String uuid = IdUtils.simpleUUID();
//            String redisKey = RedisConstants.REDIS_LOGINCODE_EMAIL+ to+":"+uuid;
//            log.info("RedisKey="+redisKey);
//            //设置redis超时时间为60s
//            redisCache.setCacheObjectNormal(redisKey, code, invalidTime, TimeUnit.MINUTES);
//            //if(true){
//                vo.setUuid(uuid);
//                return vo;
//            //}
//        }
//        return null;
//    }

    /**
     * html邮件
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
//    public boolean sendHtmlMailPut(String to, String subject, String content) {
//        //获取MimeMessage对象
//        MimeMessage message = mailSender.createMimeMessage();
//        MimeMessageHelper messageHelper;
//        try {
//            messageHelper = new MimeMessageHelper(message, true);
//            //邮件发送人
//            messageHelper.setFrom(from);
//            //邮件接收人
//            messageHelper.setTo(to);
//            //邮件主题
//            message.setSubject(subject);
//            //邮件内容，html格式
//            messageHelper.setText(content, true);
//            //发送
//            mailSender.send(message);
//            //日志信息
//            log.info("邮件已经发送。");
//            return true;
//        } catch (MessagingException e) {
//            log.error("发送邮件时发生异常！", e);
//        }
//        return false;
//    }


}
