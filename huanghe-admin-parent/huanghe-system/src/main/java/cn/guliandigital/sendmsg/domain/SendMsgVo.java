package cn.guliandigital.sendmsg.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version V1.0
 * @Description:发送短信验证码，邮箱验证码请求用实体
 * @Auther: BOHANZHANG
 * @date 2020-10-23 14:23
 * @Title: SendMsgVo.java
 * @Package: cn.guliandigital.sendmsg.domain
 */

@Data
@ApiModel(value="短信发送实体",description="短信发送实体")
public class SendMsgVo {

    /* 接收方的手机号/邮箱 */
    public String to;

    /* 验证码 */
    public String code;

    /* 验证码uuid */
    public String uuid;
    
    //错误信息
    public String errorMsg;
    
    //短信类型
    @ApiModelProperty(value = "验证码类型   0-注册  1-登录 2-其他")
    public String type;//验证码类型   0-注册  1-登录 2-其他
    /**
     * 图片验证码
     */
    @ApiModelProperty(value = "图片验证码")
    private String imageCode;
    /**
     * 图片唯一码
     */
    @ApiModelProperty(value = "图片验证码uuid")
    private String imageUuid;

}
