package cn.guliandigital.sendmsg.service.impl;

import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.sendmsg.domain.CheckImageCodeAndPhoneCodeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.sendmsg.domain.CheckCodeVo;
import cn.guliandigital.sendmsg.service.CheckCodeService;
import lombok.extern.slf4j.Slf4j;

/**
 * @version V1.0
 * @Description:
 * @Auther: BOHANZHANG
 * @date 2020-10-23 17:19
 * @Title: CheckCodeServiceImpl.java
 * @Package: cn.guliandigital.sendmsg.service.impl
 */
@Slf4j
@Service
public class CheckCodeServiceImpl implements CheckCodeService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public boolean checkCode(CheckCodeVo vo) {
        String redisKey = "";
        
        if(StringUtil.isNotEmpty(vo.getUserTel())){
            //redisKey = "sms_" + vo.getUuid();
            log.info("================code=========:"+vo.getCode());
        	String to = vo.getUserTel();
            redisKey = RedisConstants.REDIS_LOGINCODE_SMS+ to+":"+vo.getUuid();
        }else{
            //redisKey = "mail_" + vo.getUuid();
        	String to = vo.getUserEmail();
            redisKey = RedisConstants.REDIS_LOGINCODE_EMAIL+ to+":"+vo.getUuid();
        }
        log.info("redisKey={}",redisKey);
        String code = redisCache.getCacheObject(redisKey);
        log.info("code"+code);

        if(code != null && code.equals(vo.getCode())){
            return true;
        }
        return false;
    }
    @Override
    public boolean checkPhoneCodeAndImageCode(CheckImageCodeAndPhoneCodeVo vo) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY_WAPI + vo.getImageUuid();
        String imageCode = redisCache.getCacheObject(verifyKey);
        log.info("================图片验证码code=========:"+vo.getImageCode());
        String redisKey = "";
        if(StringUtil.isNotEmpty(vo.getUserTel())){
            //redisKey = "sms_" + vo.getUuid();
            log.info("================手机验证码code=========:"+vo.getPhoneCode());
            String to = vo.getUserTel();
            redisKey = RedisConstants.REDIS_LOGINCODE_SMS+ to+":"+vo.getPhoneUuid();
        }else if(StringUtil.isNotBlank(vo.getUserEmail())){
            //redisKey = "mail_" + vo.getUuid();
            String to = vo.getUserEmail();
            redisKey = RedisConstants.REDIS_LOGINCODE_EMAIL+ to+":"+vo.getPhoneUuid();
        }
        log.info("redisKey={}",redisKey);
        String phoneCode = redisCache.getCacheObject(redisKey);
        log.info("获取的手机验证码code"+phoneCode);

        if(imageCode != null && imageCode.equals(vo.getImageCode())&&phoneCode != null && phoneCode.equals(vo.getPhoneCode())){
            return true;
        }
        return false;
    }
}
