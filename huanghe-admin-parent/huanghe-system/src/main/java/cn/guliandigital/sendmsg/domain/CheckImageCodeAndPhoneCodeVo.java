package cn.guliandigital.sendmsg.domain;

import lombok.Data;

/**
 * @version V1.0
 * @Description:验证验证码请求用实体
 * @Auther: BOHANZHANG
 * @date 2020-10-23 14:23
 * @Title: SendMsgVo.java
 * @Package: cn.guliandigital.sendmsg.domain
 */

@Data
public class CheckImageCodeAndPhoneCodeVo {

    /* 手机号 */
    public String userTel;

    /* 邮箱 */
    public String userEmail;

    /* 验证码 */
    public String code;

    /* 验证码uuid */
    public String uuid;
    /**
     * 图片验证码
     */
    private String imageCode;
    /**
     * 验证码
     */
    public String phoneCode;
    /**
     * 图片验证码uuid
     */
    public String imageUuid;
    /**
     * 手机验证码uuid
     */
    public String phoneUuid;

    /* 用户id */
    private String userId;

}
