package cn.guliandigital.pvlog.domain;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 操作日志记录对象 t_user_visit_log
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TUserVisitLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志主键 */
    private Long id;
    private Integer day;

    /** 模块标题 */
    @Excel(name = "模块标题")
    private String title;
    private Long visitCount;
    
    /** 业务类型（0其它 1新增 2修改 3删除） */
    @Excel(name = "业务类型", readConverterExp = "0-访问,1-检索,2-登录")
    private Integer businessType;
    private String  timeForVisits;

    /** 操作人员 */
    @Excel(name = "操作人员")
    private String operName;

    /** 请求URL */
    @Excel(name = "请求URL")
    private String operUrl;

    /** 主机地址 */
    @Excel(name = "主机地址")
    private String operIp;

    /** 操作地点 */
    @Excel(name = "操作地点")
    private String operLocation;

    /** 请求参数 */
    @Excel(name = "请求参数")
    private String operParam;

    /** 返回参数 */
    @Excel(name = "返回参数")
    private String jsonResult;

    /** 操作状态（0正常 1异常） */
    @Excel(name = "操作状态", readConverterExp = "0=正常,1=异常")
    private Integer status;

    /** 错误消息 */
    @Excel(name = "错误消息")
    private String errorMsg;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operTime;
    
    private Integer count;

     /** 机构ID */
    private String orgId;
    
    /** 来源 P-pc A-app **/
    private String userSource;

    /** 登录形式 U-个人 O-机构 **/
    private String loginForm;
   
}
