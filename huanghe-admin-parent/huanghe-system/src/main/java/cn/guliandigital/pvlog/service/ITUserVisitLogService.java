package cn.guliandigital.pvlog.service;

import java.util.List;
import java.util.Map;

import cn.guliandigital.pvlog.domain.TUserVisitLog;

/**
 * 操作日志记录Service接口
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
public interface ITUserVisitLogService 
{
    /**
     * 查询操作日志记录
     * 
     * @param id 操作日志记录ID
     * @return 操作日志记录
     */
        TUserVisitLog selectTUserVisitLogById(Long id);

    /**
     * 查询操作日志记录列表
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 操作日志记录集合
     */
    List<TUserVisitLog> selectTUserVisitLogList(TUserVisitLog tUserVisitLog);

    /**
     * 新增操作日志记录
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    int insertTUserVisitLog(TUserVisitLog tUserVisitLog);

    /**
     * 修改操作日志记录
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    int updateTUserVisitLog(TUserVisitLog tUserVisitLog);

    /**
     * 批量删除操作日志记录
     * 
     * @param ids 需要删除的操作日志记录ID
     * @return 结果
     */
    int deleteTUserVisitLogByIds(Long[] ids);

    /**
     * 删除操作日志记录信息
     * 
     * @param id 操作日志记录ID
     * @return 结果
     */
    int deleteTUserVisitLogById(Long id);

    //独立访客
    List<Map<String, Object>>  selectVisits(TUserVisitLog tUserVisitLog);



    List<Map<String, Object>>  aloneVisitsCounts(TUserVisitLog tUserVisitLog);



    List<Map<String, Object>>   selectVisits1(TUserVisitLog tUserVisitLog);

    List<Map<String, Object>> aloneVisitsCounts1(TUserVisitLog tUserVisitLog);
}
