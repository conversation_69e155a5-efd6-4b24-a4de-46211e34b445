package cn.guliandigital.pvlog.service.impl;

import cn.guliandigital.pvlog.domain.TUserVisitLog;
import cn.guliandigital.pvlog.mapper.TUserVisitLogMapper;
import cn.guliandigital.pvlog.service.ITUserVisitLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 操作日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-11-10
 */
@Service
public class TUserVisitLogServiceImpl implements ITUserVisitLogService {
    @Autowired
    private TUserVisitLogMapper tUserVisitLogMapper;

    /**
     * 查询操作日志记录
     *
     * @param id 操作日志记录ID
     * @return 操作日志记录
     */
    @Override
    public TUserVisitLog selectTUserVisitLogById(Long id) {
        return tUserVisitLogMapper.selectTUserVisitLogById(id);
    }

    /**
     * 查询操作日志记录列表
     *
     * @param tUserVisitLog 操作日志记录
     * @return 操作日志记录
     */
    @Override
    public List<TUserVisitLog> selectTUserVisitLogList(TUserVisitLog tUserVisitLog) {
        return tUserVisitLogMapper.selectTUserVisitLogList(tUserVisitLog);
    }

    /**
     * 新增操作日志记录
     *
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    @Override
    public int insertTUserVisitLog(TUserVisitLog tUserVisitLog) {
        return tUserVisitLogMapper.insertTUserVisitLog(tUserVisitLog);
    }

    /**
     * 修改操作日志记录
     *
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    @Override
    public int updateTUserVisitLog(TUserVisitLog tUserVisitLog) {
        return tUserVisitLogMapper.updateTUserVisitLog(tUserVisitLog);
    }

    /**
     * 批量删除操作日志记录
     *
     * @param ids 需要删除的操作日志记录ID
     * @return 结果
     */
    @Override
    public int deleteTUserVisitLogByIds(Long[] ids) {
        return tUserVisitLogMapper.deleteTUserVisitLogByIds(ids);
    }

    /**
     * 删除操作日志记录信息
     *
     * @param id 操作日志记录ID
     * @return 结果
     */
    @Override
    public int deleteTUserVisitLogById(Long id) {
        return tUserVisitLogMapper.deleteTUserVisitLogById(id);
    }

    @Override
    public List<Map<String, Object>> selectVisits(TUserVisitLog tUserVisitLog) {

        List<TUserVisitLog> selectList = tUserVisitLogMapper.selectVisits(tUserVisitLog);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (TUserVisitLog log : selectList) {
            log.setTimeForVisits(log.getTimeForVisits()+":00");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("count", log.getCount());
            map.put("timeForVisits", log.getTimeForVisits());
            list.add(map);
        }

        List<Map<String, Object>> result = new ArrayList<>();
        boolean dbDateExist = false;
        List<String> dateList = Arrays.<String>asList("0:00", "1:00", "2:00", "3:00", "4:00", "5:00", "6:00", "7:00", "8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00","21:00","22:00","23:00","24:00");

        for (String date : dateList) {
            //table为数据库查询出来的对象列表，结构为List<Map<String, Object>>
            for (Map<String, Object> row : list) {
                if (row.get("timeForVisits").equals(date)) {
                    //集合已包含该日期
                    dbDateExist = true;
                    result.add(row);
                    break;
                }
            }
            if (!dbDateExist) {
                Map<String, Object> temp = new HashMap<>(2);
                temp.put("timeForVisits", date);
                temp.put("count", 0);
                result.add(temp);
            }
            dbDateExist = false;
        }

        //状态修改为不存在
        return result;

    }

    @Override
    public List<Map<String, Object>> aloneVisitsCounts(TUserVisitLog tUserVisitLog) {

        List<TUserVisitLog> selectList = tUserVisitLogMapper.aloneVisitsCounts(tUserVisitLog);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (TUserVisitLog log : selectList) {
            log.setTimeForVisits(log.getTimeForVisits()+":00");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("visitCount", log.getVisitCount());
            map.put("timeForVisits", log.getTimeForVisits());
            list.add(map);
        }

        List<Map<String, Object>> result = new ArrayList<>();
        boolean dbDateExist = false;
        List<String> dateList = Arrays.<String>asList("0:00", "1:00", "2:00", "3:00", "4:00", "5:00", "6:00", "7:00", "8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00","21:00","22:00","23:00","24:00");

        for (String date : dateList) {
            //table为数据库查询出来的对象列表，结构为List<Map<String, Object>>
            for (Map<String, Object> row : list) {
                if (row.get("timeForVisits").equals(date)) {
                    //集合已包含该日期
                    dbDateExist = true;
                    result.add(row);
                    break;
                }
            }
            if (!dbDateExist) {
                Map<String, Object> temp = new HashMap<>(2);
                temp.put("timeForVisits", date);
                temp.put("visitCount", 0);
                result.add(temp);
            }
            dbDateExist = false;
        }

        //状态修改为不存在
        return result;
    }


    @Override
    public List<Map<String, Object>> selectVisits1(TUserVisitLog tUserVisitLog) {
        List<TUserVisitLog> selectList = tUserVisitLogMapper.testVisits1(tUserVisitLog);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (TUserVisitLog log : selectList) {

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("count", log.getCount());
            map.put("timeForVisits", log.getTimeForVisits());
            list.add(map);
        }
        LocalDateTime startDate = null;
        if (tUserVisitLog.getDay() != 0) {
            startDate = LocalDateTime.now().minusDays((long)(tUserVisitLog.getDay() - 1));
        } else {
            startDate = LocalDateTime.now().minusDays((long)tUserVisitLog.getDay());
        }

        LocalDateTime endDate = LocalDateTime.now();
        //3.补全为空的日期
        //补全后的结果
        List<Map<String, Object>> result = new ArrayList<>();
        boolean dbDateExist = false;
        List<String> dateList = completionDate(startDate, endDate);

        for (String date : dateList) {
            //table为数据库查询出来的对象列表，结构为List<Map<String, Object>>
            for (Map<String, Object> row : list) {
                if (row.get("timeForVisits").equals(date)) {
                    //集合已包含该日期
                    dbDateExist = true;
                    result.add(row);
                    break;
                }
            }
            if (!dbDateExist) {
                Map<String, Object> temp = new HashMap<>(2);
                temp.put("timeForVisits", date);
                temp.put("count", 0);
                result.add(temp);
            }
            dbDateExist = false;
        }

        //状态修改为不存在
        return result;
    }

    @Override
    public List<Map<String, Object>> aloneVisitsCounts1(TUserVisitLog tUserVisitLog) {
        List<TUserVisitLog> selectList = tUserVisitLogMapper.alone(tUserVisitLog);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (TUserVisitLog log : selectList) {

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("visitCount", log.getVisitCount());
            map.put("timeForVisits", log.getTimeForVisits());
            list.add(map);
        }
        LocalDateTime startDate = null;
        if (tUserVisitLog.getDay() != 0) {
            startDate = LocalDateTime.now().minusDays((long)(tUserVisitLog.getDay() - 1));
        } else {
            startDate = LocalDateTime.now().minusDays((long)tUserVisitLog.getDay());
        }

        LocalDateTime endDate = LocalDateTime.now();
        //3.补全为空的日期
        //补全后的结果
        List<Map<String, Object>> result = new ArrayList<>();
        boolean dbDateExist = false;
        List<String> dateList = completionDate(startDate, endDate);

        for (String date : dateList) {
            //table为数据库查询出来的对象列表，结构为List<Map<String, Object>>
            for (Map<String, Object> row : list) {
                if (row.get("timeForVisits").equals(date)) {
                    //集合已包含该日期
                    dbDateExist = true;
                    result.add(row);
                    break;
                }
            }
            if (!dbDateExist) {
                Map<String, Object> temp = new HashMap<>(2);
                temp.put("timeForVisits", date);
                temp.put("visitCount", 0);
                result.add(temp);
            }
            dbDateExist = false;
        }

        //状态修改为不存在
        return result;
    }

    public static List<String> completionDate(
            LocalDateTime startDate,
            LocalDateTime endDate) {
        //日期格式化
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        List<String> dateList = new ArrayList<>();
        //遍历给定的日期期间的每一天
        for (int i = 0; !Duration.between(startDate.plusDays(i), endDate).isNegative(); i++) {
            //添加日期
            dateList.add(startDate.plusDays(i).format(formatter));
        }
        return dateList;


    }



}
