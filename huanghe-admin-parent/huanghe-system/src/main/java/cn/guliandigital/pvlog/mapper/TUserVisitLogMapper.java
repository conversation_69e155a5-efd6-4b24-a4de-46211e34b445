package cn.guliandigital.pvlog.mapper;

import java.util.List;
import cn.guliandigital.pvlog.domain.TUserVisitLog;

/**
 * 操作日志记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
public interface TUserVisitLogMapper 
{
    /**
     * 查询操作日志记录
     * 
     * @param id 操作日志记录ID
     * @return 操作日志记录
     */
        TUserVisitLog selectTUserVisitLogById(Long id);

    /**
     * 查询操作日志记录列表
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 操作日志记录集合
     */
    List<TUserVisitLog> selectTUserVisitLogList(TUserVisitLog tUserVisitLog);

    /**
     * 新增操作日志记录
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    int insertTUserVisitLog(TUserVisitLog tUserVisitLog);

    /**
     * 修改操作日志记录
     * 
     * @param tUserVisitLog 操作日志记录
     * @return 结果
     */
    int updateTUserVisitLog(TUserVisitLog tUserVisitLog);

    /**
     * 删除操作日志记录
     * 
     * @param id 操作日志记录ID
     * @return 结果
     */
    int deleteTUserVisitLogById(Long id);

    /**
     * 批量删除操作日志记录
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserVisitLogByIds(Long[] ids);


    List<TUserVisitLog> selectVisits(TUserVisitLog tUserVisitLog);


    List<TUserVisitLog> aloneVisitsCounts(TUserVisitLog tUserVisitLog);
    
    
    /**
     * 查询统计值 按天汇总
     * @param tUserVisitLog
     * @return
     */
    List<TUserVisitLog> selectSumVisitLogList(TUserVisitLog tUserVisitLog);


    List<TUserVisitLog> testVisits(TUserVisitLog tUserVisitLog);


    List<TUserVisitLog> testVisits1(TUserVisitLog tUserVisitLog);

    List<TUserVisitLog> alone(TUserVisitLog tUserVisitLog);
    
    List<TUserVisitLog> selectSumLoginLogList(TUserVisitLog tUserVisitLog);
    
}
