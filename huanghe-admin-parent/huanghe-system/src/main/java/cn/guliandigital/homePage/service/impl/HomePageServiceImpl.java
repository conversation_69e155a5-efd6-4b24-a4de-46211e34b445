package cn.guliandigital.homePage.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;

import cn.guliandigital.common.core.domain.entity.SysUser;
import cn.guliandigital.common.utils.StorageUnitConversionUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.homePage.domain.HomeCommonVo;
import cn.guliandigital.homePage.domain.OrganUserType;
import cn.guliandigital.homePage.domain.ResourceStatistic;
import cn.guliandigital.homePage.domain.TUserLoginSum;
import cn.guliandigital.homePage.service.HomePageService;
import cn.guliandigital.homePage.service.TUserLoginSumService;
import cn.guliandigital.homePage.service.TUserVisitSumService;
import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.plat.mapper.TPlatOrganMapper;
import cn.guliandigital.plat.service.ITPlatOrganService;
import cn.guliandigital.plat.service.ITPlatOrganTypeService;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import cn.guliandigital.product.database.domain.TProDatabase;
import cn.guliandigital.product.database.service.ITProDatabaseService;
import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;
import cn.guliandigital.product.readhistory.service.ITUserReadHistorySumService;
import cn.guliandigital.product.search.domain.TUserSearchHistory;
import cn.guliandigital.product.search.mapper.TUserSearchHistoryMapper;
import cn.guliandigital.system.mapper.SysUserMapper;

/**
 * @param :
 * <AUTHOR>
 * @Description
 * @Date 2020/11/4 11:30
 **/
@Service
public class HomePageServiceImpl implements HomePageService {
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TPlatOrganMapper tPlatOrganMapper;
    @Autowired
    private TUserSearchHistoryMapper tUserSearchHistoryMapper;
    @Autowired
    private ITPlatOrganTypeService itPlatOrganTypeService;
    @Autowired
    private ITPlatOrganService itPlatOrganService;
    @Autowired
    private TUserVisitSumService tUserVisitSumService;
    
    @Autowired
    private TUserLoginSumService tUserLoginSumService;
    
    @Autowired
    private ITProBooksService itProBooksService;

    @Autowired
    private ITProDatabaseService itProDatabaseService;

    @Autowired
    private ITConfigClassicTreeService itConfigClassicTreeService;

    @Autowired
    private ITUserReadHistorySumService itUserReadHistorySumService;



    /**
     * <AUTHOR>
     * @Description 新增用户量
     * @Date 2020/11/4 11:08
     **/
    @Override
    public SysUser newUser() {
        return sysUserMapper.newUser();
    }

    /**
     * @param :
     * @Description 新增机构
     * @Date 2020/11/6 11:10
     **/
    @Override
    public TPlatOrgan newPlat() {
        return tPlatOrganMapper.newPlat();
    }

    /**
     * @Description 搜索历史搜索词排行
     * @Date 2020/11/6 11:10
     **/
    @Override
    public List<TUserSearchHistory> searchTop(TUserSearchHistory tUserSearchHistory) {
        List<TUserSearchHistory> list = tUserSearchHistoryMapper.searchTop(tUserSearchHistory);
        return list;
    }


    /**
     * 用户机构类型
     *
     * @return
     */
    @Override
    public List<OrganUserType> getOrganUserType() {
        //查询机构类型

        List<TPlatOrgan> tPlatOrgans = itPlatOrganService.selectTPlatOrganList(new TPlatOrgan() {{
            setDelFlag(0);
        }});
        Map<String, List<TPlatOrgan>> map = tPlatOrgans.stream()
                .collect(Collectors.groupingBy(TPlatOrgan::getOrgType));

        List<OrganUserType> list = new ArrayList<>();
        for (Map.Entry<String, List<TPlatOrgan>> entry : map.entrySet()) {
            OrganUserType organUserType = new OrganUserType();
            organUserType.setValue(entry.getValue().size() + "");
            organUserType.setName(itPlatOrganTypeService.selectTPlatOrganTypeById(entry.getKey()).getTypeName());
            organUserType.setProportion(countPercentage(entry.getValue().size(), tPlatOrgans.size()));
            list.add(organUserType);
        }

        return list;
    }


//    @Override
//    public Map getUserLoginSum(String type) {
//
//        Map<String, Object> map = new HashMap<>();
//        List<String> dateTimeList = getDateTimeList();
//
//        map.put("date", dateTimeList);
//
//
//        if (type.equals("LOGIN_FORM")) {
//            //登录形式 个人 机构
//            List<TUserVisitSum> list = tUserVisitSumService.getUserLoginSum(new TUserVisitSum() {{
//                setDelFlag(0);
//            }});
//
//            List<TUserVisitSum> uList = new ArrayList();
//            List<TUserVisitSum> oList = new ArrayList();
//
//            for (TUserVisitSum tUserVisitSum : list) {
//                String sumDate = tUserVisitSum.getSumDate();
//                String[] split = sumDate.split("-");
//                String newDate = split[0] + "-" + split[1];
//                tUserVisitSum.setSumDate(newDate);
//            }
//
//            //Map<String, List<TUserVisitSum>> collect = list.stream().collect(Collectors.groupingBy(TUserVisitSum::getLoginForm));
//            Map<String, List<TUserVisitSum>> collect = list.stream().filter(s -> Objects.nonNull(s.getLoginForm())).collect(Collectors.groupingBy(TUserVisitSum::getLoginForm));
//            
//
//            for (Map.Entry<String, List<TUserVisitSum>> entry : collect.entrySet()) {
//                if (StringUtil.equals(entry.getKey(), "U")) {
//                    List<TUserVisitSum> sortedObjects = entry.getValue().stream()
//                            .sorted(Comparator.comparing(TUserVisitSum::getSumDate))
//                            .collect(Collectors.toList());
//                    entry.setValue(sortedObjects);
//                    for (TUserVisitSum tUserVisitSum : sortedObjects) {
//                        uList.add(tUserVisitSum);
//                    }
//                }
//                if (StringUtil.equals(entry.getKey(), "O")) {
//                    List<TUserVisitSum> sortedObjects = entry.getValue().stream()
//                            .sorted(Comparator.comparing(TUserVisitSum::getSumDate))
//                            .collect(Collectors.toList());
//                    entry.setValue(sortedObjects);
//                    for (TUserVisitSum tUserVisitSum : sortedObjects) {
//                        oList.add(tUserVisitSum);
//                    }
//                }
//            }
//
////            Map<String, List<TUserVisitSum>> oMap = oList.stream().collect(Collectors.groupingBy(TUserVisitSum::getSumDate));
////            Map<String, List<TUserVisitSum>> uMap = uList.stream().collect(Collectors.groupingBy(TUserVisitSum::getSumDate));
//
//            Map<String, List<TUserVisitSum>> oMap = oList.stream()
//                    .collect(Collectors.groupingBy(TUserVisitSum::getSumDate, TreeMap::new, Collectors.toList()));
//            Map<String, List<TUserVisitSum>> uMap = uList.stream()
//                    .collect(Collectors.groupingBy(TUserVisitSum::getSumDate, TreeMap::new, Collectors.toList()));
//
//            map.put("u", dateDetail(uMap, dateTimeList));
//            map.put("o", dateDetail(oMap, dateTimeList));
//
//
//        } else if (type.equals("USER_SOURCE")) {
//
//            //用户来源 pc app
//            //查询汇总
//            List<TUserVisitSum> pcList = new ArrayList();
//            List<TUserVisitSum> appList = new ArrayList();
//            List<TUserVisitSum> list = tUserVisitSumService.getUserLoginSum(new TUserVisitSum() {{
//                setDelFlag(0);
//            }});
//
//            for (TUserVisitSum tUserVisitSum : list) {
//                String sumDate = tUserVisitSum.getSumDate();
//                String[] split = sumDate.split("-");
//                String newDate = split[0] + "-" + split[1];
//                tUserVisitSum.setSumDate(newDate);
//            }
//
//            //Map<String, List<TUserVisitSum>> collect = list.stream().collect(Collectors.groupingBy(TUserVisitSum::getUserSource));
//            Map<String, List<TUserVisitSum>> collect = list.stream().filter(s -> Objects.nonNull(s.getUserSource())).collect(Collectors.groupingBy(TUserVisitSum::getUserSource));
//           
//            for (Map.Entry<String, List<TUserVisitSum>> entry : collect.entrySet()) {
//                if (StringUtil.equals(entry.getKey(), "P")) {
//
//                    List<TUserVisitSum> sortedObjects = entry.getValue().stream()
//                            .sorted(Comparator.comparing(TUserVisitSum::getSumDate))
//                            .collect(Collectors.toList());
//                    entry.setValue(sortedObjects);
//                    for (TUserVisitSum tUserVisitSum : sortedObjects) {
//                        pcList.add(tUserVisitSum);
//                    }
//
//                }
//                if (StringUtil.equals(entry.getKey(), "A")) {
//                    List<TUserVisitSum> sortedObjects = entry.getValue().stream()
//                            .sorted(Comparator.comparing(TUserVisitSum::getSumDate))
//                            .collect(Collectors.toList());
//                    entry.setValue(sortedObjects);
//                    for (TUserVisitSum tUserVisitSum : sortedObjects) {
//                        appList.add(tUserVisitSum);
//                    }
//
//                }
//            }
//            Map<String, List<TUserVisitSum>> pcMap = pcList.stream()
//                    .collect(Collectors.groupingBy(TUserVisitSum::getSumDate, TreeMap::new, Collectors.toList()));
//            Map<String, List<TUserVisitSum>> appMap = appList.stream()
//                    .collect(Collectors.groupingBy(TUserVisitSum::getSumDate, TreeMap::new, Collectors.toList()));
//
//
//            map.put("PC", dateDetail(pcMap, dateTimeList));
//
//            map.put("APP", dateDetail(appMap, dateTimeList));
//
//        }
//
//        return map;
//    }
    
    
    @Override
    public Map getUserLoginSum(String type) {

        Map<String, Object> map = new HashMap<>();
        List<String> dateTimeList = getDateTimeList();

        map.put("date", dateTimeList);


        if (type.equals("LOGIN_FORM")) {
            //登录形式 个人 机构
            List<TUserLoginSum> list = tUserLoginSumService.getUserLoginSum(new TUserLoginSum() {{
                setDelFlag(0);
            }});

            List<TUserLoginSum> uList = new ArrayList();
            List<TUserLoginSum> oList = new ArrayList();

            for (TUserLoginSum tUserVisitSum : list) {
                String sumDate = tUserVisitSum.getSumDate();
                String[] split = sumDate.split("-");
                String newDate = split[0] + "-" + split[1];
                tUserVisitSum.setSumDate(newDate);
            }

            //Map<String, List<TUserLoginSum>> collect = list.stream().collect(Collectors.groupingBy(TUserLoginSum::getLoginForm));
            Map<String, List<TUserLoginSum>> collect = list.stream().filter(s -> Objects.nonNull(s.getLoginForm())).collect(Collectors.groupingBy(TUserLoginSum::getLoginForm));
            

            for (Map.Entry<String, List<TUserLoginSum>> entry : collect.entrySet()) {
                if (StringUtil.equals(entry.getKey(), "U")) {
                    List<TUserLoginSum> sortedObjects = entry.getValue().stream()
                            .sorted(Comparator.comparing(TUserLoginSum::getSumDate))
                            .collect(Collectors.toList());
                    entry.setValue(sortedObjects);
                    for (TUserLoginSum tUserVisitSum : sortedObjects) {
                        uList.add(tUserVisitSum);
                    }
                }
                if (StringUtil.equals(entry.getKey(), "O")) {
                    List<TUserLoginSum> sortedObjects = entry.getValue().stream()
                            .sorted(Comparator.comparing(TUserLoginSum::getSumDate))
                            .collect(Collectors.toList());
                    entry.setValue(sortedObjects);
                    for (TUserLoginSum tUserVisitSum : sortedObjects) {
                        oList.add(tUserVisitSum);
                    }
                }
            }

//            Map<String, List<TUserLoginSum>> oMap = oList.stream().collect(Collectors.groupingBy(TUserLoginSum::getSumDate));
//            Map<String, List<TUserLoginSum>> uMap = uList.stream().collect(Collectors.groupingBy(TUserLoginSum::getSumDate));

            Map<String, List<TUserLoginSum>> oMap = oList.stream()
                    .collect(Collectors.groupingBy(TUserLoginSum::getSumDate, TreeMap::new, Collectors.toList()));
            Map<String, List<TUserLoginSum>> uMap = uList.stream()
                    .collect(Collectors.groupingBy(TUserLoginSum::getSumDate, TreeMap::new, Collectors.toList()));

            map.put("u", dateDetail(uMap, dateTimeList));
            map.put("o", dateDetail(oMap, dateTimeList));


        } else if (type.equals("USER_SOURCE")) {

            //用户来源 pc app
            //查询汇总
            List<TUserLoginSum> pcList = new ArrayList();
            List<TUserLoginSum> appList = new ArrayList();
            List<TUserLoginSum> list = tUserLoginSumService.getUserLoginSum(new TUserLoginSum() {{
                setDelFlag(0);
            }});

            for (TUserLoginSum tUserVisitSum : list) {
                String sumDate = tUserVisitSum.getSumDate();
                String[] split = sumDate.split("-");
                String newDate = split[0] + "-" + split[1];
                tUserVisitSum.setSumDate(newDate);
            }

            //Map<String, List<TUserLoginSum>> collect = list.stream().collect(Collectors.groupingBy(TUserLoginSum::getUserSource));
            Map<String, List<TUserLoginSum>> collect = list.stream().filter(s -> Objects.nonNull(s.getUserSource())).collect(Collectors.groupingBy(TUserLoginSum::getUserSource));
           
            for (Map.Entry<String, List<TUserLoginSum>> entry : collect.entrySet()) {
                if (StringUtil.equals(entry.getKey(), "P")) {

                    List<TUserLoginSum> sortedObjects = entry.getValue().stream()
                            .sorted(Comparator.comparing(TUserLoginSum::getSumDate))
                            .collect(Collectors.toList());
                    entry.setValue(sortedObjects);
                    for (TUserLoginSum tUserVisitSum : sortedObjects) {
                        pcList.add(tUserVisitSum);
                    }

                }
                if (StringUtil.equals(entry.getKey(), "A")) {
                    List<TUserLoginSum> sortedObjects = entry.getValue().stream()
                            .sorted(Comparator.comparing(TUserLoginSum::getSumDate))
                            .collect(Collectors.toList());
                    entry.setValue(sortedObjects);
                    for (TUserLoginSum tUserVisitSum : sortedObjects) {
                        appList.add(tUserVisitSum);
                    }

                }
            }
            Map<String, List<TUserLoginSum>> pcMap = pcList.stream()
                    .collect(Collectors.groupingBy(TUserLoginSum::getSumDate, TreeMap::new, Collectors.toList()));
            Map<String, List<TUserLoginSum>> appMap = appList.stream()
                    .collect(Collectors.groupingBy(TUserLoginSum::getSumDate, TreeMap::new, Collectors.toList()));


            map.put("PC", dateDetail(pcMap, dateTimeList));

            map.put("APP", dateDetail(appMap, dateTimeList));

        }

        return map;
    }

    public static String countPercentage(Integer divisor, Integer dividend) {
//计算存藏目录百分比
        BigDecimal divisorBigDecima = BigDecimal.valueOf(divisor);
        BigDecimal dividendBigDecima = BigDecimal.valueOf(dividend);
        BigDecimal divide = divisorBigDecima.divide(dividendBigDecima, 2, RoundingMode.HALF_UP);
//下面将结果转化成百分比
        NumberFormat percent = NumberFormat.getPercentInstance();
        percent.setMaximumFractionDigits(2);
        return percent.format(divide.doubleValue());
    }


    public List<String> getDateTimeList() {
        List<String> list = new ArrayList<>();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 获取当前月份往前推12个月的月份
        for (int i = 0; i < 12; i++) {
            // 使用Period类的minusMonths方法来往前推月份
            Period period = Period.ofMonths(i);
            LocalDate date = currentDate.minus(period);
            // 输出月份
            list.add(date.format(formatter));
        }
        return list;
    }


    public Map dateDetail(Map<String, List<TUserLoginSum>> map, List list) {

        int keyCount = map.size();

        list.forEach((v -> {
            boolean b = map.containsKey(v);
            if (!b) {
                map.put(v.toString(), new ArrayList<>(0));
            }
        }));

        return map;
    }


    @Override
    public Map<String, Object> getResourceProportionDeatil(String id) {
        OrganUserType organUserType = new OrganUserType();
        Map<String, Object> objectObjectMap = new HashMap<String, Object>();

        if (StringUtil.equals("000000",id)) {
            List<TProBooks> tProBooks = itProBooksService.selectTProBooksList(new TProBooks());

            Map<String, List<TProBooks>> collect = tProBooks.stream().filter(e-> !Strings.isNullOrEmpty(e.getDbId())).collect(Collectors.groupingBy(TProBooks::getDbId));
            for (Map.Entry<String, List<TProBooks>> entry : collect.entrySet()) {
                long wordNum = 0;
                for (TProBooks proBooks : entry.getValue()) {
                	long wn = proBooks.getWordNum()==null?0:proBooks.getWordNum();
                    wordNum = wordNum + wn;
                }
                TProDatabase tProDatabase = itProDatabaseService.selectTProDatabaseById(entry.getKey());
               
//                BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal(1000), BigDecimal.ROUND_DOWN);
                BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal("1000"),  RoundingMode.HALF_UP);
                if(StringUtil.isNotNull(tProDatabase)){
                    objectObjectMap.put(tProDatabase.getDbName(), bd.longValue());
                }
            }
        } else {
            List<TProBooks> tProBooks = itProBooksService.selectTProBooksList(new TProBooks() {{
                setDbId(id);
            }});
            Map<String, List<TProBooks>> collect = tProBooks.stream().filter(e-> !Strings.isNullOrEmpty(e.getResourceClassesId())).collect(Collectors.groupingBy(TProBooks::getResourceClassesId));
            for (Map.Entry<String, List<TProBooks>> entry : collect.entrySet()) {
                long wordNum = 0;
                for (TProBooks proBooks : entry.getValue()) {
                    wordNum = wordNum + proBooks.getWordNum();
                }
//                BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal(1000), BigDecimal.ROUND_DOWN);
                BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal("1000"),  RoundingMode.HALF_UP);

                List<TConfigClassicTree> configClassicTrees = itConfigClassicTreeService.selectTConfigClassicTreeById(entry.getKey());
                if (StringUtil.isNotEmpty(configClassicTrees.get(0).getTreeNameS())) {
                    objectObjectMap.put(configClassicTrees.get(0).getTreeNameS(), bd.longValue());
                }
            }
        }
        return objectObjectMap;
    }


    @Override
    public HomeCommonVo getdataStatisticsDeatil(String id) {

        HomeCommonVo homeCommonVo = new HomeCommonVo();
        if (StringUtil.equals("000000",id)) {
            //全部
            List<TProBooks> tProBooks = itProBooksService.selectTProBooksList(new TProBooks());
            long dataSize = 0;
            for (TProBooks tProBook : tProBooks) {
                if (!Objects.isNull(tProBook.getResourceSize())) {
                    dataSize = dataSize + tProBook.getResourceSize();
                }
            }
            BigDecimal space = new BigDecimal(dataSize);
//            String spaceunit = StorageUnitConversionUtils.getBestString(space);
            BigDecimal conversion = StorageUnitConversionUtils.conversion(space, StorageUnitConversionUtils.Unit.B, StorageUnitConversionUtils.Unit.GB, 2);
            homeCommonVo.setName("全部");
            homeCommonVo.setValue(conversion.toString());
        } else {
            TProDatabase tProDatabase = itProDatabaseService.selectTProDatabaseById(id);
            List<TProBooks> tProBooks = itProBooksService.selectTProBooksList(new TProBooks() {{
                setDbId(id);
            }});
            long dataSize = 0;
            for (TProBooks tProBook : tProBooks) {
                if (!Objects.isNull(tProBook.getResourceSize())) {
                    dataSize = dataSize + tProBook.getResourceSize();
                }
            }
            BigDecimal space = new BigDecimal(dataSize);
//            String spaceunit = StorageUnitConversionUtils.getBestString(space);
            BigDecimal conversion = StorageUnitConversionUtils.conversion(space, StorageUnitConversionUtils.Unit.B, StorageUnitConversionUtils.Unit.GB, 2);

            homeCommonVo.setName(tProDatabase.getDbName());
            homeCommonVo.setValue(conversion.toString());
        }

        return homeCommonVo;
    }


    @Override
    public List<ResourceStatistic> getResourceStatistic(ResourceStatistic resourceStatistic) {
        TProBooks tProBook = new TProBooks();

        if(StringUtil.isEmpty(resourceStatistic.getDbName())){

        }else {
            List<TProDatabase> tProDatabases = itProDatabaseService.selectTProDatabase(new TProDatabase() {{
                setDbName(resourceStatistic.getDbName());
            }});
            if(tProDatabases.size()>0){
                tProBook.setDbId(tProDatabases.get(0).getDbId());
            }


        }
        List<ResourceStatistic> list = new ArrayList<>();
        List<TProBooks> tProBooks = itProBooksService.selectTProBooksList(tProBook);
        Map<String, List<TProBooks>> collect = tProBooks.stream().filter(item-> StringUtils.isNotBlank(item.getDbName())).collect(Collectors.groupingBy(TProBooks::getDbName));
       // DecimalFormat df = new DecimalFormat("#.00");
        
        for (Map.Entry<String, List<TProBooks>> entry : collect.entrySet()) {
            int picNum = 0;
            long wordNum = 0;
            ResourceStatistic resourceStatistic1 = new ResourceStatistic();
            resourceStatistic1.setDbName(entry.getKey());
            for (TProBooks proBooks : entry.getValue()) {
                if (!Objects.isNull(proBooks.getPicNum())) {
                    picNum = picNum + proBooks.getPicNum();
                }
                if (!Objects.isNull(proBooks.getWordNum())) {
                    wordNum = wordNum + proBooks.getWordNum();
                }
            }
            
            resourceStatistic1.setPicNum(picNum);
//            BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal(1000), BigDecimal.ROUND_DOWN);
            BigDecimal bd = new BigDecimal(wordNum).divide(new BigDecimal("1000"),  RoundingMode.HALF_UP);


            resourceStatistic1.setWordNum(bd.longValue());
            resourceStatistic1.setResourceQuantity(entry.getValue().size() + "");
            list.add(resourceStatistic1);
        }

        //获取统计各个次数
        List<TUserReadHistorySum> tUserReadHistorySums = itUserReadHistorySumService.organCount(new TUserReadHistorySum());
        Map<String, List<TUserReadHistorySum>> collect1 = tUserReadHistorySums.stream().filter(e-> !Strings.isNullOrEmpty(e.getDbName())).collect(Collectors.groupingBy(TUserReadHistorySum::getDbName));
        for (Map.Entry<String, List<TUserReadHistorySum>> entry : collect1.entrySet()) {
            long quoteCount = 0;
            long copyCount = 0;
            long visitCount = 0;
            long noteCount = 0;
            for (TUserReadHistorySum tUserReadHistorySum : entry.getValue()) {
                if (!Objects.isNull(tUserReadHistorySum.getQuoteCount())) {
                    quoteCount = quoteCount + tUserReadHistorySum.getQuoteCount();
                }
                if (!Objects.isNull(tUserReadHistorySum.getCopyCount())) {
                    copyCount = copyCount + tUserReadHistorySum.getCopyCount();
                }
                if (!Objects.isNull(tUserReadHistorySum.getVisitCount())) {
                    visitCount = visitCount + tUserReadHistorySum.getVisitCount();
                }
                if (!Objects.isNull(tUserReadHistorySum.getNoteCount())) {
                    noteCount = noteCount + tUserReadHistorySum.getNoteCount();
                }
            }

            long finalCopyCount = copyCount;
            long finalNoteCount = noteCount;
            long finalVisitCount = visitCount;
            long finalQuoteCount = quoteCount;
            list.forEach(e -> {
                if (e.getDbName().equals(entry.getKey())) {
                    e.setCopyCount(finalCopyCount);
                    e.setNoteCount(finalNoteCount);
                    e.setVisitCount(finalVisitCount);
                    e.setQuoteCount(finalQuoteCount);
                }
            });
        }

        return list;
    }
}
