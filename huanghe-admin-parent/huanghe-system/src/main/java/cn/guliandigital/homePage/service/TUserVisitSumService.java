package cn.guliandigital.homePage.service;

import cn.guliandigital.homePage.domain.TUserVisitSum;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 11:11
 **/
public interface TUserVisitSumService {
    //授权机构统计
    List<TUserVisitSum> organCount(TUserVisitSum tUserVisitSum);


    TUserVisitSum selectVisits();

    List<TUserVisitSum> getUserLoginSum(TUserVisitSum tUserVisitSum);

}
