package cn.guliandigital.homePage.mapper;

import cn.guliandigital.homePage.domain.TUserVisitSum;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 访问统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
public interface TUserVisitSumMapper 
{
    /**
     * 查询访问统计
     * 
     * @param id 访问统计ID
     * @return 访问统计
     */
        TUserVisitSum selectTUserVisitSumById(String id);

    /**
     * 查询访问统计列表
     * 
     * @param tUserVisitSum 访问统计
     * @return 访问统计集合
     */
    List<TUserVisitSum> selectTUserVisitSumList(TUserVisitSum tUserVisitSum);

    /**
     * 新增访问统计
     * 
     * @param tUserVisitSum 访问统计
     * @return 结果
     */
    int insertTUserVisitSum(TUserVisitSum tUserVisitSum);

    /**
     * 修改访问统计
     * 
     * @param tUserVisitSum 访问统计
     * @return 结果
     */
    int updateTUserVisitSum(TUserVisitSum tUserVisitSum);

    /**
     * 删除访问统计
     * 
     * @param id 访问统计ID
     * @return 结果
     */
    int deleteTUserVisitSumById(String id);

    /**
     * 批量删除访问统计
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserVisitSumByIds(String[] ids);


    List<TUserVisitSum> organCount(TUserVisitSum tUserVisitSum);




    TUserVisitSum selectVisits();

    List<TUserVisitSum> selectTUserVisitSumListByTwelve(TUserVisitSum tUserVisitSum);

}
