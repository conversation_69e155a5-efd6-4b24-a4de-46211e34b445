package cn.guliandigital.homePage.mapper;

import java.util.List;

import cn.guliandigital.homePage.domain.TUserLoginSum;


/**
 * 访问统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
public interface TUserLoginSumMapper 
{
    /**
     * 查询访问统计
     * 
     * @param id 访问统计ID
     * @return 访问统计
     */
        TUserLoginSum selectTUserLoginSumById(String id);

    /**
     * 查询访问统计列表
     * 
     * @param tUserLoginSum 访问统计
     * @return 访问统计集合
     */
    List<TUserLoginSum> selectTUserLoginSumList(TUserLoginSum tUserLoginSum);

    /**
     * 新增访问统计
     * 
     * @param tUserLoginSum 访问统计
     * @return 结果
     */
    int insertTUserLoginSum(TUserLoginSum tUserLoginSum);

    /**
     * 修改访问统计
     * 
     * @param tUserLoginSum 访问统计
     * @return 结果
     */
    int updateTUserLoginSum(TUserLoginSum tUserLoginSum);

    /**
     * 删除访问统计
     * 
     * @param id 访问统计ID
     * @return 结果
     */
    int deleteTUserLoginSumById(String id);

    /**
     * 批量删除访问统计
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserLoginSumByIds(String[] ids);
    
    
    List<TUserLoginSum> selectTUserLoginSumListByTwelve(TUserLoginSum tUserLoginSum);


}
