package cn.guliandigital.homePage.service;

import cn.guliandigital.common.core.domain.entity.SysUser;
import cn.guliandigital.homePage.domain.HomeCommonVo;
import cn.guliandigital.homePage.domain.OrganUserType;
import cn.guliandigital.homePage.domain.ResourceStatistic;
import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.product.search.domain.TUserSearchHistory;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HomePageService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/4 11:06
 */

public interface HomePageService {

    /**
     * <AUTHOR>
     * @Description 新增用户量
     * @Date 2020/11/4 11:08
     **/
    SysUser newUser();
    /**
     * @Description 新增机构
     * @Date 2020/11/6 11:10
     * @param :
     **/
    TPlatOrgan newPlat();
    /**
     * @Description 搜索历史搜索词排行
     * @Date 2020/11/6 11:10
     * @param :
     **/
    List<TUserSearchHistory> searchTop(TUserSearchHistory tUserSearchHistory);

    List<OrganUserType> getOrganUserType();

    Map getUserLoginSum(String type);

    Map<String,Object> getResourceProportionDeatil(String id);

    HomeCommonVo getdataStatisticsDeatil(String id);

    List<ResourceStatistic> getResourceStatistic(ResourceStatistic resourceStatistic);
}
