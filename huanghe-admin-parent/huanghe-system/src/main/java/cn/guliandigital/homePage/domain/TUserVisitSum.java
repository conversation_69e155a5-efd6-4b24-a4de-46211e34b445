package cn.guliandigital.homePage.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.guliandigital.common.annotation.Excel;
import lombok.Data;

/**
 * 访问统计对象 t_user_visit_sum
 * 
 * <AUTHOR>
 * @date 2020-11-10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TUserVisitSum 
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;
   
    private Integer day;
    /** 机构ID */
    @Excel(name = "机构ID")
    private String orgId;

    /** 图书ID */
    @Excel(name = "图书ID")
    private String orgName;

    /** 检索次数 */
    @Excel(name = "检索次数")
    private Long searchCount;
   
    /** 访问次数 */
    @Excel(name = "访问次数")
    private Long visitCount;
    private long totalCount;

    /** 登陆次数 */
    @Excel(name = "登陆次数")
    private Long loginCount;
    private  Long  totalCountLogin;

    /** 统计日期 yyyy-mm-dd */
    @Excel(name = "统计日期 yyyy-mm-dd")
    private String sumDate;
    
    
    private Date createTime;

   
    /** $column.columnComment */
    @Excel(name = "统计日期 yyyy-mm-dd")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
    @Excel(name = "创建人")
    private String updatebyId;


    /** 来源 P-pc A-app **/
    private  String userSource;

    /** 登录形式 U-个人 O-机构 **/
    private  String loginForm;

   
    private  Date updateTime;
    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;
    

}
