package cn.guliandigital.homePage.service.impl;/**
 * <AUTHOR>
 * @Description
 * @Date 11:15
 * @param ${}:
 **/

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.homePage.domain.TUserLoginSum;
import cn.guliandigital.homePage.mapper.TUserLoginSumMapper;
import cn.guliandigital.homePage.service.TUserLoginSumService;

/**
 * @ClassName TUserLoginSumServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/10 11:15
 */
@Service
public class TUserLoginSumServiceImpl implements TUserLoginSumService {
	
	
    @Autowired
    private TUserLoginSumMapper tUserLoginSumMapper;

	@Override
	public List<TUserLoginSum> getUserLoginSum(TUserLoginSum tUserLoginSum) {
		return tUserLoginSumMapper.selectTUserLoginSumListByTwelve(tUserLoginSum);
	}
    
    
    
}
