package cn.guliandigital.homePage.service.impl;/**
 * <AUTHOR>
 * @Description
 * @Date 11:15
 * @param ${}:
 **/

import cn.guliandigital.homePage.domain.TUserVisitSum;
import cn.guliandigital.homePage.mapper.TUserVisitSumMapper;
import cn.guliandigital.homePage.service.TUserVisitSumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName TUserVisitSumServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/10 11:15
 */
@Service
public class TUserVisitSumServiceImpl implements TUserVisitSumService {
    @Autowired
    private TUserVisitSumMapper tUserVisitSumMapper;
    //授权机构统计
    @Override
    public List<TUserVisitSum> organCount(TUserVisitSum tUserVisitSum) {
        return tUserVisitSumMapper.organCount(tUserVisitSum);
    }

    @Override
    public TUserVisitSum selectVisits() {
        return tUserVisitSumMapper.selectVisits();
    }

    @Override
    public List<TUserVisitSum> getUserLoginSum(TUserVisitSum tUserVisitSum) {

        return tUserVisitSumMapper.selectTUserVisitSumListByTwelve(tUserVisitSum);
    }
}
