package cn.guliandigital.analysis.service;


import cn.guliandigital.analysis.domain.ImportPDF;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.storage.storage.domain.TTaskSettle;

import java.io.File;

/**
 * 解析全文
 */
public interface IAutoPDFSynService {

	/**
	 * 处理全文电子书
	 * @param
	 */
	void handlePDFBook(TTaskSettle task, File rootFile );

	void savePDFMenu(TProBooks pdfBook);

	public TProBooks checkData(ImportPDF ipdf, String rootPath);

}
