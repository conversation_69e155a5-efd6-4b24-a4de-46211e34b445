package cn.guliandigital.analysis.service.impl;

import cn.guliandigital.analysis.domain.ImportPDF;
import cn.guliandigital.analysis.service.IAutoPDFSynService;
import cn.guliandigital.analysis.service.IAutoSynbookService;
import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.DataStatusEnum;
import cn.guliandigital.common.enums.DelFlagEnum;
import cn.guliandigital.common.enums.ProStatusEnum;
import cn.guliandigital.common.enums.ResourceTypeEnum;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.es.domain.THuangHeEsBook;
import cn.guliandigital.es.domain.THuangHeEsMenu;
import cn.guliandigital.es.service.IElasticService;
import cn.guliandigital.product.book.domain.TBookMenuContentMongo;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.book.service.TBookMenuContentMongoService;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.product.menu.mapper.TProBookMenuMapper;
import cn.guliandigital.product.readhistory.mapper.TUserReadBookMapper;
import cn.guliandigital.product.readhistory.mapper.TUserReadHistoryMapper;
import cn.guliandigital.product.readnotes.mapper.TUserReadNotesMapper;
import cn.guliandigital.product.userbook.mapper.TUserBookshelfMapper;
import cn.guliandigital.storage.log.domain.TTaskLog;
import cn.guliandigital.storage.log.service.ITTaskLogService;
import cn.guliandigital.storage.storage.domain.TTaskSettle;
import cn.guliandigital.storage.storage.service.impl.EncodeFontUtils;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.dom4j.tree.DefaultElement;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/28 21:40
 *
 */
@Slf4j
@Service
//@Transactional
public class AutoSynbookServiceImpl implements IAutoSynbookService {


	@Autowired
	private ITTaskLogService taskLogService;

	@Autowired
	private TProBooksMapper proBooksMapper;

	@Autowired
	private IElasticService elasticService;

	@Autowired
	private IAutoPDFSynService pdfSynService;


	@Autowired
	private TUserBookshelfMapper tUserBookshelfMapper;

	@Autowired
	private TUserReadHistoryMapper tUserReadHistoryMapper;

	@Autowired
	private TUserReadBookMapper tUserReadBookMapper;

	@Autowired
	private TUserReadNotesMapper tUserReadNotesMapper;

	@Autowired
	private TProBookMenuMapper bookMenuMapper;

	@Autowired
	private EncodeFontUtils encodeFontUtils;

	@Autowired
	private TBookMenuContentMongoService contentMongoService;


	//@Transactional(rollbackFor = Exception.class)
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void handleBooks(TTaskSettle task, File rootFile ) {
		log.info("==>开始解析图书文件路径：{}",rootFile.getPath());
		File[] files = rootFile.listFiles();
		task.getAllCount().addAndGet(files.length);
		for (File file:files){
			TTaskLog tTaskLog = new TTaskLog();
			tTaskLog.setTaskId(task.getId());
			tTaskLog.setId(IdUtil.simpleUUID());
			tTaskLog.setParseStartTime(new Date());
			tTaskLog.setFileName(file.getName());
			tTaskLog.setDataStatus(DataStatusEnum.ING.getCode());//解析状态 1-正在解析 2-解析成功 3-解析失败
			tTaskLog.setDelFlag(0);
			taskLogService.insertTTaskLog(tTaskLog);
			try{
				if (file.isDirectory()){
					//获取解析的文件
					List<File> xpros = Arrays.stream(Objects.requireNonNull(file.listFiles())).filter(f -> f.getName().equals("main.xpro")).collect(Collectors.toList());
					if (!xpros.isEmpty()){
						//解析xpro文件
						TProBooks books = parseMainXpro(xpros.get(0));
						String errorMsg = books.getErrorMsg();
						if (StringUtil.isNotEmpty(errorMsg)){
							//删除封面
							deleteFile(books.getCoverUrl());
							throw new Exception(errorMsg);
						}
						//根据唯一标识查询图书
						List<TProBooks> bookList = proBooksMapper.selectBookByUniqueId(books.getUniqueId(), ResourceTypeEnum.T.getCode());
						if (!bookList.isEmpty()){
                            for (TProBooks tProBooks:bookList){
                                elasticService.deleteBook(tProBooks.getId());
                                elasticService.deleteMenu(tProBooks.getId());
                                contentMongoService.deleteByCondition(tProBooks.getId());
                                bookMenuMapper.deleteTProBookMenuByBookId(tProBooks.getId());
                                //删除,收藏，笔记，阅读历史
                                tUserReadBookMapper.deleteTUserBookByBookId(tProBooks.getId());
                                tUserReadHistoryMapper.deleteTUserReadHistoryByBookId(tProBooks.getId());
                                tUserBookshelfMapper.deleteTUserBookshelfByBookId(tProBooks.getId());
                                tUserReadNotesMapper.deleteTUserReadNotesByBookId(tProBooks.getId());
                                //删除之前的字体包
                                String base = FilenameUtils.normalize(HuangHeConfig.getUploadPath() + File.separator + "fonts");
                                String outPath = FilenameUtils.normalize(base+File.separator+"menu"+File.separator+tProBooks.getId());
                                FileUtils.deleteDirectory(new File(outPath));
                                //删除之前的封面
                                deleteFile(tProBooks.getCoverPath());
                            }
						}
						//添加图书
						books.setId(IdUtil.simpleUUID());
						books.setProStatus(ProStatusEnum.OFFLINE.getCode());
						books.setDelFlag(DelFlagEnum.NORMAL.getCode());
						books.setCreatebyId(task.getUpdatebyId());
						books.setCreatebyName(task.getUpdatebyName());
						books.setCreateTime(DateUtil.getCuurentDate());
						//下面解析章节
						parseBookDetail(books.getXmlFiles(),books,task);

						books.setProStatus(ProStatusEnum.ONLINE.getCode());
						proBooksMapper.insertTProBooks(books);
						//图书添加Es
						THuangHeEsBook es = new THuangHeEsBook();
						BeanUtils.copyProperties(books,es);
						es.setResId(IdUtil.simpleUUID());
						es.setBookId(books.getId());
						es.setProStatus(ProStatusEnum.ONLINE.getCode().toString());
						es.setCreateTime(DateUtil.getCuurentDate());
						elasticService.insertEntityBook(es);
						//修改Es上架状态
						elasticService.editESUp(books.getId());
					}
				}else {
					throw new Exception("解析文件格式不正确");
				}
				tTaskLog.setDataStatus(DataStatusEnum.SUCCESS.getCode());
			}catch (Exception e){
				tTaskLog.setDataStatus(DataStatusEnum.LOST.getCode());
				tTaskLog.setErrorMsg(e.getMessage());
				task.getErrorCount().incrementAndGet();
				log.error("解析人大纪实资源报错===>",e);
			}
			tTaskLog.setParseEndTime(new Date());
			taskLogService.updateTTaskLog(tTaskLog);
		}
	}

    /**
     * 解析章节
     * @param xmlFiles
     * @param books
     * @param task
     * @throws Exception
     */
	private void parseBookDetail(List<File> xmlFiles,TProBooks books,TTaskSettle task)throws Exception {
		AtomicInteger display= new AtomicInteger(1);
		StringBuilder mainContent=new StringBuilder();
		TProBookMenu bookMenu=null;
		List<TProBookMenu> pList=new ArrayList<>();
		for (File xml:xmlFiles){
			SAXReader reader = new SAXReader();
			Document document = reader.read(xml);
			Element root = document.getRootElement();// 获取根元素
			List<Element> elements = root.elements();
			for(Element element:elements){
				if ("标题".equals(element.getName())){
					int level=0;
					try{
						level =Integer.parseInt(element.attributeValue("level"));
					}catch (Exception e){
						throw new Exception("level为空或不是数字");
					}
					String id =element.attributeValue("id");
					String menuName = element.getText().replace("\n        ","");
					//添加章节
					if (StringUtil.isNotNull(bookMenu)){
						bookMenu.setZwContent(mainContent.toString());
						bookMenu.setDisplay((long) display.get());
						display.incrementAndGet();
						mainContent.setLength(0);
						saveMenu(bookMenu,books);
					}
					bookMenu=new TProBookMenu();
					bookMenu.setLevel(level);
					bookMenu.setMenuName(menuName);
					if (level==1){
						bookMenu.setFullMenuName(menuName);
						bookMenu.setPid("0");
						pList.clear();
						pList.add(bookMenu);
					}else{
						//将大于等于同等级的目录去掉
						int finalLevel = level;
						List<TProBookMenu> levelList = pList.stream()
								.filter(f ->f.getLevel()>= finalLevel).collect(Collectors.toList());
						pList.removeAll(levelList);
						//寻找上一级目录,升序排序
						pList.sort(new Comparator<TProBookMenu>() {
							@Override
							public int compare(TProBookMenu o1, TProBookMenu o2) {
								return Integer.compare(o1.getLevel(), o2.getLevel());
							}
						});
						//获取上级id
						String pid = pList.get(pList.size() - 1).getId();
						bookMenu.setPid(pid);
						//获取完全章节名
						StringBuilder full=new StringBuilder();
						for (TProBookMenu pm : pList) {
							full.append(pm.getMenuName()).append(">");
						}
						bookMenu.setFullMenuName(full.toString()+ bookMenu.getMenuName());
						pList.add(bookMenu);
					}
				}else if ("段落".equals(element.getName())){
					mainContent.append("<p>");
					List<Element> duan = element.elements();
					for (int i=0;i<duan.size();i++){
						Element zhengwen=duan.get(i);
						boolean identical=false;
						if (i>0){
							//判断上一个标签跟当前标签是否一致
							Element shang = duan.get(i - 1);
							if (StringUtil.equals(zhengwen.getName(),shang.getName())){
								//跟上一个是一样的标签放到一个标签里面
								identical=true;
							}else {
								//是不相同的，一个新的标签将这个标签闭合
								mainContent.append("</span>");
							}
						}
						if ("正文".equals(zhengwen.getName())){
							if (!identical){
								mainContent.append("<span class=\"zhengwen\">");
							}
							List<Node> content = zhengwen.content();
							for (Node node:content){
								if ("注号".equals(node.getName())){
									//注号的内容
									mainContent.append("<span class=\"zhuhao\">")
											.append(node.getText()).append("</span>");
								}else if ("字图".equals(node.getName())){
									String zitu = ((DefaultElement) node).attributeValue("src");
									String path = parseImage(zitu, xml, books.getId());
									if (StringUtil.isNotEmpty(path)){
										mainContent.append("<img src=\""+HuangHeConfig.getPlatHttpUrl()+path+"\" class=\"zitu\"/>");
									}
								}else if ("插图".equals(node.getName())){
									String chatu = ((DefaultElement) node).attributeValue("src");
									String path = parseImage(chatu, xml, books.getId());
									if (StringUtil.isNotEmpty(path)){
										mainContent.append("<img src=\""+HuangHeConfig.getPlatHttpUrl()+path+"\" class=\"chatu\"/>");
									}
								}else {
									mainContent.append(node.getText());
								}
							}
						}
						if("插图".equals(zhengwen.getName())){
							String chatu = zhengwen.attributeValue("src");
							String path = parseImage(chatu, xml, books.getId());
							if (StringUtil.isNotEmpty(path)){
								mainContent.append("<img src=\""+HuangHeConfig.getPlatHttpUrl()+path+"\" class=\"chatu\"/>");
							}
						}
						if ("注释".equals(zhengwen.getName())){
							if (!identical){
								mainContent.append("<span class=\"zhushi\">");
							}
							mainContent.append(zhengwen.getText());
						}
						if ("注文".equals(zhengwen.getName())){
							if (!identical){
								mainContent.append("<span class=\"zhuwen\">");
							}
							mainContent.append(zhengwen.getText());
						}
					}
					mainContent.append("</span>");
					mainContent.append("</p>");
				}else if ("table".equals(element.getName())){
					mainContent.append(parseAttribute(element));
					//下面提取表格内容
					List<Element> biaoge = element.elements();
					for (Element head:biaoge){
						if ("tgroup".equals(head.getName())){
							List<Element> tgroupList = head.elements();
							for (Element tgroup:tgroupList){
								if ("colspec".equals(tgroup.getName())){
									//只提取表头和属性
									mainContent.append(parseAttribute(tgroup)).append("</colspec");
								}else if ("tbody".equals(tgroup.getName())){
									mainContent.append(parseAttribute(tgroup));
									List<Element> rowList = tgroup.elements();
									for (Element row:rowList){
										if ("row".equals(row.getName())){
											row.setName("tr");
											mainContent.append(parseAttribute(row));
											List<Element> tdList = row.elements();
											for (Element entry:tdList){
												if ("entry".equals(entry.getName())){
													entry.setName("td");
													mainContent.append(parseAttribute(entry));
													String value = entry.getStringValue().replace("\uE011", "-");
													mainContent.append(value).append("</td>");
												}
											}
											mainContent.append("</tr>");
										}
									}
									mainContent.append("</tbody>");
								}
							}
							mainContent.append("</tgroup>");
						}
					}
					mainContent.append("</table>");
				}
			}
		}
		//添加章节
		if (StringUtil.isNotNull(bookMenu)){
			bookMenu.setZwContent(mainContent.toString());
			bookMenu.setDisplay((long) display.get());
			display.incrementAndGet();
			mainContent.setLength(0);
			saveMenu(bookMenu,books);
		}
	}

	private  String parseAttribute(Element element) {
		//拿到表格头里面的属性
		StringBuilder builder=new StringBuilder();
		builder.append("<").append(element.getName());
		List<Attribute> lableAtts = element.attributes();
		if (StringUtil.isNotEmpty(lableAtts)){
			for (Attribute attribute:lableAtts){
				builder.append(" ").append(attribute.getName())
						.append("=").append("\"").append(attribute.getValue())
						.append("\"");
			}
		}
		return builder.append(">").toString();
	}

	private String parseImage(String src,File xml,String bookId)throws Exception {
		//处理字图
		String path="";
		String zituUrl = FilenameUtils.normalize(xml.getParent()+File.separator + src,true);
		File zituFile=new File(zituUrl);
		if (zituFile.exists()){
			//将字图复制到指定路径
			String targetPath=FilenameUtils.normalize(HuangHeConfig.getUploadPath()+File.separator+"zitu"+File.separator+bookId,true);
			new File(targetPath).mkdirs();
			targetPath=targetPath+File.separator+IdUtils.simpleUUID()+ "."+FilenameUtils.getExtension(zituFile.getName());
			FileUtils.copyFile(zituFile,new File(targetPath));
			//拼接http访问路径
			path =FilenameUtils.normalize(targetPath,true)
					.replace(FilenameUtils.normalize(HuangHeConfig.getProfile(),true),"");
		}
		return path;
	}


	public void saveMenu(TProBookMenu menus,TProBooks book) throws Exception{
		menus.setDelFlag(DelFlagEnum.NORMAL.getCode());
		menus.setCreateTime(new Date());
		menus.setBookId(book.getId());
		menus.setId(IdUtil.simpleUUID());
		try {
			bookMenuMapper.insertTProBookMenu(menus);
			//添加章节内容
			TBookMenuContentMongo contentMongo=new TBookMenuContentMongo();
			contentMongo.setMenuId(menus.getId());
			contentMongo.setBookId(book.getId());
			contentMongo.setId(IdUtil.simpleUUID());
			contentMongo.setDisplay(menus.getDisplay());
			contentMongo.setBookName(book.getBookName());
			contentMongo.setMenuName(menus.getMenuName());
			contentMongo.setStartPageNo(menus.getStartPageNo());
			contentMongo.setLevel(menus.getLevel());
			contentMongo.setReadMode(book.getResourceType());
			contentMongo.setCreateTime(DateUtil.getCuurentTime());
			//加密
			String fullText=null;
			if (StringUtil.isNotEmpty(menus.getZwContent())) {
				contentMongo.setBookContent(menus.getZwContent());
				Map<String, Object> data = encodeFontUtils.encodeFonts(contentMongo.getBookContent(), book.getId(),menus.getId());
				List<String> fontList = (List<String>) data.get("fontPaths");
				Map<String, String> mappingMap = (Map<String, String>) data.get("mappingMap");
				contentMongo.setBookContentEnc(data.get("contentEnc").toString());
				contentMongo.setEncFontPaths(String.join(",",fontList));
				contentMongo.setEncWordMappingMap(JSONObject.toJSONString(mappingMap));
				fullText = contentMongo.getBookContent().replaceAll("<{1}[^<>]*>{1}", "");
				fullText = StringUtil.replaceAllBlank(fullText);
			}
			//添加章节内容
			contentMongoService.add(contentMongo);
			// 保存到es中
			THuangHeEsMenu es = new THuangHeEsMenu();
			BeanUtils.copyProperties(book, es);
			es.setResId(IdUtil.simpleUUID());
			es.setMenuId(menus.getId());
			es.setBookId(book.getId());
			es.setMenuName(menus.getMenuName());
			es.setDisplay(Math.toIntExact(menus.getDisplay()));
			es.setFullMenuName(menus.getFullMenuName());
			es.setStartPageNo(menus.getStartPageNo());
			es.setFullText(fullText);
			es.setProStatus(ProStatusEnum.OFFLINE.getCode().toString());
			elasticService.insertEntityMenu(es);
			log.info("全文数据，书名==>{},章节====>{},排序===>{},入库成功",book.getBookName(),menus.getMenuName(),menus.getDisplay());
		}catch(Exception e) {
			log.error("error,",e);
			throw e;
		}
	}


	private  TProBooks parseMainXpro(File xpro) throws Exception {
		ImportPDF bean = new ImportPDF();
		try{
			SAXReader reader = new SAXReader();
			Document document = reader.read(xpro);
			Element root = document.getRootElement();// 获取根元素
			Element bookE = root.element("书籍");
			if (bookE == null) {
				throw new Exception("<书籍>不存在");
			}
			Element bookInfoE = bookE.element("书籍信息");
			if (bookInfoE == null) {
				throw new Exception("<书籍信息>不存在");
			}
			Element bookNameE = bookInfoE.element("书名");
			if (bookNameE != null) {
				bean.setBookName(bookNameE.getText());
			}
			Element coverPathE = bookInfoE.element("封面图");
			if (coverPathE != null) {
				bean.setCoverUrl(coverPathE.getText());
			}
			Element otherinfo = bookInfoE.element("其他信息");
			if (otherinfo != null) {
				String uniqueId = otherinfo.element("文献编号") != null ? otherinfo.element("文献编号").getText() : "";
				String resourceClasses = otherinfo.element("所属卷") != null ? otherinfo.element("所属卷").getText() : "";
				String revision = otherinfo.element("版次") != null ? otherinfo.element("版次").getText() : "";
				String wordNum = otherinfo.element("字数") != null ? otherinfo.element("字数").getText() : "";
				String subjectText = otherinfo.element("主题词-内容") != null ? otherinfo.element("主题词-内容").getText() : "";
				String subjectWord = otherinfo.element("主题词-词频") != null ? otherinfo.element("主题词-词频").getText() : "";
				String bookDesc = otherinfo.element("简介") != null ? otherinfo.element("简介").getText() : "";
				String display = otherinfo.element("排序") != null ? otherinfo.element("排序").getText() : "";
				bean.setUniqueId(uniqueId);
				bean.setResourceClasses(resourceClasses);
				bean.setRevision(revision);
				bean.setWordNum(wordNum.replace("千字",""));
				bean.setSubjectText(subjectText);
				bean.setSubjectWord(subjectWord);
				bean.setBookDesc(bookDesc);
				bean.setDisplay(display);
			}
			Element zereninfo = bookInfoE.element("责任信息");
			if (zereninfo != null) {
				String mainResponsibility = zereninfo.element("主编") != null ? zereninfo.element("主编").getText():"";
				bean.setMainResponsibility(mainResponsibility);
			}
			Element publishinfo = bookInfoE.element("出版信息");
			if (publishinfo != null) {
				String publishDate = publishinfo.element("出版时间") != null ? publishinfo.element("出版时间").getText():"";
				String publisher = publishinfo.element("出版社") != null ? publishinfo.element("出版社").getText():"";
				bean.setPublishDate(publishDate);
				bean.setPublisher(publisher);

			}
			List<String> xmllist = Lists.newArrayList();
			Element files = bookE.element("文件目录");
			if(files==null){
				throw new ServiceException("文件目录不存在");
			}
			Element bufen = files.element("部分");
			if(bufen==null){
				throw new ServiceException("部分不存在");
			}
			List<Element> fs = files.elements();
			for (Element fe : fs) {
				List<Element> _fss = fe.elements();
				for (Element _fe : _fss) {
					String xmlpath = _fe.attributeValue("路径");
					if (StringUtil.isNotEmpty(xmlpath)){
						xmllist.add(FilenameUtils.normalize(xmlpath,true));
					}
				}
			}
			List<File> xmlFileList=new ArrayList<>(xmllist.size());
			//判断文件是否存在
			for(String xml:xmllist){
				String xmlPath=FilenameUtils.normalize(xpro.getParent()+File.separator+xml,true);
				File xmlFile=new File(xmlPath);
				if (xmlFile.exists()){
					xmlFileList.add(xmlFile);
				}else {
					throw new Exception(xml+"不存在");
				}
			}
			bean.setResourceType(ResourceTypeEnum.T.getCode());
			TProBooks books = pdfSynService.checkData(bean, xpro.getParent());
			books.setXmlFiles(xmlFileList);
			return books;
		}catch (Exception e){
			throw new Exception("解析main.xpro失败,"+e.getMessage());
		}
	}

	/**
	 * 删除之前的文件
	 * @param path
	 */
	private void deleteFile(String path){
		if (StringUtil.isNotEmpty(path)){
			path= HuangHeConfig.getUploadPath()+path;
			FileUtils.deleteQuietly(new File(path));
		}
	}
}
