package cn.guliandigital.analysis.service.impl;

import cn.guliandigital.analysis.domain.ImportPDF;
import cn.guliandigital.analysis.service.IAutoPDFSynService;
import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.enums.DataStatusEnum;
import cn.guliandigital.common.enums.DelFlagEnum;
import cn.guliandigital.common.enums.ProStatusEnum;
import cn.guliandigital.common.enums.ResourceTypeEnum;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.pdf.PdfContent;
import cn.guliandigital.common.utils.pdf.PdfUtil;
import cn.guliandigital.common.utils.pinyin.PingYinUtil;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.es.domain.THuangHeEsBook;
import cn.guliandigital.es.domain.THuangHeEsMenu;
import cn.guliandigital.es.service.IElasticService;
import cn.guliandigital.product.book.domain.TBookMenuContentMongo;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.book.service.TBookMenuContentMongoService;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.product.menu.mapper.TProBookMenuMapper;
import cn.guliandigital.product.readhistory.mapper.TUserReadBookMapper;
import cn.guliandigital.product.readhistory.mapper.TUserReadHistoryMapper;
import cn.guliandigital.product.readnotes.mapper.TUserReadNotesMapper;
import cn.guliandigital.product.userbook.mapper.TUserBookshelfMapper;
import cn.guliandigital.storage.log.domain.TTaskLog;
import cn.guliandigital.storage.log.service.ITTaskLogService;
import cn.guliandigital.storage.storage.domain.TTaskSettle;
import cn.guliandigital.storage.storage.service.impl.EncodeFontUtils;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AutoPDFSynServiceImpl implements IAutoPDFSynService {

    @Autowired
    private ITTaskLogService taskLogService;

    @Autowired
    private TProBooksMapper proBooksMapper;

    @Autowired
    private TProBookMenuMapper proBookMenuMapper;

    @Autowired
    private IElasticService elasticService;

    @Autowired
    private ITConfigClassicTreeService configClassicTreeService;


    @Autowired
    private TUserBookshelfMapper tUserBookshelfMapper;

    @Autowired
    private TUserReadHistoryMapper tUserReadHistoryMapper;

    @Autowired
    private TUserReadBookMapper tUserReadBookMapper;

    @Autowired
    private TUserReadNotesMapper tUserReadNotesMapper;

    @Autowired
    private TProBookMenuMapper bookMenuMapper;

    @Autowired
    private EncodeFontUtils encodeFontUtils;

    @Autowired
    private TBookMenuContentMongoService contentMongoService;

    /**
     * 解析全文
     *
     * @param task
     * @param rootFile
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handlePDFBook(TTaskSettle task, File rootFile) {
        log.info("==>开始解析全文数据库文件路径：{}", rootFile.getPath());
        File[] files = rootFile.listFiles();
        //筛选excel文件
        List<File> excelList = Arrays.stream(files).filter(f -> StringUtil.equalsAnyIgnoreCase(
                FilenameUtils.getExtension(f.getName()), "xlsx", "xls")).collect(Collectors.toList());
        List<ImportPDF> importList = new ArrayList<>();
        for (File excel : excelList) {
            try {
                //读取excel文件
                ExcelUtil<ImportPDF> excelUtil = new ExcelUtil<>(ImportPDF.class);
                List<ImportPDF> pdfImportList = excelUtil.importExcel(new FileInputStream(excel));
                importList.addAll(pdfImportList);
            } catch (Exception ignored) {
            }
        }
        task.getAllCount().addAndGet(importList.size());
        //数据校验入库
        for (ImportPDF ipdf : importList) {
            TTaskLog tTaskLog = new TTaskLog();
            tTaskLog.setTaskId(task.getId());
            tTaskLog.setId(IdUtil.simpleUUID());
            tTaskLog.setParseStartTime(new Date());
            tTaskLog.setFileName(ipdf.getBookName());
            tTaskLog.setDataStatus(DataStatusEnum.ING.getCode());//解析状态 1-正在解析 2-解析成功 3-解析失败
            tTaskLog.setDelFlag(0);
            taskLogService.insertTTaskLog(tTaskLog);
            try {
                //校验数据
                ipdf.setResourceType(ResourceTypeEnum.PDF.getCode());
                TProBooks pdfBook = checkData(ipdf,rootFile.getPath());
                String errorMsg = pdfBook.getErrorMsg();
                if (StringUtil.isEmpty(errorMsg)) {
                    //加入可以正常入库的集合做下一步处理
                    //查询是否重复
                    List<TProBooks> bookList = proBooksMapper.selectBookByUniqueId(pdfBook.getUniqueId(), ResourceTypeEnum.PDF.getCode());
                    if (!bookList.isEmpty()) {
                        //删除重复的数据
                        String[] array = bookList.stream().map(TProBooks::getId).toArray(String[]::new);
                        for (TProBooks books : bookList) {
                            elasticService.deleteBook(books.getId());
                            elasticService.deleteMenu(books.getId());
                            contentMongoService.deleteByCondition(books.getId());
                            //删除个人中心的数据
                            tUserReadBookMapper.deleteTUserBookByBookId(books.getId());
                            tUserReadHistoryMapper.deleteTUserReadHistoryByBookId(books.getId());
                            tUserBookshelfMapper.deleteTUserBookshelfByBookId(books.getId());
                            tUserReadNotesMapper.deleteTUserReadNotesByBookId(books.getId());
                            proBookMenuMapper.deleteTProBookMenuByBookId(books.getId());
                            //删除字体包
                            String base = FilenameUtils.normalize(HuangHeConfig.getUploadPath() + File.separator + "fonts");
                            String outPath = FilenameUtils.normalize(base+File.separator+"menu"+File.separator+books.getId());
                            FileUtils.deleteDirectory(new File(outPath));
                            //删除PDF和封面文件
                            deleteFile(books.getPdfPath());
                            deleteFile(books.getCoverUrl());
                        }
                        proBooksMapper.deleteTProBooksByIds(array);
                    }
                    pdfBook.setProStatus(ProStatusEnum.OFFLINE.getCode());
                    pdfBook.setDelFlag(DelFlagEnum.NORMAL.getCode());
                    pdfBook.setCreatebyId(task.getUpdatebyId());
                    pdfBook.setCreatebyName(task.getUpdatebyName());
                    pdfBook.setCreateTime(DateUtil.getCuurentDate());
                    pdfBook.setId(IdUtil.simpleUUID());
                    //保存章节
                    savePDFMenu(pdfBook);
                    pdfBook.setProStatus(ProStatusEnum.ONLINE.getCode());
                    proBooksMapper.insertTProBooks(pdfBook);
                    //添加Es
                    THuangHeEsBook es = new THuangHeEsBook();
                    BeanUtils.copyProperties(pdfBook, es);
                    es.setResId(IdUtil.simpleUUID());
                    es.setBookId(pdfBook.getId());
                    es.setCreateTime(DateUtil.getCuurentDate());
                    es.setProStatus(ProStatusEnum.ONLINE.getCode().toString());
                    elasticService.insertEntityBook(es);
                    //上架es
                    elasticService.editESUp(pdfBook.getId());
                    //校验通过
                    tTaskLog.setDataStatus(DataStatusEnum.SUCCESS.getCode());
                } else {
                    deleteFile(pdfBook.getCoverUrl());
                    deleteFile(pdfBook.getPdfPath());
                    tTaskLog.setDataStatus(DataStatusEnum.LOST.getCode());
                    tTaskLog.setErrorMsg(errorMsg);
                    task.getErrorCount().incrementAndGet();
                }
            } catch (Exception e) {
                tTaskLog.setDataStatus(DataStatusEnum.LOST.getCode());
                tTaskLog.setErrorMsg(e.getMessage());
                task.getErrorCount().incrementAndGet();
                log.error("解析PDF数据报错===>", e);
            }
            tTaskLog.setParseEndTime(new Date());
            taskLogService.updateTTaskLog(tTaskLog);
        }
    }

    @Override
    public void savePDFMenu(TProBooks proBooks) {
        List<PdfContent> contentList = proBooks.getCntentList();
        List<PdfContent> menuList = proBooks.getMenuList();
        if (StringUtil.isNotEmpty(contentList) && StringUtil.isEmpty(menuList)) {
            menuList = new ArrayList<>();
            PdfContent pdfContent = new PdfContent();
            pdfContent.setTitle(proBooks.getBookName());
            pdfContent.setLevel(1);
            pdfContent.setPage(1);
            pdfContent.setId(proBooks.getId());
            menuList.add(pdfContent);
        }
        int startPageNo = 1;
        long display = 0L;
        List<TProBookMenu> pList = new ArrayList<>();
        if (StringUtil.isNotEmpty(menuList) && StringUtil.isNotEmpty(contentList)) {
            //开始解析章节
            int menuSize = menuList.size();
            for (int i = 0; i < menuSize; i++) {
                display++;
                PdfContent menu = menuList.get(i);
                TProBookMenu bookMenu = new TProBookMenu();
                bookMenu.setBookId(proBooks.getId());
                bookMenu.setId(IdUtil.simpleUUID());
                bookMenu.setMenuName(menu.getTitle());
                startPageNo = menu.getPage();
                bookMenu.setStartPageNo(startPageNo);
                bookMenu.setDelFlag(DelFlagEnum.NORMAL.getCode());
                bookMenu.setCreatebyId(proBooks.getUpdatebyId());
                bookMenu.setCreatebyName(proBooks.getCreatebyName());
                bookMenu.setCreateTime(DateUtil.getCuurentDate());

                List<PdfContent> collect = null;
                long finalStartPageNo = startPageNo;
                StringBuilder text = new StringBuilder();
                if (i + 1 == menuSize) {
                    //这是最后的章节丛起始页到最后
                    collect = contentList.stream().filter(f -> f.getPage() >= finalStartPageNo).collect(Collectors.toList());
                } else {
                    //获取下一个章节的起始页
                    Integer page = menuList.get(i + 1).getPage();
                    collect = contentList.stream().filter(f -> f.getPage() >= finalStartPageNo &&
                            f.getPage() < page).collect(Collectors.toList());
                }
                if (StringUtil.isNotEmpty(collect)) {
                    for (PdfContent c : collect) {
                        text.append(PdfUtil.removePageNumber(c.getContent()));
                    }
                }
                //确定章节层级和完全目录名称
                Integer level = menu.getLevel();
                bookMenu.setLevel(level);
                if (level == 1) {
                    bookMenu.setPid("0");
                    if (menu.getId().equals(proBooks.getId())) {
                        bookMenu.setFullMenuName("");
                    } else {
                        bookMenu.setFullMenuName(menu.getTitle());
                    }
                    bookMenu.setDisplay(display);
                    pList.clear();
                    pList.add(bookMenu);
                } else {
                    //将大于等于同等级的目录去掉
                    List<TProBookMenu> levelList = pList.stream()
                            .filter(f -> f.getLevel() >= level).collect(Collectors.toList());
                    pList.removeAll(levelList);
                    //寻找上一级目录,升序排序
                    pList.sort(new Comparator<TProBookMenu>() {
                        @Override
                        public int compare(TProBookMenu o1, TProBookMenu o2) {
                            return Integer.compare(o1.getLevel(), o2.getLevel());
                        }
                    });
                    //获取上级id
                    String pid = pList.get(pList.size() - 1).getId();
                    bookMenu.setPid(pid);
                    //获取完全章节名
                    StringBuilder full = new StringBuilder();
                    for (TProBookMenu pm : pList) {
                        full.append(pm.getMenuName()).append(">");
                    }
                    bookMenu.setFullMenuName(full.toString() + menu.getTitle());
                    bookMenu.setDisplay(display);
                    //加入到集合
                    pList.add(bookMenu);
                }
                //添加章节
                bookMenuMapper.insertTProBookMenu(bookMenu);
                //添加章节内容
                TBookMenuContentMongo contentMongo=new TBookMenuContentMongo();
                contentMongo.setMenuId(bookMenu.getId());
                contentMongo.setBookId(proBooks.getId());
                contentMongo.setId(IdUtil.simpleUUID());
                contentMongo.setDisplay(bookMenu.getDisplay());
                contentMongo.setBookName(proBooks.getBookName());
                contentMongo.setMenuName(bookMenu.getMenuName());
                contentMongo.setStartPageNo(bookMenu.getStartPageNo());
                contentMongo.setLevel(bookMenu.getLevel());
                contentMongo.setReadMode(proBooks.getResourceType());
                contentMongo.setCreateTime(DateUtil.getCuurentTime());
                //加密
                String fullText=null;
                if (StringUtil.isNotEmpty(text.toString())) {
                    contentMongo.setBookContent(text.toString());
                    Map<String, Object> data = encodeFontUtils.encodeFonts(contentMongo.getBookContent(), proBooks.getId(),bookMenu.getId());
                    List<String> fontList = (List<String>) data.get("fontPaths");
                    Map<String, String> mappingMap = (Map<String, String>) data.get("mappingMap");
                    contentMongo.setBookContentEnc(data.get("contentEnc").toString());
                    contentMongo.setEncFontPaths(String.join(",",fontList));
                    contentMongo.setEncWordMappingMap(JSONObject.toJSONString(mappingMap));
                    fullText = contentMongo.getBookContent().replaceAll("<{1}[^<>]*>{1}", "");
                    fullText = StringUtil.replaceAllBlank(fullText);
                }
                //添加章节内容
                contentMongoService.add(contentMongo);
                //添加Es
                THuangHeEsMenu es = new THuangHeEsMenu();
                BeanUtils.copyProperties(proBooks, es);
                es.setResId(IdUtil.simpleUUID());
                es.setMenuId(bookMenu.getId());
                es.setBookId(proBooks.getId());
                es.setMenuName(bookMenu.getMenuName());
                es.setDisplay(Math.toIntExact(bookMenu.getDisplay()));
                es.setFullMenuName(bookMenu.getFullMenuName());
                es.setStartPageNo(bookMenu.getStartPageNo());
                es.setFullText(fullText);
                es.setProStatus(ProStatusEnum.OFFLINE.getCode().toString());
                elasticService.insertEntityMenu(es);
                log.info("PDF资源，书名===>{},章节名===>{},排序===>{}，入库成功", proBooks.getBookName(), bookMenu.getMenuName(), bookMenu.getDisplay());
            }
        }
    }
    @Override
    public TProBooks checkData(ImportPDF ipdf,String rootPath) {
        TProBooks pdfBook = new TProBooks();
        StringBuilder errorMsg = new StringBuilder();
        //校验书名
        if (StringUtil.isEmpty(ipdf.getBookName())) {
            errorMsg.append("书名不能为空;");
        } else {
            if (ipdf.getBookName().length() > 255) {
                errorMsg.append("书名不能超过255个字符;");
            } else {
                pdfBook.setBookName(ipdf.getBookName());
            }
        }
        //校验文献编号
        if (StringUtil.isEmpty(ipdf.getUniqueId())) {
            errorMsg.append("文献编号不能为空;");
        }else {
            if (ipdf.getUniqueId().length() > 100) {
                errorMsg.append("文献编号不能超过100个字符;");
            }else {
                pdfBook.setUniqueId(ipdf.getUniqueId());
            }
        }
        //校验主编
        if (StringUtil.isEmpty(ipdf.getMainResponsibility())) {
            errorMsg.append("主编不能为空;");
        } else {
            if (ipdf.getMainResponsibility().length() > 100) {
                errorMsg.append("作者不能超过100个字符;");
            } else {
                pdfBook.setMainResponsibility(ipdf.getMainResponsibility());
            }
        }
        //校验所属卷
        if (StringUtil.isEmpty(ipdf.getResourceClasses())) {
            errorMsg.append("所属卷不能为空;");
        } else {
            //解析分类
            HashMap<String, String> map = getClassicId(ipdf.getResourceClasses(), "2ba44c2f8aa64de1a60eed30e1627225");
            if (StringUtil.isEmpty(map.get("id"))) {
                errorMsg.append("所属卷不存在;");
            } else {
                pdfBook.setResourceClassesId(map.get("id"));
                pdfBook.setResourceClasses(map.get("name"));
            }
        }
        //校验出版社
        if (StringUtil.isEmpty(ipdf.getPublisher())) {
            errorMsg.append("出版社不能为空;");
        } else {
            if (ipdf.getPublisher().length() > 255) {
                errorMsg.append("出版社不能超过255个字符;");
            } else {
                pdfBook.setPublisher(ipdf.getPublisher());
            }
        }
        //校验出版日期
        if (StringUtil.isNotEmpty(ipdf.getPublishDate())) {
            if (ipdf.getPublishDate().length() > 255) {
                errorMsg.append("出版日期不能超过255个字符;");
            } else {
                pdfBook.setPublishDate(ipdf.getPublishDate());
            }
        }
        //校验版次
        if (StringUtil.isNotEmpty(ipdf.getRevision())) {
            if (ipdf.getRevision().length() > 255) {
                errorMsg.append("版次不能超过255个字符;");
            } else {
                pdfBook.setRevision(ipdf.getRevision());
            }
        }
        //校验字数
        if (StringUtil.isEmpty(ipdf.getWordNum())) {
            errorMsg.append("字数（千字）不能为空;");
        }else {
            try {
                pdfBook.setWordNum(Long.parseLong(ipdf.getWordNum()));
            } catch (Exception e) {
                errorMsg.append("字数（千字）不是整数;");
            }
        }
        //校验排序
        if (StringUtil.isNotEmpty(ipdf.getDisplay())) {
            try {
                int display = Integer.parseInt(ipdf.getDisplay());
                pdfBook.setDisplay(display);
            } catch (Exception ignored) {}
        }
        if (ResourceTypeEnum.PDF.getCode().equals(ipdf.getResourceType())){
            //校验PDF文件路径
            if (StringUtil.isEmpty(ipdf.getPdfPath())){
                errorMsg.append("PDF文件路径不能为空");
            }else {
                //校验上传pdf文件
                String path = rootPath + File.separator + ipdf.getPdfPath();
                String extension = FilenameUtils.getExtension(path);
                if (!extension.equalsIgnoreCase("pdf")){
                    errorMsg.append("PDF文件路径不是pdf文件格式");
                }else {
                    File file=new File(path);
                    if (!file.exists()){
                        errorMsg.append("未找到pdf文件");
                    }else {
                        try {
                            String pdfPath=FilenameUtils.normalize(HuangHeConfig.getUploadPath()+File.separator+"pdf",true);
                            new File(pdfPath).mkdirs();
                            pdfPath=pdfPath+File.separator+ IdUtils.simpleUUID()+ ".pdf";
                            File pdfFile = new File(pdfPath);
                            FileUtils.copyFile(file,pdfFile);
                            String filePath=pdfPath.replace(FilenameUtils.normalize(HuangHeConfig.getUploadPath(),true),"");
                            pdfBook.setPdfPath(FilenameUtils.normalize(filePath,true));
                            pdfBook.setPdfName(file.getName());
                            //读取pdf目录和内容
                            List<PdfContent> cntentList = PdfUtil.getPdfContent(pdfFile);
                            List<PdfContent> menuList = PdfUtil.getPdfMenu(pdfFile);
                            pdfBook.setCntentList(cntentList);
                            pdfBook.setMenuList(menuList);
                        }catch (Exception e){
                            errorMsg.append("读取PDF异常");
                        }
                    }
                }
            }
        }
        //校验封面路径
        if (StringUtil.isEmpty(ipdf.getCoverUrl())){
            errorMsg.append("封面路径不能为空");
        }else {
            //校验上传封面
            String path = rootPath + File.separator + ipdf.getCoverUrl();
            String extension = FilenameUtils.getExtension(path);
            if (!StringUtil.equalsAnyIgnoreCase(extension,"jpg","png")){
                errorMsg.append("封面路径不是png/jpg文件格式");
            }else {
                File file=new File(path);
                if (!file.exists()){
                    errorMsg.append("未找到封面图像文件");
                }else {
                    try {
                        String coverPath=FilenameUtils.normalize(HuangHeConfig.getUploadPath()+File.separator+"cover",true);
                        new File(coverPath).mkdirs();
                        coverPath=coverPath+File.separator+ IdUtils.simpleUUID()+ "."+FilenameUtils.getExtension(file.getName());
                        FileUtils.copyFile(file,new File(coverPath));
                        String sourcePath=coverPath.replace(FilenameUtils.normalize(HuangHeConfig.getUploadPath(),true),"");
                        pdfBook.setCoverUrl(FilenameUtils.normalize(sourcePath,true));
                        pdfBook.setCoverName(file.getName());
                    }catch (Exception e){
                        errorMsg.append("封面图片文件已损坏");
                    }
                }
            }
        }
        pdfBook.setSubjectWord(ipdf.getSubjectWord());
        pdfBook.setSubjectText(ipdf.getSubjectText());
        pdfBook.setBookDesc(ipdf.getBookDesc());
        pdfBook.setResourceType(ipdf.getResourceType());
        pdfBook.setErrorMsg(errorMsg.toString());
        return pdfBook;
    }

    //查询分类
    private HashMap<String, String> getClassicId(String name, String rootId) {
        HashMap<String, String> map = new HashMap<>();
        //查找制定机关分类
        String[] split = name.trim().split("/");
        String resourceClasses = split[split.length - 1];
        TConfigClassicTree tree = new TConfigClassicTree();
        tree.setClassicId(rootId);
        tree.setTreeName(resourceClasses);
        //将繁体转为简体
        String classesNameSimple = ZhConverterUtil.convertToSimple(tree.getTreeName());
        tree.setTreeName(null);
        tree.setTreeNameS(classesNameSimple);
        List<TConfigClassicTree> queryClassList = configClassicTreeService.selectTConfigClassicTreeList(tree);
        //校验xpro中的资源分类是否存在
        if (queryClassList.size() > 0) {
            TConfigClassicTree queryTree = queryClassList.get(0);
            String id = queryTree.getId();
            String pid = queryTree.getTreePid();
            String treeName = queryTree.getTreeName();
            map.put("id", id);
            //寻找父级
            if (StringUtil.isNotEmpty(pid)) {
                List<TConfigClassicTree> trees = configClassicTreeService.selectTConfigClassicTreeById(pid);
                if (StringUtil.isNotEmpty(trees)) {
                    treeName = trees.get(0).getTreeName() + "/" + treeName;
                }
            }
            map.put("name", treeName);
        }
        return map;
    }
    /**
     * 删除之前的文件
     * @param path
     */
    private void deleteFile(String path){
        if (StringUtil.isNotEmpty(path)){
            path= HuangHeConfig.getUploadPath()+path;
            FileUtils.deleteQuietly(new File(path));
        }
    }
}
