package cn.guliandigital.analysis.domain;

import cn.guliandigital.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入pdf
 */
@Data
public class ImportPDF implements Serializable {

    @Excel(name = "书名")
    private String bookName;

    @Excel(name = "文献编号")
    private String uniqueId;

    @Excel(name = "所属卷")
    private String resourceClasses;

    @Excel(name = "主编")
    private String mainResponsibility;

    @Excel(name = "出版社")
    private String publisher;

    @Excel(name = "出版时间")
    private String publishDate;

    @Excel(name = "版次")
    private String revision;

    @Excel(name = "字数（千字）")
    private String wordNum;

    @Excel(name = "排序")
    private String display;

    @Excel(name = "PDF文件路径")
    private String pdfPath;

    @Excel(name = "封面路径")
    private String coverUrl;

    @Excel(name = "简介")
    private String bookDesc;

    @Excel(name = "主题词-词频")
    private String subjectWord;

    @Excel(name = "主题词-内容")
    private String subjectText;

    //资源类型 T-全文，PDF-pdf
    private String resourceType;
}
