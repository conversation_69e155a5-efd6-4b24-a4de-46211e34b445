package cn.guliandigital.product.readnotes.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.readnotes.domain.TUserReadNotes;
import cn.guliandigital.product.readnotes.mapper.TUserReadNotesMapper;
import cn.guliandigital.product.readnotes.service.ITUserReadNotesService;

/**
 * 用户阅读笔记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-28
 */
@Service
public class TUserReadNotesServiceImpl implements ITUserReadNotesService 
{
    @Autowired
    private TUserReadNotesMapper tUserReadNotesMapper;

    /**
     * 查询用户阅读笔记
     * 
     * @param id 用户阅读笔记ID
     * @return 用户阅读笔记
     */
    @Override
    public TUserReadNotes selectTUserReadNotesById(String id)
    {
        return tUserReadNotesMapper.selectTUserReadNotesById(id);
    }

    /**
     * 查询用户阅读笔记列表
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 用户阅读笔记
     */
    @Override
    public List<TUserReadNotes> selectTUserReadNotesList(TUserReadNotes tUserReadNotes)
    {
        return tUserReadNotesMapper.selectTUserReadNotesList(tUserReadNotes);
    }

    /**
     * 新增用户阅读笔记
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 结果
     */
    @Override
    public int insertTUserReadNotes(TUserReadNotes tUserReadNotes)
    {
        tUserReadNotes.setId(IdUtils.simpleUUID());
        tUserReadNotes.setDelFlag(0);
        tUserReadNotes.setCreateTime(DateUtil.getCuurentDate());
        return tUserReadNotesMapper.insertTUserReadNotes(tUserReadNotes);
    }

    /**
     * 修改用户阅读笔记
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 结果
     */
    @Override
    public int updateTUserReadNotes(TUserReadNotes tUserReadNotes)
    {
        tUserReadNotes.setUpdateTime(DateUtil.getCuurentDate());
        return tUserReadNotesMapper.updateTUserReadNotes(tUserReadNotes);
    }
    @Override
    public int updateDelFalgByBookId(String  bookId)
    {
        return tUserReadNotesMapper.updateDelFalgByBookId(bookId);
    }

    /**
     * 批量删除用户阅读笔记
     * 
     * @param ids 需要删除的用户阅读笔记ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadNotesByIds(String[] ids)
    {
        return tUserReadNotesMapper.deleteTUserReadNotesByIds(ids);
    }

    /**
     * 删除用户阅读笔记信息
     * 
     * @param id 用户阅读笔记ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadNotesById(String id)
    {
        return tUserReadNotesMapper.deleteTUserReadNotesById(id);
    }
}
