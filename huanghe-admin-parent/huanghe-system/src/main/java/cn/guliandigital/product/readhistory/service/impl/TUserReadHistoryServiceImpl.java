package cn.guliandigital.product.readhistory.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.readhistory.domain.TUserReadBook;
import cn.guliandigital.product.readhistory.domain.TUserReadHistory;
import cn.guliandigital.product.readhistory.mapper.TUserReadBookMapper;
import cn.guliandigital.product.readhistory.mapper.TUserReadHistoryMapper;
import cn.guliandigital.product.readhistory.service.ITUserReadHistoryService;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户阅读历史Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
@Slf4j
@Service
public class TUserReadHistoryServiceImpl implements ITUserReadHistoryService 
{
    @Autowired
    private TUserReadHistoryMapper tUserReadHistoryMapper;
    
    @Autowired
    private TUserReadBookMapper tUserReadBookMapper;
    
    private static String LOCK = "LOCK";

    /**
     * 查询用户阅读历史
     * 
     * @param id 用户阅读历史ID
     * @return 用户阅读历史
     */
    @Override
    public TUserReadHistory selectTUserReadHistoryById(String id)
    {
        return tUserReadHistoryMapper.selectTUserReadHistoryById(id);
    }

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史
     */
    @Override
    public List<TUserReadHistory> selectTUserReadHistoryList(TUserReadHistory tUserReadHistory)
    {
        return tUserReadHistoryMapper.selectTUserReadHistoryList(tUserReadHistory);
    }

    /**
     * 新增用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    @Override
    public int insertTUserReadHistory(TUserReadHistory tUserReadHistory, boolean savebook)
    {
    	
    	if(Strings.isNullOrEmpty(tUserReadHistory.getId())) {
    		tUserReadHistory.setId(IdUtils.simpleUUID());
    	}
        tUserReadHistory.setCreateTime(DateUtil.getCuurentDate());
        
        tUserReadHistoryMapper.insertTUserReadHistory(tUserReadHistory);
        synchronized(LOCK) {
	        if(savebook) {
		        //汇总到book表中
		        TUserReadBook query = new TUserReadBook();
		        query.setBookId(tUserReadHistory.getBookId());        
		        query.setCreatebyId(tUserReadHistory.getCreatebyId());
		        List<TUserReadBook> list = tUserReadBookMapper.selectList(query);
		        if(list != null && list.size() > 0) {
		        	TUserReadBook book = list.get(0);
		        	
		        	TUserReadBook update = new TUserReadBook();
		        	update.setId(book.getId());
		        	update.setMenuId(tUserReadHistory.getMenuId());
		        	update.setMenuName(tUserReadHistory.getMenuName());
		        	update.setMenuPath(tUserReadHistory.getMenuPath());
		        	update.setUpdateTime(new Date());
		        	update.setUpdatebyId(tUserReadHistory.getUpdatebyId());
		        	update.setUpdatebyName(tUserReadHistory.getUpdatebyName());
		        	update.setReadMode(tUserReadHistory.getReadMode());
		        	update.setPageNo(tUserReadHistory.getPageNo());
		        	tUserReadBookMapper.updateTUserReadBook(update);
		        	log.info("==>更新TUserReadBook成功....");
		        }else {
		        	TUserReadBook insert = new TUserReadBook();
		        	BeanUtils.copyProperties(tUserReadHistory, insert);
		        	insert.setCreateTime(new Date());
		        	insert.setCreatebyId(tUserReadHistory.getCreatebyId());
		        	insert.setCreatebyName(tUserReadHistory.getCreatebyName());
		        	insert.setUpdateTime(new Date());
		        	insert.setUpdatebyId(tUserReadHistory.getCreatebyId());
		        	insert.setUpdatebyName(tUserReadHistory.getCreatebyName());
		        	insert.setId(IdUtils.simpleUUID());
		        	insert.setReadMode(tUserReadHistory.getReadMode());
		        	insert.setPageNo(tUserReadHistory.getPageNo());
		        	tUserReadBookMapper.insertTUserReadBook(insert);
		        	log.info("==>新增TUserReadBook成功....");
		        }
	        }
    	}
        
        return 1;
    }

    /**
     * 修改用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    @Override
    public int updateTUserReadHistory(TUserReadHistory tUserReadHistory)
    {
        tUserReadHistory.setUpdateTime(DateUtil.getCuurentDate());
        return tUserReadHistoryMapper.updateTUserReadHistory(tUserReadHistory);
    }

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的用户阅读历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadHistoryByIds(String[] ids)
    {
        return tUserReadHistoryMapper.deleteTUserReadHistoryByIds(ids);
    }

    /**
     * 删除用户阅读历史信息
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadHistoryById(String id)
    {
        return tUserReadHistoryMapper.deleteTUserReadHistoryById(id);
    }
}
