package cn.guliandigital.product.book.domain;

import java.util.List;
import java.util.Map;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;


/**
 * 章节内容对应表
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Document(collection = "t_huabghe_menu_content")
public class TBookMenuContentMongo {

	/**
	 * 资源ID
	 */
	@Id
	private String id;

	/**
	 * 图书ID
	 */
	private String bookId;
	
	/**
	 * 书名
	 */
	private String bookName;

	/**
	 * 章节名
	 */
	private String menuName;

	/**
	 * 章节ID
	 */
	private String menuId;

	private Integer level;//级别

	/**
	 * 原始正文
	 */
	private String bookContent;
	
	/**
	 * 密文
	 */
	private String bookContentEnc;
	
	/**
	 * 排序(从1开始)
	 */
	private Long display;

	/** 起始页码 */
	private Integer startPageNo;
	
	/**
	 * 字体链接
	 */
	private String encFontPaths;

	/**
	 * 创建时间
	 */	
	private String createTime;

	//资源类型 资源类型,T-全文，PDF-pdf
	private String readMode;


	private String encWordMappingMap;

	
}
