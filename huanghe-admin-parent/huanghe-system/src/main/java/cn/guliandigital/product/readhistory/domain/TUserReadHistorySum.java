package cn.guliandigital.product.readhistory.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 用户阅读历史对象 t_user_read_history_sum
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class TUserReadHistorySum extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 图书ID */
    private String bookId;

    public Long getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(Long quoteCount) {
        this.quoteCount = quoteCount;
    }

    public Long getCopyCount() {
        return copyCount;
    }

    public void setCopyCount(Long copyCount) {
        this.copyCount = copyCount;
    }

    //引用次数
    private Long quoteCount;
    //复制次数
    private Long copyCount;
    /** 阅读时长 单位秒 */
    private Long readTime;

    /** 阅读时长 单位秒 */
    private Long readPeoples;
    
    private Long searchCount;
    
    private Long visitCount;

    private Long noteCount;

    /** 阅读日期 */
    private String readDate;
    
    /** 机构ID */
    private String orgId;
    
    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /**  */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    private Integer delFlag;

    /** 主要责任人 */
    private String mainResponsibility;

    /** 书名 */
    private String bookName;
    
    private String thumbCoverUrl;
    
    private String publisher;
    
    private String resourceType;
    
    private String resourceClasses;
    
    private String dbName;
    
    /** 阅读时长 单位秒 */
    private String readTimeStr;
    
}
