package cn.guliandigital.product.readhistory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.product.readhistory.domain.TUserReadBook;
import cn.guliandigital.product.readhistory.mapper.TUserReadBookMapper;
import cn.guliandigital.product.readhistory.service.ITUserReadBookService;

/**
 * 用户阅读进度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-02-10
 */
@Service
public class TUserReadBookServiceImpl implements ITUserReadBookService 
{
    @Autowired
    private TUserReadBookMapper tUserReadBookMapper;

    /**
     * 查询用户阅读进度
     * 
     * @param id 用户阅读进度ID
     * @return 用户阅读进度
     */
    @Override
    public TUserReadBook selectTUserReadBookById(String id)
    {
        return tUserReadBookMapper.selectTUserReadBookById(id);
    }

    /**
     * 查询用户阅读进度列表
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 用户阅读进度
     */
    @Override
    public List<TUserReadBook> selectTUserReadBookList(TUserReadBook tUserReadBook)
    {
        return tUserReadBookMapper.selectTUserReadBookList(tUserReadBook);
    }
    /**
     * 查询用户阅读进度列表 (小程序专用)
     * @param tUserReadBook
     * @return
     */
    @Override
    public List<TUserReadBook> selectTUserReadBookListOrderBy(TUserReadBook tUserReadBook)
    {
        return tUserReadBookMapper.selectTUserReadBookListOrderBy(tUserReadBook);
    }

    /**
     * 新增用户阅读进度
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 结果
     */
    @Override
    public int insertTUserReadBook(TUserReadBook tUserReadBook)
    {
        tUserReadBook.setCreateTime(DateUtil.getCuurentDate());
        return tUserReadBookMapper.insertTUserReadBook(tUserReadBook);
    }

    /**
     * 修改用户阅读进度
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 结果
     */
    @Override
    public int updateTUserReadBook(TUserReadBook tUserReadBook)
    {
        tUserReadBook.setUpdateTime(DateUtil.getCuurentDate());
        return tUserReadBookMapper.updateTUserReadBook(tUserReadBook);
    }

    /**
     * 批量删除用户阅读进度
     * 
     * @param ids 需要删除的用户阅读进度ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadBookByIds(String[] ids)
    {
        return tUserReadBookMapper.deleteTUserReadBookByIds(ids);
    }

    /**
     * 删除用户阅读进度信息
     * 
     * @param id 用户阅读进度ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadBookById(String id)
    {
        return tUserReadBookMapper.deleteTUserReadBookById(id);
    }
}
