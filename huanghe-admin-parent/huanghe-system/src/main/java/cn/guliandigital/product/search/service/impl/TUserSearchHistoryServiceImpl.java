package cn.guliandigital.product.search.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.search.domain.TUserSearchHistory;
import cn.guliandigital.product.search.mapper.TUserSearchHistoryMapper;
import cn.guliandigital.product.search.service.ITUserSearchHistoryService;

/**
 * 用户检索历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-10-08
 */
@Service
public class TUserSearchHistoryServiceImpl implements ITUserSearchHistoryService
{
    @Autowired
    private TUserSearchHistoryMapper tUserSearchHistoryMapper;

    /**
     * 查询用户检索历史
     *
     * @param id 用户检索历史ID
     * @return 用户检索历史
     */
    @Override
    public TUserSearchHistory selectTUserSearchHistoryById(String id)
    {
        return tUserSearchHistoryMapper.selectTUserSearchHistoryById(id);
    }

    /**
     * 查询用户检索历史列表
     *
     * @param tUserSearchHistory 用户检索历史
     * @return 用户检索历史
     */
    @Override
    public List<TUserSearchHistory> selectTUserSearchHistoryList(TUserSearchHistory tUserSearchHistory)
    {
        return tUserSearchHistoryMapper.selectTUserSearchHistoryList(tUserSearchHistory);
    }

    /**
     * 新增用户检索历史
     *
     * @param tUserSearchHistory 用户检索历史
     * @return 结果
     */
    @Override
    public int insertTUserSearchHistory(TUserSearchHistory tUserSearchHistory)
    {
    	if(Strings.isNullOrEmpty(tUserSearchHistory.getId())) {
    		tUserSearchHistory.setId(IdUtils.simpleUUID());
    	}
//    	try {
//	    	if(SecurityUtils.getLoginUser() != null) {
//	    		tUserSearchHistory.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
//	    		tUserSearchHistory.setCreatebyName(SecurityUtils.getUsername());
//	    	}
//    	}catch(Exception e) {
//    		
//    	}
        tUserSearchHistory.setCreateTime(DateUtil.getCuurentDate());
        return tUserSearchHistoryMapper.insertTUserSearchHistory(tUserSearchHistory);
    }

    /**
     * 修改用户检索历史
     *
     * @param tUserSearchHistory 用户检索历史
     * @return 结果
     */
    @Override
    public int updateTUserSearchHistory(TUserSearchHistory tUserSearchHistory)
    {
        tUserSearchHistory.setUpdateTime(DateUtil.getCuurentDate());
        return tUserSearchHistoryMapper.updateTUserSearchHistory(tUserSearchHistory);
    }

    /**
     * 批量删除用户检索历史
     *
     * @param ids 需要删除的用户检索历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserSearchHistoryByIds(String[] ids)
    {
        return tUserSearchHistoryMapper.deleteTUserSearchHistoryByIds(ids);
    }

    /**
     * 删除用户检索历史信息
     *
     * @param id 用户检索历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserSearchHistoryById(String id)
    {
        return tUserSearchHistoryMapper.deleteTUserSearchHistoryById(id);
    }

    @Override
    public int updateStaus(List<TUserSearchHistory> tUserSearchHistories) {
        for (TUserSearchHistory tUserSearchHistory : tUserSearchHistories) {
            String id = tUserSearchHistory.getId();
            String[] ids = id.split(",");
            int count = tUserSearchHistoryMapper.updateStaus(ids);
//            String str = tUserSearchHistory.getId();
//            String[] arr = str.split(",");
            //System.out.println(ids);
        }
        return 1;
    }



    @Override
    public int delBydelFlag(String[] ids) {
        return tUserSearchHistoryMapper.updateStaus(ids);

    }

	@Override
	public List<TUserSearchHistory> selectHotList(TUserSearchHistory tUserSearchHistory) {		
		return tUserSearchHistoryMapper.searchTop(tUserSearchHistory);
	}



}
