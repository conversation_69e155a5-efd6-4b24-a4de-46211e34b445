package cn.guliandigital.product.readhistory.service;

import java.util.List;

import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;

/**
 * 用户阅读历史Service接口
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
public interface ITUserReadHistorySumService 
{
    

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史集合
     */
    List<TUserReadHistorySum> selectTUserReadHistorySumList(TUserReadHistorySum tUserReadHistory);

    /**
     * 新增用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int insertTUserReadHistorySum(TUserReadHistorySum tUserReadHistory);

    /**
     * 修改用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int updateTUserReadHistorySum(TUserReadHistorySum tUserReadHistory);

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistorySumByIds(String[] ids);

    /**
     * 删除用户阅读历史信息
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistorySumById(String id);

    List<TUserReadHistorySum> organCount( TUserReadHistorySum tUserReadHistorySum);



}
