package cn.guliandigital.product.clasic.domain;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 分类树对象 t_config_classic_tree
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
@Data
public class TConfigClassicTree extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 分类ID */
    @Excel(name = "分类ID")
    private String classicId;

    /** 分类树编号 */
    @Excel(name = "分类树编号")
    private String treeCode;
    
    private String treeNameS;
    
    /** 分类树名称 */
    @Excel(name = "分类树名称")
    private String treeName;

    /** 上级分类树ID */
    @Excel(name = "上级分类树ID")
    private String treePid;

    /** 分类树描述 */
    @Excel(name = "分类树描述")
    private String treeDesc;

    /** 排序 */
    @Excel(name = "排序")
    private Long display;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人名称 */
    @Excel(name = "创建人名称")
    private String createbyName;

    @Excel(name = "创建人名称")
    private String lalbel;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setClassicId(String classicId) 
    {
        this.classicId = classicId;
    }

    public String getClassicId() 
    {
        return classicId;
    }
    public void setTreeCode(String treeCode) 
    {
        this.treeCode = treeCode;
    }

    public String getTreeCode() 
    {
        return treeCode;
    }
    public void setTreeName(String treeName) 
    {
        this.treeName = treeName;
    }

    public String getTreeName() 
    {
        return treeName;
    }
    public void setTreePid(String treePid) 
    {
        this.treePid = treePid;
    }

    public String getTreePid() 
    {
        return treePid;
    }
    public void setTreeDesc(String treeDesc) 
    {
        this.treeDesc = treeDesc;
    }

    public String getTreeDesc() 
    {
        return treeDesc;
    }

    public String getTreeNameS() {
        return treeNameS;
    }

    public void setTreeNameS(String treeNameS) {
        this.treeNameS = treeNameS;
    }

    public Long getDisplay() {
        return display;
    }

    public void setDisplay(Long display) {
        this.display = display;
    }

    public void setCreatebyId(String createbyId) {
        this.createbyId = createbyId;
    }

    public String getLalbel() {
        return lalbel;
    }

    public void setLalbel(String lalbel) {
        this.lalbel = lalbel;
    }

    public String getCreatebyId()
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }

    private List<TConfigClassicTree> children = new ArrayList<TConfigClassicTree>();

    public List<TConfigClassicTree> getChildren()
    {
        return children;
    }

    public void setChildren(List<TConfigClassicTree> children)
    {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classicId", getClassicId())
            .append("treeCode", getTreeCode())
            .append("treeName", getTreeName())
            .append("treePid", getTreePid())
            .append("treeDesc", getTreeDesc())
            .append("display", getDisplay())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .toString();
    }
}
