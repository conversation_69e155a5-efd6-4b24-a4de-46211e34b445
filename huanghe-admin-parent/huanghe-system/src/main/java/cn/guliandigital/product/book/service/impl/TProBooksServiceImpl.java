package cn.guliandigital.product.book.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.google.common.base.Strings;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.ProStatusEnum;
import cn.guliandigital.common.enums.PushStatusEnum;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.book.service.IAsyncDatabaseService;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.service.TBookMenuContentMongoService;
import cn.guliandigital.product.book.vo.ClassTreeType;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.mapper.TConfigClassicTreeMapper;
import cn.guliandigital.product.database.domain.TProDatabase;
import cn.guliandigital.product.database.mapper.TProDatabaseMapper;
import cn.guliandigital.product.menu.service.ITProBookMenuService;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品资源管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-09-09
 */
@Slf4j
@Service
public class TProBooksServiceImpl implements ITProBooksService {

	@Autowired
	private TProBooksMapper tProBooksMapper;
	// @Autowired
	// private ITProBooksService itProBooksService;

	@Autowired
	private TProDatabaseMapper tProDatabaseMapper;

	@Autowired
	private TConfigClassicTreeMapper tConfigClassicTreeMapper;

	@Autowired
	private RedisCache redisCache;

	@Autowired
	private TBookMenuContentMongoService tbookMenuContentMongoService;

	@Autowired
	private ITProBookMenuService itProBookMenuService;
	
	@Autowired
	private ElasticsearchTemplate elasticsearchTemplate;
	
	@Autowired
	private IAsyncDatabaseService asyncDatabaseService;

	/**
	 * 查询产品资源管理
	 *
	 * @param
	 * @return 产品资源管理
	 */

	@Override
	public TProBooks selectTProBooksById(String id) {
		return tProBooksMapper.selectTProBooksById(id);
	}

	@Override
	public List<TProBooks> selectTProBooksList(TProBooks tProBooks) {
		List<TProBooks> booksLists = tProBooksMapper.selectTProBooksList(tProBooks);
		// tProBooks.getDbName();
		return booksLists;
	}

	/**
	 * 新增产品资源管理
	 *
	 * @param tProBooks
	 *            产品资源管理
	 * @return 结果
	 */
	@Override
	public int insertTProBooks(TProBooks tProBooks) {
		if (tProBooks != null) {
			if (Strings.isNullOrEmpty(tProBooks.getId())) {
				tProBooks.setId(IdUtils.simpleUUID());
			}
			tProBooks.setDelFlag(0);

			if (Strings.isNullOrEmpty(tProBooks.getCreatebyId())) {
				tProBooks.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
			}
			if (Strings.isNullOrEmpty(tProBooks.getCreatebyName())) {
				tProBooks.setCreatebyName(SecurityUtils.getUsername());
			}
			tProBooks.setCreateTime(DateUtil.getCuurentDate());

			if (StringUtil.isNotEmpty(tProBooks.getResourceClasses())) {
				tProBooks.setResourceClasses(tProBooks.getResourceClasses());
			}
			if (StringUtil.isNotEmpty(tProBooks.getSiClassification())) {
				tProBooks.setSiClassification(tProBooks.getSiClassification());
			}

			tProBooks.setSiClassification(tProBooks.getSiClassification());
			if (StringUtil.isNotEmpty(tProBooks.getRevision())) {
				tProBooks.setRevision(tProBooks.getRevision());
			}
			tProBooks.setSubjectWord(tProBooks.getSubjectWord());
			
			int count1 = tProBooksMapper.insertTProBooks(tProBooks);
			log.info("==>insert book success {}", tProBooks.getUniqueId());
			if (count1 > 0) {
				//更新数据库资源量
				asyncDatabaseService.sumDatabaseCount();
//				updateDatabaseRelate(tProBooks, true);
			}
		}

		return 1;
	}

	@Override
	public void updateDatabaseRelate(TProBooks tProBooks, Boolean ischeck) {
		log.info("==>是否校验数据库与书目关系{}", ischeck);
		// if(ischeck) {
		// //先查询关系是否存在
		// TProDatabaseBooks query = new TProDatabaseBooks();
		// query.setDbId(tProBooks.getDbId());
		// query.setProId(tProBooks.getId());
		// List<TProDatabaseBooks> dblist =
		// tProDatabaseBooksMapper.selectTProDatabaseBooksList(query);
		// //关系不存在
		// if(dblist == null || dblist.size() == 0) {
		// TProDatabaseBooks tProDatabaseBooks = new TProDatabaseBooks();
		// tProDatabaseBooks.setId(IdUtils.simpleUUID());
		// tProDatabaseBooks.setDbId(tProBooks.getDbId());
		// tProDatabaseBooks.setProId(tProBooks.getId());
		// tProDatabaseBooksMapper.insertTProDatabaseBooks(tProDatabaseBooks);
		// }
		// }

		TProDatabase database = new TProDatabase();
//		if(!ischeck){
//			int dbcount = tProBooksMapper.selectCount(tProBooks.getDbId());
//			if(dbcount>0){
//				database.setDbId(tProBooks.getDbId());
//				database.setBookCount(dbcount-1);
//			}
//		}

		int dbcount = tProBooksMapper.selectCount(tProBooks.getDbId());

		database.setDbId(tProBooks.getDbId());
		database.setBookCount(dbcount);
		tProDatabaseMapper.updateTProDatabase(database);
	}

	@Override
	public List<TProBooks> recoursePercent() {
		return tProBooksMapper.recoursePercent();
	}

	/**
	 * 修改产品资源管理
	 * @throws FileNotFoundException 
	 *
	 */
	@Override
	public File buildXlsById() throws FileNotFoundException {
		
		File file = ResourceUtils.getFile("classpath:templates/import.xlsx");
		
		return file;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW, rollbackFor = { RuntimeException.class, Exception.class })
	public int updateTProBooks(TProBooks tProBooks) {
		
		
		//tProBooks.setId(tProBooksOld.getId());
		tProBooks.setUpdateTime(DateUtil.getCuurentDate());
		if (Strings.isNullOrEmpty(tProBooks.getUpdatebyId())) {
			try {
				if(SecurityUtils.getLoginUser() != null) {
					tProBooks.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
				}
			}catch(Exception e) {
				
			}
		}
		if (Strings.isNullOrEmpty(tProBooks.getUpdatebyName())) {
			try {
				tProBooks.setUpdatebyName(SecurityUtils.getUsername());
			}catch(Exception e) {
				
			}
		}

		tProBooks.setDelFlag(tProBooks.getDelFlag());
		tProBooksMapper.updateTProBooks(tProBooks);

		//更新数据库资源量
		asyncDatabaseService.sumDatabaseCount();

		log.info("getResourceClassesId={}", tProBooks.getResourceClassesId());
		log.info("getResourceClasses={}", tProBooks.getResourceClasses());

		String jianti = ZhConverterUtil.convertToSimple(tProBooks.getResourceClasses());
		log.info("jianti={}", jianti);

		tProBooks.setResourceClasses(jianti);
	

//		String[] bookNullFields = BeanUtil.getNullPropertyNames(tProBooks);
//		for (String nullField : bookNullFields) {
//			log.info("==>空值字段是：{}", nullField);
//		}
		

		
		return 1;
	}

	/**
	 * 批量删除产品资源管理
	 *
	 * @param ids
	 *            需要删除的产品资源管理ID
	 * @return 结果
	 */
	@Override
	public int deleteTProBooksByIds(String[] ids) {
		return tProBooksMapper.deleteTProBooksByIds(ids);
	}

	/**
	 * 删除产品资源管理信息
	 *
	 * @param id
	 *            产品资源管理ID
	 * @return 结果
	 */
	@Override
	public int deleteTProBooksById(String id) {
		return tProBooksMapper.deleteTProBooksById(id);
	}

	@Override
	public List<String> findType() {
		return tProBooksMapper.selectType();
	}

	/**
	 * @param booksList:
	 * @param :
	 * @param operName:
	 * <AUTHOR>
	 * @Description
	 * @Date 2020/9/10 17:20
	 **/
	@Override
	public String importBooks(List<TProBooks> booksList, Boolean isUpdateSupport, String operName) {
		int successNum = 0;
		int failureNum = 0;
		String msg = "";
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		if (StringUtil.isNull(booksList) || booksList.size() == 0) {
			failureMsg.append("导入资源不能为空");
			return failureMsg.toString();
		}
		try {
			for (TProBooks tProBooks : booksList) {
				if (tProBooks.getBookName().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
				} else if (tProBooks.getBookName().length() > 200) {
					failureMsg.append("导入资源书名长度不能超过200");
					return failureMsg.toString();
				} else if (tProBooks.getResourceClasses().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
					return failureMsg.toString();
				} else if (tProBooks.getResourceClasses().length() > 100) {
					failureMsg.append("导入资源类型长度不能超过200");
					return failureMsg.toString();
				} else if (tProBooks.getMainResponsibility().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
					return failureMsg.toString();
				} else if (tProBooks.getMainResponsibility().length() > 200) {
					failureMsg.append("导入资源主要责任者长度不能超过200");
					return failureMsg.toString();
				} else if (tProBooks.getPublishDate().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
					return failureMsg.toString();
				} else if (tProBooks.getPublishDate().length() > 200) {
					failureMsg.append("导入资源出版日期不能超过200");
					return failureMsg.toString();
				} else if (tProBooks.getRevision().length() > 100) {
					failureMsg.append("导入资源版次长度不能超过100");
					return failureMsg.toString();
				} else if (tProBooks.getRevision().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
					return failureMsg.toString();
				} else if (tProBooks.getPublisher().isEmpty()) {
					failureMsg.append("您导入的资源存在为空的字段，请检查后重新导入");
					return failureMsg.toString();
				} else if (tProBooks.getPublisher().length() > 100) {
					failureMsg.append("导入资源版次长度不能超过100");

					return failureMsg.toString();
				}

				TProBooks t = tProBooksMapper.selectTProBooksByName(tProBooks.getBookName());

				if (StringUtil.isNull(t)) {
					tProBooks.setCreateBy(operName);
					tProBooks.setBookName(tProBooks.getBookName());
					tProBooks.setResourceClasses(tProBooks.getResourceClasses());
					tProBooks.setSubjectWord(tProBooks.getSubjectWord());
					if (StringUtil.isNotEmpty(tProBooks.getSiClassification())) {
						tProBooks.setSiClassification(tProBooks.getSiClassification());
						String str1 = tProBooks.getSiClassification()
								.substring(tProBooks.getSiClassification().indexOf("/") + 1);

						TConfigClassicTree tConfigClassicTree = tConfigClassicTreeMapper.selectBySiName(str1);
						if (tConfigClassicTree != null) {
							tProBooks.setSiClassificationId(tConfigClassicTree.getId());
						}

					}

					tProBooks.setRevision(tProBooks.getRevision());
					tProBooks.setPublishDate(tProBooks.getPublishDate());
					tProBooks.setMainResponsibility(tProBooks.getMainResponsibility());
					tProBooks.setPublisher(tProBooks.getPublisher());
					tProBooks.setCreateTime(new Date(System.currentTimeMillis()));
					if (tProBooks.getDbName() != null) {
						tProBooks.setDbName(tProBooks.getDbName());
						TProDatabase tProDatabase = tProDatabaseMapper.selecDbId(tProBooks.getDbName());
						if (tProDatabase != null) {
							String dbId = tProDatabase.getDbId();
							tProBooks.setDbId(dbId);
						}

					}

					this.insertTProBooks(tProBooks);
					successNum++;

				} else if (t != null) {
					TProBooks tProBooks1 = tProBooksMapper.selectAll(t);
					if (tProBooks.getDbName() != null) {
						TProDatabase tProDatabase = tProDatabaseMapper.selecDbId(tProBooks.getDbName());
						if (tProDatabase != null) {
							String dbId = tProDatabase.getDbId();
							tProBooks.setId(tProBooks1.getId());
							tProBooks.setDbId(dbId);
						}
					}

					if (tProBooks.getRevision() != null) {
						tProBooks.setRevision(tProBooks.getRevision());
					}
					tProBooks.setSubjectWord(tProBooks.getSubjectWord());
					if (tProBooks.getSiClassification() != null) {
						TConfigClassicTree tConfigClassicTree = tConfigClassicTreeMapper
								.selectBySiName(tProBooks.getSiClassification());
						if (tConfigClassicTree != null) {
							tProBooks.setSiClassification(tProBooks.getSiClassification());
							tProBooks.setSiClassificationId(tConfigClassicTree.getId());
						}

					}
					tProBooks.setId(t.getId());
					this.updateTProBooks(tProBooks);
					successNum++;

				} else {
					failureNum++;
					failureMsg.append("<br/>").append(failureNum).append("、书名： ").append(tProBooks.getBookName());
				}

			}
		} catch (Exception e) {
			failureNum++;
			String aa = "<br/>" + " 导入失败：";
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确");
			return failureMsg.toString();
		} else {
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
		}

		return successMsg.toString();
	}

	// @Override
	// public int updateTProBook(List<TProBooks> tProBooks) {
	// for (TProBooks tProBook : tProBooks) {
	//// String id = tProBook.getId();
	//// String[] ids = id.split(",");
	//// int count = tProBooksMapper.del(ids);
	//// String str = tProBook.getDbId();
	//// String[] arr = str.split(",");
	//// //System.out.println(ids);
	//// if (count > 0) {
	//// TProDatabase tProDatabase = new TProDatabase();
	//// int count2 = tProDatabaseBooksMapper.updateCount2(arr);
	//// if (count2 > 0) {
	//// tProDatabaseBooksMapper.deleteTProDatabaseBooksByIds(ids);
	//// }
	//// }
	// updateTProBooks(tProBook);
	// }
	// return 1;
	// }

	@Override
	public List<ClassTreeType> selectType(ClassTreeType type) {
		List<ClassTreeType> list = tProBooksMapper.selectClassic(type);
		return list;
	}

	@Override
	public List<ClassTreeType> selectFourPart(ClassTreeType type) {
		List<ClassTreeType> list = tProBooksMapper.selectFourPart(type);

		return list;
	}

	/**
	 * @param type:
	 * <AUTHOR>
	 * @Description 后台四部分类
	 * @Date 2020/10/14 17:31
	 **/
	@Override
	public List<ClassTreeType> selectFourPartBehind(ClassTreeType type) {

		List<ClassTreeType> list = tConfigClassicTreeMapper.selectFourPartBehind(type);

		// for (ClassTreeType _type : list) {
		// Integer bookCount =
		// redisCache.getCacheObject(RedisConstants.REDIS_SUM_SIBU_PRIX +
		// _type.getId());
		// if (null != bookCount) {
		// _type.setBookCount(bookCount);
		// } else {
		// _type.setBookCount(0);
		// }
		//
		// if (_type.getChildren().size() == 0) {
		// _type.setChildren(null);
		// }
		// }
		return list;
	}

	@Override
	public List<ClassTreeType> selectOther(ClassTreeType type) {
		List<ClassTreeType> list = tConfigClassicTreeMapper.selectResourceClass(type);
		// for (ClassTreeType _type : list) {
		// log.info(_type.getValue());
		// Integer bookCount =
		// redisCache.getCacheObject(RedisConstants.REDIS_SUM_SIBU_PRIX +
		// _type.getValue());
		// if (null != bookCount) {
		// _type.setBookCount(bookCount);
		// } else {
		// _type.setBookCount(0);
		// }
		// if (_type.getChildren().size() == 0) {
		// _type.setChildren(null);
		// }
		// }
		return list;
	}

	@Override
	public List<TProBooks> selectCount(TProBooks tProBooks) {
		return tProBooksMapper.selectCountClassic(tProBooks);
	}

	/**
	 * @param tProBooks:
	 * <AUTHOR>
	 * @Description 资源统计
	 * @Date 2020/10/13 16:05
	 **/
	@Override
	public List<TProBooks> selectRecourseStatisticsList(TProBooks tProBooks) {
		return tProBooksMapper.selectRecourseStatisticsList(tProBooks);
	}

	@Override
	public TProBooks selectWebTProBooksById(String id) {
		return tProBooksMapper.selectWebTProBooksById(id);
	}

	@Override
	public List<TProDatabase> selectdbName(TProDatabase tProDatabase) {
		return tProBooksMapper.selectDbName(tProDatabase);
	}

	/**
	 * @param
	 * @Description 数据库管理的查看资源
	 * @Date 2020/9/16 17:18
	 **/
	@Override
	public List<TProBooks> selectByDbId(TProBooks tProBooks) {

		return tProBooksMapper.selectView(tProBooks);
	}

	@Override
	public List<TProBooks> selectByDbId(String dbId) {
		return tProBooksMapper.select(dbId);
	}

	@Override
	public List<TProBooks> selectByDbIds(TProBooks tProBooks) {
		return tProBooksMapper.selectsList(tProBooks);
	}

	/**
	 * @param ids:
	 * <AUTHOR>
	 * @Description 下架
	 * @Date 2020/10/14 15:58
	 **/
	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW ,rollbackFor = { RuntimeException.class, Exception.class })
	public int updateStatuOffLine(String[] ids) {

		int count = tProBooksMapper.updateOffStatu(ids);
		//更新数据库资源量
		asyncDatabaseService.sumDatabaseCount();

		return count;
	}

	/**
	 * @param ids:
	 * <AUTHOR>
	 * @Description 上架
	 * @Date 2020/10/14 13:38
	 **/
	@Override
	@Transactional(rollbackFor = { RuntimeException.class, Exception.class },propagation=Propagation.REQUIRES_NEW)
	public int updateStatuOnline(String[] ids) {

		int count = 0;
		for(String bookId : ids) {
			TProBooks update = new TProBooks();
			update.setId(bookId);
			update.setParseStatus(PushStatusEnum.PUSH_STATUS_2.getCode());
			update.setProStatus(ProStatusEnum.ONLINE.getCode());
			update.setUpdateTime(new Date());
			tProBooksMapper.updateTProBooks(update);
			count ++;
		}
		//int count = tProBooksMapper.upStatu(ids);

		return count;

	}

	
	@Transactional(rollbackFor = { RuntimeException.class, Exception.class },propagation=Propagation.REQUIRES_NEW)
	public int updateStatuOnline(TProBooks book) {
		log.info("==>开始上架图书 {} {}", book.getId(), book.getBookName());
		TProBooks update = new TProBooks();
		update.setId(book.getId());
		update.setProStatus(book.getProStatus());
		update.setWordNum(book.getWordNum());
		update.setPicNum(book.getPicNum());
		update.setResourceSize(book.getResourceSize());
		update.setParseStatus(PushStatusEnum.PUSH_STATUS_2.getCode());
		int count = tProBooksMapper.updateTProBooks(update);

		//更新数据库资源量
		asyncDatabaseService.sumDatabaseCount();
				

		return count;

	}
	
	@Override
	@Transactional(rollbackFor = { RuntimeException.class, Exception.class },propagation=Propagation.REQUIRES_NEW)
	public int updateTProBook4Delete(TProBooks tProBooks) {
		String bookId = tProBooks.getId();


		int count = tProBooksMapper.deleteTProBooksById(bookId);

		//更新数据库资源量
		asyncDatabaseService.sumDatabaseCount();

		return count;
	}

	@Override
	public List<TProBooks> check() {
		return tProBooksMapper.check();
	}
	
	@Override
	public void getList(ClassTreeType orgItem, List<ClassTreeType> orgList, String pId) {
		List<ClassTreeType> rellist = orgList.stream().filter(t -> Objects.equals(t.getTreePid(), pId))
				.collect(Collectors.toList());
		orgItem.setChildren(rellist);
		for (ClassTreeType item : rellist) {

			getList(item, orgList, item.getId());
		}
	}



	@Override
	public int selectBooks(String dbId) {
		return tProBooksMapper.selectBooks(dbId);
	}

	@Override
	public int insertBatchTProBooks(List<TProBooks> list) {
		int success = 0;
		try {
			
			for(TProBooks book : list) {
				String id = book.getId();
				book.setDcMetaid(id);
				book.setId(IdUtils.simpleUUID());
				//拷贝封面
				String coverPath = FilenameUtils.normalize(book.getCoverPath(),true);
				if(!Strings.isNullOrEmpty(coverPath)) {
					String uploadpath = FilenameUtils.normalize(HuangHeConfig.getUploadPath(),true);
					String extension = FilenameUtils.getExtension(coverPath);
					String fileName = DateUtil.datePath() + "/" + id + "." + extension;
					String thumbFileName = DateUtil.datePath() + "/thumb_" +id  + "." + extension;
					String picPath = uploadpath + File.separator + fileName;
					picPath = FilenameUtils.normalize(picPath,true);

					File destFile = new File(picPath);
					if (!destFile.getParentFile().exists()) {
						destFile.getParentFile().mkdirs();
					}

					File srcFile = new File(coverPath);
					if(srcFile.exists()) {
						log.info("==>拷贝文件 {} ==> {}", coverPath, picPath);
						FileUtils.copyFile(srcFile, destFile);
						String picPathSave = StringUtil.remove(picPath, uploadpath);
						picPathSave = "/profile/upload/" + picPathSave;
						picPathSave = FilenameUtils.normalize(picPathSave, true);
						book.setCoverUrl(picPathSave);

						//缩略图
						String thumbPath=FilenameUtils.normalize(uploadpath + File.separator + thumbFileName,true);
						File file = new File(coverPath);
						File destThumbFile = new File(thumbPath);
						log.info("图片的大小---=="+file.length());
						log.info("是否大于500kb---=="+(file.length() > 500*1024));
//						if(file.length() > 500*1024) {//大于500K，则压缩
//							Thumbnails.of(coverPath)
//									.scale(0.5f)
//									.outputQuality(0.8f)
//									.toFile(thumbPath);
////							TifCompressPythonUtils.tifCompressHandler(coverPath, thumbPath, 0, 0);
//							log.info("==>重新压缩文件");
//						}else {
							FileUtils.copyFile(srcFile, destThumbFile);
//						}
						if(destThumbFile.exists()){
							log.info("是否复制存在---存在");
						}else{
							log.info("是否复制存在---不存在");

						}
						String thumbPathSave = StringUtil.remove(thumbPath, uploadpath);
						thumbPathSave = "/profile/upload/" + thumbPathSave;
						thumbPathSave = FilenameUtils.normalize(thumbPathSave, true);
						log.info("==>拷贝文件缩略图 {} ==> {}", coverPath, thumbPath);
						book.setThumbCoverUrl(thumbPathSave);

					}else {
						log.info("==>封面图片{} 不存在", srcFile.getAbsolutePath());
					}
				}
				TProBooks query = new TProBooks();
				query.setUniqueId(book.getUniqueId());
				List<TProBooks> _list = tProBooksMapper.selectsAllList(query);
				if(CollectionUtils.isEmpty(_list)) {
					book.setCreateTime(DateUtil.getCuurentDate());
					//默认是2
					book.setOcrVersion("2");
					int c = tProBooksMapper.insertTProBooks(book);
					success = success + c;
					log.info("==>新增成功 {}", book.getDcMetaid());
				}else {
					TProBooks _book = _list.get(0);
					book.setId(_book.getId());
					book.setUpdateTime(DateUtil.getCuurentDate());
					if (StringUtil.isEmpty(_book.getOcrVersion())){
						book.setOcrVersion("2");
					}else{
						book.setOcrVersion(_book.getOcrVersion());
					}
					int u = tProBooksMapper.updateTProBooks(book);
					success = success + u;
					log.info("==>更新成功 {}", book.getDcMetaid());
				}
				
			}
		}catch(Exception e) {
			log.error("error,",e);
		}
		return success;
	}

	@Override
	public List<TProBooks> selectsAllList(TProBooks tProBooks) {		
		return tProBooksMapper.selectsAllList(tProBooks);
	}

	@Override
	public TProBooks selectTProBooksEntity(TProBooks tProBooks) {
		return tProBooksMapper.selectTProBooksEntity(tProBooks);
	}

	@Override
	public List<TProBooks> selectBook(String[] ids) {
		return tProBooksMapper.selectBook(ids);
	}

}