package cn.guliandigital.product.book.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.annotation.Excel;

import lombok.Data;

/**
 * @ClassName Type
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/14 17:30
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ClassTreeType implements Serializable {

    private static final long serialVersionUID = 1L;
  
    public Integer bookCount;
   
    private String label;
    private String value;

    private String labelSim;
    private String valueSim;
    /** 主键 */
    private String id;
    
    private String dbId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private String classicId;

    /** 分类树编号 */
    @Excel(name = "分类树编号")
    private String treeCode;

    /** 分类树名称 */
    @Excel(name = "分类树名称")
    private String treeName;

    /** 上级分类树ID */
    @Excel(name = "上级分类树ID")
    private String treePid;

    /** 分类树描述 */
    @Excel(name = "分类树描述")
    private String treeDesc;

    /** 排序 */
    @Excel(name = "排序")
    private Long display;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人名称 */
    @Excel(name = "创建人名称")
    private String createbyName;

    @Excel(name = "创建人名称")
    private String lalbel;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String classicName;

    /** 分类编号 */
    @Excel(name = "分类编号")
    private String classicCode;

    /** 分类描述 */
    @Excel(name = "分类描述")
    private String classicDesc;

    private Integer level;


    private List<ClassTreeType> children = new ArrayList<ClassTreeType>();


	public List<ClassTreeType> getChildren() {
		return children;
	}


	public void setChildren(List<ClassTreeType> children) {
//		if(children == null) {
//			children = new ArrayList<ClassTreeType>();
//		}
		this.children = children;
	}
  

}
