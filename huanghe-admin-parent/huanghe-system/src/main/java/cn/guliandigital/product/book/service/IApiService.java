package cn.guliandigital.product.book.service;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.HttpClient;
import org.dom4j.DocumentException;

import java.io.IOException;

public interface IApiService {
    /**
     * 调用接口
     * @param params
     * @param appKey
     * @param appSecrect
     * @param url
     * @return
     */
    JSONObject getCallInterface(HttpClient client,String params, String appKey, String appSecrect, String url);

    /**
     * 自动标点
     * @param inputPath 输入路径
     * @param outPath 输出路径
     */
    void punRecognition(String inputPath, String outPath) throws Exception;

    /**
     * 生成xml文件
     * @param path
     * @param xmlStr
     */
    void generateXmlFile(String path,String xmlStr,String xmlName)throws IOException, DocumentException;

    /**
     * 繁简转换
     * @param inputPath 输入路径
     * @param outPath 输出路径
     * @param outPath transMode 转换模型 1-转简体 2-转繁体 3-筛简体 4-筛繁体
     */
    void getT2s(String inputPath, String outPath,Integer transMode) throws Exception;
}
