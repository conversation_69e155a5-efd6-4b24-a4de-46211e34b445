
package cn.guliandigital.product.book.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;
import com.mongodb.client.result.DeleteResult;

import cn.guliandigital.common.utils.MongoBulidUtils;
import cn.guliandigital.product.book.domain.TBookMenuContentMongo;
import cn.guliandigital.product.book.service.TBookMenuContentMongoService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TBookMenuContentMongoServiceImpl implements TBookMenuContentMongoService {

	@Autowired  
	private MongoTemplate mongoTemplate;

	/**
	 * 新增一条记录
	 */
	@Override
	public void add(TBookMenuContentMongo t) {		
		mongoTemplate.insert(t);
	}

	/**
	 * 根据ID删除单条数据
	 */
	@Override
	public void deleteById(String ids) {		
		if (Strings.isNullOrEmpty(ids)) {  
            return;  
        }  
        for (String id : ids.split(",")) {  
            Query query = new Query(Criteria.where("_id").is(id));  
            DeleteResult result = this.mongoTemplate.remove(query, this.getEntityClass());
            if(result.getDeletedCount()>0){
            	log.info("删除成功......");
            }
        }  
	}

	/**
	 * 根据ID查询单条数据
	 */
	@Override
	public TBookMenuContentMongo findById(String id) {
        return this.mongoTemplate.findById(id, this.getEntityClass());
	}

	@Override
	public void updateById(TBookMenuContentMongo t){
		if(t == null){
			throw new RuntimeException("Entity is null!");
		}
		try{
			Update update = new Update();
			MongoBulidUtils.buildUpdate(update, t);
			if(MongoBulidUtils.isEmpty(update)) {
				throw new RuntimeException("Updated data could not be found!");
			}
			Object primaryKey = MongoBulidUtils.getPrimaryKey(t);
			if(null == primaryKey) {
				throw new RuntimeException("The primary key @Id not be found or is null!");
			}
			Query query = new Query(); 
			query.addCriteria(Criteria.where("_id").is(primaryKey));
			this.mongoTemplate.updateFirst(query, update, this.getEntityClass());  
		}catch(RuntimeException e){
			log.error("异常",e);
			throw e;
		}catch(Exception e){
			log.error("异常",e);
			throw new RuntimeException(e);
		}
	}

	@Override
	public List<TBookMenuContentMongo> findListByCondition(Map<String, Object> condition) {
		Query query = new Query();
		// 处理约定后缀为Key的条件
		MongoBulidUtils.buildQuery(query, condition, getEntityClass(), true);
		// Other
		// ...

		Order order = null;
		if (condition.get("order") != null) {
			String query_order = condition.get("order").toString();
			if (!Strings.isNullOrEmpty(query_order)) {
				order = new Order(Direction.ASC, query_order);
			} else {
				order = new Order(Direction.ASC, "createTime");
			}
		} else {
			order = new Order(Direction.ASC, "createTime");
		}
		//query.with(Sort.by(order));//列表不用排序，影响性能

		return this.mongoTemplate.find(query, this.getEntityClass());
	}
		 
	/**
	 * 返回实体
	 */
	public Class<TBookMenuContentMongo> getEntityClass() {
		return TBookMenuContentMongo.class;
	}

	@Override
	public void insertOrUpdate(TBookMenuContentMongo t) {
		mongoTemplate.save(t);		
	}

	@Override
	public TBookMenuContentMongo findByCondition(Map<String, Object> condition) {
		Query query = new Query();
		// 处理约定后缀为Key的条件
		MongoBulidUtils.buildQuery(query, condition, getEntityClass(), true);
		Order order = null;
		if (condition.get("order") != null) {
			String query_order = condition.get("order").toString();
			if(condition.get("sort").equals("asc")) {
				order = new Order(Direction.ASC, query_order);
			}else {
				order = new Order(Direction.DESC, query_order);
			}			
			query.with(Sort.by(order));//列表不用排序，影响性能
		} 
		
		return this.mongoTemplate.findOne(query, getEntityClass());
	}

	@Override
	public int deleteByCondition(String bookId) {
		if(!Strings.isNullOrEmpty(bookId)) {
			Query query = new Query(Criteria.where("bookId").is(bookId));
			DeleteResult result = this.mongoTemplate.remove(query, this.getEntityClass());
			if(result.getDeletedCount()>0){
				log.info("删除{}条成功......",result.getDeletedCount());
			}
		}
		return 0;
	}
}
