package cn.guliandigital.product.clasic.service;

import java.util.List;

import cn.guliandigital.common.core.domain.TreeSelect;
import cn.guliandigital.common.core.domain.entity.SysDept;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;

/**
 * 分类树Service接口
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ITConfigClassicTreeService {
	/**
	 * 查询分类树
	 *
	 * @param id
	 *            分类树ID
	 * @return 分类树
	 */
	List<TConfigClassicTree> selectTConfigClassicTreeById(String id);

	List<TConfigClassicTree> selectClassicTreeById(String id);

	/**
	 * 查询分类树列表
	 *
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 分类树集合
	 */
	List<TConfigClassicTree> selectTConfigClassicTreeList(TConfigClassicTree tConfigClassicTree);

	/**
	 * 新增分类树
	 *
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	int insertTConfigClassicTree(TConfigClassicTree tConfigClassicTree);

	/**
	 * 修改分类树
	 *
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	int updateTConfigClassicTree(TConfigClassicTree tConfigClassicTree);

	/**
	 * 批量删除分类树
	 *
	 * @param 需要删除的分类树ID
	 */
	void deleteTConfigClassicTreeByIds(String id);

	List<TConfigClassicTree> selectParent(String id);

	TConfigClassicTree selectP(String treePid);
	/**
	 * 删除分类树信息
	 *
	 * @param id
	 *            分类树ID
	 * @return 结果
	 */

}
