
package cn.guliandigital.product.book.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import cn.guliandigital.common.config.HuangHeConfig;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

import cn.guliandigital.common.config.PunConfig;
import cn.guliandigital.common.config.T2sConfig;
import cn.guliandigital.common.enums.PunIgnoreEnum;
import cn.guliandigital.common.enums.PunTypeEnum;
import cn.guliandigital.common.enums.RespTypeEnum;
import cn.guliandigital.common.enums.TextTypeEnum;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.XproUtils;
import cn.guliandigital.product.book.service.IApiService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ApiServiceImpl implements IApiService {

    @Autowired
    private PunConfig punConfig;
    @Autowired
    private HuangHeConfig qiluwenkuConfig;
    @Autowired
    private T2sConfig t2sConfig;

    @Override
    public JSONObject getCallInterface(HttpClient client,String params, String appKey, String appSecrect, String url) {
        JSONObject result=null;
        //HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        try{
            RequestConfig config = RequestConfig.custom().setConnectTimeout(60 * 60 * 1000) // 连接超时时间
                    .setConnectionRequestTimeout(60 * 60 * 1000) // 从连接池中取的连接的最长时间
                    .setSocketTimeout(60 * 60 * 1000) // 数据传输的超时时间
                    .setStaleConnectionCheckEnabled(true) // 提交请求前测试连接是否可用
                    .build();
            // 设置请求配置时间
            post.setConfig(config);
            StringEntity entity = new StringEntity(params, "utf-8");
            post.setHeader("appKey", appKey);
            post.setHeader("appSecrect",appSecrect);
            post.setHeader("Content-Type", "application/json");
            post.setEntity(entity);
            HttpResponse response = client.execute(post);
            String entityStr = EntityUtils.toString(response.getEntity(), "utf-8");
            JSONObject entityJson = JSONObject.parseObject(entityStr);
            String code=entityJson.getString("code");
            if("200".equals(code)||"404".equals(code)){
                result= entityJson.getJSONObject("data");
                entityJson=null;
                entityStr=null;
            }else{
            
                String msg=entityJson.getString("msg");
                log.error("调用接口报错,", msg);
                throw new ServiceException(msg);
            }

        }catch (Exception e){
        	log.error("调用接口报错,",e);
            throw new ServiceException(e.getMessage());
        }
        finally{
            post.releaseConnection();

        }
        return result;
    }

    @Override
    public void punRecognition(String inputPath,String outPath) throws Exception {
        //List<String> list=Collections.synchronizedList(new ArrayList<String>());
        //获取xpro文件
        String xproPath = FilenameUtils.normalize(inputPath + File.separator + "main.xpro", true);
        List<String> list = XproUtils.parseMainXpro4Meta(xproPath);//获取xpro中xml文件
        AtomicInteger atomicLoop = new AtomicInteger(0);
        Map<File,File> map=Maps.newConcurrentMap();
        String tempFolder="temp";
        log.info("==>文件总数：{}", list.size());
        //自动标点
        HttpClient client = HttpClients.createDefault();
        list.stream().forEach(item->{
//            if(item.contains("ZSK43074 000027.xml")){
//                System.out.println("容易出错");
//            }
            String path=FilenameUtils.normalize(inputPath + File.separator + item, true);
            File file=new File(path);
            String fileName=file.getName();
            item=FilenameUtils.normalize(item, true);
            Path filePath = Paths.get(outPath,item);
            log.info("==>开始转换文件：{}", file.getAbsolutePath());
            // 1.创建Reader对象
            SAXReader reader = new SAXReader();
            // 2.加载xml
            try {
                Document document = reader.read(file);
                String content=document.asXML();
                //获取xml内容
                Map<String,Object> customStorage=new HashMap<>();
                customStorage.put("text",content);
                customStorage.put("textType", TextTypeEnum.XML.getCode());
                customStorage.put("type", PunTypeEnum.PUN_TYPE_7.getCode());
                customStorage.put("ignore", PunIgnoreEnum.PUN_TYPE_0.getCode());
                JSONObject punResult=this.getCallInterface(client,JSONObject.toJSONString(customStorage),punConfig.getAppKey(),punConfig.getAppSecrect(),punConfig.getPunAddress());
                // 自动标点xml存放路径
                Path tempPath = Paths.get(outPath,tempFolder,item);
                map.put(tempPath.toFile(),filePath.toFile());
                if(punResult == null) {
                	FileUtils.copyFile(file, tempPath.toFile());
                }else {
                	this.generateXmlFile(tempPath.toString(),punResult.getString("text"),fileName);
                }
                atomicLoop.incrementAndGet();
                document.clearContent();
            } catch (Exception e) {
            	log.error("自动标点失败路径{},错误信息{},",item,e);
                //log.error("自动标点失败路径{},错误信息{}",item,e);
                //throw new ServiceException("解析失败!错误代码:0001，请联系管理员!");
                throw new ServiceException(e.getMessage());
            }finally {

            }
        });
        int num=atomicLoop.get();
        if(num==list.size()){
            FileUtils.copyDirectory(new File(inputPath+File.separator),new File(outPath));
            map.forEach((key, value) -> {
                log.info("key {},value {}",key,value);
                try {
                    FileUtils.copyFile(key,value);

                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            });
            //删除临时文件
            FileUtils.deleteDirectory(Paths.get(outPath,tempFolder).toFile());
        }
    }


    /**
     * 生成xml文件
     * @param path
     * @param xmlStr
     */
    @Override
    public void generateXmlFile(String path,String xmlStr,String xmlName) throws IOException, DocumentException {
        File filePath=new File(path);
        if (!filePath.getParentFile().exists()) {
            //创建文件夹
            filePath.getParentFile().mkdirs();
        }
        //1、设置生成xml的格式
        OutputFormat punFormat = OutputFormat.createPrettyPrint();
        //2、设置编码格式
        punFormat.setEncoding("UTF-8");
        //3、生成xml文件
        File punFile = new File(path);
        XMLWriter punWriter = new XMLWriter(new FileOutputStream(punFile), punFormat);
        // 设置是否转义，默认使用转义字符
        punWriter.setEscapeText(false);
        Document punDocument = DocumentHelper.parseText(xmlStr);
        punWriter.write(punDocument);
        punWriter.close();
    }

    @Override
    public void getT2s(String inputPath, String outPath,Integer transMode) throws Exception {
        //List<String> list=Collections.synchronizedList(new ArrayList<String>());
        //获取xpro文件
        String xproPath = FilenameUtils.normalize(inputPath + File.separator + "main.xpro", true);
        List<String> list = XproUtils.parseMainXpro4Meta(xproPath);//获取xpro中xml文件
        AtomicInteger atomicLoop = new AtomicInteger(0);
        Map<File,File> map=Maps.newConcurrentMap();
        String tempFolder="temp";
        log.info("==>文件总数：{}", list.size());
        //自动标点
        HttpClient client = HttpClients.createDefault();
        list.stream().forEach(item->{
            String path=FilenameUtils.normalize(inputPath + File.separator + item, true);
            File file=new File(path);
            String fileName=file.getName();
            item=FilenameUtils.normalize(item, true);
            Path filePath = Paths.get(outPath,item);
            log.info("==>开始转换文件：{}", file.getAbsolutePath());
            // 1.创建Reader对象
            SAXReader reader = new SAXReader();
            // 2.加载xml
            try {
                Document document = reader.read(file);
                String content=document.asXML();
                //获取xml内容
                Map<String,Object> customStorage=new HashMap<>();
                customStorage.put("transText",content);
                customStorage.put("textType", TextTypeEnum.XML.getCode());
                customStorage.put("transMode", transMode);
                customStorage.put("respType", RespTypeEnum.TEXT.getCode());
                JSONObject punResult=this.getCallInterface(client,JSONObject.toJSONString(customStorage),t2sConfig.getAppKey(),t2sConfig.getAppSecrect(),t2sConfig.getT2sAddress());
                // 自动标点xml存放路径
                Path tempPath = Paths.get(outPath,tempFolder,item);
                map.put(tempPath.toFile(),filePath.toFile());
                if(punResult == null) {
                	FileUtils.copyFile(file, tempPath.toFile());
                }else {
                	this.generateXmlFile(tempPath.toString(),punResult.getString("content"),fileName);
                }
                atomicLoop.incrementAndGet();
                punResult=null;
            } catch (Exception e) {
            	log.error("繁简转换失败路径{},错误信息{},",item,e);
                //log.error("繁简转换失败"+e.getMessage());
                //throw new ServiceException("解析失败!错误代码:0001，请联系管理员!");
                throw new ServiceException(e.getMessage());
            }
        });
        int num=atomicLoop.get();
        if(num==list.size()){
            FileUtils.copyDirectory(new File(inputPath+File.separator),new File(outPath));
            map.forEach((key, value) -> {
                log.info("key {},value {}",key,value);
                try {
                    FileUtils.copyFile(key,value);

                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            });
            //删除临时文件
            FileUtils.deleteDirectory(Paths.get(outPath,tempFolder).toFile());
        }
    }

}