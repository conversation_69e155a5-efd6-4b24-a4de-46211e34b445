package cn.guliandigital.product.database.service;


import cn.guliandigital.product.database.domain.TProDatabase;

import java.util.List;

/**
 * 数据库管理Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-14
 */
public interface ITProDatabaseService 
{
    /**
     * 查询数据库管理
     * 
     * @param id 数据库管理ID
     * @return 数据库管理
     */
        TProDatabase selectTProDatabaseById(String id);

    /**
     * 查询数据库管理列表
     * 
     * @param tProDatabase 数据库管理
     * @return 数据库管理集合
     */
    List<TProDatabase> selectTProDatabaseList(TProDatabase tProDatabase);

    /**
     * 新增数据库管理
     * 
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    int insertTProDatabase(TProDatabase tProDatabase);

    /**
     * 修改数据库管理
     * 
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    int updateTProDatabase(TProDatabase tProDatabase);

    /**
     * 批量删除数据库管理
     * 
     * @param ids 需要删除的数据库管理ID
     * @return 结果
     */
    int deleteTProDatabaseByIds(String[] ids);

    /**
     * 删除数据库管理信息
     * 
     * @param id 数据库管理ID
     * @return 结果
     */
    int deleteTProDatabaseById(String id);


    List<TProDatabase> selectRe();
    
    TProDatabase selecDbId(String dbName);


    List<TProDatabase> selectTProDatabase(TProDatabase tProDatabase);
}
