package cn.guliandigital.product.readhistory.mapper;

import java.util.List;

import cn.guliandigital.product.readhistory.domain.TUserReadHistory;

/**
 * 用户阅读历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
public interface TUserReadHistoryMapper 
{
    /**
     * 查询用户阅读历史
     * 
     * @param id 用户阅读历史ID
     * @return 用户阅读历史
     */
        TUserReadHistory selectTUserReadHistoryById(String id);

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史集合
     */
    List<TUserReadHistory> selectTUserReadHistoryList(TUserReadHistory tUserReadHistory);

    /**
     * 新增用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int insertTUserReadHistory(TUserReadHistory tUserReadHistory);

    /**
     * 修改用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int updateTUserReadHistory(TUserReadHistory tUserReadHistory);

    /**
     * 删除用户阅读历史
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistoryById(String id);

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserReadHistoryByIds(String[] ids);


    List<TUserReadHistory> selectReadTimeAndSearchPeoPle(String today);

    List<TUserReadHistory> selectQueryCount(String today);

    /**
     * 根据bookId删除
     * @param bookId
     * @return
     */
    int deleteTUserReadHistoryByBookId(String bookId);
}
