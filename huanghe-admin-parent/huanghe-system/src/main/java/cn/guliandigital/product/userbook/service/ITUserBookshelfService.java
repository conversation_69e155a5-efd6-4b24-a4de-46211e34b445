package cn.guliandigital.product.userbook.service;

import cn.guliandigital.product.userbook.domain.TUserBookshelf;

import java.util.List;

/**
 * 用户书架Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
public interface ITUserBookshelfService 
{
    /**
     * 查询用户书架
     * 
     * @param id 用户书架ID
     * @return 用户书架
     */
        TUserBookshelf selectTUserBookshelfById(String id);

    /**
     * 查询用户书架列表
     * 
     * @param tUserBookshelf 用户书架
     * @return 用户书架集合
     */
    List<TUserBookshelf> selectTUserBookshelfList(TUserBookshelf tUserBookshelf);

    /**
     * 新增用户书架
     * 
     * @param tUserBookshelf 用户书架
     * @return 结果
     */
    int insertTUserBookshelf(TUserBookshelf tUserBookshelf);

    /**
     * 修改用户书架
     * 
     * @param tUserBookshelf 用户书架
     * @return 结果
     */
    int updateTUserBookshelf(TUserBookshelf tUserBookshelf);

    /**
     * 批量删除用户书架
     * 
     * @param ids 需要删除的用户书架ID
     * @return 结果
     */
    int deleteTUserBookshelfByIds(String[] ids);

    /**
     * 删除用户书架信息
     * 
     * @param id 用户书架ID
     * @return 结果
     */
    int deleteTUserBookshelfById(String id);
}
