package cn.guliandigital.product.menu.mapper;

import cn.guliandigital.product.menu.domain.TProBookMenu;

import java.util.List;

/**
 * 书目章节树状结构Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-10-12
 */
public interface TProBookMenuMapper 
{
    /**
     * 查询书目章节树状结构
     * 
     * @param id 书目章节树状结构ID
     * @return 书目章节树状结构
     */
        TProBookMenu selectTProBookMenuById(String id);

    /**
     * 查询书目章节树状结构列表
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 书目章节树状结构集合
     */
    List<TProBookMenu> selectTProBookMenuList(TProBookMenu tProBookMenu);

    /**
     * 新增书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    int insertTProBookMenu(TProBookMenu tProBookMenu);

    /**
     * 修改书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    int updateTProBookMenu(TProBookMenu tProBookMenu);

    /**
     * 删除书目章节树状结构
     * 
     * @param id 书目章节树状结构ID
     * @return 结果
     */
    int deleteTProBookMenuById(String id);

    /**
     * 批量删除书目章节树状结构
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTProBookMenuByIds(String[] ids);
    
    int deleteTProBookMenuByBookId(String bookId);
    
    TProBookMenu selectTProBookMenuByPid(String pid);
    
}
