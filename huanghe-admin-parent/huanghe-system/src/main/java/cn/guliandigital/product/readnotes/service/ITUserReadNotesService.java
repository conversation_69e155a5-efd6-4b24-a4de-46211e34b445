package cn.guliandigital.product.readnotes.service;

import java.util.List;
import cn.guliandigital.product.readnotes.domain.TUserReadNotes;

/**
 * 用户阅读笔记Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-28
 */
public interface ITUserReadNotesService 
{
    /**
     * 查询用户阅读笔记
     * 
     * @param id 用户阅读笔记ID
     * @return 用户阅读笔记
     */
        TUserReadNotes selectTUserReadNotesById(String id);

    /**
     * 查询用户阅读笔记列表
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 用户阅读笔记集合
     */
    List<TUserReadNotes> selectTUserReadNotesList(TUserReadNotes tUserReadNotes);

    /**
     * 新增用户阅读笔记
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 结果
     */
    int insertTUserReadNotes(TUserReadNotes tUserReadNotes);

    /**
     * 修改用户阅读笔记
     * 
     * @param tUserReadNotes 用户阅读笔记
     * @return 结果
     */
    int updateTUserReadNotes(TUserReadNotes tUserReadNotes);
    int updateDelFalgByBookId(String bookId);

    /**
     * 批量删除用户阅读笔记
     * 
     * @param ids 需要删除的用户阅读笔记ID
     * @return 结果
     */
    int deleteTUserReadNotesByIds(String[] ids);

    /**
     * 删除用户阅读笔记信息
     * 
     * @param id 用户阅读笔记ID
     * @return 结果
     */
    int deleteTUserReadNotesById(String id);
}
