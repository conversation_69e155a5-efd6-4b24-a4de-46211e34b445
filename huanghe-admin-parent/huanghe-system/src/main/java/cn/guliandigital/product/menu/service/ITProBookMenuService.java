package cn.guliandigital.product.menu.service;

import cn.guliandigital.product.menu.domain.MenuTreeSelect;
import cn.guliandigital.product.menu.domain.TProBookMenu;

import java.util.List;

/**
 * 书目章节树状结构Service接口
 * 
 * <AUTHOR>
 * @date 2020-10-12
 */
public interface ITProBookMenuService 
{
    /**
     * 查询书目章节树状结构
     * 
     * @param id 书目章节树状结构ID
     * @return 书目章节树状结构
     */
     TProBookMenu selectTProBookMenuById(String id);
     TProBookMenu selectTProBookMenuByPid(String pid);
    /**
     * 查询书目章节树状结构列表
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 书目章节树状结构集合
     */
    List<TProBookMenu> selectTProBookMenuList(TProBookMenu tProBookMenu);

    /**
     * 新增书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    int insertTProBookMenu(TProBookMenu tProBookMenu);

    /**
     * 修改书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    int updateTProBookMenu(TProBookMenu tProBookMenu);

    /**
     * 批量删除书目章节树状结构
     * 
     * @param ids 需要删除的书目章节树状结构ID
     * @return 结果
     */
    int deleteTProBookMenuByIds(String[] ids);

    /**
     * 删除书目章节树状结构信息
     * 
     * @param id 书目章节树状结构ID
     * @return 结果
     */
    int deleteTProBookMenuById(String id);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 章节列表
     * @return 树结构列表
     */
    List<MenuTreeSelect> buildMenuTreeSelect(List<TProBookMenu> menus);
    List<MenuTreeSelect> buildMenuTreeSelectV2(String bookId);
    int deleteTProBookMenuByBookId(String bookId);
}
