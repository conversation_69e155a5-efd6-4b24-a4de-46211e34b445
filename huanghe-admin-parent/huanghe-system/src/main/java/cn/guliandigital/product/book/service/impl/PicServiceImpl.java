package cn.guliandigital.product.book.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.google.common.collect.Maps;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.utils.ImageSmUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.picenc.ImageSmUtilsV2;
import cn.guliandigital.product.book.service.IPicService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/10/28 21:40
 * 
 */
@Slf4j
//@Transactional
@Service("picService")
public class PicServiceImpl implements IPicService {
	
		
	

//	@Override
//	public Map<String,Object> makePic(String jpgPath, int height, String md5strs) {
//		try {
//			StopWatch sw = new StopWatch("图片加密计时");
//			sw.start("适配图片");
//			String localPath = HuangHeConfig.getUploadPath();
//			int platHeight = height;
//	
//			if (platHeight % 2 == 1) {
//				platHeight = platHeight + 1;
//			}
//				
//			File picfile = new File(jpgPath);
//			log.info("File Path : {}", jpgPath);
//			if(!picfile.exists()) {
//				log.info("==>图片不存在 {}",picfile.getAbsolutePath());
//				return null;
//			}
//			String extend = FilenameUtils.getExtension(jpgPath);
////			if(!StringUtil.equalsIgnoreCase(extend, "jpg")) {
////				return null;
////			}
//			FileInputStream fis = new FileInputStream(picfile);
//			ImageIO.setUseCache(false);
//			BufferedImage image = ImageIO.read(fis);
//			int picHeight = image.getHeight();
//			int picWidth = image.getWidth();
//			log.info("Image Height : {}", picHeight);
//			log.info("Image Width : {}", picWidth);
//			if (fis != null) {
//				fis.close();
//			}
//			sw.stop();
//			//log.info("==>页面高度：{}", height);
//			//log.info("==>补充高度：{}", platHeight);
//			sw.start("预处理图片-读取高宽");
//			//String id = md5strs;
//			String picName = picfile.getName();
//			String basename = FilenameUtils.getBaseName(picName);
//			basename = StringUtil.remove(basename, "_tif2jpg");
//			basename = StringUtil.remove(basename, "_png2jpg");
//			String router = StringUtil.substring(basename, StringUtil.length(basename)-1);
//			String splitImagePath = localPath + File.separator + "tif2jpg" + File.separator	+ router + File.separator + md5strs;
//			splitImagePath = FilenameUtils.normalize(splitImagePath);
//			File sip = new File(splitImagePath);
//			if (!sip.exists()) {
//				sip.mkdirs();
//			}
//				
//			int w = picWidth;
//			int h = picHeight;
//	
//			if (picHeight >= 4300 && picWidth >= 3100) {
//				h = 4300;
//				w = 3100;
////			} else if (picHeight > 1200 && picWidth > 1200) {
////				h = 4300;
////				w = 3100;
//			} else {	
//				log.info("==>高宽变化：[{}:{}]",picHeight, picWidth);
//				double radio = 1;
//				if(picHeight < 1200) {
//					//扩展到1200					
//					radio = (double)1200 / picHeight;
//					picHeight = 1200;
//					picWidth = (int)(radio*picWidth);
//					log.info("radio:{}", radio);
//				}
//				if(picHeight%100 != 0) { 
//					picHeight = (picHeight /100 +1)*100;
//				}
//				if(picWidth%100 != 0) {
//					picWidth = (picWidth /100 +1)*100;
//				}
//				log.info("==>高宽变化==>[{}:{}]",picHeight, picWidth);
//				Map<String, Integer> deMap = ImageSmUtils.deduceHeight(platHeight, picHeight, picWidth);
//				log.info("deMap:{}", deMap);
//				w = deMap.get("width");
//				h = deMap.get("height");
//			}
//			
//			// 压缩 File Path :
//			// /home/<USER>/uploadPath/upload/2826d5060a974576aaece0b68822833a/ystx/ZSK86450-000055-L00051.jpg
//			String _picPath = FilenameUtils.getFullPath(picfile.getAbsolutePath()) + File.separator + FilenameUtils.getBaseName(jpgPath)
//					+ "_format." + FilenameUtils.getExtension(jpgPath);
//			File pp = new File(_picPath);
//			pp.delete();
//			if(h%2==1) {
//				h +=1;				
//			}
//			if(w%2==1) {
//				w +=1;				
//			}
//			if (!pp.exists()) {
//				ImageSmUtils.compressPicSize(jpgPath, _picPath, h, w);
//				log.info("==>需要预压缩图片：{}", _picPath);
//			}
//			sw.stop();
//			sw.start("切分图片");
//			ImageSmUtils util = new ImageSmUtils();
//			LinkedHashMap<Integer, List<Integer>> map = util.splitImage(_picPath, splitImagePath, h, w);
//						
//			String encPicname = FilenameUtils.getBaseName(jpgPath) + "_E." + FilenameUtils.getExtension(picName);
//			
//			String encOutpath = splitImagePath + File.separator + encPicname;
//			encOutpath = FilenameUtils.normalize(encOutpath, true);
//			//
//			String url = "%%";
//			String baseUrl = url + "/wapi/common/picio";
//			sw.stop();
//			sw.start("合并图片");
//			
//			Map<String, Object> encMap = util.mergeImage(map, splitImagePath, encOutpath, platHeight, baseUrl, "E", md5strs, w, h, extend);
//			
//			sw.stop();
//			String divCss = encMap.get("divCss").toString();
//			String encPicPath = encMap.get("encPicPath").toString();
//			String encPicUrl = new StringBuilder().append(baseUrl).append("?picname=/").append(md5strs).append("/").append(FilenameUtils.getBaseName(encPicPath) + "."+extend).toString();
//			// log.info(encPicUrl+":"+"encPicUrl-----------------------");
//			
//			Map<String,Object> ajax = Maps.newHashMap();
//			ajax.put("divCss", divCss);
//			ajax.put("picUrl", encPicUrl);
//			ajax.put("totalHeight", encMap.get("totalHeight"));
//			ajax.put("totalWidth", encMap.get("totalWidth"));
//			log.info(sw.prettyPrint());
//
//			return ajax;
//		}catch(Exception e) {
//			log.error("{}  error,",jpgPath, e);
//		}
//		
//		return null;
//	}

	
	
	@Override
	public Map<String,Object> makePicV2(String jpgPath, int height, String md5strs) {
		try {
			StopWatch sw = new StopWatch("图片加密计时");
			sw.start("适配图片");
			String localPath = HuangHeConfig.getUploadPath();
			int platHeight = height;
	
			if (platHeight % 2 == 1) {
				platHeight = platHeight + 1;
			}
				
			File picfile = new File(jpgPath);
			log.info("File Path : {}", jpgPath);
			if(!picfile.exists()) {
				log.info("==>图片不存在 {}",picfile.getAbsolutePath());
				return null;
			}
			if(picfile.length() == 0) {
				log.info("==>图片为空 {}",picfile.getAbsolutePath());
				return null;
			}
			String extend = FilenameUtils.getExtension(jpgPath);
//			if(!StringUtil.equalsIgnoreCase(extend, "jpg")) {
//				return null;
//			}
			FileInputStream fis = new FileInputStream(picfile);
			ImageIO.setUseCache(false);
			BufferedImage image = ImageIO.read(fis);
			int picHeight = image.getHeight();
			int picWidth = image.getWidth();
			log.info("Image Height : {}", picHeight);
			log.info("Image Width : {}", picWidth);
			if (fis != null) {
				fis.close();
			}
			sw.stop();
			//log.info("==>页面高度：{}", height);
			//log.info("==>补充高度：{}", platHeight);
			sw.start("预处理图片-读取高宽");
			//String id = md5strs;
			String picName = picfile.getName();
			String basename = FilenameUtils.getBaseName(picName);
			basename = StringUtil.remove(basename, "_tif2jpg");
			basename = StringUtil.remove(basename, "_png2jpg");
			String router = StringUtil.substring(basename, StringUtil.length(basename)-1);
			String splitImagePath = localPath + File.separator + "tif2jpg" + File.separator	+ router + File.separator + md5strs;
			splitImagePath = FilenameUtils.normalize(splitImagePath);
			File sip = new File(splitImagePath);
			if (!sip.exists()) {
				boolean ismk = sip.mkdirs();
				if(!ismk) {
                	log.info("文件夹{}创建失败",sip.getAbsolutePath());
                }
			}
				
//			int w = picWidth;
//			int h = picHeight;
			int std_height = picHeight;
			int std_width = picWidth;
			log.info("==>计算最终图片高宽......");
			if(picHeight > Constants.PIC_HEIGHT) {
				std_height = Constants.PIC_HEIGHT;
				double radio = (double)std_height/picHeight;
				std_width = (int) (radio*picWidth);
				if(std_width%2==1) {
					std_width -=1;				
				}
			}else {				
				if(std_height%2==1) {
					std_height -=1;				
				}
				if(std_width%2==1) {
					std_width -=1;				
				}
			}
			log.info("==>计算图片高宽......[ {}:{} ]", std_height, std_width);
			// 压缩 File Path :
			// /home/<USER>/uploadPath/upload/2826d5060a974576aaece0b68822833a/ystx/ZSK86450-000055-L00051.jpg
			String _picPath = FilenameUtils.getFullPath(picfile.getAbsolutePath()) + File.separator + FilenameUtils.getBaseName(jpgPath)
					+ "_format." + FilenameUtils.getExtension(jpgPath);
			File pp = new File(_picPath);
			boolean isdelete = pp.delete();
			if(!isdelete) {
            	log.info("文件{}删除失败",pp.getAbsolutePath());
            }
			if (!pp.exists()) {
				ImageSmUtils.compressPicSize(jpgPath, _picPath, std_height, std_width);
				log.info("==>需要预压缩图片：{}", _picPath);
			}
			sw.stop();
			sw.start("切分图片");
			ImageSmUtilsV2 util = new ImageSmUtilsV2();
			LinkedHashMap<Integer, List<Integer>> map = util.splitImage(_picPath, splitImagePath, std_height, std_width);
						
			String encPicname = FilenameUtils.getBaseName(jpgPath) + "_E." + FilenameUtils.getExtension(picName);
			
			String encOutpath = splitImagePath + File.separator + encPicname;
			encOutpath = FilenameUtils.normalize(encOutpath, true);
			//
			String url = "%%";
			String baseUrl = url + "/wapi/common/picio";
			sw.stop();
			sw.start("合并图片");
			
			Map<String, Object> encMap = util.mergeImage(map, splitImagePath, encOutpath, baseUrl, "E", md5strs, std_height, std_width, extend);
			
			sw.stop();
			String divCss = encMap.get("divCss").toString();
			String encPicPath = encMap.get("encPicPath").toString();
			String encPicUrl = new StringBuilder().append(baseUrl).append("?picname=/").append(md5strs).append("/").append(FilenameUtils.getBaseName(encPicPath) + "."+extend+"&st="+System.currentTimeMillis()).toString();
			// log.info(encPicUrl+":"+"encPicUrl-----------------------");
			
			Map<String,Object> ajax = Maps.newHashMap();
			ajax.put("divCss", divCss);
			ajax.put("picUrl", encPicUrl);
			ajax.put("totalHeight", std_height);
			ajax.put("totalWidth", std_width);
			log.info(sw.prettyPrint());

			return ajax;
		}catch(Exception e) {
			log.error("{}  error,",jpgPath, e);
		}
		
		return null;
	}
	
	
}
