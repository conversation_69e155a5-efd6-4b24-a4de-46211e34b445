package cn.guliandigital.product.clasic.mapper;

import java.util.List;
import cn.guliandigital.product.clasic.domain.TConfigClassic;

/**
 * 分类定义Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface TConfigClassicMapper 
{
    /**
     * 查询分类定义
     * 
     * @param id 分类定义ID
     * @return 分类定义
     */
        TConfigClassic selectTConfigClassicById(String id);

    /**
     * 查询分类定义列表
     * 
     * @param tConfigClassic 分类定义
     * @return 分类定义集合
     */
    List<TConfigClassic> selectTConfigClassicList(TConfigClassic tConfigClassic);

    /**
     * 新增分类定义
     * 
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    int insertTConfigClassic(TConfigClassic tConfigClassic);

    /**
     * 修改分类定义
     * 
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    int updateTConfigClassic(TConfigClassic tConfigClassic);

    /**
     * 删除分类定义
     * 
     * @param id 分类定义ID
     * @return 结果
     */
    int deleteTConfigClassicById(String id);

    /**
     * 批量删除分类定义
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTConfigClassicByIds(String[] ids);


}
