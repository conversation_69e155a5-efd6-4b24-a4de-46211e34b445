package cn.guliandigital.product.book.service;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.List;

import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.vo.ClassTreeType;
import cn.guliandigital.product.database.domain.TProDatabase;

/**
 * 产品资源管理Service接口
 *
 * <AUTHOR>
 * @date 2020-09-09
 */
public interface ITProBooksService
{

    /**
     * 查询产品资源管理
     *
     * @param id 产品资源管理ID
     * @return 产品资源管理
     */
    TProBooks selectTProBooksById(String id);

    /**
     * 查询产品资源管理列表
     *
     * @param tProBooks 产品资源管理
     * @return 产品资源管理集合
     */



    List<TProBooks>  selectTProBooksList(TProBooks tProBooks);

    /**
     * 新增产品资源管理
     *
     * @param tProBooks 产品资源管理
     * @return 结果
     */
    int insertTProBooks(TProBooks tProBooks);

    /**
     * 修改产品资源管理
     *
     * @param tProBooks 产品资源管理
     */
    int updateTProBooks(TProBooks tProBooks);
     File buildXlsById() throws FileNotFoundException;

    /**
     * 批量删除产品资源管理
     *
     * @param ids 需要删除的产品资源管理ID
     * @return 结果
     */
    int deleteTProBooksByIds(String[] ids);

    /**
     * 删除产品资源管理信息
     *
     * @param id 产品资源管理ID
     * @return 结果
     */
    int deleteTProBooksById(String id);
    /**
     * <AUTHOR>
     * @Description 查询资源类型
     * @Date 2020/9/9 18:30
     * @param :
     **/
    List<String> findType();
/**
 * <AUTHOR>
 * @Description 下架
 * @Date 2020/10/14 15:38
 * @param ids:
 **/
    int updateStatuOffLine(String ids[]);
    String importBooks(List<TProBooks> booksList, Boolean updateSupport, String operName);

    int updateTProBook4Delete(TProBooks tProBooks);

    List<ClassTreeType> selectType(ClassTreeType type);


    List<TProDatabase> selectdbName(TProDatabase tProDatabase);


    List<TProBooks> selectByDbId(TProBooks tProBooks);
    List<TProBooks> selectByDbIds(TProBooks tProBooks);
    List<TProBooks> selectByDbId(String dbId);


    int updateStatuOnline(String[] ids);
    int updateStatuOnline(TProBooks tProBooks);
    //用户是喊出更新状态
    //int updateTProBook1(TProBooks tProBooks);

    List<TProBooks> check();


    /**
     * 把书从数据库剔除
     */
    //void deleteFromDb(String id);

    void getList(ClassTreeType item, List<ClassTreeType> orgList, String id);

    List<ClassTreeType> selectFourPart(ClassTreeType type);

    List<ClassTreeType> selectFourPartBehind(ClassTreeType type);
    List<ClassTreeType> selectOther(ClassTreeType type);


    List<TProBooks> selectCount(TProBooks tProBooks);


    List<TProBooks> selectRecourseStatisticsList(TProBooks tProBooks);

    TProBooks selectWebTProBooksById(String id);

    void updateDatabaseRelate(TProBooks tProBooks,Boolean ischeck);


    List<TProBooks> recoursePercent();

    int selectBooks(String dbId);

    /**
	 * @param ids:
	 * <AUTHOR>
	 * @Description 上架
	 * @Date 2020/10/14 13:38
	 **/
//	int updateStatuOnlineByIndex(String[] ids, String index, String type);

  
    
    int insertBatchTProBooks(List<TProBooks> list);
    
    List<TProBooks> selectsAllList(TProBooks tProBooks);

    /**
     * 查询产品资源管理
     *
     * @param tProBooks 产品资源管理ID
     * @return 产品资源管理
     */
    TProBooks selectTProBooksEntity(TProBooks tProBooks);

    List<TProBooks> selectBook(String[] ids);
}
