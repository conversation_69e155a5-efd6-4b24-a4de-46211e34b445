package cn.guliandigital.product.userbook.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.userbook.domain.TUserBookshelfClasses;
import cn.guliandigital.product.userbook.mapper.TUserBookshelfClassesMapper;
import cn.guliandigital.product.userbook.service.ITUserBookshelfClassesService;

/**
 * 用户书架分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
@Service
public class TUserBookshelfClassesServiceImpl implements ITUserBookshelfClassesService
{
    @Autowired
    private TUserBookshelfClassesMapper tUserBookshelfClassesMapper;

    /**
     * 查询用户书架分类
     * 
     * @param id 用户书架分类ID
     * @return 用户书架分类
     */
    @Override
    public TUserBookshelfClasses selectTUserBookshelfClassesById(String id)
    {
        return tUserBookshelfClassesMapper.selectTUserBookshelfClassesById(id);
    }

    /**
     * 查询用户书架分类列表
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 用户书架分类
     */
    @Override
    public List<TUserBookshelfClasses> selectTUserBookshelfClassesList(TUserBookshelfClasses tUserBookshelfClasses)
    {
        //获取书架分类名称与书籍数量
        return tUserBookshelfClassesMapper.selectTUserBookshelfClassesList(tUserBookshelfClasses);
    }

    /**
     * 新增用户书架分类
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 结果
     */
    @Override
    public int insertTUserBookshelfClasses(TUserBookshelfClasses tUserBookshelfClasses)
    {

        tUserBookshelfClasses.setCreateTime(DateUtil.getCuurentDate());
        return tUserBookshelfClassesMapper.insertTUserBookshelfClasses(tUserBookshelfClasses);
    }

    /**
     * 修改用户书架分类
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 结果
     */
    @Override
    public int updateTUserBookshelfClasses(TUserBookshelfClasses tUserBookshelfClasses)
    {
        tUserBookshelfClasses.setUpdateTime(DateUtil.getCuurentDate());
        return tUserBookshelfClassesMapper.updateTUserBookshelfClasses(tUserBookshelfClasses);
    }

    /**
     * 批量删除用户书架分类
     * 
     * @param ids 需要删除的用户书架分类ID
     * @return 结果
     */
    @Override
    public int deleteTUserBookshelfClassesByIds(String[] ids)
    {
        return tUserBookshelfClassesMapper.deleteTUserBookshelfClassesByIds(ids);
    }

    /**
     * 删除用户书架分类信息
     * 
     * @param id 用户书架分类ID
     * @return 结果
     */
    @Override
    public int deleteTUserBookshelfClassesById(String id)
    {
        return tUserBookshelfClassesMapper.deleteTUserBookshelfClassesById(id);
    }

	@Override
	public List<TUserBookshelfClasses> selectClassesList(TUserBookshelfClasses tUserBookshelfClasses) {		
		return tUserBookshelfClassesMapper.selectClassesList(tUserBookshelfClasses);
	}
}
