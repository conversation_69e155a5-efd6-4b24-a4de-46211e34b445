package cn.guliandigital.product.readhistory.service;

import java.util.List;
import cn.guliandigital.product.readhistory.domain.TUserReadHistory;

/**
 * 用户阅读历史Service接口
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
public interface ITUserReadHistoryService 
{
    /**
     * 查询用户阅读历史
     * 
     * @param id 用户阅读历史ID
     * @return 用户阅读历史
     */
        TUserReadHistory selectTUserReadHistoryById(String id);

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史集合
     */
    List<TUserReadHistory> selectTUserReadHistoryList(TUserReadHistory tUserReadHistory);

    /**
     * 新增用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int insertTUserReadHistory(TUserReadHistory tUserReadHistory, boolean savebook);

    /**
     * 修改用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    int updateTUserReadHistory(TUserReadHistory tUserReadHistory);

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistoryByIds(String[] ids);

    /**
     * 删除用户阅读历史信息
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistoryById(String id);
}
