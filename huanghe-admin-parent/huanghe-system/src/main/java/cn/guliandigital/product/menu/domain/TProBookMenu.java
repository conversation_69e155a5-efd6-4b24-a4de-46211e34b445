package cn.guliandigital.product.menu.domain;

import java.util.ArrayList;
import java.util.List;

import cn.guliandigital.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 书目章节树状结构对象 t_pro_book_menu
 * 
 * <AUTHOR>
 * @date 2020-10-12
 */
@Data
public class TProBookMenu extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 章节名称 */
    private String menuName;

    /** 图书ID */
    private String bookId;

    /** 上级ID */
    private String pid;
    
    /** 完整路径 */
    private String fullMenuName;
    
    /** 排序 */
    private Long display;
    
    /** 起始页码 */
    private Integer startPageNo;

    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** $column.columnComment */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    private Integer delFlag;

    /** 子章节 */
    private List<TProBookMenu> children = new ArrayList<TProBookMenu>();
    
    //用于传输数据，没有实际用途
    private String zwContent;
    //
    private Integer level;
    
    //页码
    private Integer pageNo;
    
    private Integer textPageNo;

    
}
