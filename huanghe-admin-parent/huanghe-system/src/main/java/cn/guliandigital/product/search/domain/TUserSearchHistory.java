package cn.guliandigital.product.search.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 用户检索历史对象 t_user_search_history
 * 
 * <AUTHOR>
 * @date 2020-10-08
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TUserSearchHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 检索内容 */
    @Excel(name = "检索内容")
    private String searchContent;
    
    @ExcelProperty(value = "检索json串" ,index = 1)
	private String searchContentJson;
			
	@ExcelProperty(value = "检索类型 F-全文  A-高级" ,index = 2)
	private String searchType;

    /** $column.columnComment */
    @Excel(name = "检索内容")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
    @Excel(name = "创建人")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    private String orgId;
    private String reqIp;
    
    private String searchCount;

    /**
    *数据来源  P-平台  A-小程序
    */
    private String dataFrom;
    
    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setSearchContent(String searchContent) 
    {
        this.searchContent = searchContent;
    }

    public String getSearchContent() 
    {
        return searchContent;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("searchContent", getSearchContent())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
