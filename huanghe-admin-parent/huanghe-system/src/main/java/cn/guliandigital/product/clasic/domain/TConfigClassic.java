package cn.guliandigital.product.clasic.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 分类定义对象 t_config_classic
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
public class TConfigClassic extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String classicName;

    /** 分类编号 */
    @Excel(name = "分类编号")
    private String classicCode;

    /** 分类描述 */
    @Excel(name = "分类描述")
    private String classicDesc;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;


    private Integer display;

    /** 创建人名称 */
    @Excel(name = "创建人名称")
    private String createbyName;


    private String classicStatus;

    public String getClassicStatus() {
        return classicStatus;
    }

    public void setClassicStatus(String classicStatus) {
        this.classicStatus = classicStatus;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setClassicName(String classicName) 
    {
        this.classicName = classicName;
    }

    public String getClassicName() 
    {
        return classicName;
    }
    public void setClassicCode(String classicCode) 
    {
        this.classicCode = classicCode;
    }

    public String getClassicCode() 
    {
        return classicCode;
    }
    public void setClassicDesc(String classicDesc) 
    {
        this.classicDesc = classicDesc;
    }

    public String getClassicDesc() 
    {
        return classicDesc;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classicName", getClassicName())
            .append("classicCode", getClassicCode())
            .append("classicDesc", getClassicDesc())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .toString();
    }
}
