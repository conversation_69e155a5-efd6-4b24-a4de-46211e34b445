package cn.guliandigital.product.clasic.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.houbb.opencc4j.util.ZhConverterUtil;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.mapper.TConfigClassicTreeMapper;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import lombok.extern.slf4j.Slf4j;

/**
 * 分类树Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */

@Slf4j
@Service
public class TConfigClassicTreeServiceImpl implements ITConfigClassicTreeService {
	@Autowired
	private TConfigClassicTreeMapper tConfigClassicTreeMapper;

	/**
	 * 查询分类树
	 * 
	 * @param id
	 *            分类树ID
	 * @return 分类树
	 */
	@Override
	public List<TConfigClassicTree> selectTConfigClassicTreeById(String id) {
		return tConfigClassicTreeMapper.selectTConfigClassicTreeById(id);
	}

	@Override
	public List<TConfigClassicTree> selectClassicTreeById(String id) {
		return tConfigClassicTreeMapper.selectClassicTreeById(id);
	}

	/**
	 * 查询分类树列表
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 分类树
	 */
	@Override
	public List<TConfigClassicTree> selectTConfigClassicTreeList(TConfigClassicTree tConfigClassicTree) {
		return tConfigClassicTreeMapper.selectTConfigClassicTreeList(tConfigClassicTree);
	}


	/**
	 * 新增分类树
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	@Override
	public int insertTConfigClassicTree(TConfigClassicTree tConfigClassicTree) {
//		TConfigClassicTree CattConfigClassicTree =	tConfigClassicTreeMapper.selectByName(tConfigClassicTree.getTreeName());
//		if(!Objects.isNull(CattConfigClassicTree)){
//			throw new ServiceException("已有改分类！");
//		}

		tConfigClassicTree.setTreeNameS(tConfigClassicTree.getTreeName());
		tConfigClassicTree.setTreeName(ZhConverterUtil.convertToSimple(tConfigClassicTree.getTreeName()));
		tConfigClassicTree.setCreateTime(DateUtil.getCuurentDate());
		tConfigClassicTree.setId(IdUtils.simpleUUID());
		tConfigClassicTree.setTreePid(tConfigClassicTree.getTreePid());
		tConfigClassicTree.setTreeCode(tConfigClassicTree.getTreeCode());
		if (tConfigClassicTree.getTreePid() == null) {
			tConfigClassicTree.setTreePid(null);
		}

		tConfigClassicTree.setClassicId(tConfigClassicTree.getClassicId());

		return tConfigClassicTreeMapper.insertTConfigClassicTree(tConfigClassicTree);
	}

	/**
	 * 修改分类树
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	@Override
	public int updateTConfigClassicTree(TConfigClassicTree tConfigClassicTree) {
		tConfigClassicTree.setTreeNameS(tConfigClassicTree.getTreeName());
		tConfigClassicTree.setTreeName(ZhConverterUtil.convertToSimple(tConfigClassicTree.getTreeName()));
		return tConfigClassicTreeMapper.updateTConfigClassicTree(tConfigClassicTree);
	}

	/**
	 * 批量删除分类树
	 * 
	 * @param
	 */
	@Override
	public void deleteTConfigClassicTreeByIds(String id) {
		
		List<TConfigClassicTree> tConfigClassicTree = tConfigClassicTreeMapper.selectHasChild(id);
		for (TConfigClassicTree configClassicTree : tConfigClassicTree) {
			if(StringUtil.isNotEmpty(configClassicTree.getId())) {
				log.info("==>删除子节点：{}",configClassicTree.getId());
				tConfigClassicTreeMapper.deleteTConfigClassicTreeById(configClassicTree.getId());
			}
		}
		tConfigClassicTreeMapper.deleteTConfigClassicTreeById(id);
	}

	@Override
	public List<TConfigClassicTree> selectParent(String treePid) {
		return tConfigClassicTreeMapper.selectHasChild(treePid);
	}

	@Override
	public TConfigClassicTree selectP(String treePid) {
		return tConfigClassicTreeMapper.selectP(treePid);
	}

//	public void delChildren(String treePid, String id) {
//		List<TConfigClassicTree> tConfigClassicTreeList = tConfigClassicTreeMapper.selectHasChild(treePid);
//		for (TConfigClassicTree configClassicTree : tConfigClassicTreeList) {
//			if (null != tConfigClassicTreeList && tConfigClassicTreeList.size() > 0) {
//				tConfigClassicTreeMapper.deleteTConfigClassicTreeById(treePid);
//			}
//		}
//		tConfigClassicTreeMapper.deleteTConfigClassicTreeById(id);
//
//	}

}
