package cn.guliandigital.product.book.vo;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import cn.guliandigital.product.clasic.domain.TConfigClassic;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName Type
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/14 17:30
 */
public class Type extends BaseEntity {

    private static final long serialVersionUID = 1L;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    private String  label;
    private String value;
    /** 主键 */
    private String id;

    /** 分类ID */
    @Excel(name = "分类ID")
    private String classicId;

    /** 分类树编号 */
    @Excel(name = "分类树编号")
    private String treeCode;

    /** 分类树名称 */
    @Excel(name = "分类树名称")
    private String treeName;

    /** 上级分类树ID */
    @Excel(name = "上级分类树ID")
    private String treePid;

    /** 分类树描述 */
    @Excel(name = "分类树描述")
    private String treeDesc;

    /** 排序 */
    @Excel(name = "排序")
    private Long treeDisplay;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人名称 */
    @Excel(name = "创建人名称")
    private String createbyName;

    @Excel(name = "创建人名称")
    private String lalbel;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String classicName;

    /** 分类编号 */
    @Excel(name = "分类编号")
    private String classicCode;

    /** 分类描述 */
    @Excel(name = "分类描述")
    private String classicDesc;


    public Integer getBookCount() {
        return bookCount;
    }

    public void setBookCount(Integer bookCount) {
        this.bookCount = bookCount;
    }

    private Integer bookCount;

    public String getClassicName() {
        return classicName;
    }

    public void setClassicName(String classicName) {
        this.classicName = classicName;
    }

    public String getClassicCode() {
        return classicCode;
    }

    public void setClassicCode(String classicCode) {
        this.classicCode = classicCode;
    }

    public String getClassicDesc() {
        return classicDesc;
    }

    public void setClassicDesc(String classicDesc) {
        this.classicDesc = classicDesc;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setClassicId(String classicId)
    {
        this.classicId = classicId;
    }

    public String getClassicId()
    {
        return classicId;
    }
    public void setTreeCode(String treeCode)
    {
        this.treeCode = treeCode;
    }

    public String getTreeCode()
    {
        return treeCode;
    }
    public void setTreeName(String treeName)
    {
        this.treeName = treeName;
    }

    public String getTreeName()
    {
        return treeName;
    }
    public void setTreePid(String treePid)
    {
        this.treePid = treePid;
    }

    public String getTreePid()
    {
        return treePid;
    }
    public void setTreeDesc(String treeDesc)
    {
        this.treeDesc = treeDesc;
    }

    public String getTreeDesc()
    {
        return treeDesc;
    }
    public void setTreeDisplay(Long treeDisplay)
    {
        this.treeDisplay = treeDisplay;
    }

    public Long getTreeDisplay()
    {
        return treeDisplay;
    }
    public void setCreatebyId(String createbyId)
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId()
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName)
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName()
    {
        return createbyName;
    }

    private List<Type> children = new ArrayList<Type>();

    public List<Type> getChildren()
    {
        return children;
    }
    public void setChildren(List<Type> children)
    {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("classicId", getClassicId())
                .append("treeCode", getTreeCode())
                .append("treeName", getTreeName())
                .append("treePid", getTreePid())
                .append("treeDesc", getTreeDesc())
                .append("treeDisplay", getTreeDisplay())
                .append("createbyId", getCreatebyId())
                .append("createbyName", getCreatebyName())
                .append("createTime", getCreateTime())
                .toString();
    }

}
