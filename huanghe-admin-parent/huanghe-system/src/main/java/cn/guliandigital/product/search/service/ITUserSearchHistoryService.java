package cn.guliandigital.product.search.service;

import java.util.List;

import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;
import cn.guliandigital.product.search.domain.TUserSearchHistory;

/**
 * 用户检索历史Service接口
 * 
 * <AUTHOR>
 * @date 2020-10-08
 */
public interface ITUserSearchHistoryService 
{
    /**
     * 查询用户检索历史
     * 
     * @param id 用户检索历史ID
     * @return 用户检索历史
     */
        TUserSearchHistory selectTUserSearchHistoryById(String id);

    /**
     * 查询用户检索历史列表
     * 
     * @param tUserSearchHistory 用户检索历史
     * @return 用户检索历史集合
     */
    List<TUserSearchHistory> selectTUserSearchHistoryList(TUserSearchHistory tUserSearchHistory);

    /**
     * 新增用户检索历史
     * 
     * @param tUserSearchHistory 用户检索历史
     * @return 结果
     */
    int insertTUserSearchHistory(TUserSearchHistory tUserSearchHistory);

    /**
     * 修改用户检索历史
     * 
     * @param tUserSearchHistory 用户检索历史
     * @return 结果
     */
    int updateTUserSearchHistory(TUserSearchHistory tUserSearchHistory);

    /**
     * 批量删除用户检索历史
     * 
     * @param ids 需要删除的用户检索历史ID
     * @return 结果
     */
    int deleteTUserSearchHistoryByIds(String[] ids);

    /**
     * 删除用户检索历史信息
     * 
     * @param id 用户检索历史ID
     * @return 结果
     */
    int deleteTUserSearchHistoryById(String id);

    int updateStaus(List<TUserSearchHistory> tUserSearchHistories);

    int delBydelFlag(String[] ids);


    List<TUserSearchHistory> selectHotList(TUserSearchHistory tUserSearchHistory);

}
