package cn.guliandigital.product.menu.domain;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * Treeselect树结构实体类
 * 
 * <AUTHOR>
 */

@Data
public class MenuTreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private String id;

    /** 章节全路径 */
    private String fullName;

    /** 章节名称 */
    private String menuName;
    
    /** 起始页码 */
    private Integer startPageNo;
    
    private Integer pageNo;
    private Integer textPageNo;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<MenuTreeSelect> children;

    public MenuTreeSelect()
    {

    }

    public MenuTreeSelect(TProBookMenu menu)
    {
        this.id = menu.getId();
        this.fullName = menu.getFullMenuName();
        this.menuName = menu.getMenuName();
        this.startPageNo = menu.getStartPageNo();
        this.pageNo = menu.getPageNo();
        this.textPageNo = menu.getTextPageNo();
        this.children = menu.getChildren().stream().map(MenuTreeSelect::new).collect(Collectors.toList());
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public List<MenuTreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<MenuTreeSelect> children)
    {
        this.children = children;
    }
}
