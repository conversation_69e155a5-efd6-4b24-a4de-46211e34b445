package cn.guliandigital.product.menu.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.BigwordUtil;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.product.menu.domain.MenuTreeSelect;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.product.menu.mapper.TProBookMenuMapper;
import cn.guliandigital.product.menu.service.ITProBookMenuService;
import lombok.extern.slf4j.Slf4j;

/**
 * 书目章节树状结构Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-10-12
 */

@Slf4j
@Service
public class TProBookMenuServiceImpl implements ITProBookMenuService
{
    @Autowired
    private TProBookMenuMapper tProBookMenuMapper;
    
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询书目章节树状结构
     * 
     * @param id 书目章节树状结构ID
     * @return 书目章节树状结构
     */
    @Override
    public TProBookMenu selectTProBookMenuById(String id)
    {
        return tProBookMenuMapper.selectTProBookMenuById(id);
    }

    /**
     * 查询书目章节树状结构列表
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 书目章节树状结构
     */
    @Override
    public List<TProBookMenu> selectTProBookMenuList(TProBookMenu tProBookMenu)
    {
        return tProBookMenuMapper.selectTProBookMenuList(tProBookMenu);
    }

    /**
     * 新增书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    @Override
    public int insertTProBookMenu(TProBookMenu tProBookMenu)
    {
        tProBookMenu.setCreateTime(DateUtil.getCuurentDate());
        return tProBookMenuMapper.insertTProBookMenu(tProBookMenu);
    }

    /**
     * 修改书目章节树状结构
     * 
     * @param tProBookMenu 书目章节树状结构
     * @return 结果
     */
    @Override
    public int updateTProBookMenu(TProBookMenu tProBookMenu)
    {
        tProBookMenu.setUpdateTime(DateUtil.getCuurentDate());
        return tProBookMenuMapper.updateTProBookMenu(tProBookMenu);
    }

    /**
     * 批量删除书目章节树状结构
     * 
     * @param ids 需要删除的书目章节树状结构ID
     * @return 结果
     */
    @Override
    public int deleteTProBookMenuByIds(String[] ids)
    {
        return tProBookMenuMapper.deleteTProBookMenuByIds(ids);
    }

    /**
     * 删除书目章节树状结构信息
     * 
     * @param id 书目章节树状结构ID
     * @return 结果
     */
    @Override
    public int deleteTProBookMenuById(String id)
    {
        return tProBookMenuMapper.deleteTProBookMenuById(id);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 章节列表
     * @return 树结构列表
     */
    @Override
    public List<MenuTreeSelect> buildMenuTreeSelect(List<TProBookMenu> menus) {
        List<TProBookMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(MenuTreeSelect::new).collect(Collectors.toList());
    }

    @Override
	public List<MenuTreeSelect> buildMenuTreeSelectV2(String bookId) {
		
		TProBookMenu queryb = new TProBookMenu();
		queryb.setLevel(0);
		queryb.setBookId(bookId);
		queryb.setPid("0");
		List<TProBookMenu> booklist = tProBookMenuMapper.selectTProBookMenuList(queryb);
		TProBookMenu book = booklist.get(0);
		
		TProBookMenu query = new TProBookMenu();
		//query.setLevel(1);
		query.setBookId(bookId);
		query.setPid(book.getId());
		query.getParams().put("orderbydisplay", "asc");
		List<TProBookMenu> onelist = tProBookMenuMapper.selectTProBookMenuList(query);
		formatMenuNames(onelist);
		book.setChildren(onelist);
		
		//log.info("==>查询出1级目录size={}", onelist.size());
		int total = onelist.size();
		int idx = 0;
		for(TProBookMenu menu1 : onelist) {
			idx++;
			//log.info("==>进度：{}>>{} 1级目录 ：{}", idx, total, menu1.getMenuName());
			
			TProBookMenu query2 = new TProBookMenu();
			//query2.setLevel(2);
			query2.setBookId(bookId);
			query2.setPid(menu1.getId());
			query2.getParams().put("orderbydisplay", "asc");
			List<TProBookMenu> onelist2 = tProBookMenuMapper.selectTProBookMenuList(query2);
			//log.info("==>查询出2级目录size={}", onelist2.size());
			formatMenuNames(onelist2);
			menu1.setChildren(onelist2);
			
			for(TProBookMenu menu2 : onelist2) {
				//log.info("==>2级目录 ：{}", menu2.getMenuName());
				
				TProBookMenu query3 = new TProBookMenu();
				//query3.setLevel(3);
				query3.setBookId(bookId);
				query3.setPid(menu2.getId());
				query3.getParams().put("orderbydisplay", "asc");
				List<TProBookMenu> onelist3 = tProBookMenuMapper.selectTProBookMenuList(query3);
				//log.info("==>查询出3级目录size={}", onelist3.size());
				formatMenuNames(onelist3);
				menu2.setChildren(onelist3);				
			}
		}
		
		
		return booklist.stream().map(MenuTreeSelect::new).collect(Collectors.toList());
	}
    
    
    public void formatMenuNames(List<TProBookMenu> list) {
    	 for (TProBookMenu menu : list) {
             String menuName = menu.getMenuName();
             String fullMenuName = menu.getFullMenuName();
             menuName = StringUtil.replace(menuName, "&amp;", "&");
             menuName = StringUtil.replace(menuName, "(char)12288, ", " ");
             menuName = StringUtil.replace(menuName, "&#12288;", " ");
             menu.setMenuName(menuName);

             fullMenuName = StringUtil.replace(fullMenuName, "&amp;", "&");
             fullMenuName = StringUtil.replace(fullMenuName, "(char)12288, ", " ");
             fullMenuName = StringUtil.replace(fullMenuName, "&#12288;", " ");
             menu.setFullMenuName(fullMenuName);

             //查找大字 加class
             String fontMenuName = BigwordUtil.tranferBigwordSpan(menu.getMenuName());
             String fontFullMenuName = BigwordUtil.tranferBigwordSpan(menu.getFullMenuName());
             fontMenuName = StringUtil.replace(fontMenuName, "&#12288;", " ");
             fontFullMenuName = StringUtil.replace(fontFullMenuName, "&#12288;", " ");
             //log.info("==>转换结果 ：{}", fontMenuName);
             //log.info("==>转换结果 ：{}", fontFullMenuName);
             menu.setMenuName(fontMenuName);
             menu.setFullMenuName(fontFullMenuName);
             
             int pageNo = 0;
             int textPageNo = 0;

             String menuRediskey = RedisConstants.HUANGHE_MENUID_DISPLAY_MAPPING + menu.getBookId() +":"+ menu.getId();	                    
             pageNo = redisCache.getCacheObject(menuRediskey) == null ? 0: redisCache.getCacheObject(menuRediskey);
         	                 	
//         	 String menuTextRediskey = RedisConstants.HUANGHE_MENUID_DISPLAY_TEXT_MAPPING + menu.getBookId() +":"+ menu.getId();	                    
//         	 textPageNo = redisCache.getCacheObject(menuTextRediskey)== null ? 0: redisCache.getCacheObject(menuTextRediskey);
             
             menu.setPageNo(pageNo);
             menu.setTextPageNo(pageNo);
         }
    }
    /**
     * 构建前端所需要树结构
     *
     * @param menus 章节列表
     * @return 树结构列表
     */
    public List<TProBookMenu> buildMenuTree(List<TProBookMenu> menus)
    {
        List<TProBookMenu> returnList = new ArrayList<TProBookMenu>();
        List<String> tempList = new ArrayList<String>();
        for (TProBookMenu menu : menus)
        {
        	menu.setMenuName(StringUtil.replace(menu.getMenuName(), "&amp;", "&"));
        	menu.setFullMenuName(StringUtil.replace(menu.getFullMenuName(), "&amp;", "&"));
            tempList.add(menu.getId());
        }
        for (Iterator<TProBookMenu> iterator = menus.iterator(); iterator.hasNext();)
        {
            TProBookMenu menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getPid()))
            {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<TProBookMenu> list, TProBookMenu t)
    {
        // 得到子节点列表
        List<TProBookMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (TProBookMenu tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                // 判断是否有子节点
                Iterator<TProBookMenu> it = childList.iterator();
                while (it.hasNext())
                {
                    TProBookMenu n = it.next();
                    recursionFn(list, n);
                }
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<TProBookMenu> getChildList(List<TProBookMenu> list, TProBookMenu t)
    {
        List<TProBookMenu> tlist = new ArrayList<TProBookMenu>();
        Iterator<TProBookMenu> it = list.iterator();
        while (it.hasNext())
        {
            TProBookMenu n = it.next();
            if (StringUtil.isNotNull(n.getPid()) && n.getPid().equals(t.getId()))
            {
            	n.setMenuName(StringUtil.replace(n.getMenuName(), "&amp;", "&"));
            	n.setFullMenuName(StringUtil.replace(n.getFullMenuName(), "&amp;", "&"));
                tlist.add(n);
            }
        }
        //将子节点列表按照升序排列
        Collections.sort(tlist, Comparator.comparing(TProBookMenu::getDisplay));
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<TProBookMenu> list, TProBookMenu t)
    {
        return getChildList(list, t).size() > 0;
    }

	@Override
	public int deleteTProBookMenuByBookId(String bookId) {
		
		return tProBookMenuMapper.deleteTProBookMenuByBookId(bookId);
	}

	@Override
	public TProBookMenu selectTProBookMenuByPid(String pid) {
		return tProBookMenuMapper.selectTProBookMenuByPid(pid);
	}
}
