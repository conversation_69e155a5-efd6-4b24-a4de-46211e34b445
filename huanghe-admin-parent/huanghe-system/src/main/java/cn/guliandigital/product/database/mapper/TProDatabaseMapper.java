package cn.guliandigital.product.database.mapper;

import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.database.domain.TProDatabase;

import java.util.List;

/**
 * 数据库管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-14
 */
public interface TProDatabaseMapper 
{
    /**
     * 查询数据库管理
     * 
     * @param id 数据库管理ID
     * @return 数据库管理
     */
        TProDatabase selectTProDatabaseById(String id);

    /**
     * 查询数据库管理列表
     * 
     * @param tProDatabase 数据库管理
     * @return 数据库管理集合
     */
    List<TProDatabase> selectTProDatabaseList(TProDatabase tProDatabase);

    /**
     * 新增数据库管理
     * 
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    int insertTProDatabase(TProDatabase tProDatabase);

    /**
     * 修改数据库管理
     * 
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    int updateTProDatabase(TProDatabase tProDatabase);

    /**
     * 删除数据库管理
     * 
     * @param id 数据库管理ID
     * @return 结果
     */
    int deleteTProDatabaseById(String id);

    /**
     * 批量删除数据库管理
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTProDatabaseByIds(String[] ids);




    List<TProDatabase> selectRe();


    int updateCount(String orgdbId);


    int updateC(String dbId);


    TProDatabase selecDbId(String dbName);



    List<TProDatabase> selectTProDatabase(TProDatabase tProDatabase);
}
