package cn.guliandigital.product.readhistory.domain;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 用户阅读进度对象 t_user_read_book
 * 
 * <AUTHOR>
 * @date 2022-02-10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TUserReadBook extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 图书ID */
    @Excel(name = "图书ID")
    private String bookId;

    /** 章节ID */
    @Excel(name = "章节ID")
    private String menuId;

    /** 章节名称 */
    @Excel(name = "章节名称")
    private String menuName;

    /** 章节菜单全路径 */
    @Excel(name = "章节菜单全路径")
    private String menuPath;

    /** 机构ID */
    @Excel(name = "机构ID")
    private String orgId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** 更新人ID */
    @Excel(name = "更新人ID")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    /**
     * 主要责任人
     */
    private String mainResponsibility;

    /**
     * 书名
     */
    private String bookName;
    /**
     * 缩微图
     */
    private String thumbCoverUrl;

    /**
     * 是否已加入书架 false-未加入；true-已加入
     */
    private Boolean haveAddshelf;
    //书架ID
    private String shelfClassId;

    private String queryDate;
    
    //图文类型 P-图片  T-图文
    private String imageTextType; 
    
    //阅读模式
    private String readMode;
    
    //页码
    private Integer pageNo;
    
    
}
