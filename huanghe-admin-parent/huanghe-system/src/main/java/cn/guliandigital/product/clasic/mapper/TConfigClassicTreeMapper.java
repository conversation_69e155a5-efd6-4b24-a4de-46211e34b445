package cn.guliandigital.product.clasic.mapper;

import java.util.List;

import cn.guliandigital.product.book.vo.ClassTreeType;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;

/**
 * 分类树Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface TConfigClassicTreeMapper 
{
    /**
     * 查询分类树
     * 
     * @param id 分类树ID
     * @return 分类树
     */
        List<TConfigClassicTree> selectTConfigClassicTreeById(String id);

    /**
     * 查询分类树列表
     * 
     * @param tConfigClassicTree 分类树
     * @return 分类树集合
     */
    List<TConfigClassicTree> selectTConfigClassicTreeList(TConfigClassicTree tConfigClassicTree);

    /**
     * 新增分类树
     * 
     * @param tConfigClassicTree 分类树
     * @return 结果
     */
    int insertTConfigClassicTree(TConfigClassicTree tConfigClassicTree);

    /**
     * 修改分类树
     * 
     * @param tConfigClassicTree 分类树
     * @return 结果
     */
    int updateTConfigClassicTree(TConfigClassicTree tConfigClassicTree);

    /**
     * 删除分类树
     * 
     * @param id 分类树ID
     * @return 结果
     */
    int deleteTConfigClassicTreeById(String id);

    /**
     * 批量删除分类树
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTConfigClassicTreeByIds(String[] ids);
    /**
     * <AUTHOR>
     * @Description 根据分类id查询数据
     * @Date 2020/9/7 18:37
     * @param classicId:
     **/
    List<TConfigClassicTree> selectClassicTreeById(String classicId);
    TConfigClassicTree selectBySiName(String classicId);
    List<TConfigClassicTree> selectHasChild(String id);


    TConfigClassicTree selectP(String treePid);


    List<TConfigClassicTree> selectParent(String id);
    
    List<ClassTreeType> selectFourPartBehind(ClassTreeType type);
    
    List<ClassTreeType> selectResourceClass(ClassTreeType type);
    
    ClassTreeType selectById(String id);

    List<TConfigClassicTree> selectTConfigClassicTreeListS(TConfigClassicTree tConfigClassicTree);

    TConfigClassicTree selectByName(String treeName);
}
