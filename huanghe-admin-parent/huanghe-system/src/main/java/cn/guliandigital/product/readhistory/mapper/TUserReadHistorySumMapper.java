package cn.guliandigital.product.readhistory.mapper;

import java.util.List;

import cn.guliandigital.product.readhistory.domain.TUserReadHistory;
import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;

/**
 * 用户阅读历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
public interface TUserReadHistorySumMapper 
{
   

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史集合
     */
    List<TUserReadHistorySum> selectTUserReadHistorySumList(TUserReadHistorySum TUserReadHistorySum);

    /**
     * 新增用户阅读历史
     * 
     * @param TUserReadHistorySum 用户阅读历史
     * @return 结果
     */
    int insertTUserReadHistorySum(TUserReadHistorySum TUserReadHistorySum);

    /**
     * 修改用户阅读历史
     * 
     * @param TUserReadHistorySum 用户阅读历史
     * @return 结果
     */
    int updateTUserReadHistorySum(TUserReadHistorySum TUserReadHistorySum);

    /**
     * 删除用户阅读历史
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    int deleteTUserReadHistorySumById(String id);

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserReadHistorySumByIds(String[] ids);


    void updateReadTimeAndSearchPeoPle(TUserReadHistory tUserReadHistory);


    TUserReadHistorySum selectId(TUserReadHistory tUserReadHistory);


    List<TUserReadHistorySum> seletorganCount(TUserReadHistorySum tUserReadHistorySum);
}
