package cn.guliandigital.product.userbook.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 用户书架对象 t_user_bookshelf
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
public class TUserBookshelf extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 书架分类ID */
    private String classesId;

    /** 图书ID */
    private String bookId;

    /** 书名 */
    private String bookName;

    /** 书作者 */
    private String author;

    public String getImageTextType() {
        return imageTextType;
    }

    public void setImageTextType(String imageTextType) {
        this.imageTextType = imageTextType;
    }

    /** 书类型1 */
    private String imageTextType;


    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** $column.columnComment */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    private Integer delFlag;

    /** 图书封面路径 */
    private String coverUrl;




    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setClassesId(String classesId) 
    {
        this.classesId = classesId;
    }

    public String getClassesId() 
    {
        return classesId;
    }
    public void setBookId(String bookId) 
    {
        this.bookId = bookId;
    }

    public String getBookId() 
    {
        return bookId;
    }
    public void setBookName(String bookName) 
    {
        this.bookName = bookName;
    }

    public String getBookName() 
    {
        return bookName;
    }
    public void setAuthor(String author) 
    {
        this.author = author;
    }

    public String getAuthor() 
    {
        return author;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classesId", getClassesId())
            .append("bookId", getBookId())
            .append("bookName", getBookName())
            .append("author", getAuthor())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("coverUrl", getCoverUrl())
            .toString();
    }
}
