package cn.guliandigital.product.userbook.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.userbook.domain.TUserBookshelf;
import cn.guliandigital.product.userbook.mapper.TUserBookshelfMapper;
import cn.guliandigital.product.userbook.service.ITUserBookshelfService;

/**
 * 用户书架Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
@Service
public class TUserBookshelfServiceImpl implements ITUserBookshelfService
{
    @Autowired
    private TUserBookshelfMapper tUserBookshelfMapper;
    @Autowired
    private TProBooksMapper tProBooksMapper;

    /**
     * 查询用户书架
     * 
     * @param id 用户书架ID
     * @return 用户书架
     */
    @Override
    public TUserBookshelf selectTUserBookshelfById(String id)
    {
        return tUserBookshelfMapper.selectTUserBookshelfById(id);
    }

    /**
     * 查询用户书架列表
     * 
     * @param tUserBookshelf 用户书架
     * @return 用户书架
     */
    @Override
    public List<TUserBookshelf> selectTUserBookshelfList(TUserBookshelf tUserBookshelf)
    {
        return tUserBookshelfMapper.selectTUserBookshelfList(tUserBookshelf);
    }

    /**
     * 新增用户书架
     * 
     * @param tUserBookshelf 用户书架
     * @return 结果
     */
    @Override
    public int insertTUserBookshelf(TUserBookshelf tUserBookshelf)
    {
        tUserBookshelf.setId(IdUtils.simpleUUID());
        tUserBookshelf.setCreateTime(DateUtil.getCuurentDate());
        TProBooks tProBooks = tProBooksMapper.selectTProBooksById(tUserBookshelf.getBookId());
        if(tProBooks != null){
            tUserBookshelf.setBookName(tProBooks.getBookName());
            tUserBookshelf.setAuthor(tProBooks.getMainResponsibility());
        }
        return tUserBookshelfMapper.insertTUserBookshelf(tUserBookshelf);
    }

    /**
     * 修改用户书架
     * 
     * @param tUserBookshelf 用户书架
     * @return 结果
     */
    @Override
    public int updateTUserBookshelf(TUserBookshelf tUserBookshelf)
    {
        tUserBookshelf.setUpdateTime(DateUtil.getCuurentDate());
        return tUserBookshelfMapper.updateTUserBookshelf(tUserBookshelf);
    }

    /**
     * 批量删除用户书架
     * 
     * @param ids 需要删除的用户书架ID
     * @return 结果
     */
    @Override
    public int deleteTUserBookshelfByIds(String[] ids)
    {
        return tUserBookshelfMapper.deleteTUserBookshelfByIds(ids);
    }

    /**
     * 删除用户书架信息
     * 
     * @param id 用户书架ID
     * @return 结果
     */
    @Override
    public int deleteTUserBookshelfById(String id)
    {
        return tUserBookshelfMapper.deleteTUserBookshelfById(id);
    }
}
