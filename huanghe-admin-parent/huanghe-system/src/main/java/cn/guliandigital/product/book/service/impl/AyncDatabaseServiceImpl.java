package cn.guliandigital.product.book.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.book.service.IAsyncDatabaseService;
import cn.guliandigital.product.book.vo.DataBaseVo;
import cn.guliandigital.product.database.domain.TProDatabase;
import cn.guliandigital.product.database.mapper.TProDatabaseMapper;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service("asyncDatabaseService")
public class AyncDatabaseServiceImpl implements IAsyncDatabaseService{

	

	@Autowired
	private TProBooksMapper tProBooksMapper;

	@Autowired
	private TProDatabaseMapper tProDatabaseMapper;
	 
	 
	
	
	@Async("sumcountExecutor")
	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW, rollbackFor = { RuntimeException.class, Exception.class })
	public void sumDatabaseCount() {
		log.info("==>重新更新数据库数量");
		List<DataBaseVo> dataBaseVoList = tProBooksMapper.selectCountGroupByDbId();
		for (DataBaseVo dataBaseVo : dataBaseVoList) {
			//根据数据库id查询，更新数据库表资源量
			TProDatabase database = new TProDatabase();
			database.setDbId(dataBaseVo.getDbId());
			database.setBookCount(dataBaseVo.getDbCount());
			log.info("===>更新数据 {}，{}", dataBaseVo.getDbId(), dataBaseVo.getDbCount());
			tProDatabaseMapper.updateTProDatabase(database);
		}

		
	}

}
