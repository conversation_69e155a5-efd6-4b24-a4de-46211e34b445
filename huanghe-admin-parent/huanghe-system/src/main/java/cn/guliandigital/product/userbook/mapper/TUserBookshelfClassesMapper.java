package cn.guliandigital.product.userbook.mapper;

import cn.guliandigital.product.userbook.domain.TUserBookshelfClasses;

import java.util.List;

/**
 * 用户书架分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
public interface TUserBookshelfClassesMapper 
{
    /**
     * 查询用户书架分类
     * 
     * @param id 用户书架分类ID
     * @return 用户书架分类
     */
        TUserBookshelfClasses selectTUserBookshelfClassesById(String id);

    /**
     * 查询用户书架分类列表
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 用户书架分类集合
     */
    List<TUserBookshelfClasses> selectTUserBookshelfClassesList(TUserBookshelfClasses tUserBookshelfClasses);

    /**
     * 新增用户书架分类
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 结果
     */
    int insertTUserBookshelfClasses(TUserBookshelfClasses tUserBookshelfClasses);

    /**
     * 修改用户书架分类
     * 
     * @param tUserBookshelfClasses 用户书架分类
     * @return 结果
     */
    int updateTUserBookshelfClasses(TUserBookshelfClasses tUserBookshelfClasses);

    /**
     * 删除用户书架分类
     * 
     * @param id 用户书架分类ID
     * @return 结果
     */
    int deleteTUserBookshelfClassesById(String id);

    /**
     * 批量删除用户书架分类
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUserBookshelfClassesByIds(String[] ids);
    
    
    List<TUserBookshelfClasses> selectClassesList(TUserBookshelfClasses tUserBookshelfClasses);
}
