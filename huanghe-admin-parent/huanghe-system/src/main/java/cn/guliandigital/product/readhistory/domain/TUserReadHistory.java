package cn.guliandigital.product.readhistory.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.guliandigital.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户阅读历史对象 t_user_read_history
 *
 * <AUTHOR>
 * @date 2020-10-09
 */
@Data
public class TUserReadHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String id;

    private String orgId;

    private Long readPeople;

    private Long visitCount;

    private Long searchCount;


    /**
     * 图书ID
     */
    private String bookId;

    /**
     * 章节ID
     */
    private String menuId;

    /**
     * 章节名称
     */
    private String menuName;

    /**
     * 章节菜单全路径
     */
    private String menuPath;

    /**
     * 阅读时长 单位秒
     */
    private Long readTime;

    /**
     * $column.columnComment
     */
    private String createbyId;
    /**
     * 解析时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd") //入参
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") //出参
    private Date createTime;

    /**
     * 创建人
     */
    private String createbyName;

    /**
     * $column.columnComment
     */
    private String updatebyId;

    /**
     * 更新人
     */
    private String updatebyName;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 主要责任人
     */
    private String mainResponsibility;

    /**
     * 书名
     */
    private String bookName;
    /**
     * 缩微图
     */
    private String thumbCoverUrl;

    /**
     * 是否已加入书架 false-未加入；true-已加入
     */
    private Boolean haveAddshelf;
    //书架ID
    private String shelfClassId;

    private String queryDate;

    /**
     * 阅读类型  R-阅读 Q-检索 C-复制  Y-引用 N-笔记
     */
    private String readType;
    
  //图文类型 P-图片  T-图文
    private String imageTextType; 
    
    //阅读模式
    private String readMode;
    
    //页码
    private Integer pageNo;
    /**
     *数据来源  P-平台  A-小程序
     */
    private String dataFrom;
    

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuPath(String menuPath) {
        this.menuPath = menuPath;
    }

    public String getMenuPath() {
        return menuPath;
    }

    public void setReadTime(Long readTime) {
        this.readTime = readTime;
    }

    public Long getReadTime() {
        return readTime;
    }

    public void setCreatebyId(String createbyId) {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() {
        return createbyId;
    }

    public void setCreatebyName(String createbyName) {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() {
        return createbyName;
    }

    public void setUpdatebyId(String updatebyId) {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() {
        return updatebyId;
    }

    public void setUpdatebyName(String updatebyName) {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() {
        return updatebyName;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public String getMainResponsibility() {
        return mainResponsibility;
    }

    public void setMainResponsibility(String mainResponsibility) {
        this.mainResponsibility = mainResponsibility;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getThumbCoverUrl() {
        return thumbCoverUrl;
    }

    public void setThumbCoverUrl(String thumbCoverUrl) {
        this.thumbCoverUrl = thumbCoverUrl;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Long getReadPeople() {
        return readPeople;
    }

    public void setReadPeople(Long readPeople) {
        this.readPeople = readPeople;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("bookId", getBookId())
                .append("menuId", getMenuId())
                .append("menuName", getMenuName())
                .append("menuPath", getMenuPath())
                .append("readTime", getReadTime())
                .append("createbyId", getCreatebyId())
                .append("createbyName", getCreatebyName())
                .append("createTime", getCreateTime())
                .append("updatebyId", getUpdatebyId())
                .append("updatebyName", getUpdatebyName())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("mainResponsibility", getMainResponsibility())
                .append("bookName", getBookName())
                .toString();
    }
}
