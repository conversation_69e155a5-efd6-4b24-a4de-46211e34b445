package cn.guliandigital.product.clasic.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.enums.ClassicStatus;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.clasic.domain.TConfigClassic;
import cn.guliandigital.product.clasic.mapper.TConfigClassicMapper;
import cn.guliandigital.product.clasic.service.ITConfigClassicService;
import lombok.extern.slf4j.Slf4j;

/**
 * 分类定义Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@Slf4j
@Service
public class TConfigClassicServiceImpl implements ITConfigClassicService {
    
	@Autowired
    private TConfigClassicMapper tConfigClassicMapper;

    @Autowired
    private TProBooksMapper tProBooksMapper;

    /**
     * 查询分类定义
     *
     * @param id 分类定义ID
     * @return 分类定义
     */
    @Override
    public TConfigClassic selectTConfigClassicById(String id) {
        return tConfigClassicMapper.selectTConfigClassicById(id);
    }

    /**
     * 查询分类定义列表
     *
     * @param tConfigClassic 分类定义
     * @return 分类定义
     */
    @Override
    public List<TConfigClassic> selectTConfigClassicList(TConfigClassic tConfigClassic) {
        return tConfigClassicMapper.selectTConfigClassicList(tConfigClassic);
    }

    /**
     * 新增分类定义
     *
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    @Override
    public int insertTConfigClassic(TConfigClassic tConfigClassic) {
        List<TConfigClassic> tConfigClassics = tConfigClassicMapper.selectTConfigClassicList(new TConfigClassic() {{
            setClassicName(tConfigClassic.getClassicName());
        }});
        if(tConfigClassics.size()>0){
            throw new ServiceException("该分类已存在!");
        }
        tConfigClassic.setCreateTime(DateUtil.getCuurentDate());
        tConfigClassic.setId(IdUtils.simpleUUID());
        return tConfigClassicMapper.insertTConfigClassic(tConfigClassic);
    }

    /**
     * 修改分类定义
     *
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    @Override
    public int updateTConfigClassic(TConfigClassic tConfigClassic) {
        List<TConfigClassic> tConfigClassics = tConfigClassicMapper.selectTConfigClassicList(new TConfigClassic() {{
            setClassicName(tConfigClassic.getClassicName());
        }});
        if(tConfigClassics.size()>0){
            throw new ServiceException("该分类已存在!");
        }
        return tConfigClassicMapper.updateTConfigClassic(tConfigClassic);
    }

    /**
     * 批量删除分类定义
     *
     * @param ids 需要删除的分类定义ID
     * @return 结果
     */
    @Override
    public int deleteTConfigClassicByIds(String[] ids) {

        for (String id : ids) {
            TConfigClassic tConfigClassic = tConfigClassicMapper.selectTConfigClassicById(id);
            if(StringUtil.equals(tConfigClassic.getClassicStatus(), ClassicStatus.UP.getCode())){
                throw new ServiceException("当前分类未下架，不能删除！");
            }

        }
        return tConfigClassicMapper.deleteTConfigClassicByIds(ids);
    }

    /**
     * 删除分类定义信息
     *
     * @param id 分类定义ID
     * @return 结果
     */
    @Override
    public int deleteTConfigClassicById(String id) {
        return tConfigClassicMapper.deleteTConfigClassicById(id);
    }

    @Override

    public int batchDel(List<TConfigClassic> tConfigClassics) {
        for (TConfigClassic tConfigClassic : tConfigClassics) {
            String id = tConfigClassic.getId();
            String[] ids = id.split(",");
            tConfigClassicMapper.deleteTConfigClassicByIds(ids);
        }
        return 1;
    }

    @Override
    public int changeStatus(TConfigClassic tConfigClassic) {
        TConfigClassic tConfigClassic1 = tConfigClassicMapper.selectTConfigClassicById(tConfigClassic.getId());
        if(tConfigClassic1.getClassicStatus().equals(ClassicStatus.UP.getCode())){
            List<TProBooks> tProBooks = tProBooksMapper.selectTProBooksList(new TProBooks() {{
                setResourceClassesId(tConfigClassic.getId());
            }});
            if(tProBooks.size()>0){
                throw new ServiceException("当前分类已被图书应用，无法停用！");
            }
        }
        return tConfigClassicMapper.updateTConfigClassic(tConfigClassic);
    }


}
