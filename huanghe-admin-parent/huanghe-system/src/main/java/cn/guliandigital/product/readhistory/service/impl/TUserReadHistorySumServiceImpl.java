package cn.guliandigital.product.readhistory.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;
import cn.guliandigital.product.readhistory.mapper.TUserReadHistorySumMapper;
import cn.guliandigital.product.readhistory.service.ITUserReadHistorySumService;

/**
 * 用户阅读历史Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-10-09
 */
@Service
public class TUserReadHistorySumServiceImpl implements ITUserReadHistorySumService 
{
    @Autowired
    private TUserReadHistorySumMapper tUserReadHistorySumMapper;

   

    /**
     * 查询用户阅读历史列表
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 用户阅读历史
     */
    @Override
    public List<TUserReadHistorySum> selectTUserReadHistorySumList(TUserReadHistorySum tUserReadHistory)
    {
        return tUserReadHistorySumMapper.selectTUserReadHistorySumList(tUserReadHistory);
    }

    /**
     * 新增用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    @Override
    public int insertTUserReadHistorySum(TUserReadHistorySum tUserReadHistory)
    {
        tUserReadHistory.setId(IdUtils.simpleUUID());
        tUserReadHistory.setCreateTime(DateUtil.getCuurentDate());
        return tUserReadHistorySumMapper.insertTUserReadHistorySum(tUserReadHistory);
    }

    /**
     * 修改用户阅读历史
     * 
     * @param tUserReadHistory 用户阅读历史
     * @return 结果
     */
    @Override
    public int updateTUserReadHistorySum(TUserReadHistorySum tUserReadHistory)
    {
        tUserReadHistory.setUpdateTime(DateUtil.getCuurentDate());
        return tUserReadHistorySumMapper.updateTUserReadHistorySum(tUserReadHistory);
    }

    /**
     * 批量删除用户阅读历史
     * 
     * @param ids 需要删除的用户阅读历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadHistorySumByIds(String[] ids)
    {
        return tUserReadHistorySumMapper.deleteTUserReadHistorySumByIds(ids);
    }

    /**
     * 删除用户阅读历史信息
     * 
     * @param id 用户阅读历史ID
     * @return 结果
     */
    @Override
    public int deleteTUserReadHistorySumById(String id)
    {
        return tUserReadHistorySumMapper.deleteTUserReadHistorySumById(id);
    }

    @Override
    public List<TUserReadHistorySum> organCount(TUserReadHistorySum tUserReadHistorySum) {
        return tUserReadHistorySumMapper.seletorganCount(tUserReadHistorySum);
    }

}
