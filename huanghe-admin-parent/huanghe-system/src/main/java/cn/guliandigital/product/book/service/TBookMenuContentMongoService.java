package cn.guliandigital.product.book.service;

import java.util.List;
import java.util.Map;

import cn.guliandigital.product.book.domain.TBookMenuContentMongo;

public interface TBookMenuContentMongoService{

	
	void insertOrUpdate(TBookMenuContentMongo t);

	
	TBookMenuContentMongo findByCondition(Map<String, Object> condition);
	
	
	/**
     * 添加
     *
     * @param
     */
    void add(TBookMenuContentMongo t);

    /**
     * 根据id删除
     *
     * @param id
     * @return 
     */
    void deleteById(String id);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    TBookMenuContentMongo findById(String id);

    /**
     * 更新
     *
     * @param role
     */
    void updateById(TBookMenuContentMongo t);

	/**
	 * 通过条件查询集合
	 * @param condition
	 * @return
	 */
    List<TBookMenuContentMongo> findListByCondition(Map<String, Object> condition);

    
    int deleteByCondition(String bookId);
}