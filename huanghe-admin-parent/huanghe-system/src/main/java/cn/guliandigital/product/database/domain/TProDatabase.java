package cn.guliandigital.product.database.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 数据库管理对象 t_pro_database
 * 
 * <AUTHOR>
 * @date 2020-09-14
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TProDatabase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */

   @Excel(name="数据库")
    private String dbId;

    public String getDbId() {
        return dbId;
    }

    public void setDbId(String dbId) {
        this.dbId = dbId;
    }

    /** 数据库编号 */
    @Excel(name = "数据库编号")
    private String dbCode;

    /** 数据库名称 */
    @Excel(name = "数据库名称")
    private String dbName;

    /** 产品数量 */
    @Excel(name = "产品数量")
    private Integer bookCount;

    /** 状态 0-下架 1-上架 */
    @Excel(name = "状态 0-下架 1-上架")
    private String dbStatus;

    /** 产品价格 */
    @Excel(name = "产品价格")
    private BigDecimal price;

    /** 折扣 */
    @Excel(name = "折扣")
    private BigDecimal discount;

    //排序
    private Integer dataDisplay;

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    private String count;
    /** 最后价格 */
    @Excel(name = "最后价格")
    private BigDecimal finalPrice;

    /** 图片路径 */
    @Excel(name = "图片路径")
    private String coverUrl;

    //大图路径
    private String bigCoverUrl;

    /** 简介 */
    @Excel(name = "简介")
    private String dbDesc;

    /** $column.columnComment */
    @Excel(name = "创建者")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
    @Excel(name = "创建人")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    private String appletPic;


    private String bookNum;
    /**
     * 小程序书城图片路径
     */
    private String appScPic;


    public String getBookNum() {
        return bookNum;
    }

    public void setBookNum(String bookNum) {
        this.bookNum = bookNum;
    }

    public String getAppletPic() {
        return appletPic;
    }

    public void setAppletPic(String appletPic) {
        this.appletPic = appletPic;
    }

    public Integer getDataDisplay() {
        return dataDisplay;
    }

    public void setDataDisplay(Integer dataDisplay) {
        this.dataDisplay = dataDisplay;
    }

    public String getDbNameEn() {
        return dbNameEn;
    }

    public void setDbNameEn(String dbNameEn) {
        this.dbNameEn = dbNameEn;
    }

    private String dbNameEn;
    public void setDbCode(String dbCode)
    {
        this.dbCode = dbCode;
    }

    public String getDbCode()
    {
        return dbCode;
    }
    public void setDbName(String dbName)
    {
        this.dbName = dbName;
    }

    public String getDbName()
    {
        return dbName;
    }
    public void setBookCount(Integer bookCount)
    {
        this.bookCount = bookCount;
    }

    public Integer getBookCount() {
        return bookCount;
    }

    public String getDbStatus() {
        return dbStatus;
    }

    public void setDbStatus(String dbStatus) {
        this.dbStatus = dbStatus;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }
    public void setDiscount(BigDecimal discount)
    {
        this.discount = discount;
    }

    public BigDecimal getDiscount()
    {
        return discount;
    }
    public void setFinalPrice(BigDecimal finalPrice)
    {
        this.finalPrice = finalPrice;
    }

    public BigDecimal getFinalPrice()
    {
        return finalPrice;
    }
    public void setCoverUrl(String coverUrl)
    {
        this.coverUrl = coverUrl;
    }

    public String getCoverUrl()
    {
        return coverUrl;
    }
    public void setDbDesc(String dbDesc)
    {
        this.dbDesc = dbDesc;
    }

    public String getDbDesc()
    {
        return dbDesc;
    }
    public void setCreatebyId(String createbyId)
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId()
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName)
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName()
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId)
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId()
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName)
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName()
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("dbId", getDbId())
                .append("dbCode", getDbCode())
                .append("dbName", getDbName())
                .append("bookCount", getBookCount())
                .append("dbStatus", getDbStatus())
                .append("price", getPrice())
                .append("discount", getDiscount())
                .append("finalPrice", getFinalPrice())
                .append("coverUrl", getCoverUrl())
                .append("dbDesc", getDbDesc())
                .append("createbyId", getCreatebyId())
                .append("createbyName", getCreatebyName())
                .append("createTime", getCreateTime())
                .append("updatebyId", getUpdatebyId())
                .append("updatebyName", getUpdatebyName())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("count",getCount())
                .append("dbNameEn",getDbNameEn())
                .toString();
    }
}

