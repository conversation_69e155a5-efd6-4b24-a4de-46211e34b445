package cn.guliandigital.plat.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import cn.guliandigital.common.enums.UserStatus;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.wxamp.domain.TWxampRight;
import cn.guliandigital.wxamp.service.ITWxampRightService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.domain.model.WebLoginBody;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.UserStatus;
import cn.guliandigital.common.qq.QQProperties;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.http.HttpClientUtils;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.common.utils.sign.Md5Utils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.common.wechat.WeixinLoginProperties;
import cn.guliandigital.order.domain.TProOrder;
import cn.guliandigital.order.mapper.TProOrderMapper;
import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.plat.domain.TPlatOrganIp;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.plat.mapper.TPlatOrganMapper;
import cn.guliandigital.plat.mapper.TPlatUserMapper;
import cn.guliandigital.plat.service.ITPlatOrganIpService;
import cn.guliandigital.plat.service.ITPlatUserService;
import lombok.extern.slf4j.Slf4j;

/**
 * 平台用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-09-15
 */
@Slf4j
@Service
public class TPlatUserServiceImpl implements ITPlatUserService {

    @Autowired
    private RedisCache redisCache;

    @Resource
    private QQProperties qqProperties;

    @Autowired
    private TPlatUserMapper tPlatUserMapper;

    @Resource
    private WeixinLoginProperties weixinLoginProperties;

    @Autowired
    private TPlatOrganMapper tPlatOrganMapper;

    @Autowired
    private ITPlatOrganIpService tPlatOrganIpService;

    @Autowired
    private TProOrderMapper tProOrderMapper;
    @Autowired
    private ITWxampRightService itWxampRightService;

    /**
     * 查询平台用户
     *
     * @param id 平台用户ID
     * @return 平台用户
     */
    @Override
    public TPlatUser selectTPlatUserById(String id) {
        //TPlatUser tPlatUser = tPlatUserMapper.selectTPlatUserById(id);
        return tPlatUserMapper.selectTPlatUserById(id);
    }

    /**
     * 查询平台用户列表
     *
     * @param tPlatUser 平台用户
     * @return 平台用户
     */
    @Override
    public List<TPlatUser> selectTPlatUserList(TPlatUser tPlatUser) {
        return tPlatUserMapper.selectTPlatUserList(tPlatUser);
    }

    /**
     * 新增平台用户
     *
     * @param tPlatUser 平台用户
     * @return 结果
     * @throws Exception
     */
    @Override
    public int insertTPlatUser(TPlatUser tPlatUser) throws Exception {
        String id = IdUtils.simpleUUID();
        tPlatUser.setId(id);
        tPlatUser.setCreatebyId(id);
        tPlatUser.setIpArea(IpUtils.getHostIp());
//        tPlatUser.setUserStatus("0");
        tPlatUser.setCreatebyName(tPlatUser.getUserName());
        tPlatUser.setCreateTime(DateUtil.getCuurentDate());
        if (!StringUtil.isEmpty(tPlatUser.getLoginPass())) {
            tPlatUser.setLoginPass(Md5Utils.hash(tPlatUser.getLoginPass()));//登陆密码md5加密
        }
        tPlatUser.setDelFlag(0);
        return tPlatUserMapper.insertTPlatUser(tPlatUser);
    }

    /**
     * 修改平台用户
     *
     * @param tPlatUser 平台用户
     * @return 结果
     * @throws Exception
     */
    @Override
    public int updateTPlatUser(TPlatUser tPlatUser) throws Exception {
        TPlatUser tPlatUser1 = tPlatUserMapper.selectTPlatUserById(tPlatUser.getId());
        if (tPlatUser.getLoginPass() != null) {
            if (tPlatUser1.getLoginPass().equals(tPlatUser.getLoginPass())) {
                tPlatUser.setLoginPass(tPlatUser.getLoginPass());
            } else {
                tPlatUser.setLoginPass(Md5Utils.hash(tPlatUser.getLoginPass()));
            }

        }
        tPlatUser.setUpdateTime(DateUtil.getCuurentDate());
        return tPlatUserMapper.updateTPlatUser(tPlatUser);
    }

    /**
     * 批量删除平台用户
     *
     * @param ids 需要删除的平台用户ID
     * @return 结果
     */
    @Override
    public int deleteTPlatUserByIds(String[] ids) {
        return tPlatUserMapper.deleteTPlatUserByIds(ids);
    }

    /**
     * 删除平台用户信息
     *
     * @param id 平台用户ID
     * @return 结果
     */
    @Override
    public int deleteTPlatUserById(String id) {
        return tPlatUserMapper.deleteTPlatUserById(id);
    }

    @Override
    public TPlatUser userLogin(WebLoginBody body) throws Exception {
        if (!Strings.isNullOrEmpty(body.getUserTel()) || !Strings.isNullOrEmpty(body.getUserEmail())) {
            TPlatUser tPlatUser = new TPlatUser();
            tPlatUser.setUserTel(body.getUserTel());
            //tPlatUser.setUserEmail(body.getUserEmail());
            TPlatUser loginUser = tPlatUserMapper.selectLoginUser(tPlatUser);
            if (loginUser == null) {
                loginUser = new TPlatUser();
                loginUser.setErrorMsg("手机号/昵称不存在");
                return loginUser;
            }
            if (loginUser.getUserStatus().equals(UserStatus.DISABLE.getCode())) {
                loginUser.setErrorMsg("当前用户已停用!");
            }
            if (!Strings.isNullOrEmpty(loginUser.getOrgId())) {
                TPlatOrgan organ = tPlatOrganMapper.selectTPlatOrganById(loginUser.getOrgId());
                if (organ != null) {
                    loginUser.setOrgName(organ.getOrgName());
                }
            }
            if (!Strings.isNullOrEmpty(body.getUserTel())) {
                loginUser.setLoginName(body.getUserTel());
            } else {
                if (!Strings.isNullOrEmpty(body.getUserEmail())) {
                    loginUser.setLoginName(body.getUserEmail());
                }
            }
            if (loginUser != null) {
                //将输入的密码MD5加密后对比密码是否一致
                if (!Strings.isNullOrEmpty(body.getPassword())) {
                    if (!StringUtil.equals(Md5Utils.hash(body.getPassword()), loginUser.getLoginPass())) {
                        loginUser = new TPlatUser();
                        loginUser.setErrorMsg("密码不正确");
//                        //获取redis计数密码登录错误次数
                        Object cacheObject = redisCache.getCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel());
                        if (Objects.isNull(cacheObject)) {
                            int num = 1;
                            redisCache.setCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel(), num);
                        } else {
                            int num = redisCache.getCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel());
                            if (num == 5) {
                                loginUser.setErrorMsg("账号锁定，请使用手机号及验证码登录。");
                            } else {
                                redisCache.setCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel(), num + 1);
                            }
                        }
                        return loginUser;
                    } else {
                        Object cacheObject = redisCache.getCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel());
                        if (Objects.isNull(cacheObject)) {
                            return loginUser;
                        } else {
                            int num = redisCache.getCacheObject(RedisConstants.LOGIN_PASS_FAIL + body.getUserTel());
                            if (num == 5) {
                                loginUser.setErrorMsg("账号锁定，请使用手机号及验证码登录。");
                            }
                        }
                        return loginUser;
                    }
                } else {
                    return loginUser;
                }
            }
        }
        return null;
    }

    @SuppressWarnings({"resource"})
    @Override
    public TPlatUser getUserInfoByAccessToken(String accessToken, String appUserid, String refreshToken, Integer expiresIn) throws Exception {
        if (StringUtil.isEmpty(accessToken)) {
            return null;
            //"accessToken为空"
        }
        String url = null;
        url = "https://api.weixin.qq.com/sns/userinfo?" +
                "access_token=" +
                accessToken +
                "&openid=" +
                appUserid;
        try {
            JSONObject wechatAccessToken = HttpClientUtils.httpGet(url);
            if (wechatAccessToken.get("errcode") != null) {
                log.error("获取accessToken失败");
            }
            String unionid = wechatAccessToken.getString("unionid");
            String wxName = wechatAccessToken.getString("nickname");
            String userSex = wechatAccessToken.getString("sex");
            String wxCoverUrl = wechatAccessToken.getString("headimgurl");
            log.info(unionid + "unionid" + "nikeName" + wxName + "refreshToken" + refreshToken);
            TPlatUser user = tPlatUserMapper.selectByUnionid(appUserid);
            if (user != null) {
                log.info("==>查询的实体：{}", user.toString());
                user.setWxCoverUrl(wxCoverUrl);
                user.setUserSex(userSex);
                user.setWxName(wxName);
                user.setUpdateTime(DateUtil.getCuurentDate());
                user.setLastLoginTime(DateUtil.getCuurentDate());
                tPlatUserMapper.updateTPlatUser(user);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid, refreshToken);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid, expiresIn);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid, accessToken);
                log.info("修改的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "unionid:" + unionid + "," + "expiresIn:" + expiresIn);
                user.setRefreshToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid));
                user.setExpiresIn(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid));
                user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid));
                user.setAppUserid(appUserid);
                return user;
            } else {
                user = new TPlatUser();
                log.info("新增的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "unionid:" + unionid + "expiresIn:" + expiresIn);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid, refreshToken);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid, expiresIn);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid, accessToken);
                log.info("此处refreshToken为：" + redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid));
                user.setExpiresIn(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid));
                user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid));
                user.setId(IdUtils.simpleUUID());
                user.setAppUserid(appUserid);
                user.setDelFlag(0);
                user.setUserType("P");
                //用户类型  S-机构管理员  P-普通用户
                user.setUserSource("P");
                //用户来源 P-pc端 A-app
                user.setUserStatus("0");
                //用户状态  0-正常 -1 停用
                user.setWxCoverUrl(wxCoverUrl);
                user.setUserSex(userSex);
                user.setWxName(wxName);
                user.setLastLoginTime(DateUtil.getCuurentDate());
                user.setCreateTime(DateUtil.getCuurentDate());
                tPlatUserMapper.insertTPlatUser(user);
                return user;
            }

        } catch (Exception e) {
            log.error("error", e);
            throw e;
        }

    }


    @SuppressWarnings({"resource"})
    @Override
    public void refreshAccessToken(String appUserid, String unionid, String refreshtoken) throws Exception {
        String appID = weixinLoginProperties.getWeixinappID();
        TPlatUser user = tPlatUserMapper.selectByUnionid(appUserid);
        if (null == user) {
            user = new TPlatUser();
        }
        String uri = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid="
                + appID + "&grant_type=refresh_token&refresh_token=" + refreshtoken;

        try {
            JSONObject wechatAccessToken = HttpClientUtils.httpGet(uri);
            if (wechatAccessToken.get("errcode") != null) {
                log.error("获取accessToken失败");
            }
            String accessToken = wechatAccessToken.getString("access_token");
            String refreshToken = wechatAccessToken.getString("refresh_token");
            appUserid = wechatAccessToken.getString("openid");
            Integer expiresIn = wechatAccessToken.getInteger("expires_in");
            redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid, refreshToken);
            user.setRefreshToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid));
            redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid, expiresIn);
            user.setExpiresIn(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + unionid));
            redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid, accessToken);
            user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid));
            log.info("refreshAccessToken的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "unionid:" + unionid + "expiresIn:" + expiresIn);
        } catch (Exception e) {
            log.error("error", e);
            throw e;
        }

    }

    @Override
    public TPlatUser selectByUnionid(String appUserid) {
        return tPlatUserMapper.selectByUnionid(appUserid);
    }

    @Override
    public TPlatUser getQQUserInfo(String accessToken, String appUserid, String refreshToken, Integer expiresIn) throws Exception {
        if (StringUtil.isEmpty(accessToken)) {
            return null;
            //"accessToken为空"
        }
        String url = null;


        url = "https://graph.qq.com/user/get_user_info?" +
                "access_token=" +
                accessToken +
                "oauth_consumer_key=" + qqProperties.getClient_id() +
                "&openid=" +
                appUserid;
        try {
            JSONObject wechatAccessToken = HttpClientUtils.httpGet(url);
            if (wechatAccessToken.get("errcode") != null) {
                log.error("获取accessToken失败");
            }
            String qqName = wechatAccessToken.getString("nickname");
            String userSex = wechatAccessToken.getString("gender");
            String qqCoverUrl = wechatAccessToken.getString("figureurl_qq_1");
            log.info("nikeName" + qqName + "refreshToken" + refreshToken);
            TPlatUser user = tPlatUserMapper.selectByUnionid(appUserid);
            if (user != null) {
                log.info("==>查询的实体：{}", user.toString());
                user.setQqCoverUrl(qqCoverUrl);
                user.setUserSex(userSex);
                user.setQqName(qqName);
                user.setUpdateTime(DateUtil.getCuurentDate());
                user.setLastLoginTime(DateUtil.getCuurentDate());
                tPlatUserMapper.updateTPlatUser(user);
                redisCache.setCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + appUserid, refreshToken);
                redisCache.setCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + accessToken + appUserid, accessToken);
                log.info("修改的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "expiresIn:" + expiresIn);
                user.setRefreshToken(redisCache.getCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + appUserid));
                user.setExpiresIn(redisCache.getCacheObject(RedisConstants.LOGIN_QQ_ExpiresIn + appUserid));

                user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + accessToken + appUserid));
                user.setAppUserid(appUserid);
                return user;
            } else {
                user = new TPlatUser();
                log.info("新增的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "expiresIn:" + expiresIn);
                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid, refreshToken);
                redisCache.setCacheObject(RedisConstants.LOGIN_QQ_ExpiresIn + appUserid, expiresIn);

                redisCache.setCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid, accessToken);
                log.info("此处refreshToken为：" + redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid));
                user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + accessToken + appUserid));
                user.setId(IdUtils.simpleUUID());
                user.setAppUserid(appUserid);
                user.setDelFlag(0);
                user.setUserType("P");
                //用户类型  S-机构管理员  P-普通用户
                user.setUserSource("P");
                //用户来源 P-pc端 A-app
                user.setUserStatus("0");
                //用户状态  0-正常 -1 停用
                user.setQqCoverUrl(qqCoverUrl);
                user.setUserSex(userSex);
                user.setQqName(qqName);
                user.setLastLoginTime(DateUtil.getCuurentDate());
                user.setCreateTime(DateUtil.getCuurentDate());
                tPlatUserMapper.insertTPlatUser(user);
                return user;
            }

        } catch (Exception e) {
            log.error("error", e);
            throw e;
        }

    }

    @Override
    public void refreshQQAccessToken(String appUserid, String refreshtoken) {
        String appID = weixinLoginProperties.getWeixinappID();
        TPlatUser user = tPlatUserMapper.selectByUnionid(appUserid);
        if (null == user) {
            user = new TPlatUser();
        }
        String uri = "https://graph.qq.com/oauth2.0/token?" +
                "grant_type=refresh_token" + "&client_id=" + qqProperties.getClient_id() +
                "&client_secret="
                + qqProperties.getClient_secret()
                + "&refresh_token=" + refreshtoken
                + "&fmt=json";

        try {
            JSONObject wechatAccessToken = HttpClientUtils.httpGet(uri);
            if (wechatAccessToken.get("errcode") != null) {
                log.error("获取accessToken失败");
            }
            String accessToken = wechatAccessToken.getString("access_token");
            String refreshToken = wechatAccessToken.getString("refresh_token");
            appUserid = wechatAccessToken.getString("openid");
            Integer expiresIn = wechatAccessToken.getInteger("expires_in");
            redisCache.setCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + appUserid, refreshToken);
            user.setRefreshToken(redisCache.getCacheObject(RedisConstants.LOGIN_WX_USER_TOKEN + appUserid));
            redisCache.setCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + appUserid, expiresIn);
            user.setExpiresIn(redisCache.getCacheObject(RedisConstants.LOGIN_QQ_ExpiresIn + appUserid));
            redisCache.setCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + accessToken + appUserid, accessToken);
            user.setAccessToken(redisCache.getCacheObject(RedisConstants.LOGIN_QQ_USER_TOKEN + accessToken + appUserid));
            log.info("refreshAccessToken的" + "accesstoken:" + accessToken + "," + "openId:" + appUserid + "," + "expiresIn:" + expiresIn);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public int updateTPlatUserByBr(TPlatUser tPlatUser) {
        return tPlatUserMapper.updateTPlatUser(tPlatUser);
    }

    @Override
    public List<TPlatUser> selectTPlatUserListByName(TPlatUser tPlatUser) {
        return tPlatUserMapper.selectTPlatUserListByName(tPlatUser);
    }

    @Override
    public boolean checkLimit(TPlatUser user, String ip, String dbId) {
        boolean islimit = false;
        /** ---获取用户信息 end -- **/
        String orgId = null;
        String orderStatus = null;
        String telno = null;
        //不管用户登录没登录，可以先判断机构订单是否授权
        if (user!=null){
            orgId = user.getOrgId();
            orderStatus = user.getOrderStatus();// 0-未开通 1-开通授权
            telno = user.getUserTel();
            log.info("==>user={}", user.getUserTel());
        }
        log.info("==>机构授权判断...{}==>{}", telno, orgId);
        if (Strings.isNullOrEmpty(orgId)) {
            log.info("==>请求ip:{}", ip);
            TPlatOrganIp tPlatOrganIp = tPlatOrganIpService.selectOrganbyIp(ip);
            if (tPlatOrganIp != null) {
                log.info("==>匹配到机构：{}", tPlatOrganIp.getOrgId());
                orgId = tPlatOrganIp.getOrgId();
            }
        }

        if (!Strings.isNullOrEmpty(orgId)) {
            // 查询该机构是否开通
            TProOrder query = new TProOrder();
            query.setOrgId(orgId);
            List<TProOrder> orderList = tProOrderMapper.selectTProOrderList(query);
            if (orderList == null || orderList.size() == 0) {
                log.info("==>没有订单数据.机构ID:{}", orgId);
                islimit = true;
            } else {
                TProOrder proorder = orderList.get(0);
                if (proorder.getOrderStatus() == null) {
                    // 未开通
                    islimit = true;
                    log.info("==>订单没有开通.订单号：{}", proorder.getOrderCode());
                } else {
                    if (Integer.parseInt(proorder.getOrderStatus()) == 0) {
                        // 未开通
                        islimit = true;
                        log.info("==>订单没有开通.订单号：{}", proorder.getOrderCode());
                    } else {
                        // 开通，看是否在开通时间段内
                        //SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
                        String startTime = proorder.getAuthStartTime();
                        String endTime = proorder.getAuthEndTime();

                        String now = cn.guliandigital.common.utils.DateUtil.getCuurentDateStr();
                        log.info("==>授权时间：start:{},end:{},now:{}", startTime, endTime, now);
                        if (startTime.compareTo(now) <= 0 && endTime.compareTo(now) >= 0) {
                            islimit = false;
                        } else {
                            islimit = true;
                        }
                    }
                }
            }

        } else {
            islimit = true;
        }
        //如果判断完机构订单，islimit的值为true，再判断用户是否为空
        if (islimit) {
            //判断用户是否为空
            if (user != null) {
                //根据用户id和数据库id查询个人订单
                TWxampRight tWxampRight = new TWxampRight();
                tWxampRight.setUserId(user.getId());
                tWxampRight.setDatabaseId(dbId);
                List<TWxampRight> tWxampRights = itWxampRightService.selectTWxampRightList(tWxampRight);
                if (!Strings.isNullOrEmpty(orderStatus) && StringUtil.equalsIgnoreCase(orderStatus, "1")) {
                    log.info("==>个人授权判断...{}", orderStatus);
                    islimit = false;
                } else if (StringUtil.isNotEmpty(tWxampRights)) {
                    //判断订单是永久订单 还有限时订单
                    TWxampRight wxampRight = tWxampRights.get(0);
                    Integer status = wxampRight.getStatus();
                    if (status==1) {
                        //判断订单的到期时间  如果到期时间在当前时间之后，说明还没有到期
                        boolean after = wxampRight.getExpireTime().after(DateUtil.getCuurentDate());
                        if (after) {
                            islimit = false;
                        } else {
                            islimit = true;
                        }
                    } else {
                        islimit = false;
                    }

                } else {
                    islimit = true;
                }
            }
        }
        return islimit;
    }

    @Override
    public String getOrgId(TPlatUser user, String ip) {

        /** ---获取用户信息 end -- **/
        String orgId = null;

        if (user != null) {
            orgId = user.getOrgId();
        }

        log.info("==>机构授权判断...{}", orgId);
        if (Strings.isNullOrEmpty(orgId)) {
            log.info("==>请求ip:{}", ip);
            TPlatOrganIp tPlatOrganIp = tPlatOrganIpService.selectOrganbyIp(ip);
            if (tPlatOrganIp != null) {
                log.info("==>匹配到机构：{}", tPlatOrganIp.getOrgId());
                orgId = tPlatOrganIp.getOrgId();
            }
        }

        return orgId;
    }

    @Override
    public TPlatUser selectUserById(String id) {
        return tPlatUserMapper.selectUserById(id);
    }
}
