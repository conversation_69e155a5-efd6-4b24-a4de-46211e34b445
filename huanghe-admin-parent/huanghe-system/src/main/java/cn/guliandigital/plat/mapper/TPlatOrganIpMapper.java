package cn.guliandigital.plat.mapper;

import java.util.List;

import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.plat.domain.TPlatOrganIp;

/**
 * 平台机构IP范围Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-15
 */
public interface TPlatOrganIpMapper 
{
    /**
     * 查询平台机构IP范围
     * 
     * @param id 平台机构IP范围ID
     * @return 平台机构IP范围
     */
        TPlatOrganIp selectTPlatOrganIpById(String id);

    /**
     * 查询平台机构IP范围列表
     * 
     * @param tPlatOrganIp 平台机构IP范围
     * @return 平台机构IP范围集合
     */
    List<TPlatOrganIp> selectTPlatOrganIpList(TPlatOrganIp tPlatOrganIp);

    /**
     * 新增平台机构IP范围
     * 
     * @param tPlatOrganIp 平台机构IP范围
     * @return 结果
     */
    int insertTPlatOrganIp(TPlatOrganIp tPlatOrganIp);

    /**
     * 修改平台机构IP范围
     * 
     * @param tPlatOrganIp 平台机构IP范围
     * @return 结果
     */
    int updateTPlatOrganIp(TPlatOrganIp tPlatOrganIp);

    /**
     * 删除平台机构IP范围
     * 
     * @param id 平台机构IP范围ID
     * @return 结果
     */
    int deleteTPlatOrganIpById(String id);

    /**
     * 批量删除平台机构IP范围
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTPlatOrganIpByIds(String[] ids);

    /**
     * 删除平台机构IP范围
     *
     * @param orgId 平台机构ID
     * @return 结果
     */
    int deleteTPlatOrganIpByOrgId(String orgId);

    /**
     * 批量删除平台机构IP范围
     *
     * @param orgIds 需要删除的机构ID
     * @return 结果
     */
    int deleteTPlatOrganIpByOrgIds(String[] orgIds);

}
