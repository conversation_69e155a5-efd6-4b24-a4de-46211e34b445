package cn.guliandigital.tsystem.service;

import java.util.List;
import cn.guliandigital.tsystem.domain.TSysProject;

/**
 * 工程项目简介Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
public interface ITSysProjectService 
{
    /**
     * 查询工程项目简介
     * 
     * @param id 工程项目简介ID
     * @return 工程项目简介
     */
        TSysProject selectTSysProjectById(String id);

    /**
     * 查询工程项目简介列表
     * 
     * @param tSysProject 工程项目简介
     * @return 工程项目简介集合
     */
    List<TSysProject> selectTSysProjectList(TSysProject tSysProject);

    /**
     * 新增工程项目简介
     * 
     * @param tSysProject 工程项目简介
     * @return 结果
     */
    int insertTSysProject(TSysProject tSysProject);

    /**
     * 修改工程项目简介
     * 
     * @param tSysProject 工程项目简介
     * @return 结果
     */
    int updateTSysProject(TSysProject tSysProject);

    /**
     * 批量删除工程项目简介
     * 
     * @param ids 需要删除的工程项目简介ID
     * @return 结果
     */
    int deleteTSysProjectByIds(String[] ids);

    /**
     * 删除工程项目简介信息
     * 
     * @param id 工程项目简介ID
     * @return 结果
     */
    int deleteTSysProjectById(String id);

    int upPublishStatus(String id);
    int downPublishStatus(String id);

    int updatePublishStatus(String id);
}
