package cn.guliandigital.tsystem.mapper;

import cn.guliandigital.tsystem.domain.TSysFeedback;
import java.util.List;


/**
 * 意见反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-06
 */
public interface TSysFeedbackMapper 
{
    /**
     * 查询意见反馈
     * 
     * @param id 意见反馈ID
     * @return 意见反馈
     */
    TSysFeedback selectTSysFeedbackById(String id);

    /**
     * 查询意见反馈列表
     * 
     * @param tSysFeedback 意见反馈
     * @return 意见反馈集合
     */
    List<TSysFeedback> selectTSysFeedbackList(TSysFeedback tSysFeedback);

    /**
     * 新增意见反馈
     * 
     * @param tSysFeedback 意见反馈
     * @return 结果
     */
    int insertTSysFeedback(TSysFeedback tSysFeedback);

    /**
     * 修改意见反馈
     * 
     * @param tSysFeedback 意见反馈
     * @return 结果
     */
    int updateTSysFeedback(TSysFeedback tSysFeedback);

    /**
     * 删除意见反馈
     * 
     * @param id 意见反馈ID
     * @return 结果
     */
    int deleteTSysFeedbackById(String id);

    /**
     * 批量删除意见反馈
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTSysFeedbackByIds(String[] ids);
}
