package cn.guliandigital.tsystem.domain;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 轮播图对象 t_sys_banner
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public class TSysBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 分组名称 G1-视频 G2-书卷 G3-专题 G4-地图 */
    @Excel(name = "分组名称")
    private String groupName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文件类型 JPG MP4 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 内容 介绍 */
    @Excel(name = "内容介绍")
    private String content;

    /** 跳转链接 */
    @Excel(name = "跳转链接")
    private String linkUrl;

    /** 排序 */
    @Excel(name = "排序")
    private String display;

    /** 地市 */
    @Excel(name = "地市")
    private String area;

    /** 上架状态 0-上架 1-下架 */
    @Excel(name = "上架状态 0-上架 1-下架")
    private String status;

    /** 创建人id */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** 更新人id */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 0-未删除 1-已删除 */
    private Integer delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setLinkUrl(String linkUrl) 
    {
        this.linkUrl = linkUrl;
    }

    public String getLinkUrl() 
    {
        return linkUrl;
    }

    public void setDisplay(String display) 
    {
        this.display = display;
    }

    public String getDisplay() 
    {
        return display;
    }

    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }

    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }

    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }

    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupName", getGroupName())
            .append("filePath", getFilePath())
            .append("fileType", getFileType())
            .append("content", getContent())
            .append("linkUrl", getLinkUrl())
            .append("display", getDisplay())
            .append("area", getArea())
            .append("status", getStatus())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
