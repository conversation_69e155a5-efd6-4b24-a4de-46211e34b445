package cn.guliandigital.tsystem.domain;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 轮播图对象 t_sys_banner
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public class TSysBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 图片名称 */
    @Excel(name = "图片名称")
    private String imageName;

    /** 图片路径 */
    @Excel(name = "图片路径")
    private String imagePath;

    /** 上架状态 0-上架 1-下架 */
    @Excel(name = "上架状态 0-上架 1-下架")
    private String status;

    /** 排序 */
    @Excel(name = "排序")
    private String display;

    /** 创建人id */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** 更新人id */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 0-未删除 1-已删除 */
    private Integer delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setImageName(String imageName) 
    {
        this.imageName = imageName;
    }

    public String getImageName() 
    {
        return imageName;
    }
    public void setImagePath(String imagePath) 
    {
        this.imagePath = imagePath;
    }

    public String getImagePath() 
    {
        return imagePath;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setDisplay(String display) 
    {
        this.display = display;
    }

    public String getDisplay() 
    {
        return display;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("imageName", getImageName())
            .append("imagePath", getImagePath())
            .append("status", getStatus())
            .append("display", getDisplay())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
