package cn.guliandigital.tsystem.service.impl;


import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.tsystem.domain.TSysBanner;
import cn.guliandigital.tsystem.mapper.TSysBannerMapper;
import cn.guliandigital.tsystem.service.ITSysBannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 轮播图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class TSysBannerServiceImpl implements ITSysBannerService
{
    @Autowired
    private TSysBannerMapper tSysBannerMapper;

    /**
     * 查询轮播图
     * 
     * @param id 轮播图ID
     * @return 轮播图
     */
    @Override
    public TSysBanner selectTSysBannerById(String id)
    {
        return tSysBannerMapper.selectTSysBannerById(id);
    }

    /**
     * 查询轮播图列表
     * 
     * @param tSysBanner 轮播图
     * @return 轮播图
     */
    @Override
    public List<TSysBanner> selectTSysBannerList(TSysBanner tSysBanner)
    {
        return tSysBannerMapper.selectTSysBannerList(tSysBanner);
    }

    /**
     * 新增轮播图
     * 
     * @param tSysBanner 轮播图
     * @return 结果
     */
    @Override
    public int insertTSysBanner(TSysBanner tSysBanner)
    {
        tSysBanner.setId(IdUtils.simpleUUID());
        tSysBanner.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysBanner.setCreatebyName(SecurityUtils.getUsername());
        tSysBanner.setDelFlag(0);
        tSysBanner.setCreateTime(DateUtil.getCuurentDate());
        //查询是否有上架的，如果有status赋值为1 如果没有赋值为0
        TSysBanner query = new TSysBanner();
        query.setStatus("0");
        List<TSysBanner> tSysBanners = tSysBannerMapper.selectTSysBannerList(query);
        if (StringUtil.isNotEmpty(tSysBanners)){
            tSysBanner.setStatus("1");
        }else{
            tSysBanner.setStatus("0");
        }
        return tSysBannerMapper.insertTSysBanner(tSysBanner);
    }

    /**
     * 修改轮播图
     * 
     * @param tSysBanner 轮播图
     * @return 结果
     */
    @Override
    public int updateTSysBanner(TSysBanner tSysBanner)
    {
        tSysBanner.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysBanner.setUpdatebyName(SecurityUtils.getUsername());
        tSysBanner.setUpdateTime(DateUtil.getCuurentDate());
        return tSysBannerMapper.updateTSysBanner(tSysBanner);
    }

    /**
     * 批量删除轮播图
     * 
     * @param ids 需要删除的轮播图ID
     * @return 结果
     */
    @Override
    public int deleteTSysBannerByIds(String[] ids)
    {
        return tSysBannerMapper.deleteTSysBannerByIds(ids);
    }

    @Override
    public List<TSysBanner> selectTSysBannerByIds(String[] ids)
    {
        return tSysBannerMapper.selectTSysBannerByIds(ids);
    }

    /**
     * 删除轮播图信息
     * 
     * @param id 轮播图ID
     * @return 结果
     */
    @Override
    public int deleteTSysBannerById(String id)
    {
        return tSysBannerMapper.deleteTSysBannerById(id);
    }

    @Override
    public int updateTSysBannerByStatus(TSysBanner tSysBanner) {
        //把之前打开的关闭
        TSysBanner query = new TSysBanner();
        query.setStatus("0");//上架
        List<TSysBanner> tSysBanners = tSysBannerMapper.selectTSysBannerList(query);
        for (TSysBanner sysBanner : tSysBanners) {
            sysBanner.setStatus("1");//下架
            tSysBannerMapper.updateTSysBanner(sysBanner);
        }
        //更新状态
        tSysBanner.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysBanner.setUpdatebyName(SecurityUtils.getUsername());
        tSysBanner.setUpdateTime(DateUtil.getCuurentDate());
        return tSysBannerMapper.updateTSysBanner(tSysBanner);

    }
}
