package cn.guliandigital.tsystem.mapper;


import cn.guliandigital.tsystem.domain.TWxampNotice;

import java.util.List;

/**
 * 关于我们、使用帮助Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface TWxampNoticeMapper 
{
    /**
     * 查询关于我们、使用帮助
     * 
     * @param id 关于我们、使用帮助ID
     * @return 关于我们、使用帮助
     */
        TWxampNotice selectTWxampNoticeById(Integer id);

    /**
     * 查询关于我们、使用帮助列表
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 关于我们、使用帮助集合
     */
    List<TWxampNotice> selectTWxampNoticeList(TWxampNotice tWxampNotice);

    /**
     * 新增关于我们、使用帮助
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 结果
     */
    int insertTWxampNotice(TWxampNotice tWxampNotice);

    /**
     * 修改关于我们、使用帮助
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 结果
     */
    int updateTWxampNotice(TWxampNotice tWxampNotice);

    /**
     * 删除关于我们、使用帮助
     * 
     * @param id 关于我们、使用帮助ID
     * @return 结果
     */
    int deleteTWxampNoticeById(Integer id);

    /**
     * 批量删除关于我们、使用帮助
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTWxampNoticeByIds(Integer[] ids);

    int deleteByType(String type);

}
