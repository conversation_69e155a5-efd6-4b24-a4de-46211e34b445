package cn.guliandigital.tsystem.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.PublishStatus;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.tsystem.domain.TSysProject;
import cn.guliandigital.tsystem.mapper.TSysProjectMapper;
import cn.guliandigital.tsystem.service.ITSysProjectService;

/**
 * 工程项目简介Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@Service
public class TSysProjectServiceImpl implements ITSysProjectService 
{
    @Autowired
    private TSysProjectMapper tSysProjectMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询工程项目简介
     * 
     * @param id 工程项目简介ID
     * @return 工程项目简介
     */
    @Override
    public TSysProject selectTSysProjectById(String id)
    {
        TSysProject tSysProject = tSysProjectMapper.selectTSysProjectById(id);

        if(tSysProject!=null){
            redisCache.incr(RedisConstants.HUANGHE_PROJECT_ID+id,1);
        }

        return tSysProject;
    }

    /**
     * 查询工程项目简介列表
     * 
     * @param tSysProject 工程项目简介
     * @return 工程项目简介
     */
    @Override
    public List<TSysProject> selectTSysProjectList(TSysProject tSysProject)
    {

        return tSysProjectMapper.selectTSysProjectList(tSysProject);
    }

    /**
     * 新增工程项目简介
     * 
     * @param tSysProject 工程项目简介
     * @return 结果
     */
    @Override
    public int insertTSysProject(TSysProject tSysProject)
    {
        tSysProject.setId(IdUtils.simpleUUID());
        tSysProject.setUpdateTime(DateUtil.getCuurentDate());
        tSysProject.setCreateTime(DateUtil.getCuurentDate());
        tSysProject.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysProject.setCreatebyName(SecurityUtils.getUsername());
        tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.NOT.getCode()));

        return tSysProjectMapper.insertTSysProject(tSysProject);
    }

    /**
     * 修改工程项目简介
     * 
     * @param tSysProject 工程项目简介
     * @return 结果
     */
    @Override
    public int updateTSysProject(TSysProject tSysProject)
    {

        tSysProject.setUpdateTime(DateUtil.getCuurentDate());
        tSysProject.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysProject.setUpdatebyName(SecurityUtils.getUsername());
//        //如果为已发布的资讯，编辑内容后状态变为已撤销，需要从新发布
//        if(tSysProject.getPublishStatus().compareTo(Integer.parseInt(PublishStatus.DONE.getCode()))==0){
//            tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.CANCEL.getCode()));
//        }

        return tSysProjectMapper.updateTSysProject(tSysProject);
    }

    /**
     * 批量删除工程项目简介
     * 
     * @param ids 需要删除的工程项目简介ID
     * @return 结果
     */
    @Override
    public int deleteTSysProjectByIds(String[] ids)
    {
        return tSysProjectMapper.deleteTSysProjectByIds(ids);
    }

    /**
     * 删除工程项目简介信息
     * 
     * @param id 工程项目简介ID
     * @return 结果
     */
    @Override
    public int deleteTSysProjectById(String id)
    {
        return tSysProjectMapper.deleteTSysProjectById(id);
    }


    @Override
    public int upPublishStatus(String id) {
        TSysProject tSysProject = new TSysProject();
        tSysProject.setId(id);
        tSysProject.setUpdateTime(DateUtil.getCuurentDate());
        tSysProject.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysProject.setUpdatebyName(SecurityUtils.getUsername());
        tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.DONE.getCode()));

        return tSysProjectMapper.updateTSysProject(tSysProject);
    }


    @Override
    public int downPublishStatus(String id) {
        TSysProject tSysProject = new TSysProject();
        tSysProject.setId(id);
        tSysProject.setUpdateTime(DateUtil.getCuurentDate());
        tSysProject.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysProject.setUpdatebyName(SecurityUtils.getUsername());
        tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.NOT.getCode()));

        return tSysProjectMapper.updateTSysProject(tSysProject);
    }

    @Override
    public int updatePublishStatus(String id) {

        TSysProject tSysProject = new TSysProject();
        tSysProject.setId(id);
        tSysProject.setUpdateTime(DateUtil.getCuurentDate());
        tSysProject.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysProject.setUpdatebyName(SecurityUtils.getUsername());
        tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.DONE.getCode()));
        return tSysProjectMapper.updateTSysProject(tSysProject);

    }


}
