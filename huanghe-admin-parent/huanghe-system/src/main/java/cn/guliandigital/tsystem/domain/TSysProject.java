package cn.guliandigital.tsystem.domain;

import cn.guliandigital.tsystem.domain.vo.ProjectVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 工程项目简介对象 t_sys_project
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
public class TSysProject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 类型  0-介绍 1-编委会 2-典籍编 3-研究编 4-史志编 5-人物编 6-文物编 7-文学艺术编 8-山水编 9-科技编 10-红色文献编 */
    @Excel(name = "类型  0-介绍 1-编委会 2-典籍编 3-研究编 4-史志编 5-人物编 6-文物编 7-文学艺术编 8-山水编 9-科技编 10-红色文献编")
    private String contentType;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 图片链接 一般存路径 */
    @Excel(name = "图片链接 一般存路径")
    private String iconUrl;

    /** 处理状态 0-未发布 1-已发布 2-已撤销 */
    @Excel(name = "处理状态 0-未发布 1-已发布 2-已撤销")
    private Integer publishStatus;

    /** 发布时间 */
    @Excel(name = "发布时间")
    private String publishTime;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** 更新人ID */
    @Excel(name = "更新人ID")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    private ProjectVo next;
    private ProjectVo previous;


    /** 阅读次数 */
    private Integer readCount;

    /** 排序 **/
    private Integer display;

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    public ProjectVo getNext() {
        return next;
    }

    public void setNext(ProjectVo next) {
        this.next = next;
    }

    public ProjectVo getPrevious() {
        return previous;
    }

    public void setPrevious(ProjectVo previous) {
        this.previous = previous;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setIconUrl(String iconUrl) 
    {
        this.iconUrl = iconUrl;
    }

    public String getIconUrl() 
    {
        return iconUrl;
    }
    public void setPublishStatus(Integer publishStatus) 
    {
        this.publishStatus = publishStatus;
    }

    public Integer getPublishStatus() 
    {
        return publishStatus;
    }
    public void setPublishTime(String publishTime) 
    {
        this.publishTime = publishTime;
    }

    public String getPublishTime() 
    {
        return publishTime;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("contentType", getContentType())
            .append("content", getContent())
            .append("iconUrl", getIconUrl())
            .append("publishStatus", getPublishStatus())
            .append("publishTime", getPublishTime())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
