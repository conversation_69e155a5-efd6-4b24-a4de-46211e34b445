package cn.guliandigital.tsystem.mapper;

import cn.guliandigital.tsystem.domain.TSysLink;

import java.util.List;

/**
 * 友情链接Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-11
 */
public interface TSysLinkMapper
{
    /**
     * 查询友情链接
     * 
     * @param id 友情链接ID
     * @return 友情链接
     */
        TSysLink selectTSysLinkById(String id);

    /**
     * 查询友情链接列表
     * 
     * @param tSysLink 友情链接
     * @return 友情链接集合
     */
    List<TSysLink> selectTSysLinkList(TSysLink tSysLink);

    /**
     * 新增友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    int insertTSysLink(TSysLink tSysLink);

    /**
     * 修改友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    int updateTSysLink(TSysLink tSysLink);

    /**
     * 删除友情链接
     * 
     * @param id 友情链接ID
     * @return 结果
     */
    int deleteTSysLinkById(String id);

    /**
     * 批量删除友情链接
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTSysLinkByIds(String[] ids);

    /**
     * 查询当前最大排序值
     *
     * @return 结果
     */
    int selectTSysLinkDisplayMax();

}
