package cn.guliandigital.tsystem.mapper;

import cn.guliandigital.tsystem.domain.TSysNews;

import java.util.List;

/**
 * 资讯管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-09-10
 */
public interface TSysNewsMapper 
{
    /**
     * 查询资讯管理
     * 
     * @param id 资讯管理ID
     * @return 资讯管理
     */
        TSysNews selectTSysNewsById(String id);

    /**
     * 查询资讯管理列表
     * 
     * @param tSysNews 资讯管理
     * @return 资讯管理集合
     */
    List<TSysNews> selectTSysNewsList(TSysNews tSysNews);

    /**
     * 新增资讯管理
     * 
     * @param tSysNews 资讯管理
     * @return 结果
     */
    int insertTSysNews(TSysNews tSysNews);

    /**
     * 修改资讯管理
     * 
     * @param tSysNews 资讯管理
     * @return 结果
     */
    int updateTSysNews(TSysNews tSysNews);

    /**
     * 删除资讯管理
     * 
     * @param id 资讯管理ID
     * @return 结果
     */
    int deleteTSysNewsById(String id);

    /**
     * 批量删除资讯管理
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTSysNewsByIds(String[] ids);
}
