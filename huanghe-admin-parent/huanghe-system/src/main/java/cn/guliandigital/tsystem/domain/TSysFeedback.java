package cn.guliandigital.tsystem.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 意见反馈对象 t_sys_feedback
 * 
 * <AUTHOR>
 * @date 2020-09-06
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TSysFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String recContent;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 处理状态 0-未处理 1-已处理 */
    @Excel(name = "处理状态 0-未处理 1-已处理")
    private Integer handleStatus;
    /** 是否查看  0-未查看 1-已查看 */
    @Excel(name = " 是否查看  0-未查看 1-已查看")
    private Integer isLook;

    /** 处理人ID */
    private String handlebyId;

    /** 处理人姓名 */
    @Excel(name = "处理人姓名")
    private String handlebyName;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date handleTime;

    /** 数据来源  P-平台  A-小程序 */
    @Excel(name = "数据来源  P-平台  A-小程序")
    private String dataFrom;

    /** 答复内容 */
    private String feedbackContent;

    /** 章节ID */
    private String bookMenuId;

    /** 章节名称 */
    private String bookMenuName;

    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** $column.columnComment */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    private Integer delFlag;

    /** 电话 */
    private String userTel;

    /** 邮箱 */
    private String userEmail;

    /** 姓名 */
    private String userName;

    /** 书名 */
    private String bookName;

    /** 标题 */
    private String title;

    private String idList;

    public String getIdList() {
        return idList;
    }

    public void setIdList(String idList) {
        this.idList = idList;
    }

    public Integer getIsLook() {
        return isLook;
    }

    public void setIsLook(Integer isLook) {
        this.isLook = isLook;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setRecContent(String recContent) 
    {
        this.recContent = recContent;
    }

    public String getRecContent() 
    {
        return recContent;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setHandleStatus(Integer handleStatus) 
    {
        this.handleStatus = handleStatus;
    }

    public Integer getHandleStatus() 
    {
        return handleStatus;
    }
    public void setHandlebyId(String handlebyId) 
    {
        this.handlebyId = handlebyId;
    }

    public String getHandlebyId() 
    {
        return handlebyId;
    }
    public void setHandlebyName(String handlebyName) 
    {
        this.handlebyName = handlebyName;
    }

    public String getHandlebyName() 
    {
        return handlebyName;
    }
    public void setHandleTime(Date handleTime)
    {
        this.handleTime = handleTime;
    }

    public Date getHandleTime()
    {
        return handleTime;
    }
    public void setDataFrom(String dataFrom) 
    {
        this.dataFrom = dataFrom;
    }

    public String getDataFrom() 
    {
        return dataFrom;
    }
    public void setFeedbackContent(String feedbackContent) 
    {
        this.feedbackContent = feedbackContent;
    }

    public String getFeedbackContent() 
    {
        return feedbackContent;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setBookMenuId(String bookMenuId)
    {
        this.bookMenuId = bookMenuId;
    }

    public String getBookMenuId()
    {
        return bookMenuId;
    }
    public void setBookMenuName(String bookMenuName)
    {
        this.bookMenuName = bookMenuName;
    }

    public String getBookMenuName()
    {
        return bookMenuName;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("recContent", getRecContent())
            .append("userId", getUserId())
            .append("handleStatus", getHandleStatus())
            .append("handlebyId", getHandlebyId())
            .append("handlebyName", getHandlebyName())
            .append("handleTime", getHandleTime())
            .append("bookMenuId", getBookMenuId())
            .append("bookMenuName", getBookMenuName())
            .append("dataFrom", getDataFrom())
            .append("feedbackContent", getFeedbackContent())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("bookName", getBookName())
            .toString();
    }
}
