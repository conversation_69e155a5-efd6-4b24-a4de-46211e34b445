package cn.guliandigital.tsystem.service.impl;

import java.util.Date;
import java.util.List;

import cn.guliandigital.common.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.PublishStatus;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.tsystem.domain.TSysNews;
import cn.guliandigital.tsystem.mapper.TSysNewsMapper;
import cn.guliandigital.tsystem.service.ITSysNewsService;

/**
 * 资讯管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-10
 */
@Service
public class TSysNewsServiceImpl implements ITSysNewsService 
{
    @Autowired
    private TSysNewsMapper tSysNewsMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询资讯管理
     * 
     * @param id 资讯管理ID
     * @return 资讯管理
     */
    @Override
    public TSysNews selectTSysNewsById(String id)
    {

        return tSysNewsMapper.selectTSysNewsById(id);
    }

    /**
     * 查询资讯管理列表
     * 
     * @param tSysNews 资讯管理
     * @return 资讯管理
     */
    @Override
    public List<TSysNews> selectTSysNewsList(TSysNews tSysNews)
    {
        return tSysNewsMapper.selectTSysNewsList(tSysNews);
    }

    /**
     * 新增资讯管理
     * 
     * @param tSysNews 资讯管理
     * @return 结果
     */
    @Override
    public int insertTSysNews(TSysNews tSysNews)
    {
        tSysNews.setId(IdUtils.simpleUUID());
        Date cuurentDate = DateUtil.getCuurentDate();
        tSysNews.setUpdateTime(cuurentDate);

        tSysNews.setCreateTime(cuurentDate);
        tSysNews.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysNews.setCreatebyName(SecurityUtils.getUsername());
//        tSysNews.setPublishStatus(PublishStatus.NOT.getCode());
        tSysNews.setReadCount(0);
        if (StringUtil.equals(tSysNews.getPublishStatus(),PublishStatus.DONE.getCode())){
            tSysNews.setPublishTime(DateUtil.formatDate(cuurentDate,"yyyy-MM-dd HH:mm:ss"));
        }
        return tSysNewsMapper.insertTSysNews(tSysNews);
    }

    /**
     * 修改资讯管理
     * 
     * @param tSysNews 资讯管理
     * @return 结果
     */
    @Override
    public int updateTSysNews(TSysNews tSysNews)
    {
        tSysNews.setUpdateTime(DateUtil.getCuurentDate());
        tSysNews.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysNews.setUpdatebyName(SecurityUtils.getUsername());
        //如果为已发布的资讯，编辑内容后状态变为已撤销，需要从新发布
//        if(tSysNews.getPublishStatus().equals(PublishStatus.DONE.getCode())){
//            tSysNews.setPublishStatus(PublishStatus.CANCEL.getCode());
//        }
        //如果是下架改成上架更新发布时间
        TSysNews news = tSysNewsMapper.selectTSysNewsById(tSysNews.getId());
        if (StringUtil.isNotNull(news)){
            if (StringUtil.equals(news.getPublishStatus(),PublishStatus.NOT.getCode())&&
            StringUtil.equals(tSysNews.getPublishStatus(),PublishStatus.DONE.getCode())){
                tSysNews.setPublishTime(DateUtil.getDateTime());
            }
        }
        return tSysNewsMapper.updateTSysNews(tSysNews);
    }

    /**
     * 批量删除资讯管理
     * 
     * @param ids 需要删除的资讯管理ID
     * @return 结果
     */
    @Override
    public int deleteTSysNewsByIds(String[] ids)
    {
        for (String id : ids) {
            TSysNews tSysNews = tSysNewsMapper.selectTSysNewsById(id);
            if(tSysNews.getPublishStatus().equals(PublishStatus.DONE.getCode())){
                throw  new ServiceException("上架状态不允许删除");
            }
        }
        return tSysNewsMapper.deleteTSysNewsByIds(ids);
    }

    /**
     * 删除资讯管理信息
     * 
     * @param id 资讯管理ID
     * @return 结果
     */
    @Override
    public int deleteTSysNewsById(String id)
    {
        return tSysNewsMapper.deleteTSysNewsById(id);
    }

    /**
     * 修改资讯状态
     *
     * @param id 资讯管理ID
     * @return 结果
     */
    @Override
    public int updatePublishStatus(String id)
    {
        TSysNews tSysNews = new TSysNews();
        tSysNews.setId(id);
        tSysNews.setUpdateTime(DateUtil.getCuurentDate());
        tSysNews.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysNews.setUpdatebyName(SecurityUtils.getUsername());
        tSysNews.setPublishStatus(PublishStatus.DONE.getCode());

        return tSysNewsMapper.updateTSysNews(tSysNews);
    }

    /**
     * 获取资讯详情
     *
     * @param id 资讯ID
     * @return 结果
     */
    @Override
    @Transactional
    public TSysNews selectTSysNewsWebById(String id) {
        TSysNews tSysNews = tSysNewsMapper.selectTSysNewsById(id);
        if(tSysNews != null){
            //阅读数+1
            tSysNews.setReadCount(tSysNews.getReadCount()+1);
            TSysNews countTSysNews = new TSysNews();
            countTSysNews.setId(id);
            countTSysNews.setReadCount(tSysNews.getReadCount());
            tSysNewsMapper.updateTSysNews(countTSysNews);
            redisCache.incr(RedisConstants.HUANGHE_NEWS_ID+id,1);

        }
        return tSysNews;
    }
}
