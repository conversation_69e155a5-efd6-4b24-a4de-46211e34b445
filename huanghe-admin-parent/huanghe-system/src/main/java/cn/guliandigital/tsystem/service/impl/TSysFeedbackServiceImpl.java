package cn.guliandigital.tsystem.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.enums.HandleStatus;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.tsystem.domain.TSysFeedback;
import cn.guliandigital.tsystem.mapper.TSysFeedbackMapper;
import cn.guliandigital.tsystem.service.ITSysFeedbackService;


/**
 * 意见反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-06
 */
@Service
public class TSysFeedbackServiceImpl implements ITSysFeedbackService
{
    @Autowired
    private TSysFeedbackMapper tSysFeedbackMapper;

    /**
     * 查询意见反馈
     * 
     * @param id 意见反馈ID
     * @return 意见反馈
     */
    @Override
    public TSysFeedback selectTSysFeedbackById(String id)
    {
        return tSysFeedbackMapper.selectTSysFeedbackById(id);
    }

    /**
     * 查询意见反馈列表
     * 
     * @param tSysFeedback 意见反馈
     * @return 意见反馈
     */
    @Override
    public List<TSysFeedback> selectTSysFeedbackList(TSysFeedback tSysFeedback)
    {
        return tSysFeedbackMapper.selectTSysFeedbackList(tSysFeedback);
    }

    /**
     * 新增意见反馈
     * 
     * @param tSysFeedback 意见反馈
     * @return 结果
     */
    @Override
    public int insertTSysFeedback(TSysFeedback tSysFeedback)
    {
        tSysFeedback.setId(IdUtils.simpleUUID());
        tSysFeedback.setHandleStatus(HandleStatus.NOT.getCode());
        tSysFeedback.setCreateTime(DateUtil.getCuurentDate());
        return tSysFeedbackMapper.insertTSysFeedback(tSysFeedback);
    }

    /**
     * 修改意见反馈
     * 
     * @param tSysFeedback 意见反馈
     * @return 结果
     */
    @Override
    public int updateTSysFeedback(TSysFeedback tSysFeedback)
    {
        tSysFeedback.setUpdateTime(DateUtil.getCuurentDate());
        return tSysFeedbackMapper.updateTSysFeedback(tSysFeedback);
    }

    /**
     * 批量删除意见反馈
     * 
     * @param ids 需要删除的意见反馈ID
     * @return 结果
     */
    @Override
    public int deleteTSysFeedbackByIds(String[] ids)
    {
        return tSysFeedbackMapper.deleteTSysFeedbackByIds(ids);
    }

    /**
     * 删除意见反馈信息
     * 
     * @param id 意见反馈ID
     * @return 结果
     */
    @Override
    public int deleteTSysFeedbackById(String id)
    {
        return tSysFeedbackMapper.deleteTSysFeedbackById(id);
    }
}
