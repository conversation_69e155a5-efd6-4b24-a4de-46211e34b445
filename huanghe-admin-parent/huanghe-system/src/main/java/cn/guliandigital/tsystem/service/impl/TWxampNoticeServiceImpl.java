package cn.guliandigital.tsystem.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.tsystem.domain.TWxampNotice;
import cn.guliandigital.tsystem.mapper.TWxampNoticeMapper;
import cn.guliandigital.tsystem.service.ITWxampNoticeService;


/**
 * 关于我们、使用帮助Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
@Service
public class TWxampNoticeServiceImpl implements ITWxampNoticeService
{
    @Autowired
    private TWxampNoticeMapper tWxampNoticeMapper;

    /**
     * 查询关于我们、使用帮助
     * 
     * @param id 关于我们、使用帮助ID
     * @return 关于我们、使用帮助
     */
    @Override
    public TWxampNotice selectTWxampNoticeById(Integer id)
    {
        return tWxampNoticeMapper.selectTWxampNoticeById(id);
    }

    /**
     * 查询关于我们、使用帮助列表
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 关于我们、使用帮助
     */
    @Override
    public List<TWxampNotice> selectTWxampNoticeList(TWxampNotice tWxampNotice)
    {
        return tWxampNoticeMapper.selectTWxampNoticeList(tWxampNotice);
    }

    /**
     * 新增关于我们、使用帮助
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 结果
     */
    @Override
    public int insertTWxampNotice(TWxampNotice tWxampNotice)
    {
        tWxampNoticeMapper.deleteByType(tWxampNotice.getType());
        tWxampNotice.setCreateTime(DateUtil.getCuurentDate());
        return tWxampNoticeMapper.insertTWxampNotice(tWxampNotice);
    }

    /**
     * 修改关于我们、使用帮助
     * 
     * @param tWxampNotice 关于我们、使用帮助
     * @return 结果
     */
    @Override
    public int updateTWxampNotice(TWxampNotice tWxampNotice)
    {
        tWxampNotice.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampNoticeMapper.updateTWxampNotice(tWxampNotice);
    }

    /**
     * 批量删除关于我们、使用帮助
     * 
     * @param ids 需要删除的关于我们、使用帮助ID
     * @return 结果
     */
    @Override
    public int deleteTWxampNoticeByIds(Integer[] ids)
    {
        return tWxampNoticeMapper.deleteTWxampNoticeByIds(ids);
    }

    /**
     * 删除关于我们、使用帮助信息
     * 
     * @param id 关于我们、使用帮助ID
     * @return 结果
     */
    @Override
    public int deleteTWxampNoticeById(Integer id)
    {
        return tWxampNoticeMapper.deleteTWxampNoticeById(id);
    }
}
