package cn.guliandigital.tsystem.service;

import cn.guliandigital.tsystem.domain.TSysLink;
import cn.guliandigital.common.core.domain.model.DisplayParam;

import java.util.List;

/**
 * 友情链接Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-11
 */
public interface ITSysLinkService 
{
    /**
     * 查询友情链接
     * 
     * @param id 友情链接ID
     * @return 友情链接
     */
        TSysLink selectTSysLinkById(String id);

    /**
     * 查询友情链接列表
     * 
     * @param tSysLink 友情链接
     * @return 友情链接集合
     */
    List<TSysLink> selectTSysLinkList(TSysLink tSysLink);

    /**
     * 新增友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    int insertTSysLink(TSysLink tSysLink);

    /**
     * 修改友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    int updateTSysLink(TSysLink tSysLink);

    /**
     * 批量删除友情链接
     * 
     * @param ids 需要删除的友情链接ID
     * @return 结果
     */
    int deleteTSysLinkByIds(String[] ids);

    /**
     * 删除友情链接信息
     * 
     * @param id 友情链接ID
     * @return 结果
     */
    int deleteTSysLinkById(String id);

    /**
     * 修改友情链接排序
     *
     * @param displayParam 友情链接排序对象
     * @return 结果
     */
    int updateWebDisplay(DisplayParam displayParam);
}
