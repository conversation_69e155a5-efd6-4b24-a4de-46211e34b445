package cn.guliandigital.tsystem.domain;

import cn.guliandigital.tsystem.domain.vo.NewsVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 资讯管理对象 t_sys_news
 * 
 * <AUTHOR>
 * @date 2020-09-10
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TSysNews extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 资讯标题 */
    @Excel(name = "资讯标题")
    private String newsTitle;

    /** 资讯内容 */
    @Excel(name = "资讯内容")
    private String newsContent;

    /** 图片链接 一般存路径 */
    @Excel(name = "图片链接 一般存路径")
    private String iconUrl;

    /** 处理状态 0-未发布 1-已发布 */
    @Excel(name = "处理状态 0-未发布 1-已发布 2-已撤销")
    private String publishStatus;

    /** 发布时间 */

    private String  publishTime;

    /** $column.columnComment */
    private String createbyId;

    /** 创建人 */
    private String createbyName;

    /** $column.columnComment */
    private String updatebyId;

    /** 更新人 */
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    /** 阅读次数 */
    private Integer readCount;

    /** 来源 */
    private String source;

    private NewsVo next;

    private NewsVo previous;

    private Integer display;

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public NewsVo getNext() {
        return next;
    }

    public void setNext(NewsVo next) {
        this.next = next;
    }

    public NewsVo getPrevious() {
        return previous;
    }

    public void setPrevious(NewsVo previous) {
        this.previous = previous;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setNewsTitle(String newsTitle) 
    {
        this.newsTitle = newsTitle;
    }

    public String getNewsTitle() 
    {
        return newsTitle;
    }
    public void setNewsContent(String newsContent) 
    {
        this.newsContent = newsContent;
    }

    public String getNewsContent() 
    {
        return newsContent;
    }
    public void setIconUrl(String iconUrl) 
    {
        this.iconUrl = iconUrl;
    }

    public String getIconUrl() 
    {
        return iconUrl;
    }

    public String getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(String publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public void setCreatebyId(String createbyId)
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public Integer getReadCount() {
        return readCount;
    }

    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("newsTitle", getNewsTitle())
            .append("newsContent", getNewsContent())
            .append("iconUrl", getIconUrl())
            .append("publishStatus", getPublishStatus())
            .append("publishTime", getPublishTime())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("readCount", getReadCount())
            .toString();
    }
}
