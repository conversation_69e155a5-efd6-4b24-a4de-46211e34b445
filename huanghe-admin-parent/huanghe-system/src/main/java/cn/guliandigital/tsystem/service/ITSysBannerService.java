package cn.guliandigital.tsystem.service;


import cn.guliandigital.tsystem.domain.TSysBanner;

import java.util.List;

/**
 * 轮播图Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface ITSysBannerService 
{
    /**
     * 查询轮播图
     * 
     * @param id 轮播图ID
     * @return 轮播图
     */
        TSysBanner selectTSysBannerById(String id);

    /**
     * 查询轮播图列表
     * 
     * @param tSysBanner 轮播图
     * @return 轮播图集合
     */
    List<TSysBanner> selectTSysBannerList(TSysBanner tSysBanner);

    /**
     * 新增轮播图
     * 
     * @param tSysBanner 轮播图
     * @return 结果
     */
    int insertTSysBanner(TSysBanner tSysBanner);

    /**
     * 修改轮播图
     * 
     * @param tSysBanner 轮播图
     * @return 结果
     */
    int updateTSysBanner(TSysBanner tSysBanner);

    /**
     * 批量删除轮播图
     * 
     * @param ids 需要删除的轮播图ID
     * @return 结果
     */
    int deleteTSysBannerByIds(String[] ids);
    List<TSysBanner> selectTSysBannerByIds(String[] ids);

    /**
     * 删除轮播图信息
     * 
     * @param id 轮播图ID
     * @return 结果
     */
    int deleteTSysBannerById(String id);

    /**
     * 更新轮播图状态
     * 
     * @param tSysBanner 轮播图
     * @return 结果
     */
    int updateTSysBannerByStatus(TSysBanner tSysBanner);

    /**
     * 根据分组查询轮播图列表
     * 
     * @param groupName 分组名称
     * @return 轮播图集合
     */
    List<TSysBanner> selectTSysBannerByGroupName(String groupName);

    /**
     * 根据分组和地市查询轮播图列表
     * 
     * @param groupName 分组名称
     * @param area 地市
     * @return 轮播图集合
     */
    List<TSysBanner> selectTSysBannerByGroupNameAndArea(String groupName, String area);
}
