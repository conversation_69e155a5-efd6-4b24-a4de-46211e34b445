package cn.guliandigital.utils.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.utils.domain.TUtilDict;
import cn.guliandigital.utils.mapper.TUtilDictMapper;
import cn.guliandigital.utils.service.ITUtilDictService;

/**
 * 联机字典Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
@Service
public class TUtilDictServiceImpl implements ITUtilDictService
{
    @Autowired
    private TUtilDictMapper tUtilDictMapper;

    /**
     * 查询联机字典
     * 
     * @param id 联机字典ID
     * @return 联机字典
     */
    @Override
    public TUtilDict selectTUtilDictById(String id)
    {
        return tUtilDictMapper.selectTUtilDictById(id);
    }

    /**
     * 查询联机字典列表
     * 
     * @param tUtilDict 联机字典
     * @return 联机字典
     */
    @Override
    public List<TUtilDict> selectTUtilDictList(TUtilDict tUtilDict)
    {
        return tUtilDictMapper.selectTUtilDictList(tUtilDict);
    }

    /**
     * 新增联机字典
     * 
     * @param tUtilDict 联机字典
     * @return 结果
     */
    @Override
    public int insertTUtilDict(TUtilDict tUtilDict)
    {
        tUtilDict.setCreateTime(DateUtil.getCuurentDate());
        return tUtilDictMapper.insertTUtilDict(tUtilDict);
    }

    /**
     * 修改联机字典
     * 
     * @param tUtilDict 联机字典
     * @return 结果
     */
    @Override
    public int updateTUtilDict(TUtilDict tUtilDict)
    {
        return tUtilDictMapper.updateTUtilDict(tUtilDict);
    }

    /**
     * 批量删除联机字典
     * 
     * @param ids 需要删除的联机字典ID
     * @return 结果
     */
    @Override
    public int deleteTUtilDictByIds(String[] ids)
    {
        return tUtilDictMapper.deleteTUtilDictByIds(ids);
    }

    /**
     * 删除联机字典信息
     * 
     * @param id 联机字典ID
     * @return 结果
     */
    @Override
    public int deleteTUtilDictById(String id)
    {
        return tUtilDictMapper.deleteTUtilDictById(id);
    }

    @Override
    public List<TUtilDict> addDic(TUtilDict tUtilDict) {
        List<TUtilDict> tUtilDicts = tUtilDictMapper.selectTUtilDictList(tUtilDict);
        for (TUtilDict utilDict : tUtilDicts) {
            int codePoint = utilDict.getWordHead().codePointAt(0);
            utilDict.setHeadUnicode(codePoint);
            tUtilDictMapper.updateTUtilDict(utilDict);
        }
        return null;
    }
}
