package cn.guliandigital.utils.mapper;


import cn.guliandigital.utils.domain.ShujuCalendarconversion;

import java.util.List;

/**
 * 纪年换算Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-11-03
 */
public interface ShujuCalendarconversionMapper 
{
    /**
     * 查询纪年换算
     * 
     * @param id 纪年换算ID
     * @return 纪年换算
     */
        ShujuCalendarconversion selectShujuCalendarconversionById(Long id);

    /**
     * 查询纪年换算列表
     * 
     * @param shujuCalendarconversion 纪年换算
     * @return 纪年换算集合
     */
    List<ShujuCalendarconversion> selectShujuCalendarconversionList(ShujuCalendarconversion shujuCalendarconversion);

    /**
     * 新增纪年换算
     * 
     * @param shujuCalendarconversion 纪年换算
     * @return 结果
     */
    int insertShujuCalendarconversion(ShujuCalendarconversion shujuCalendarconversion);

    /**
     * 修改纪年换算
     * 
     * @param shujuCalendarconversion 纪年换算
     * @return 结果
     */
    int updateShujuCalendarconversion(ShujuCalendarconversion shujuCalendarconversion);

    /**
     * 删除纪年换算
     * 
     * @param id 纪年换算ID
     * @return 结果
     */
    int deleteShujuCalendarconversionById(Long id);

    /**
     * 批量删除纪年换算
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteShujuCalendarconversionByIds(Long[] ids);

    List<ShujuCalendarconversion> getCalendarADByGanzhi(String ganzhi);

    List<ShujuCalendarconversion> getPeriod();

    List<ShujuCalendarconversion> getCalendarADByOr(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getCalendarConversionByPeriodAndAD(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getRegimeOrMaxOrMinByPeriod(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getEmperorNameBy(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getCalendarConversionByAll(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getCalendarConversion(ShujuCalendarconversion shujuCalendarconversion);

    Long getCount(String parameter);

    List<ShujuCalendarconversion> getRegime(ShujuCalendarconversion shujuCalendarconversion);

    List<ShujuCalendarconversion> getRegimeByLike(ShujuCalendarconversion shujuCalendarconversion);
}
