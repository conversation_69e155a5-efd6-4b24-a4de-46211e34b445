package cn.guliandigital.utils.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.utils.service.IRedisService;

@Service
public class RedisServiceImpl implements IRedisService {

	@Autowired
	private RedisCache redisCache;

	@Override
	public String getWxAmpTransactionsNumber() {
		long incr = redisCache.incr("WX:AMP:TRANSACTIONS_NUMBER", 1);

		String leftPad = StringUtil.leftPad(incr + "", 3, "0");
		String transactionsNumber = "T" + DateUtil.dateToStr(new Date(), "yyMMdd") + leftPad;
		return transactionsNumber;
	}
	
	@Override
	public String getWxAmpRefundNumber() {
		long incr = redisCache.incr("WX:AMP:REFUND_NUMBER", 1);
		
		String leftPad = StringUtil.leftPad(incr + "", 3, "0");
		String transactionsNumber = "R" + DateUtil.dateToStr(new Date(), "yyMMdd") + leftPad;
		return transactionsNumber;
	}
}
