package cn.guliandigital.utils.domain;

import javax.validation.constraints.Size;

public class ShujuCalendarconversion {
	/*
	*公元  calendarAD
	*干支  power
	*时期  period
	*政权  regime
	*帝号  emperorNo
	*帝名  emperorName
	*年号  yearNo
	*年份  year
	*附注  affiliated
	*/
	
	private int id;
	
	@Size(max = 10 ,message = "查询条件最大不超过10个字符")
	private int calendarAD;
	private String power;
	private String period;
	private String regime;
	private String emperorNo;
	private String emperorName;
	private String yearNo;
	private Integer year;
	private String affiliated;
	private String periodFan;
	private String regimeFan;
	private String emperorNoFan;
	private String emperorNameFan;
	private String maxad;
	private String minad;
	private String yearNoFan;
	private String powerFan;
	private String regimeNo;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getCalendarAD() {
		return calendarAD;
	}
	public void setCalendarAD(int calendarAD) {
		this.calendarAD = calendarAD;
	}
	public String getPower() {
		return power;
	}
	public void setPower(String power) {
		this.power = power;
	}
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public String getRegime() {
		return regime;
	}
	public void setRegime(String regime) {
		this.regime = regime;
	}
	public String getEmperorNo() {
		return emperorNo;
	}
	public void setEmperorNo(String emperorNo) {
		this.emperorNo = emperorNo;
	}
	public String getEmperorName() {
		return emperorName;
	}
	public void setEmperorName(String emperorName) {
		this.emperorName = emperorName;
	}
	public String getYearNo() {
		return yearNo;
	}
	public void setYearNo(String yearNo) {
		this.yearNo = yearNo;
	}
	public Integer getYear() {
		return year;
	}
	public void setYear(Integer year) {
		this.year = year;
	}
	public String getAffiliated() {
		return affiliated;
	}
	public void setAffiliated(String affiliated) {
		this.affiliated = affiliated;
	}

	public String getPeriodFan() {
		return periodFan;
	}

	public void setPeriodFan(String periodFan) {
		this.periodFan = periodFan;
	}

	public String getRegimeFan() {
		return regimeFan;
	}

	public void setRegimeFan(String regimeFan) {
		this.regimeFan = regimeFan;
	}

	public String getEmperorNoFan() {
		return emperorNoFan;
	}

	public void setEmperorNoFan(String emperorNoFan) {
		this.emperorNoFan = emperorNoFan;
	}

	public String getEmperorNameFan() {
		return emperorNameFan;
	}

	public void setEmperorNameFan(String emperorNameFan) {
		this.emperorNameFan = emperorNameFan;
	}

	public String getCalendarADStr()
	{
		return this.calendarAD+"".replace("-", "前");
	}

	public String getMaxad() {
		return maxad;
	}

	public void setMaxad(String maxad) {
		this.maxad = maxad;
	}

	public String getMinad() {
		return minad;
	}

	public void setMinad(String minad) {
		this.minad = minad;
	}

	public String getYearNoFan() {
		return yearNoFan;
	}

	public void setYearNoFan(String yearNoFan) {
		this.yearNoFan = yearNoFan;
	}

	public String getPowerFan() {
		return powerFan;
	}

	public void setPowerFan(String powerFan) {
		this.powerFan = powerFan;
	}

	public String getRegimeNo() {
		return regimeNo;
	}

	public void setRegimeNo(String regimeNo) {
		this.regimeNo = regimeNo;
	}

	@Override
	public String toString() {
		return "ShujuCalendarconversion{" +
				"id=" + id +
				", calendarAD=" + calendarAD +
				", power='" + power + '\'' +
				", period='" + period + '\'' +
				", regime='" + regime + '\'' +
				", emperorNo='" + emperorNo + '\'' +
				", emperorName='" + emperorName + '\'' +
				", yearNo='" + yearNo + '\'' +
				", year=" + year +
				", affiliated='" + affiliated + '\'' +
				", periodFan='" + periodFan + '\'' +
				", regimeFan='" + regimeFan + '\'' +
				", emperorNoFan='" + emperorNoFan + '\'' +
				", emperorNameFan='" + emperorNameFan + '\'' +
				", maxad='" + maxad + '\'' +
				", minad='" + minad + '\'' +
				", yearNoFan='" + yearNoFan + '\'' +
				", powerFan='" + powerFan + '\'' +
				", regimeNo='" + regimeNo + '\'' +
				'}';
	}
}