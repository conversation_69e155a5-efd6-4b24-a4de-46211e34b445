package cn.guliandigital.utils.domain;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @version V1.0
 * @Description:
 * @Auther: BOHANZHANG
 * @date 2020-11-14 17:11
 * @Title: queryParams.java
 * @Package: cn.guliandigital.utils.domain
 */
@Data
public class QueryParams {

    //查询条件
    @NotBlank(message = "查询条件不能为空")
    @Size(max = 10 ,message = "查询条件最大不超过10个字符")
    private String keyword;
}
