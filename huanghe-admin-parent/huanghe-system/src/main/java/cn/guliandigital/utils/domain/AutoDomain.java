package cn.guliandigital.utils.domain;/**
 * <AUTHOR>
 * @Description
 * @Date 10:28
 * @param ${}:
 **/

import lombok.Data;

/**
 * @ClassName AutoDomain
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/18 10:28
 */
@Data
public class AutoDomain {

private String result;
    /**
     * @Description 处理状态
     * @Date 2020/11/18 10:35
     **/
    private String id;

    private String status;

    /**
     * @Description 模型类型
     * @Date 2020/11/18 10:35
     **/
    private String type;

    /**
     * @Description /内容
     * @Date 2020/11/18 10:35
     **/
    private String content;
    /**
     * @Description 可用数字
     * @Date 2020/11/18 16:35
     **/
    private Integer available;
}
