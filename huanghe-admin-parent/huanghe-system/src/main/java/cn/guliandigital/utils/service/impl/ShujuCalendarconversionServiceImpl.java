package cn.guliandigital.utils.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.common.utils.FanJianUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.utils.domain.ShujuCalendarconversion;
import cn.guliandigital.utils.mapper.ShujuCalendarconversionMapper;
import cn.guliandigital.utils.service.IShujuCalendarconversionService;
import lombok.extern.java.Log;

@Log
@Service
@Transactional
public class ShujuCalendarconversionServiceImpl implements IShujuCalendarconversionService {

	@Autowired
	private ShujuCalendarconversionMapper mapper;

	@Override
	public int saveCalendarConversion(ShujuCalendarconversion calendarCon) {
		return mapper.insertShujuCalendarconversion(calendarCon);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarConversion(ShujuCalendarconversion shujuCalendarconversion) {
		return mapper.selectShujuCalendarconversionList(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarADByGanzhi(String ganzhi) {
		return mapper.getCalendarADByGanzhi(ganzhi);
	}

	

	@Override
	public List<ShujuCalendarconversion> getPeriod() {
		return mapper.getPeriod();
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarADByOr(ShujuCalendarconversion shujuCalendarconversion) {
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getPeriod())){
			shujuCalendarconversion.setPeriodFan(FanJianUtils.toFan(shujuCalendarconversion.getPeriod()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getRegime())){
			String regimeFan=FanJianUtils.toFan(shujuCalendarconversion.getRegime());
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorNo())){
			shujuCalendarconversion.setEmperorNoFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorName())){
			shujuCalendarconversion.setEmperorNameFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorName()));
		}
		return mapper.getCalendarADByOr(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarConversionByPeriodAndAD(ShujuCalendarconversion shujuCalendarconversion) {
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getPeriod())){
			shujuCalendarconversion.setPeriodFan(FanJianUtils.toFan(shujuCalendarconversion.getPeriod()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getRegime())){
			String regimeFan=FanJianUtils.toFan(shujuCalendarconversion.getRegime());
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorNo())){
			shujuCalendarconversion.setEmperorNoFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorName())){
			shujuCalendarconversion.setEmperorNameFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorName()));
		}
		return mapper.getCalendarConversionByPeriodAndAD(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getRegimeOrMaxOrMinByPeriod(String period) {
		ShujuCalendarconversion shujuCalendarconversion = new ShujuCalendarconversion();
		shujuCalendarconversion.setPeriod(period);
		shujuCalendarconversion.setPeriodFan(FanJianUtils.toFan(period));
		return mapper.getRegimeOrMaxOrMinByPeriod(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getEmperorNameBy(String period, String regime) {
		ShujuCalendarconversion shujuCalendarconversion = new ShujuCalendarconversion();
		if(StringUtil.isNotEmpty(period)){
			shujuCalendarconversion.setPeriod(period);
			shujuCalendarconversion.setPeriodFan(FanJianUtils.toFan(period));
		}
		if(StringUtil.isNotEmpty(regime)){
			shujuCalendarconversion.setRegime(regime);
			String regimeFan=FanJianUtils.toFan(regime);
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		return mapper.getEmperorNameBy(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarConversionByGanzhiAndAD(ShujuCalendarconversion shujuCalendarconversion) {
		return mapper.selectShujuCalendarconversionList(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarConversionByAll(ShujuCalendarconversion shujuCalendarconversion) {
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getYearNo())){
			shujuCalendarconversion.setYearNoFan(FanJianUtils.toFan(shujuCalendarconversion.getYearNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getRegime())){
			String regimeFan=FanJianUtils.toFan(shujuCalendarconversion.getRegime());
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorNo())){
			shujuCalendarconversion.setEmperorNoFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorName())){
			shujuCalendarconversion.setEmperorNameFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorName()));
		}
		return mapper.getCalendarConversionByAll(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getCalendarConversionbyCondition(ShujuCalendarconversion shujuCalendarconversion) {
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getPower())){
			shujuCalendarconversion.setPowerFan(FanJianUtils.toFan(shujuCalendarconversion.getPower()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getPeriod())){
			shujuCalendarconversion.setPeriodFan(FanJianUtils.toFan(shujuCalendarconversion.getPeriod()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getYearNo())){
			shujuCalendarconversion.setYearNoFan(FanJianUtils.toFan(shujuCalendarconversion.getYearNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getRegime())){
			String regimeFan=FanJianUtils.toFan(shujuCalendarconversion.getRegime());
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorNo())){
			shujuCalendarconversion.setEmperorNoFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorName())){
			shujuCalendarconversion.setEmperorNameFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorName()));
		}
		return mapper.getCalendarConversion(shujuCalendarconversion);
	}

//	@Override
//	public void debugGenerateFan() {
//		String longChar =  "句吒呐呂吳呵呪咼呶哅哢唄員唕啓啞問喎唸唫啎啗喫喆喪單喦啣喬喒喚喲嗎嗊嗇嗩嗆嗚嗁嘖嘩嘆嘔嘗嘍嗶嘅嘮噠嘵噉噴噁嘽噅嘸嘰噰噹噦噥噯噲嘯嚀嚌嚇嚐嚙嚕嚮嚨嚥辴嚲嚶嚴嚳囁囀嚻囂囈囅囉囌囓囑囝圅國圇圌圍園圓團圖坏坐㘱坰坿坵垜垻執埡堊埜堅堝埰埳報堘堯堿堦場塗塋塚塒塤塏塢塊塵墊塹塼墟墖塲墝墳墜墮壇墻壋墾壖壓壎壙壘壠壟壞壚壜壩壯壺壻壼壽夢夥夾奐奧奩奪奬奮奼妝妬姪姙姦娛娬婭婁媧婬婦媮婣媽媼媿嫋嫗嬈嫻嫺嬋嬀嫵嬌嬃嫿嬙嬡嬝嬪嬭嬰嬸嬾孃孌馬馮馭馱馴馳馹駁駝駐駔駛駟駙駒駘駑駕駮駭駢駡駰駱騂騁駿駸騏騎騍騧騐騅騙騣騤騖騫騸騲騮騰騶騷騭驁驊驀驅驃騾驄驂驍驕驏驘驚驛驗驌驟驥驢驤驦驪驫髤髩髮鬆鬍鬚鬢鬥鬧鬨鬩鬭鬮鬱䰛魎魘魚魛魢魴魨魯鮌鮃魺鮁鮎鮋鮓鮒鮊鮣鮑鮐鮫鮮鮺鮝鮭鮚鮪鮞鮦鯈鯊鯇鯗鮷鯁鯉鯀鮶鯨鯖鯪鯕鯫鯤鯧鯝鯰鯢鯛鯔鰌鯿鰆鰈鰏鯷鰂鰓鰃鰐鰍鰒鯾鰉鰁鯽鰟鰜鰭鰱䱷鰣鰨鰥鰮鰩鰷鱅鱉鰲鰳鰹鰾鰻鰵鰼䲁鱂鱔鱗鱒鱝鱏鱖鱓鱘鱣鱤鱧鱠鱟鱭鱨鱯鱷鱸鱺鳥鳧鳩鳶鳴鳳鳲鴆鴈鴉鴇鴕鴣鴨鴞鴦鴒鴛鴟鴝鴻鵁鴯鴷鴿鴰鵂鵜鵓鵑鵒鵠鵝鵷鶉鶊鵡鶄鵶鵲鶇鵪鵾鵯鵬鵰鷀鶤鶘鶪鶡鶚鶻鶖鶩鶺鷁鶼鶯鶴鷊鷂鷄鶬鶲鷉鶹鷓鷙鷗鷖鷚鷲鷰鷯鷳鷦鷸鷥鸂鸇鷹鷺䴉鷫鸏鸌鸎鸕鸘鸚鸛鸞鸝鹵鹺鹹鹻鹽鹼麁麅麐麗麕麞麤麥麩麪麯䴸麴麵麽黃黌𪎭點黨黰黴黲黶黷黽黿鼂鼃鼈鼇鼉鼕鼦鼴䶊齇齊齋齎齏齒齔齕齗齟齡齣齙齠齩齧齦齬齪齶齲齷龍龐龑龔龕龢龜玆玨珎珮琊現琺琯琱瑯琿瑇瑒瑋瑩瑴瑪璉瑣瑲瑠璡璣璫環璦璵璽璿瓊瓈瓏瓔瓚甚産甦甯甿甽畝畢異畱畫當疇疊疋疎疿痌痙痠瘀瘂痺瘖瘧瘍瘉瘓瘋瘞瘡瘻瘲癆療癇癉癘癢癟癡癤癥癩癧癭癬癮癰癱癲發皁皜皚皰皸皺皻盃盌盜盞監盡盤盧盪眎眘眡眥眾睠睞睜瞇瞞瞘瞭瞼矇矙矚𥎊矯矴砲硃硨硤硜硯碁碕碪碩碭碻確碼磑磐磧磚䃘磣磽磾磯礎礙礦礬礪礧礫礱礮祇祕祘祐䄍禍祿禎禕禡禪禦禮禱禰秈秊秔稉稈稟稜稬稭稱種稾穀稹穈穅積穎穌穇穉穡穢穠穫穩穭穽窓窩窪窯窰窮窺窶窵竄竅竇竈竊竚竝竢竪競忼忷怱恥恆恠恉悤悵惡惪悽悶惲惷愜㥦惻愛惸惱慂愨慄愷慍愴愾慇態慶慤慚慙慳慼憂慮慾憇慟慫慴慘慣憲憑憐憤憖憫憒憚憮憊憶懍應懌懇懣懞懨懕懜懟懲懷懶懸懺懾懽懼𢥞戀戇戉戔戩戧戯戰戲戹扞扡拑抴拋担拚拏招挱捄挾捲掽掛掗掫捱掆捫捵採掙捨掄掏掃揮揀揚揜搥換搾搧搉搆搨損搯搶搗摀摯摶摳撦摣摟摑撾摠摺摻摜撈撻撓撢撩撐撲撣撝撳撫撟擕撚撥撏擁擊擋據擄擇撿擧擔擘擰擯擠擡擣擱擬擴擲攆擷擾攄擻擺攏攖攔攙攛𢹲攝攜㩳攣攟攤攢攩攪攬攷敁㪍敗敍敘敠敭敵敺數斃斂斕斬斷斸於斾旂旛並么乾舝亂亙亞伀佇佘佈佔作併來侖信俞俠侶俁係侷倣倉倖倀倈倳倆們個倸倫偰偪偵側偺偉傢傚備傜傖傘傑傭債僅傳傴僉傾僂傷傯僮僱僥僨僊僕僞僑儁億儀價儅儂儍儉儈儐儕儔儘儲優償儺儷儸儹儻儼兇兒兗兩冄冪凍凜凱凴刧刦刴剋剌剄則剠剗剛剮剝剴創剷劄劃劌劇劍劊劉劑劚効勅勁勑務動勞勛勝勣勢勠勦勩勱勳勵勸匊匭匯匱匳匵區協卹卻厓厙厖厠厤厭厲厴參叠叡叢㝃孫學孼孿宂宮寃寖寧寢實寬審寫寵寳寶尅專將尋對導尗尠尟尢尲尷屆屍屓屜屢層屧屨屬屭岡峽峴島峯崍崧崗崑崢崯崙嵒崳嵐嶄嶇嶁嶢嶔嶠嶩嶧嶮嶼嶴嶸嶺嶽巋巒巔巖巵帀帋帥師帬帳帶㡓幀幃幣幘幗幟幫幬幮幹幾庪庫廂廁庽廈廎廕廚廝廣廟廠廡廢廩廬廳廻弔弢張強彆彈彊彌彎彙彠彫彿徇後徑徠從徧復徬徹徵氾汙汎沈沍沖決況泝洩洶浹涇涖涼淚淩淶淒淺渦淨淪淛淥渾湊湮減湞測湯湌淵渙渢湧滎溝漣滅溼溳溫滄準滌溮溜滻滸滾滬漬漢滿漸漚滯滷漊漵潁漁滲漲漿澇澐潔澾澆潛澁潤潰澂潿潙潷潟潑潯潠澣澠濃澤濁澮澩澦澱濘濱濟濛濤濫濔濬濕濜澀濰瀋瀉瀅瀆瀦濾濺濼瀏瀧瀠瀨瀝瀕瀘瀟瀾瀲瀰灕灄灃灘灑灠灝灣灤灧灩火災為炧炤烏焠焫無煢煇煠煉煙煩煗煬煖煥煆煒熒燁熱熲燙熾燉燐燒燜燄燈營燦燭燬燾燻燿燼爕爗爍爐爛爭爲爺爾爿牀牆牋牐牘牴牽犂犖犛犢犧狀狥狹狽猙猘猶猨獃獅猻獄獏獎獧獨獫獪獰獮獲獷獸獵獺獻獼𤭛甆甎甌甕甖針釘釗釕釬釦釧釤釣釩釵鈁鈃鉅鈍鈔鈐鈑鈞鈒鈕鈀鉈鉉鉍鈺鉦鉗鈷鉢鈸鉞鉆鉀鈿鈴鉄鉑鉛鉤鉚鉋鈹鉸銃鉶銬銠鉺銍銕銅銓銚銖銑銛銗銜銘鉻銀鋭銻鋃鋩鋪鋙鋏鋣銷銲鋤鋜鋝銼鋌銹鋂鋒鋟鋦錠錇錈錟錏錯錡錢鋼錁錕錫錮鍋錚錘錐錦鋸錄錙鍍鎡鍥錨鍊鍼鍇鍘鍚鍔鍰鍬鍤鍾鍛鎪鍠鎚鍵鎋鎔鎊鎬鎰鎌鎣鎮鏈鎛鎘鎒鎲鎖鎧鎗鎩鎢鎦鏡鏟鏑鏞鏃鏇鏵鏌鏨鏗鏢鏚鏜鏤鏝鏐鏹鏘鐋鐘鐓鐃鐔鐐鏷鐫鐙鐮鐳鐵鐺鐸鐶鐲鏽鑌鑄鑑鑒鑊鑛鑣鑤鑠鑕鑞鑪鑲鑰鑱鑷鑾鑼鑽鑿钁長門閂閃閈閉閔閌閏開閑閎間閒閙閘閡関閨閩閤閥閣閲閬閫閭閼闍閾閹閶閿閽閻闊闇闌闃闆闋闈闕闖闔闐闒闓闚關闥闞闠闡闤闢阬阨阯阿陒陣陝陘陗陞陸陳陰隊階隄陽隕隖際隨險隱隴隸隻雋雖雜離雞雙雛難雲電霑霛霤霢霧霶霽靆靄靂靈靉靚靜靦靨靭鞀鞏鞉鞝鞦鞹鞾韃鞽韁韆韉韋韌韍韓韙韝韞韜韤韮韻響頁頂頃頇項順須頏頊頑頓頌頒頎預領頗頦頡頜頫頤頭頰頸頻頮頷頲頽顇顆額顔題顒顋顎顓類顛願顙顢顧顥顦顫顬顯顰顱顳顴風颯颭颮颱颳颶颺颸颼颻飀飄飆飛飡飣飢飥飱飩飪飫飭飯飲養飾飽飴飼飿餃餅餌餁餉餑餖餒餘餓餕館餞餛餚餧餡餬餳餵餶餿餱饈餻饁餺餼餽餾饉饅饍饒饊饋饌饑饗饜饝饞骯骾髏髒體髕髖竾筍筆筴筧筯筰筩箝箋箎箇箏箠箒箲篋範節簑篤築篔篠篩篛𥱼簆篲簀簍篳簙簣簞簾簹簴簽簷簫籌籃籐籠籜籟籛籙籥籢籤籬籩籪籮籯籲籸粃粧糉糢糞糝糧䊪糰糲糱糴糶糸糾紆紂紅紇約紈紀紉紋紡紜紘純紕紗納紛紝紙級紓紖紐紵絃絆紺紲紮紱組紳細紬絅終紿紼絀紹絞統絨結絝絰絏給絛絍絎絳絡絢絶絲綈綁綆經綃綑絹綏綌綉綜綻綰綣綾緒緊綺綫緋綽網綱緄綬綵綸維綿綹綳綢緍綯綴綠緇締編緙緤緗練緘緬緹緲緝緦緩緞緶緥緜線緱縋緯緣縗縞縭縊縑縈縝縛縟緻縉縣縕縚縧縏縫縐縯縮縴績縶縹縷縵縲繃總縱繆繈繅織繕繒繠繞繖繐繚繢繙繭繫繮繩繾繰繹繯繪繳繡繽繼纊纏纇纈續纍纕纓纖纔纘纜缽缾罃罇罈罋罌罏罎罰罸罵罷羆羅羈羢羨義羣羶翄習翬翫翹翽耑耡耬耮耼聖聝聞聲聰聳聯職聶聵聹聾聽肈肅肊肎肐肧肬脄脩脈胮脃脅脣脛脗脹腎腸腳腫腦膁膋膆膃膞膚膕膠膩臈膿臉膾膽臏臍臕臘臙臚臝臟臠臢臥臨臯臺與舉興舊舖舘艙鵃艤艣艦艪艫艱艸芻苧苺荅華莕荳莢莖莧莊菸萇著菢萊菴菓萵蓱葷葉萬葅葠葦葒葯蒞蓆蓋蓮蒔蒼蒨蓧蒐蓀蒓蔆蔴蓴蔕䔛蔞蓽蔦蔥蓯蔔蔣蔭蕩蕅蕓薘蕘蕆蕋蕢蕒蕪蕎蕕蕁薌薦薔薑薟薈薊蕷蕭薴薺藉藍薰藎薩藭藷藝藪藥蘢藹蘂蘀䕭藶蘋蘆藺蘄蘇蘊蘭蘞蘚蘗蘺𧄜蘿虆處號虜虧虯虵蚘蚦蛕蜋蛺蜆蜹蜨蝸蝕蜺蝃蝱蝡蝟蝦蝨螢螞螙螘螄蟄螮螻蟈螿蟯蟲蟬蟣蟻蠆蟶蠅蠍蠙蠐蠔蠑蠣蠟蠭蠨蠱蠶蠻衆衇衊衒術衚衝衛衞衹袞裀袷補裌裏裊裝褃製褏褌複褘褵褲褧褳襃褻褸襆襌襍襏襠襖襤襪襬襯襲襴襵覈昇昞時晉晣晝晳暉暈㬉暘暠暢暱曄暫曇曉曆曏曖曠曡曨曬書會朧末朶杇杓杝枒東柹杴柁柺柟枹枱案栔栞栢栧栰條桚梜桿梘梱梟梔椗椀棄棬棖椏棟棲極棧棡棗棃楳楨業楊楥椶榘楓榜槀榮構槖榪槓榦槤槑榿槍槃樑槨樣樁樺槧樞標樝樓槾樂樅槳橈樹橫樷樸橋檇機橢檁檥㯳檉檣檟檔檢檜檸檳檾檯檮櫃檻櫂櫈櫧櫥櫝櫫櫚櫛櫟櫓櫞櫬櫳𣟄櫪櫨櫸櫺櫻欄權欒欏欑欛欖欞欱欵欽歎歐歘歛歟歗歡歲歷歸殀殘殞殤殫殭殮殯殲殺殼毆毉毘毧毬氂毿氈氌氣氳見視規覓覘覜覡覩覥親覦覯覬覲覷覰覺覽覿觀觔觕觝觴觶觸計訂訃訐訏討訌訕託訖訓訊記訒訪詎訝訥訟訩許訛設訣註詠評証詁訶詛詗診詐訴詆詒詞詘詖詔詫該詳誆誄試詩詿詰誇詼誠詮誅詵話詬詭詣詢詾詡説誡誖誣語誚誤誥誘誨誕誑誦誒認誼諒諄誶談請諸諏諑誹課誾諍諗論諉誰諛調諂諠諦諳諺諮謎諢諞諾謀諶諜諫諧謔謁謂諤諼諭諡諷諱諝謗謚謙謐講謊謖謡謝謄謅謫譁謨謹謳謾謬識譜譆謿譚譖譌譙證譎譔譏議譴譟譯譽譫讁譸護讅譾讀讋讌讎變讓讕讖讒讚讜讞谿豈豎豐豔豘豬豶貉貍䝟貓貐貛貝貞負貢財責貪貶貧貨販貫貯貳賁貰貼貴貺買貸貿貽費賀資賅賊賈賄貲賃賂賓賕賑賒賠賡賧賦賬賣賭賚賢賤賞賜質賙賴賵賮賽賺購賻賸贅贄賾贇贈贊贏贍贓贔贐贖贗贛𧹞䞓赬趂趙趕趦趨趲跀跕跡跢跥跼踡踐䠊踰踴蹌蹏蹠蹟蹣蹕蹤蹺蹷躉躋躊躍躕躑躚躒躓躡躪躭躱躳躶軀軃車軋軍軌軒軑軔軛軟軲軻軸軹軫軼軺較軾載輊輇輈輅輒輔輕輓輬輦輛輝輩輞輥輪輟輜輳輻輭輯輸轄轂轅轀輿輾轆轉轍轔轎轟轢轡轤辠辤辦辭辮辯農迆迯迺迴這連逎逕逷過進遊運達違遡遠遝遞遜適遯遶遷遼遺遲選邁還邇邊邐邏那郃郟郤郰郵郳鄆鄉鄖鄔鄒鄘鄰鄭鄲鄧鄴鄶鄺酈酇酖酧醃醞醜醫醬醯醱醻醼釀醿釃釁釅釋釐兀唧幐𦡂袡凉説說";
//		String shortResult = "";
//		String longResult = "";
//		for (int i=0;i<longChar.length();i++)
//		{
//			if (longChar.codePointAt(i)>65535)
//			{
//				i++;
//				continue;
//			}
//			String str = longChar.charAt(i)+"";
//			Long count = mapper.getCount(str);
//			if (count>0)
//			{
//				longResult += str;
//				shortResult += FanJianUtils.toJian(str);
//			}
//		}
//		log.info(longResult);
//		log.info(shortResult);
//	}

	@Override
	public List<ShujuCalendarconversion> getRegime(ShujuCalendarconversion shujuCalendarconversion) {
		return mapper.getRegime(shujuCalendarconversion);
	}

	@Override
	public List<ShujuCalendarconversion> getRegimeByLike(ShujuCalendarconversion shujuCalendarconversion) {
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getYearNo())){
			shujuCalendarconversion.setYearNoFan(FanJianUtils.toFan(shujuCalendarconversion.getYearNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getRegime())){
			String regimeFan=FanJianUtils.toFan(shujuCalendarconversion.getRegime());
			if(StringUtil.isNotBlank(regimeFan)&&regimeFan.contains("涼")){
				regimeFan=regimeFan.replace("涼","凉");
			}
			shujuCalendarconversion.setRegimeFan(regimeFan);
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorNo())){
			shujuCalendarconversion.setEmperorNoFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorNo()));
		}
		if(StringUtil.isNotEmpty(shujuCalendarconversion.getEmperorName())){
			shujuCalendarconversion.setEmperorNameFan(FanJianUtils.toFan(shujuCalendarconversion.getEmperorName()));
		}
		return mapper.getRegimeByLike(shujuCalendarconversion);
	}

}
