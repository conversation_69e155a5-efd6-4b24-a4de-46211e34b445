package cn.guliandigital.utils.mapper;


import cn.guliandigital.utils.domain.TUtilDict;

import java.util.List;

/**
 * 联机字典Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
public interface TUtilDictMapper 
{
    /**
     * 查询联机字典
     * 
     * @param id 联机字典ID
     * @return 联机字典
     */
        TUtilDict selectTUtilDictById(String id);

    /**
     * 查询联机字典列表
     * 
     * @param tUtilDict 联机字典
     * @return 联机字典集合
     */
    List<TUtilDict> selectTUtilDictList(TUtilDict tUtilDict);

    /**
     * 新增联机字典
     * 
     * @param tUtilDict 联机字典
     * @return 结果
     */
    int insertTUtilDict(TUtilDict tUtilDict);

    /**
     * 修改联机字典
     * 
     * @param tUtilDict 联机字典
     * @return 结果
     */
    int updateTUtilDict(TUtilDict tUtilDict);

    /**
     * 删除联机字典
     * 
     * @param id 联机字典ID
     * @return 结果
     */
    int deleteTUtilDictById(String id);

    /**
     * 批量删除联机字典
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTUtilDictByIds(String[] ids);
}
