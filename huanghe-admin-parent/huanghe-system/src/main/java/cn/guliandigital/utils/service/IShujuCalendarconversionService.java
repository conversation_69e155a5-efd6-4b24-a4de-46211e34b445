package cn.guliandigital.utils.service;

import cn.guliandigital.utils.domain.ShujuCalendarconversion;
import java.util.List;

public interface IShujuCalendarconversionService {
	
	int saveCalendarConversion(ShujuCalendarconversion calendarCon);
	List<ShujuCalendarconversion> getCalendarConversion(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getCalendarADByGanzhi(String ganzhi);
	List<ShujuCalendarconversion> getPeriod();
	List<ShujuCalendarconversion> getCalendarConversionByGanzhiAndAD(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getCalendarADByOr(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getCalendarConversionByPeriodAndAD(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getEmperorNameBy(String period, String regime);
	List<ShujuCalendarconversion> getRegimeOrMaxOrMinByPeriod(String period);
	List<ShujuCalendarconversion> getCalendarConversionByAll(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getCalendarConversionbyCondition(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getRegime(ShujuCalendarconversion shujuCalendarconversion);
	List<ShujuCalendarconversion> getRegimeByLike(ShujuCalendarconversion shujuCalendarconversion);
	//void debugGenerateFan();
}
