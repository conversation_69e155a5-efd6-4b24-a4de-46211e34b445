package cn.guliandigital.utils.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 联机字典对象 t_util_dict
 *
 * <AUTHOR>
 * @date 2021-10-25
 */
@Data
public class TUtilDict extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 字典名称 */
    @Excel(name = "字典名称")
    private String dictName;

    /** 字头 */
    @Excel(name = "字头")
    private String wordHead;

    //简体字头
    private String sHead;

    /** 读音 */
    @Excel(name = "读音")
    private String likeSound;

    /** 释义 */
    @Excel(name = "释义")
    private String content;

    /** 字体包位置 */
    @Excel(name = "字体包位置")
    private String fontPath;
    private Integer  headUnicode;

    private String[] ids;
    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setDictName(String dictName)
    {
        this.dictName = dictName;
    }

    public String getDictName()
    {
        return dictName;
    }
    public void setWordHead(String wordHead)
    {
        this.wordHead = wordHead;
    }

    public String getWordHead()
    {
        return wordHead;
    }
    public void setLikeSound(String likeSound)
    {
        this.likeSound = likeSound;
    }

    public String getLikeSound()
    {
        return likeSound;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setFontPath(String fontPath)
    {
        this.fontPath = fontPath;
    }

    public String getFontPath()
    {
        return fontPath;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dictName", getDictName())
                .append("wordHead", getWordHead())
                .append("likeSound", getLikeSound())
                .append("content", getContent())
                .append("createTime", getCreateTime())
                .append("fontPath", getFontPath())
                .toString();
    }
}