package cn.guliandigital.storage.log.mapper;

import cn.guliandigital.storage.log.domain.TTaskLog;

import java.util.List;


/**
 * 数据解析日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
public interface TTaskLogMapper 
{
    /**
     * 查询数据解析日志
     * 
     * @param id 数据解析日志ID
     * @return 数据解析日志
     */
        TTaskLog selectTTaskLogById(String id);

    /**
     * 查询数据解析日志列表
     * 
     * @param tTaskLog 数据解析日志
     * @return 数据解析日志集合
     */
    List<TTaskLog> selectTTaskLogList(TTaskLog tTaskLog);

    /**
     * 新增数据解析日志
     * 
     * @param tTaskLog 数据解析日志
     * @return 结果
     */
    int insertTTaskLog(TTaskLog tTaskLog);

    /**
     * 修改数据解析日志
     * 
     * @param tTaskLog 数据解析日志
     * @return 结果
     */
    int updateTTaskLog(TTaskLog tTaskLog);

    /**
     * 删除数据解析日志
     * 
     * @param id 数据解析日志ID
     * @return 结果
     */
    int deleteTTaskLogById(String id);

    /**
     * 批量删除数据解析日志
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTTaskLogByIds(String[] ids);
}
