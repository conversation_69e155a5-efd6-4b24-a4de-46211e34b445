package cn.guliandigital.storage.storage.service.impl;

public class ConfigUtils {

	
//	public static Map<String,TBookMenuImageMongo> HUANGHE_PICID_DISPLAY_MAPPING_MAP_IMAGE = Maps.newLinkedHashMap();
//	public static Map<String,TBookPageContentMongoV2> HUANGHE_PICID_DISPLAY_MAPPING_MAP_TEXT = Maps.newLinkedHashMap();
	
	//public static Map<String,Integer>  HUANGHE_MENUID_DISPLAY_MAPPING_MAP = Maps.newLinkedHashMap();
	
	//public static Map<String,Integer>  HUANGHE_MENUID_DISPLAY_TEXT_MAPPING_MAP = Maps.newLinkedHashMap();
	
}
