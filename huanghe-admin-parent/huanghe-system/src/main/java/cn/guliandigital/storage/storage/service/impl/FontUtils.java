package cn.guliandigital.storage.storage.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.text.StringEscapeUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.typography.font.tools.sfnttool.FontsService;

import cn.guliandigital.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class FontUtils
{
    public static final Integer FONT_TYPE_SONG = Integer.valueOf(1);

    public static final Character[] UN_CHANGE_CHAR = { Character.valueOf('`'), Character.valueOf(' '), Character.valueOf('1'), Character.valueOf('2'), Character.valueOf('3'), Character.valueOf('4'), Character.valueOf('5'), Character.valueOf('6'), Character.valueOf('7'), Character.valueOf('8'), Character.valueOf('9'), Character.valueOf('0'), Character.valueOf('-'), Character.valueOf('.'), Character.valueOf('/'), Character.valueOf('\\'), Character.valueOf('t'), Character.valueOf('a'), Character.valueOf('b'), Character.valueOf('l'), Character.valueOf('e'), Character.valueOf('<'), Character.valueOf('>'), Character.valueOf('"') };

    public static final Integer RANGE = Integer.valueOf(65000);

    private static String encodeRange = "65-90,97-122,224-609;1536-1791";//A-Z a-z  http://www.ab126.com/goju/1711.html#ecms
    //private static String encodeRange = "33-90,97-122,224-609;1536-1791";//A-Z a-z  http://www.ab126.com/goju/1711.html#ecms
    
    //private static String encodeRangeDefault = "19968-40959";//普通汉字
    private static String encodeRangeDefault = "13312-40959";//普通汉字
    private static List<List<Integer>> encodeRangeList = Lists.newArrayList();
    private static List<Integer> encodeRangeDefaultList = Lists.newArrayList();

    //private static String excludeRange = "0-64,91-96,610-1535,1792-10045,12272-12318,58118-59379,65072-65509";
    private static String excludeRange = "0-128,610-1535,1792-10045,12272-12318,58118-59379,65072-65509";
    
    private static List<Integer> excludeRangeList = Lists.newArrayList();

    private static String encodeRangeFlag = "1";

    public static String tagContent = "";
    private static Map<Integer, String> tagMap = Maps.newHashMap();

    private static String excludeRangeTransfer = "19904-19967";    
    private static List<Integer> excludeRangeTransferList = Lists.newArrayList();
    
    private static void initRange()
    {
        if ((encodeRangeFlag != null) && ("1".equals(encodeRangeFlag)))
        {
            String[] ranges = encodeRange.split(";");
            String[] arrayOfString1 = ranges; int j = ranges.length;
            for (int i = 0; i < j; i++) {
                String range = arrayOfString1[i];
                List rangeList = new ArrayList();
                String[] sections = range.split(",");
                for (String section : sections)
                {
                    int begin = Integer.parseInt(section.split("-")[0]);
                    int end = Integer.parseInt(section.split("-")[1]);
                    for (int k = begin; k <= end; k++)
                        rangeList.add(Integer.valueOf(k));
                }
                encodeRangeList.add(rangeList);
            }
        }
        int begin = Integer.parseInt(encodeRangeDefault.split("-")[0]);
        int end = Integer.parseInt(encodeRangeDefault.split("-")[1]);
        for (int i = begin; i <= end; i++)
            encodeRangeDefaultList.add(Integer.valueOf(i));
    	

    	
            
    }

    private static void initExcludeRange()
    {
        String[] ranges = excludeRange.split(",");
        String[] arrayOfString1 = ranges; int j = ranges.length;
        for (int i = 0; i < j; i++) { String range = arrayOfString1[i];

            int begin = Integer.parseInt(range.split("-")[0]);
            int end = Integer.parseInt(range.split("-")[1]);
            for (int k = begin; k <= end; k++)
                excludeRangeList.add(Integer.valueOf(k));
        }
    }

    private static void initExcludeRangeTransfer()
    {
        String[] ranges = excludeRangeTransfer.split(",");
        String[] arrayOfString1 = ranges; int j = ranges.length;
        for (int i = 0; i < j; i++) { String range = arrayOfString1[i];

            int begin = Integer.parseInt(range.split("-")[0]);
            int end = Integer.parseInt(range.split("-")[1]);
            for (int k = begin; k <= end; k++)
                excludeRangeTransferList.add(Integer.valueOf(k));
        }
    }
    
    public static String encode(String source, Map<Integer, Integer> map)
    {
        return change(source, map);
    }

    public static String encode(String source)
    {
        return change(source);
    }

    private static String change(String source, Map<Integer, Integer> map)
    {
        tagContent = "";
        tagMap = new HashMap();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < source.length(); ) {
            int codePoint = source.codePointAt(i);
            char charAt = source.charAt(i);
            if ((!isExclude(charAt)) && (!inTag(i, source))) {
                Integer value = (Integer)map.get(Integer.valueOf(codePoint));
                if (value != null) {
                    if (value.intValue() < 65536)
                        sb.appendCodePoint(value.intValue());
                    else
                        sb.append(toHex(value.intValue()));
                }
                else if (codePoint < 65536)
                    sb.appendCodePoint(codePoint);
                else
                    sb.append(toHex(codePoint));
            }
            else {
                sb.appendCodePoint(codePoint);
            }
            for (int j = 0; j < Character.charCount(codePoint); j++) {
                i++;
            }
        }
        return sb.toString();
    }

    public static boolean inTag(int index, String content)
    {
        if (!tagContent.equals(content))
        {
            tagContent = content;
            tagMap = new HashMap();
            Pattern pattern = Pattern.compile("<([^>]*)>");
            Matcher matcher = pattern.matcher(content);
            boolean r = matcher.find();
            while (r) {
                int start = matcher.start();
                int end = matcher.end();
                for (int i = start; i < end; i++)
                    tagMap.put(Integer.valueOf(i), "in");
                r = matcher.find();
            }
        }

        return "in".equals(tagMap.get(Integer.valueOf(index)));
    }

    private static String change(String source)
    {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < source.length(); ) {
            int codePoint = source.codePointAt(i);
            char charAt = source.charAt(i);
            if (!isExclude(charAt)) {
                Integer value = Integer.valueOf(codePoint);
                if ((value != null) && (value.intValue() > 0)) {
                    if (codePoint < 65536)
                        sb.appendCodePoint(value.intValue());
                    else
                        sb.append(toHex(value.intValue()));
                }
                else if (codePoint < 65536)
                    sb.appendCodePoint(codePoint);
                else
                    sb.append(toHex(codePoint));
            }
            else {
                sb.appendCodePoint(codePoint);
            }
            for (int j = 0; j < Character.charCount(codePoint); j++) {
                i++;
            }
        }
        return sb.toString();
    }

    public static Map<Integer, Integer> generateDistinctMap(String source) {
        return generateDistinctMap(source, true);
    }

    public static LinkedHashMap<Integer, Integer> generateDistinctMap(String source, boolean encrypt)
    {
        if (source == null) {
            return new LinkedHashMap();
        }
        LinkedHashMap result = new LinkedHashMap();
        Set set = new HashSet();
        Random rand = new Random();

        Map deResult = new HashMap();
        if (encrypt)
        {
        	int sdx = 0;
            while (source.length() > 0) {
            	sdx++;
                int codePoint = source.codePointAt(0);
                if (!isExclude(codePoint)) {
                    int nextInt = 0;
                    nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();
                    int idx = 0;
                    while (isTransferExclude(nextInt) || (set.contains(Integer.valueOf(nextInt))) || (result.get(Integer.valueOf(nextInt)) != null) || (result.containsValue(Integer.valueOf(nextInt))))
                    {
                    	idx++;
                        nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();
                       
                        //log.info(sdx+"==>"+idx+"==>"+nextInt);
                    }
                    set.add(Integer.valueOf(nextInt));
                    result.put(Integer.valueOf(codePoint), Integer.valueOf(nextInt));
                    deResult.put(Integer.valueOf(nextInt), Integer.valueOf(codePoint));

                    while (deResult.containsKey(Integer.valueOf(codePoint))) {
                        Integer a = (Integer)deResult.get(Integer.valueOf(codePoint));

                        nextInt = 0;
                        nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();

                        while ((set.contains(Integer.valueOf(nextInt))) || (result.get(Integer.valueOf(nextInt)) != null) || (result.containsValue(Integer.valueOf(nextInt))))
                        {
                            nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();
                        }
                        result.put(a, Integer.valueOf(nextInt));
                        deResult.put(Integer.valueOf(nextInt), a);

                        deResult.remove(Integer.valueOf(codePoint));
                    }
                }
                char[] chars = Character.toChars(codePoint);
                String str = new String(chars);
                source = source.replace(str, "");
            }

        }

        String bigWord = "";
        while (source.length() > 0) {
            int codePoint = source.codePointAt(0);
            char[] chars = Character.toChars(codePoint);
            String str = new String(chars);
            if (!isExclude(codePoint)) {
                if (codePoint < 65535)
                {
                    int nextInt = 0;
                    nextInt = codePoint;
                    set.add(Integer.valueOf(nextInt));
                    result.put(Integer.valueOf(codePoint), Integer.valueOf(nextInt));
                } else {
                    bigWord = new StringBuilder().append(bigWord).append(str).toString();
                }
            }
            source = source.replace(str, "");
        }
        while (bigWord.length() > 0) {
            int codePoint = bigWord.codePointAt(0);
            int nextInt = 0;
            nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();

            while ((set.contains(Integer.valueOf(nextInt))) || (result.get(Integer.valueOf(nextInt)) != null) || (result.containsValue(Integer.valueOf(nextInt))))
            {
                nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();
            }
            set.add(Integer.valueOf(nextInt));
            result.put(Integer.valueOf(codePoint), Integer.valueOf(nextInt));

            deResult.put(Integer.valueOf(nextInt), Integer.valueOf(codePoint));

            while (deResult.containsKey(Integer.valueOf(codePoint))) {
                Integer a = (Integer)deResult.get(Integer.valueOf(codePoint));

                nextInt = 0;
                nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();

                while ((set.contains(Integer.valueOf(nextInt))) || (result.get(Integer.valueOf(nextInt)) != null) || (result.containsValue(Integer.valueOf(nextInt))))
                {
                    nextInt = getRandom(rand, Integer.valueOf(codePoint)).intValue();
                }
                result.put(a, Integer.valueOf(nextInt));
                deResult.put(Integer.valueOf(nextInt), a);

                deResult.remove(Integer.valueOf(codePoint));
            }

            char[] chars = Character.toChars(codePoint);
            String str = new String(chars);
            bigWord = bigWord.replace(str, "");
        }
        
        /**
         * 需要初始化之前的映射
         */
//        Map<Integer,Integer> wordmapping = WordMapping.mapping();
//        if(!wordmapping.isEmpty()){
//        	result.putAll(wordmapping);
//        }
        
        return result;
    }

    public static Integer getRandom(Random rand)
    {
        return Integer.valueOf(rand.nextInt(20991) + 19968);
    }

    public static Integer getRandom(Random rand, Integer charCode)
    {
//    	String existCode = WordMapping.mapping(charCode+"");
//    	if(!Strings.isNullOrEmpty(existCode)){
//    		return Integer.parseInt(existCode);
//    	}
        if (encodeRangeDefaultList.size() == 0)
            initRange();
        for (List rangeList : encodeRangeList)
        {
            for (int i = 0; i < rangeList.size(); i++)
            {
                Integer code = (Integer)rangeList.get(i);
                if (code.intValue() != charCode.intValue())
                    continue;
                int index = rand.nextInt(rangeList.size());
                return (Integer)rangeList.get(index);
            }
        }

        int index = rand.nextInt(encodeRangeDefaultList.size());
        return (Integer)encodeRangeDefaultList.get(index);
    }

    private static boolean isExclude_old(char charAt)
    {
        for (Character c : UN_CHANGE_CHAR) {
            if (c.equals(Character.valueOf(charAt))) {
                return true;
            }
        }
        return false;
    }

    private static boolean isExclude(int codePoint)
    {
        if (excludeRangeList.size() == 0)
            initExcludeRange();
        int s = 0;
        for (Integer code : excludeRangeList)
        {
            s++;
            if (code == null) {
                log.info(new StringBuilder().append("1111111111").append(s).toString());
                return false;
            }
            if (code.intValue() == codePoint)
                return true;
        }
        return false;
    }

    
    private static boolean isTransferExclude(int codePoint)
    {
        if (excludeRangeTransferList.size() == 0)
        	initExcludeRangeTransfer();
        int s = 0;
        for (Integer code : excludeRangeTransferList)
        {
            s++;
            if (code == null) {
                log.info(new StringBuilder().append("exception:").append(s).toString());
                return false;
            }
            if (code.intValue() == codePoint)
                return true;
        }
        return false;
    }
    
    public static String toHex(int codePoint)
    {
        return new StringBuilder().append("&#x").append(Integer.toHexString(codePoint)).append(";").toString();
    }

    public static String str2Hex(String source) {
        if (source == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < source.length(); ) {
            int codePoint = source.codePointAt(i);

            if (codePoint > 65535)
                sb.append(toHex(codePoint));
            else {
                sb.append(source.charAt(i));
            }

            for (int j = 0; j < Character.charCount(codePoint); j++) {
                i++;
            }
        }
        return sb.toString();
    }

    public static String hex2Str(String result)
    {
        Pattern pattern = Pattern.compile("&#x[\\S]*?;");
        Matcher matcher = pattern.matcher(result);
        while (matcher.find())
        {
            String hex = matcher.group();
            Integer code = Integer.valueOf(Integer.parseInt(hex.replace("&#x", "").replace(";", ""), 16));
            char[] chars = Character.toChars(code.intValue());
            String word = new String(chars);
            result = result.replace(hex, word);
        }
        return result;
    }
    private Map<String, String> toEncodeMap(Map<Integer, Integer> map) {
        Map result = new HashMap();
        for (Entry<Integer,Integer> entry : map.entrySet()) {
            result.put(entry.getKey(), toHex((entry.getValue()).intValue()));
        }
        return result;
    }

    public static String replaceSpecialChar(String content) {
        content = content.replace("&mdash;", "——");
        content = content.replace("&middot;", "·");
        content = content.replace("&nbsp;", " ´");
        content = content.replace("&acute;", "´");
        content = content.replace("&copy;", "©");
        content = content.replace("&gt;", ">");
        content = content.replace("&micro;", "µ");
        content = content.replace("&reg;", "®");
        content = content.replace("&amp;", "&");
        content = content.replace("&deg;", "°");
        content = content.replace("&quot;;", "\"");
        content = content.replace("&iexcl;", "¡");
        content = content.replace("&raquo;", "»");
        content = content.replace("&brvbar;", "¦");
        content = content.replace("&divide;", "÷");
        content = content.replace("&iquest;", "¿");
        content = content.replace("&not;", "¬");
        content = content.replace("&sect;", "§");
        content = content.replace("&bull;", "•");
        content = content.replace("&frac12;", "½");
        content = content.replace("&laquo;", "«");
        content = content.replace("&para;", "¶");
        content = content.replace("&uml;", "¨");
        content = content.replace("&cedil;", "¸");
        content = content.replace("&frac14;", "¼");
        content = content.replace("&lt;", "<");
        content = content.replace("&plusmn;", "±");
        content = content.replace("&times;", "×");
        content = content.replace("&cent;", "¢");
        content = content.replace("&frac34;", "¾");
        content = content.replace("&macr;", "¯");
        content = content.replace("&trade;", "™");
        content = content.replace("&euro;", "€");
        content = content.replace("&pound;", "£");
        content = content.replace("&yen;", "¥");
        content = content.replace("&bdquo;", "„");
        content = content.replace("&hellip;", "…");
        content = content.replace("&middot;", "·");
        content = content.replace("&rsaquo;", "›");
        content = content.replace("&ordf;", "ª");
        content = content.replace("&circ;", "ˆ");
        content = content.replace("&ldquo;", "“");
        content = content.replace("&mdash;", "—");
        content = content.replace("&rsquo;", "’");
        content = content.replace("&ordm;", "º");
        content = content.replace("&dagger;", "†");
        content = content.replace("&lsaquo;", "‹");
        content = content.replace("&ndash;", "–");
        content = content.replace("&sbquo;", "‚");
        content = content.replace("&rdquo;", "”");
        content = content.replace("&Dagger;", "‡");
        content = content.replace("&lsquo;", "‘");
        content = content.replace("&permil;", "‰");
        content = content.replace("&tilde;", "˜");
        content = content.replace("&asymp;", "≈");
        content = content.replace("&frasl;", " ⁄");
        content = content.replace("&larr;", "←");
        content = content.replace("&part;", "∂");
        content = content.replace("&spades;", "♠");
        content = content.replace("&cap;", "∩");
        content = content.replace("&ge;", "≥");
        content = content.replace("&le;", "≤");
        content = content.replace("&Prime;", "″");
        content = content.replace("&sum;", "∑");
        content = content.replace("&clubs;", "♣");
        content = content.replace("&harr;", "↔");
        content = content.replace("&loz;", "◊");
        content = content.replace("&prime;", "′");
        content = content.replace("&uarr;", "↑");
        content = content.replace("&darr;", "↓");
        content = content.replace("&hearts;", "♥");
        content = content.replace("&minus;", "−");
        content = content.replace("&prod;", "∏");
        content = content.replace("&diams;", "♦");
        content = content.replace("&infin;", "∞");
        content = content.replace("&ne;", "≠");
        content = content.replace("&radic;", "√");
        content = content.replace("&equiv;", "≡");
        content = content.replace("&int;", "∫");
        content = content.replace("&oline;", "‾");
        content = content.replace("&rarr;", "→");
        content = content.replace("&alpha;", "α");
        content = content.replace("&eta;", "η");
        content = content.replace("&mu;", "μ");
        content = content.replace("&pi;", "π");
        content = content.replace("&theta;", "θ");
        content = content.replace("&beta;", "β");
        content = content.replace("&gamma;", "γ");
        content = content.replace("&nu;", "ν");
        content = content.replace("&psi;", "ψ");
        content = content.replace("&upsilon;", "υ");
        content = content.replace("&chi;", "χ");
        content = content.replace("&iota;", "ι");
        content = content.replace("&omega;", "ω");
        content = content.replace("&rho;", "ρ");
        content = content.replace("&xi;", "ξ");
        content = content.replace("&delta;", "δ");
        content = content.replace("&kappa;", "κ");
        content = content.replace("&omicron;", "ο");
        content = content.replace("&sigma;", "σ");
        content = content.replace("&zeta;", "ζ");
        content = content.replace("&epsilon;", "ε");
        content = content.replace("&lambda;", "λ");
        content = content.replace("&phi;", "φ");
        content = content.replace("&tau;", "τ");
        content = content.replace("&Alpha;", "Α");
        content = content.replace("&Eta;", "Η");
        content = content.replace("&Mu;", "Μ");
        content = content.replace("&Pi;", "Π");
        content = content.replace("&Theta;", "Θ");
        content = content.replace("&Beta;", "Β");
        content = content.replace("&Gamma;", "Γ");
        content = content.replace("&Nu;", "Ν");
        content = content.replace("&Psi;", "Ψ");
        content = content.replace("&Upsilon;", "Υ");
        content = content.replace("&Chi;", "Χ");
        content = content.replace("&Iota;", "Ι");
        content = content.replace("&Omega;", "Ω");
        content = content.replace("&Rho;", "Ρ");
        content = content.replace("&Xi;", "Ξ");
        content = content.replace("&Delta;", "Δ");
        content = content.replace("&Kappa;", "Κ");
        content = content.replace("&Omicron;", "Ο");
        content = content.replace("&Sigma;", "Σ");
        content = content.replace("&Zeta;", "Ζ");
        content = content.replace("&Epsilon;", "Ε");
        content = content.replace("&Lambda;", "Λ");
        content = content.replace("&Phi;", "Φ");
        content = content.replace("&Tau;", "Τ");
        content = content.replace("&sigmaf;", "ς");
        return content;
    }

    public static String filterHtml(String str) {
        return str.replaceAll("<([^>]*)>", "");
    }

    public static Map<String, Object> cutFonts(String basePath, List<String> fontFiles, String content, String outPath, String fontFileName, boolean entry)
    {
        Map map = new HashMap();
        String destTTFFilePaths = "";
        String destEOTFilePaths = "";
        String destWOFFFilePaths = "";
        Map fontMaps = new HashMap();
        String contentHtml = null;
        try {
            long s1 = System.currentTimeMillis();

            content = hex2Str(StringEscapeUtils.unescapeHtml4(content));
            long s2 = System.currentTimeMillis();
            //log.info(new StringBuilder().append("---反转Html耗时：").append(s2 - s1).append("(ss)").toString());
            String s = filterHtml(content);
            long s3 = System.currentTimeMillis();
            //log.info(new StringBuilder().append("---去除Html标签耗时：").append(s3 - s2).append("(ss)").toString());
            Map indexMap = generateDistinctMap(s, entry);
            long s4 = System.currentTimeMillis();
            //log.info(new StringBuilder().append("---获取编号耗时：").append(s4 - s3).append("(ss)").toString());
            //String names = fontFileName;
            int idx = 0;
            for (String fontFile : fontFiles) {
                String baseName = FilenameUtils.getBaseName(fontFile);
            	String fontname = baseName.substring(baseName.length()-2,baseName.length());
            	String fontName = "HUANGHE_"+fontname+"_"+fontFileName;
                
            	idx ++;
                File f = new File(new StringBuilder().append(basePath).append("/").append(fontFile).toString());

                FontsService sfs = FontsService.getInstance(f);
                Map fontMap = sfs.deleteNotExistsFont(indexMap==null?new HashMap<Integer, Integer>():indexMap);
                if (fontMap.size() > 0) {
                    String destTTFFilePath = new StringBuilder().append(outPath).append("/").append(fontName).append(".ttf").toString();
                    String destEOTFilePath = new StringBuilder().append(outPath).append("/").append(fontName).append(".eot").toString();
                    String destWOFFFilePath = new StringBuilder().append(outPath).append("/").append(fontName).append(".woff").toString();
                    sfs.subsetFontFile(destTTFFilePath, content, indexMap);
                    fontMaps.putAll(fontMap);
                    if (!StringUtil.isBlank(destTTFFilePaths))
                        destTTFFilePaths = new StringBuilder().append(destTTFFilePaths).append(";").toString();
                    destTTFFilePaths = new StringBuilder().append(destTTFFilePaths).append(destTTFFilePath).toString();
                    if (!StringUtil.isBlank(destEOTFilePaths))
                        destEOTFilePaths = new StringBuilder().append(destEOTFilePaths).append(";").toString();
                    destEOTFilePaths = new StringBuilder().append(destEOTFilePaths).append(destEOTFilePath).toString();
                    if (!StringUtil.isBlank(destWOFFFilePaths))
                        destWOFFFilePaths = new StringBuilder().append(destWOFFFilePaths).append(";").toString();
                    destWOFFFilePaths = new StringBuilder().append(destWOFFFilePaths).append(destWOFFFilePath).toString();
                }
            }
            long s5 = System.currentTimeMillis();
            //log.info(new StringBuilder().append("---生成字库耗时：").append(s5 - s4).append("(ss)").toString());
            contentHtml = encode(content, fontMaps);
            map.put("content", contentHtml);
            map.put("destTTFFilePaths", destTTFFilePaths);
            map.put("destEOTFilePaths", destEOTFilePaths);
            map.put("destWOFFFilePaths", destWOFFFilePaths);
            map.put("codeMap", indexMap);
            long s6 = System.currentTimeMillis();
            log.info(new StringBuilder().append("---生成后转换耗时：").append(s6 - s5).append("(ss)").toString());
        } catch (Exception e) {
            log.error("error,",e);
        }
        return map;
    }

    private static Map<Integer, Integer> buildDecodeMap(String mapStr) {
        Map map = new HashMap();
        mapStr = mapStr.substring(1, mapStr.length() - 1);
        mapStr = mapStr.replace(" ", "");
        String[] codeArray = mapStr.split(",");
        for (String code : codeArray)
        {
            String[] s = code.split("=");
            Integer k = Integer.valueOf(s[1]);
            Integer v = Integer.valueOf(s[0]);
            if (map.get(k) != null) {
                log.info(new StringBuilder().append("==存在重复key=").append(k).append("&&").append(map.get(k)).toString());
            }
            map.put(k, v);
        }
        return map;
    }

    private static Map<Integer, Integer> buildUnDecodeMap(String mapStr)
    {
        Map map = new HashMap();
        mapStr = mapStr.substring(1, mapStr.length() - 1);
        mapStr = mapStr.replace(" ", "");
        String[] codeArray = mapStr.split(",");
        for (String code : codeArray)
        {
            map.put(Integer.valueOf(Integer.parseInt(code.split("=")[0])), Integer.valueOf(Integer.parseInt(code.split("=")[1])));
        }
        return map;
    }

    public static String decode(String source, String ecodeoCompare)
    {
        Map decodeMap = buildDecodeMap(ecodeoCompare);
        return decode(source, decodeMap);
    }

    public static String decode2(String source, String ecodeoCompare)
    {
        Map decodeMap = buildUnDecodeMap(ecodeoCompare);
        return decode2(source, decodeMap);
    }

    public static String decode2(String source, Map<Integer, Integer> map)
    {
        for (Entry<Integer,Integer> entry : map.entrySet())
        {
            StringBuffer sb = new StringBuffer();
            sb.appendCodePoint((entry.getValue()).intValue());
            StringBuffer sr = new StringBuffer();
            sr.appendCodePoint(entry.getKey().intValue());
            source = source.replaceAll(new StringBuilder().append("(<gg [^>]+)>").append(sr.toString()).append("</gg>").toString(), new StringBuilder().append("$1>").append(sb.toString()).append("</gg>").toString());
        }

        return source;
    }

    public static String decode(String source, Map<Integer, Integer> map)
    {
        for (Entry<Integer,Integer> entry : map.entrySet())
        {
            StringBuffer sb = new StringBuffer();
            sb.appendCodePoint((entry.getValue()).intValue());
            StringBuffer sr = new StringBuffer();
            sr.appendCodePoint(entry.getKey().intValue());
            source = source.replace(sr.toString(), sb.toString());
        }

        return source;
    }
//    public static String txt2String(File file) {
//        StringBuilder result = new StringBuilder();
//        try {
//            BufferedReader br = new BufferedReader(new FileReader(file));
//            String s = null;
//            while ((s = br.readLine()) != null) {
//                result.append(new StringBuilder().append(System.lineSeparator()).append(s).toString());
//            }
//            br.close();
//        } catch (Exception e) {
//        	log.error("error,",e);
//        }
//        return result.toString();
//    }

    public static void main(String[] args) {
//    String m = "{32769=20696, 22530=38868, 38915=27766, 38917=40506, 32773=33766, 26629=32870, 22533=24836, 36870=27787, 38918=37533, 20489=30365, 32780=29765, 36880=24243, 20498=25455, 14356=32210, 36885=34614, 38936=32718, 20505=35187, 36890=36452, 24605=36233, 36896=36666, 26657=23006, 22561=24254, 36898=22641, 36899=29251, 20515=34130, 24613=22417, 20522=37709, 20523=20224, 38957=32519, 22575=31573, 36914=22076, 32819=25572, 30772=20574, 26681=37729, 36926=20709, 26690=30289, 36930=30107, 34886=26771, 20551=26568, 36935=25821, 36938=35001, 34892=29117, 36941=26800, 34893=20093, 36942=32589, 24656=22623, 36944=24156, 36947=39361, 34899=38828, 26707=20058, 36948=26944, 38996=33425, 32854=30531, 34903=37758, 32858=28828, 22622=26251, 32862=24661, 39006=38174, 36960=29615, 34913=27744, 34914=26029, 36963=25381, 34915=29423, 20581=39591, 39015=28628, 34920=21669, 28779=23829, 24684=33663, 991331=27722, 32880=34909, 36983=35264, 32887=20351, 36984=38089, 36986=26623, 28797=39600, 36991=22699, 26753=27549, 22659=32410, 36996=34708, 32901=34260, 24713=27998, 32910=32956, 37009=39022, 22675=25876, 20629=36688, 26781=27203, 22686=25428, 24735=40901, 37024=27867, 34980=28340, 39080=37625, 34987=33036, 37039=28785, 37041=25371, 20659=30622, 37048=23507, 30906=28974, 24764=36109, 30910=26059, 22725=29668, 37063=28913, 32972=34361, 37070=39353, 20687=23497, 30929=33719, 35029=35295, 26839=24810, 26840=34652, 35036=36370, 22750=33549, 39135=36933, 26847=32481, 24800=38064, 37089=21483, 24801=29772, 32993=38153, 20711=38780, 30952=39892, 37096=23291, 22763=30269, 39154=19970, 37109=25761, 22778=27162, 37117=28914, 33021=40924, 22781=29388, 20736=39601, 20740=37453, 35079=36698, 28937=25453, 37129=23573, 24845=38171, 22806=24607, 39192=37096, 22810=36795, 37152=24711, 28961=25248, 22820=26205, 22823=34809, 37159=40545, 39208=33606, 22825=39397, 22826=35956, 22827=28360, 37165=25187, 22830=28177, 33073=39068, 22833=32601, 28982=34143, 24887=27811, 22839=28624, 31038=30553, 31040=30614, 20803=30144, 35140=29332, 20804=29315, 20805=39550, 20806=26573, 37190=36965, 20808=34658, 24904=33913, 37192=24635, 20809=37277, 22857=29906, 26954=40766, 37195=23335, 22863=36123, 39249=23219, 37202=29624, 39250=27963, 20818=37306, 31062=35879, 26970=30142, 31070=31146, 31072=21503, 22882=37721, 20837=34773, 22885=36697, 31077=31745, 20840=36525, 20843=37422, 20844=29844, 29036=33585, 20845=34000, 31085=40515, 26989=31444, 22894=26072, 24944=21570, 35186=29093, 22899=31404, 22900=25037, 33140=39467, 20853=39542, 20854=32490, 24950=36507, 20855=36250, 37240=40203, 20856=32598, 20860=26622, 22909=24577, 35199=22157, 31105=40407, 35201=39964, 22914=31833, 20869=33698, 35206=33776, 27014=29477, 35211=36790, 33167=21567, 39318=29351, 35222=30213, 20896=26390, 20898=34047, 31142=32749, 39340=31963, 39342=29156, 31150=40764, 29105=32818, 37300=37805, 20918=28262, 31161=34573, 35258=40542, 35261=39224, 31166=25819, 35264=23738, 31169=24633, 25033=39656, 22987=28802, 31179=34332, 37324=21236, 37325=39806, 37329=22751, 29138=26281, 39381=32175, 23002=36675, 29151=39939, 20961=37525, 33251=40138, 31206=39041, 33256=22515, 33258=22520, 33263=36796, 39409=38341, 33267=23489, 33268=40483, 20982=33350, 25079=38613, 25080=32756, 33274=24812, 20986=31844, 31227=34621, 33279=34531, 35328=26342, 27138=23482, 33284=37556, 20998=20107, 33287=27405, 33288=27234, 25096=22512, 33289=36679, 33290=29350, 27146=36063, 31243=25652, 25102=24001, 25104=20391, 25105=36781, 27155=22947, 37396=23739, 25110=25820, 27159=40939, 21015=35260, 35352=33983, 25114=34354, 33308=35699, 21021=34622, 25119=25693, 21024=38609, 21033=32279, 27178=33423, 21035=20603, 21040=23122, 25136=38962, 35377=30364, 31281=21822, 29234=20169, 25138=30439, 21046=25785, 29238=26533, 25143=40192, 27193=38906, 21050=22148, 21051=39426, 31293=38432, 25151=34166, 25152=24369, 23105=27367, 21063=40579, 29256=37077, 21066=34387, 27211=26348, 21069=21068, 31309=28382, 39509=30099, 29275=23595, 39515=32011, 29287=33279, 35433=30454, 29289=27808, 39530=33348, 35438=21530, 33391=22894, 21106=27878, 35443=31240, 21109=23569, 25206=37043, 29309=27626, 31359=37505, 25215=25702, 27264=27750, 37509=31055, 35461=38751, 35463=21079, 21129=26706, 25233=37602, 25239=24596, 25240=32906, 31384=23202, 21147=23412, 35486=23533, 21151=28500, 21152=32394, 27298=30090, 35492=33575, 39592=38191, 35500=27886, 29356=33467, 31406=25902, 25269=35529, 21187=31606, 25286=21771, 31434=27966, 25290=31486, 31435=20189, 35531=28903, 33489=25934, 21202=35459, 25298=22630, 39636=31143, 39640=40508, 21208=26186, 21209=23087, 21213=31672, 27359=30884, 31456=39778, 21218=20074, 25324=23315, 35569=32223, 21235=38796, 31478=33338, 35576=38122, 33531=40360, 25345=34610, 35586=34565, 33538=38451, 25351=20613, 25353=32801, 21271=40633, 29469=25605, 21277=40842, 21280=36344, 27425=24520, 37666=36025, 27427=30952, 31532=34059, 39725=23180, 23344=30215, 27442=34620, 37682=40490, 29494=32584, 35641=29166, 39740=32024, 21312=35962, 21313=26615, 21315=33858, 33606=22871, 21320=20414, 33609=20662, 31561=39171, 27465=21150, 35657=25763, 21322=22028, 27468=35509, 35660=28630, 25421=31414, 39759=36928, 23376=40268, 21330=38973, 21331=25506, 21335=29560, 23383=22325, 35672=26259, 23384=33138, 21338=24692, 39770=21806, 23389=29267, 23391=22221, 27490=36849, 27491=28349, 27492=34127, 27493=30374, 27494=35310, 21350=24778, 23403=31195, 21356=33222, 39791=24765, 27506=26264, 21367=29588, 35703=34626, 27511=34546, 27512=35430, 29560=34883, 27515=25467, 29563=34170, 21373=22126, 37758=38970, 21375=34258, 23427=40459, 29572=28588, 23429=40210, 27526=38585, 23431=40563, 23432=36682, 23433=23491, 29577=21795, 33674=36331, 27530=22216, 29579=31671, 23435=27613, 31627=29003, 25484=39499, 23436=30102, 23439=32620, 21396=20676, 23447=25957, 23448=37926, 23450=32542, 23452=31516, 21407=29107, 31649=30103, 23458=34740, 23459=25040, 23460=26762, 27556=35560, 25509=22780, 23467=25236, 37804=20458, 21421=26476, 37806=26182, 31665=34323, 21426=24781, 23478=28277, 27578=38520, 21435=25227, 33725=40470, 27583=32928, 23487=34200, 31680=37493, 27585=23851, 21443=25704, 31684=31944, 23493=30066, 21448=40613, 31689=23975, 21450=26959, 23500=29854, 21453=30731, 29645=22676, 144332=37592, 27599=36228, 25552=20771, 27604=39219, 21462=24509, 25558=26558, 21463=33779, 27611=26671, 21467=34843, 21475=24660, 21476=36812, 23524=29346, 23526=30184, 23527=32650, 39912=31570, 23532=23717, 21487=38211, 33775=32941, 23536=32239, 21490=22295, 21491=39924, 23542=22654, 21496=28375, 23546=33860, 23553=33988, 29699=23578, 21508=38457, 29702=26865, 33799=31357, 23559=28716, 21512=36021, 23560=27824, 23561=40844, 21513=35537, 33802=23378, 23562=29126, 23563=30024, 21516=26374, 21517=25869, 23565=32874, 21518=33811, 27663=28478, 23567=38210, 23569=28908, 21525=24268, 37912=34752, 21531=27930, 23588=29308, 25636=22893, 21547=27753, 33836=28912, 21549=31930, 23601=32981, 27700=32157, 37941=38597, 35895=24747, 27704=27543, 21560=29406, 23609=28464, 37947=36605, 23614=24955, 27714=40499, 37956=37973, 23621=34625, 35912=25926, 23627=40001, 31821=21240, 35920=20958, 37969=30025, 37970=24514, 33879=26954, 23646=29749, 27744=24867, 23648=27125, 35937=32794, 33891=21180, 27751=25770, 21608=26875, 35946=22277, 23660=28150, 33900=34668, 23663=37363, 29808=24307, 23665=33493, 33909=28758, 27770=27018, 21629=27139, 38015=38983, 27781=35734, 21644=25662, 23696=33161, 33945=28314, 27803=37182, 29853=26917, 35998=22534, 36002=32983, 25764=29184, 25771=34198, 36011=36422, 27822=40208, 23729=28876, 33970=22862, 27827=26120, 23731=34958, 36020=26735, 21688=35688, 23736=29865, 27835=32131, 36027=27699, 27839=35842, 36032=22172, 21696=24537, 36039=26278, 25799=23030, 36040=30824, 21705=39038, 27849=39917, 36042=35795, 25802=40869, 33995=27951, 25810=32259, 27859=39049, 36051=33323, 27861=25482, 25818=27834, 36060=34099, 31966=28475, 29920=40883, 27877=22701, 36070=31126, 27880=30250, 23784=24191, 34028=23164, 27888=35924, 40179=33261, 29940=25282, 32000=23808, 29956=32427, 32006=22707, 23815=21087, 27915=33415, 32013=36560, 21776=30816, 23826=25692, 23828=36910, 29973=23750, 29976=35599, 29978=32181, 32026=20973, 27931=25324, 23835=25127, 29983=27644, 32034=34964, 27941=23397, 29992=25003, 23849=25308, 27946=38271, 27947=25985, 29995=27371, 32047=34894, 25903=37052, 30000=33376, 32048=38579, 30001=37789, 30002=20075, 25910=28558, 30007=35277, 25911=24051, 25913=21107, 25915=24745, 25918=27068, 25919=23090, 27969=30301, 32066=37858, 25925=21900, 21830=25651, 30028=22205, 25933=37895, 21839=29285, 25935=23942, 23893=39227, 30041=38623, 30045=26644, 25950=21446, 30050=25425, 34146=37626, 25955=35237, 36196=39829, 30052=30942, 30053=34117, 36203=28576, 30059=23568, 25964=36479, 34157=25278, 23919=33781, 30064=27982, 32113=39674, 30070=38309, 38263=30366, 23927=39807, 36215=39117, 28023=38998, 25976=25546, 32121=24538, 30079=39472, 38272=35434, 21892=37703, 25991=32961, 28039=29385, 32136=25795, 36234=37992, 38283=35826, 30095=27154, 30097=36668, 38290=20592, 32147=35066, 23955=22638, 38292=21207, 26007=30662, 36249=33872, 34203=20763, 26012=34798, 23970=24214, 38307=27706, 26021=29043, 34216=30429, 36264=39627, 21934=33009, 26031=37651, 26032=36586, 30130=36932, 36275=27233, 26041=20700, 23994=33051, 23995=38346, 38331=32257, 28092=26716, 26044=22753, 36286=28586, 32191=30632, 26049=37104, 24003=38839, 26053=37195, 28102=26338, 26060=40165, 34253=26595, 26063=32295, 24020=27103, 38357=24614, 26071=22566, 38364=36620, 32220=28647, 24029=34463, 36317=33395, 24030=40671, 36321=37860, 26082=27924, 32227=26027, 26085=28708, 24037=29699, 24038=20373, 36328=24103, 34281=38562, 28138=33506, 24043=22354, 36335=31657, 28145=36411, 24050=32283, 28151=39777, 30202=24218, 19968=36477, 19969=40084, 24066=30392, 19971=30628, 28165=25728, 26118=24636, 19976=37375, 32264=25198, 19977=31417, 19978=35114, 19979=27204, 26124=21879, 19981=40061, 26126=28200, 26131=30704, 19988=36290, 19990=40392, 22039=34576, 19992=27883, 24093=25843, 28192=20229, 28193=39016, 32291=38541, 26149=39170, 24101=28762, 20006=35611, 28201=30442, 38442=37487, 24107=40425, 20013=20501, 24109=25866, 26157=22222, 28205=25044, 34349=38785, 26159=36916, 36400=25703, 24118=40461, 24120=26791, 38459=27202, 32317=24433, 38463=20941, 26178=29833, 38466=28654, 20035=38342, 38468=27418, 20037=35121, 26185=26209, 20043=31112, 38476=22891, 38477=33089, 20046=40564, 34382=26763, 34389=27333, 32341=35547, 28246=36242, 38491=21751, 20061=33154, 38493=39657, 20063=22504, 34399=32747, 32353=27238, 38500=22605, 28267=29673, 26223=25287, 38512=31163, 22130=31300, 24179=21729, 38515=30570, 24180=28960, 26228=35964, 38517=28840, 40565=39302, 24182=31636, 38519=39646, 30331=35846, 32380=31453, 30332=22908, 24188=26021, 38525=22744, 30333=28793, 24189=34564, 40573=36460, 30334=40296, 20094=20579, 24190=33324, 40575=20508, 20098=33920, 38533=39188, 38534=25797, 30342=34496, 30343=25347, 20105=33125, 28297=32298, 38539=28327, 20107=29215, 20108=34891, 20110=29679, 24207=35004, 20113=27001, 26257=40223, 40595=39876, 20116=33174, 32404=31656, 20117=33703, 38551=24637, 20121=23012, 24220=20296, 38556=34869, 28317=22289, 20126=38759, 20129=36301, 20133=38205, 20134=33120, 38570=27737, 24235=38061, 20140=40710, 24237=28095, 20141=31707, 30382=32250, 38577=30282, 38580=30877, 20150=35751, 24246=28024, 24247=34257, 38584=31786, 20154=34335, 28348=22492, 38592=36383, 20161=32517, 40644=29236, 38596=21542, 28357=36122, 38597=26709, 38598=28032, 26310=32249, 20170=24923, 36554=35420, 30410=20751, 28363=31921, 36556=21764, 38605=33483, 20173=35677, 36557=26195, 40653=37581, 40657=32721, 28369=28168, 38614=20893, 30423=24673, 22232=26536, 20185=23926, 22235=33228, 38622=26087, 22238=34699, 22240=38709, 24290=36545, 38626=30111, 30435=30311, 20195=33897, 24291=27497, 38627=32785, 20196=23889, 30436=20243, 20197=20769, 38632=34494, 30441=39459, 26352=30777, 20210=28055, 38642=38748, 26354=29658, 30452=20571, 26356=25614, 24310=28553, 38647=36980, 30456=34877, 26360=33110, 36601=26507, 26361=38723, 22266=30375, 24314=28067, 28411=22758, 26366=28563, 30465=35676, 26371=27779, 26376=20582, 26377=26934, 36617=29913, 24330=40079, 22283=39075, 26381=23885, 22285=24071, 40718=29598, 28431=38596, 20240=32313, 24337=29544, 40723=33587, 36628=32556, 26388=29405, 24341=29297, 22294=28973, 26395=27545, 22300=28303, 26397=32345, 22303=21228, 30495=31787, 24351=20559, 28450=40320, 22312=37299, 26408=36083, 26410=26330, 26411=22716, 26412=38406, 22320=30216, 26417=23903, 38706=26362, 24373=21124, 38712=38134, 28472=31973, 32570=39902, 38717=40719, 36671=27874, 22338=37548, 20294=34167, 22343=20412, 38728=24816, 36681=20326, 22346=21056, 40778=38953, 24394=27650, 20301=26075, 20302=25416, 26446=26096, 22352=20194, 38738=37995, 20309=27697, 38742=25139, 28504=36147, 20314=22203, 26460=20048, 20316=20041, 38750=22461, 22369=37303, 24418=29515, 38754=39727, 30563=23800, 22372=33977, 38761=31910, 22379=29847, 32622=34777, 26479=28219, 26481=27384, 20341=39460, 32631=23309, 24441=39480, 20351=27077, 30591=25554, 24448=38207, 24449=29013, 20358=22615, 20363=26817, 24460=36221, 32652=37105, 26508=31923, 40845=33873, 20365=24649, 32654=21473, 24464=36414, 26512=38388, 24466=36771, 20372=35275, 24471=22001, 26519=28008, 24473=36516, 36763=28209, 20381=30048, 26525=30522, 24478=29891, 36767=34751, 24481=21521, 32675=39100, 28580=34492, 24489=35028, 32681=40928, 24494=38892, 20399=38403, 24501=36262, 24503=25647, 26552=22776, 32701=37985, 24509=31235, 20415=38299, 32705=24343, 24517=32342, 32714=33593, 38859=37807, 22478=22174, 26575=33445, 36817=30359, 26579=39655, 38867=24394, 32724=23258, 24535=26333, 20439=29059, 24536=21287, 20445=20939, 32735=33234, 20447=20604, 22495=34837, 32736=39926, 24544=39238, 20449=40449, 30691=34456, 30693=21324, 32742=25400, 30701=25660, 20462=38229, 20463=29762, 36848=27508, 20465=33214, 26609=31232, 32755=37744, 30707=23449, 26611=28523, 36852=33921, 24565=29944, 22522=30434, 36861=40697, 22527=29690}";
//    Map map = buildDecodeMap(m);
//    for (Map.Entry entry : map.entrySet()) {
//      int t = 0;
//      if (map.get(entry.getKey()) != null) {
//        t++;
//      }
//      if (map.containsValue(entry.getKey())) {
//        t++;
//      }
//      if (t > 1) {
//        log.info("1111111111111111");
//      }
//
//    }
//
//    String str = "網韾讼";
//
//    String maps = "   34986=26216, 32290=28768, 23621=21545, 26612=23909, 23609=33690, 35036=32333, 30410=20277, 35032=31875, 30408=21930, 23601=28492, 32862=38126, 35009=23697, 32856=30394, 32858=21587, 30431=34009, 32327=26946, 35023=40176, 30428=29655, 32854=27989, 23588=37346, 30427=22875, 32321=27463, 21688=34933, 32879=22249, 30433=21544, 23578=21386, 21644=35678, 30446=22511, 37202=26349, 23569=24388, 23565=25058, 30452=40525, 32893=31717, 23567=35342, 23560=30653, 32363=23017, 28507=21275, 30450=33809, 23562=36466, 23556=39626, 23559=34786, 23553=35129, 32882=36053, 30456=27827, 21608=31654, 28580=26378, 26417=35013, 34837=27162, 26410=37205, 26411=39696, 34825=35016, 26408=30198, 32918=22292, 21628=32231, 26412=25389, 26413=40537, 21629=22191, 32396=30566, 39178=37863, 21578=35409, 26397=24502, 26399=35560, 26389=20746, 26376=27799, 26377=33328, 39192=20433, 26381=38091, 34849=27800, 26371=20183, 23731=31144, 34907=37372, 32974=39000, 26481=33922, 34899=38841, 34892=29660, 21566=30431, 34893=35040, 28657=30937, 34886=30434, 21556=36292, 21517=39615, 21516=29094, 21519=31949, 32997=27639, 32996=26004, 21513=38465, 23696=39375, 28608=27348, 21512=32912, 37329=25508, 37328=28405, 21508=26147, 26454=38064, 21505=21658, 26448=36127, 30332=31441, 30333=32962, 30334=32611, 26446=22304, 34920=21026, 21531=31673, 37326=38820, 37327=32518, 21525=38486, 37324=37870, 37325=24181, 33021=38100, 28639=30571, 34915=37287, 21521=20708, 37323=39435, 39249=22550, 32047=26672, 30133=22115, 24394=22850, 30142=40942, 32034=24415, 27665=33881, 27663=39905, 37396=32938, 22478=38196, 32013=20846, 20061=31067, 22519=27548, 20063=34000, 27710=39724, 30097=31430, 35712=24215, 32004=29343, 27700=38709, 35726=24434, 22521=35008, 35722=21132, 32000=26402, 25771=30061, 24418=36105, 32002=23760, 22522=20341, 24444=31873, 30086=24370, 20046=28750, 20047=33764, 35730=28667, 35731=23605, 25778=22185, 37433=39745, 20043=23727, 32027=22993, 20037=33076, 30095=37073, 24432=31971, 33538=20778, 33539=39533, 20035=30157, 27683=30279, 24330=37726, 39635=23142, 20025=32595, 27743=24920, 39636=30022, 27741=38534, 25802=40618, 39640=32904, 30202=35823, 24321=25292, 37445=24365, 32102=21585, 25805=29619, 22402=37364, 24347=22433, 24351=31294, 37470=38354, 20013=32900, 25818=28616, 24338=28979, 27714=28598, 24339=20349, 32113=23163, 32118=27277, 20006=28127, 24343=36610, 24341=30762, 19993=34806, 19992=27857, 19985=33068, 33618=31784, 30171=21105, 32066=37320, 19988=27059, 19990=28920, 37476=32847, 19976=38751, 19977=25791, 24378=28148, 19978=31361, 33609=21672, 22435=23100, 19979=20130, 30149=32754, 19981=26663, 27744=35325, 19968=25792, 32080=27709, 19969=39898, 24369=31759, 33603=26260, 19971=26203, 33606=26969, 24373=23821, 27798=30871, 24517=27117, 25613=27188, 20210=33538, 24515=24950, 20208=26225, 32173=21791, 30002=36524, 30003=30050, 32171=36162, 20219=23272, 30001=34153, 24535=33463, 20197=23447, 20196=28116, 20195=20785, 29995=29314, 27779=33027, 35641=23380, 32177=21444, 29992=27963, 22343=29790, 37532=32994, 29989=37416, 37531=37937, 33707=24707, 27785=34873, 24536=38329, 20182=38961, 29983=37133, 33682=39950, 24544=40770, 29978=21566, 27827=32411, 27839=30298, 35586=28304, 35584=27256, 27835=28961, 20167=32096, 20161=26159, 35610=38917, 32147=36912, 35607=34965, 24573=25218, 20173=37002, 33674=30135, 35600=22709, 20170=30467, 20147=39622, 24448=33756, 24449=28921, 22303=31868, 24453=38476, 27861=29610, 20154=26343, 30064=21692, 30070=23076, 39509=31196, 24460=33911, 22283=28624, 20129=37407, 24471=27688, 20134=39213, 22285=29652, 20132=35978, 35696=31435, 30050=33754, 27849=21231, 24478=31276, 30053=34034, 20141=40956, 27888=30314, 20113=38979, 24481=25183, 20114=27312, 35657=26308, 20116=37039, 30044=35913, 20117=37831, 24487=33257, 22320=31720, 24489=38016, 30036=34994, 24494=24384, 20127=25617, 22312=40498, 27875=20866, 20098=34531, 30028=39980, 24503=25681, 20104=22398, 20107=31096, 20108=30929, 33324=40551, 20335=38109, 29863=26230, 33322=36020, 35504=33509, 35519=31887, 35516=40243, 22218=34701, 35494=22256, 25991=29448, 20351=30765, 35492=39054, 39825=36301, 33337=28020, 25986=31458, 35488=23584, 35500=40344, 22235=37843, 22234=22151, 33293=39863, 26039=31444, 20301=25730, 33288=30623, 27946=27723, 33289=23068, 22240=25209, 26032=40419, 33290=23456, 35475=36484, 20294=31857, 24180=39235, 26044=22516, 35486=27472, 26045=26227, 33287=35758, 27941=39463, 24178=28585, 35480=29105, 24179=25722, 37682=27016, 26041=23873, 35461=28294, 33308=23739, 33311=22556, 37679=32902, 20316=21189, 20314=40211, 26031=34255, 20309=40482, 26028=29033, 26027=27697, 20304=31269, 37666=27949, 39872=26047, 35569=29115, 24093=31765, 33391=25869, 29926=21238, 20271=37951, 27969=31905, 35576=35711, 24086=36751, 35559=24001, 24076=20546, 20284=40913, 35563=29278, 24067=32955, 24066=29132, 22170=29337, 26063=33144, 20234=20739, 24120=28242, 28009=34358, 20239=25175, 35542=29269, 20237=38371, 24118=26334, 24107=38570, 22195=39496, 26082=38661, 26086=31955, 26085=30175, 35531=38811, 20241=20982, 20240=33737, 28023=30324, 24101=37019, 25885=28391, 20449=20033, 35387=27438, 33454=22100, 24287=34100, 20463=40603, 31934=24348, 20462=22999, 28041=35609, 28040=31466, 28053=27923, 20465=37075, 31906=28168, 29748=40375, 25919=40627, 25918=36797, 22123=29768, 20419=34703, 25915=29649, 35352=37815, 25913=28956, 35355=37649, 22120=23865, 31903=35184, 25910=40690, 29702=28126, 35347=36523, 39740=23250, 25903=32603, 20439=36854, 35336=32970, 24291=23992, 20447=25206, 28092=20769, 24300=23877, 20445=33745, 35328=38969, 37801=30104, 28088=32521, 25945=21130, 22025=36153, 24213=28348, 39759=22445, 33510=23062, 33509=21260, 35443=31790, 25937=29604, 31995=39002, 24218=40912, 25943=30455, 20399=34907, 25942=21497, 31998=37350, 35433=29856, 33521=25259, 20405=39207, 25935=35294, 35430=38169, 25925=22221, 20415=37314, 22039=35492, 24207=27856, 25976=37780, 24247=27487, 24246=31594, 20358=35400, 39791=25604, 28139=20461, 20363=35886, 35408=34504, 28142=35169, 25972=37411, 33489=39928, 24230=24327, 28151=36899, 25964=31617, 28149=28514, 25954=28660, 20379=33716, 25955=33227, 24235=27428, 29781=31577, 33503=21637, 20381=23617, 25324=32570, 22823=34841, 22829=24426, 22825=27297, 31561=23502, 36290=31216, 22827=22889, 22826=25898, 20908=31848, 31574=40882, 36317=34719, 22839=32784, 22833=39835, 36310=29316, 20896=22405, 25329=27815, 22799=30077, 22806=35255, 20876=28010, 20877=21453, 22805=39073, 25304=38260, 25300=21905, 22812=34022, 20869=21615, 22810=31277, 27211=31124, 36234=40286, 20986=31744, 22882=23811, 27193=23907, 22890=30522, 20982=36276, 29577=25245, 22899=30399, 29579=27527, 36249=28085, 22900=30233, 37941=31918, 25265=24284, 20961=21229, 37944=24455, 29572=34306, 22909=28166, 31518=36336, 29575=26949, 25226=25915, 22852=37418, 22857=20697, 22856=21383, 31532=29927, 27138=37235, 34081=32178, 25240=21914, 37908=25536, 22868=36592, 36275=24820, 34269=25121, 31689=23222, 25197=40410, 31687=32704, 31680=23009, 34255=31387, 25206=36446, 22971=31449, 25215=37548, 36196=23848, 36198=34688, 29554=24823, 25152=20202, 25165=36283, 22914=30189, 25163=36903, 29563=37131, 29560=27523, 36215=34483, 36208=28318, 20736=39678, 27331=35019, 29544=25488, 20853=25264, 20854=29801, 20855=32643, 36106=21100, 29467=40171, 20860=38357, 25134=31664, 36109=20673, 25136=29015, 20837=23292, 36118=30142, 25143=32244, 20841=20185, 20840=20439, 20843=24148, 27298=35305, 20845=40269, 31637=21479, 20844=35772, 20818=40265, 22987=27411, 29494=20695, 31648=37931, 31649=40150, 20803=33148, 34216=35124, 25105=28673, 23001=22774, 25104=34427, 20806=36073, 25110=22069, 20804=22178, 20811=30510, 22995=33294, 22993=26220, 20809=34037, 27264=20975, 20808=36404, 25119=24253, 34214=35443, 22996=28423, 36060=29982, 27494=25500, 27492=22961, 36062=27913, 38263=22983, 22581=36902, 27493=35302, 27490=39568, 27491=25562, 22577=21966, 29380=25513, 29378=31834, 33865=23535, 27511=21195, 31302=21858, 20670=33718, 33879=27209, 27506=24055, 36040=28072, 36042=29311, 31296=20298, 20663=29580, 22575=21803, 31310=24371, 36039=37035, 20659=37186, 27515=21833, 20658=38186, 33883=22109, 27512=36120, 29417=32025, 27468=23977, 20613=21370, 33900=21165, 22530=31564, 36070=30271, 36068=20173, 36066=24647, 31339=29562, 20626=32544, 27425=39539, 35998=38795, 22645=40610, 40353=37540, 33802=31019, 22649=22988, 33804=29017, 20731=32903, 27442=25511, 35980=40794, 22635=22052, 31243=30757, 25509=36155, 31245=40185, 31281=39759, 36024=25702, 31287=22585, 20687=32914, 27402=24358, 31292=30088, 36022=37615, 31293=27718, 36023=24161, 20677=34803, 33836=24013, 36020=32321, 29351=24226, 25480=29027, 20698=38804, 25484=32176, 36002=21133, 36000=21288, 36007=22318, 20693=38197, 20694=20735, 31278=39666, 25463=21480, 29255=29623, 25462=24764, 33995=37478, 20523=39275, 22707=31045, 20522=37409, 20520=27244, 31434=39215, 31435=21064, 20540=24481, 31429=22473, 29275=20576, 29273=40694, 27597=32239, 29287=25559, 27599=40199, 22686=35934, 20493=39826, 38356=26741, 27585=26090, 29289=23561, 20489=40625, 22675=23036, 20498=34759, 29298=19999, 35937=32898, 27604=23530, 31456=31282, 22659=24790, 38329=27818, 25406=25890, 27578=32297, 22763=34858, 38317=21495, 20596=23199, 40251=20568, 38307=27882, 25391=31236, 27575=21610, 27573=30833, 20547=27927, 20551=32456, 22750=24152, 38291=20890, 31411=40617, 27523=33964, 29229=21641, 27544=24486, 38283=20104, 25345=33189, 29234=22207, 27547=23781, 38286=21847, 29237=22500, 31406=23111, 27550=37557, 29238=21842, 25351=37905, 33970=30188, 25353=40799, 38272=33142, 29246=40016, 26707=39269, 31072=32710, 29183=33026, 21407=23425, 38468=30253, 31077=37687, 40660=36048, 31085=28690, 24773=37053, 24795=36633, 26688=20223, 23318=24141, 24799=31229, 36861=35994, 40654=22649, 36848=39584, 21380=36983, 31040=34878, 21435=21387, 36814=32439, 38500=24700, 24801=21077, 29141=30558, 38515=29734, 40680=24456, 38512=37718, 38518=34728, 23348=28071, 31062=31714, 38517=36635, 38520=28970, 36817=27808, 21413=32968, 36820=23759, 31070=28438, 38525=37044, 21471=38415, 36781=37679, 21467=24341, 36776=25028, 21465=29279, 21463=20519, 21462=30502, 21460=27160, 23383=32000, 23380=34757, 23381=23166, 21453=40675, 21450=26044, 23376=23569, 21448=35602, 31038=32017, 23391=35232, 23389=34929, 31034=30812, 36784=35977, 21443=37459, 23384=20891, 29087=29713, 26684=39101, 26681=23545, 21491=31307, 23403=35131, 21490=26028, 21484=24841, 36767=36443, 21487=33529, 26657=20163, 29066=31839, 34560=28301, 21476=37899, 34574=33628, 21477=32441, 21478=31299, 40613=32768, 23416=27604, 23418=34853, 24754=35269, 21474=24609, 21475=32170, 23435=35643, 23432=38011, 23433=20498, 21270=33759, 23439=30536, 38605=27543, 21271=39665, 23436=28691, 31206=33796, 38597=33692, 38598=39620, 31227=23567, 38617=40127, 23450=39627, 24656=21770, 23448=30628, 38620=24666, 21253=20156, 23452=38565, 38622=32080, 23447=32364, 38614=21121, 38632=31221, 31179=22123, 23467=36812, 38627=36522, 21305=22829, 31169=36834, 23458=25859, 36681=25445, 23460=25530, 40575=27315, 24685=39605, 23462=25381, 24687=31989, 40573=35429, 38651=33794, 21281=24192, 23481=23577, 26856=26314, 23487=20787, 23472=29698, 23475=23998, 38647=21821, 21295=23553, 23478=32907, 28982=28089, 31150=21087, 21335=27020, 26781=38551, 31146=23467, 21330=31272, 21329=32650, 21340=22312, 21338=21789, 23519=32930, 31166=36786, 38556=24598, 31165=38870, 21315=36847, 24594=31987, 31161=24504, 28961=33382, 21313=29508, 38551=36876, 24605=21824, 31153=31870, 26753=37058, 21320=23411, 31117=30444, 31119=25644, 24615=35687, 21367=33594, 23529=38640, 36611=31370, 21361=30856, 21363=40576, 31108=21863, 23526=31917, 21375=23929, 23527=39872, 24616=37656, 31105=24756, 23521=33624, 23522=39680, 21371=30810, 36617=27115, 36629=29232, 38584=21906, 23541=22944, 28937=26543, 38577=27561, 34407=34349, 38750=25861, 38748=25036, 23041=26449, 25033=32810, 21147=26005, 21151=40438, 26970=36377, 21163=23902, 23094=33999, 25084=21104, 21152=21441, 25079=26453, 34382=23526, 26989=21814, 34384=31718, 28889=20325, 36554=36460, 36557=26075, 34389=20357, 38761=39379, 34399=26268, 34398=33894, 30772=35101, 28846=31696, 21191=29757, 21187=38755, 21213=37893, 36524=24236, 21214=32098, 36523=29590, 21209=22591, 21205=20006, 24962=29495, 34311=32922, 38706=23657, 38717=37738, 21220=20836, 38712=22950, 21218=40191, 21247=26000, 36493=25195, 23142=22546, 24917=30495, 20998=32788, 38867=36458, 40779=32642, 40778=29685, 28779=23710, 21009=21143, 38859=21332, 21015=21542, 24904=22176, 36463=38146, 21021=22802, 28797=32282, 23231=25695, 21035=38980, 21033=26563, 24930=39767, 21043=22274, 21040=40085, 21046=23478, 21050=28904, 21051=34289, 24942=36313, 21063=40597, 36400=29498, 21069=22122, 24863=40702, 24859=26600, 21076=40735, 23244=28852, 21078=27148, 24847=29489, 24840=34150, 23233=37948, 36368=39587, 24895=35217, 38829=40198, 38830=40108, 21109=34919";
//
//    String ss = "越絶卷第三\n越絶吴要傳第四\n吴何以稱人乎〔一〕？夷狄之也〔二〕。憂中邦奈何乎〔三〕？伍子胥父誅於楚，子胥挾弓，身干闔廬〔四〕。闔廬曰：“士之甚〔五〕，勇之甚〔六〕。”將爲之報仇〔七〕。子胥曰：“不可〔八〕，諸侯不爲匹夫報仇〔九〕。臣聞事君猶事父也〔一○〕，虧君之行〔一一〕，報父之仇〔一二〕，不可〔一三〕。”於是止〔一四〕。\n蔡昭公册朝楚〔一五〕，被羔裘〔一六〕，囊瓦求之〔一七〕，昭公不與〔一八〕。即拘昭公册郢〔一九〕，三年然後歸之〔二○〕。昭公去，至河，用事〔二一〕曰：“天下誰能伐楚乎？寡人願爲前列〔二二〕！”楚聞之〔二三〕，使囊瓦興師伐蔡〔二四〕。昭公聞子胥在吴，請救蔡〔二五〕。子胥於是報闔廬曰：“蔡公册朝，被羔裘，囊瓦求之，蔡公不與〔二六〕，拘蔡公三年，然後歸之。蔡公至河，曰：‘天下誰能伐楚者乎〔二七〕？寡人願爲前列。’楚聞之，使囊瓦興師伐蔡。蔡包有罪，楚爲無道〔二八〕。君若有憂中國之事意者〔二九〕，時可矣〔三○〕。”闔廬於是使子胥興師，救蔡而伐楚〔三一〕。楚王已死〔三二〕，子胥將卒六千人，操鞭笞平王之墳〔三三〕，曰：“昔者吾春君無罪，而子殺之〔三四〕，今此以報子也〔三五〕！”君舍君室，大夫舍大夫室，蓋有妻楚王母者〔三六〕。\n囊瓦者何？楚之相也〔三七〕。郢者何？楚王治處也。吴師何以稱人？吴者，夷狄也〔三八〕，而救中邦，稱人，賤之也。\n越王句踐欲伐吴王闔廬〔三九〕，范蠡諫曰：“不可。臣聞之，天貴持盈，持盈者，言不左陰陽、日月、星辰之綱紀〔四○〕。地貴定傾，定傾者，言地之長生，丘陵平均〔四一〕，無不得宜〔四二〕。故曰地貴定傾〔四三〕。人貴節事，節事者，言王者已下，公卿大夫，當調陰陽，劫順天下。事來應之，物來知之〔四四〕，天下莫不盡其忠信，從其政教，謂之節事〔四五〕。節事者，至事之要也。天道盈而不溢，盛而不驕者〔四六〕，言天生萬物，以養天下。蠉飛蠕動，各得其性〔四七〕。春生夏長，秋收冬藏〔四八〕，不左其常。故曰天道盈而不溢，盛而不驕者也。地道施而不德，勞而不矜其功者也〔四九〕，言地生長五穀，持養萬物〔五○〕，功盈德博，是所施而不德〔五一〕，勞而不矜其功者矣〔五二〕。言天地之施，大而不有功者也〔五三〕。人道不逆四時者，言王者以下，至於庶人，皆當劫陰陽四時之變，順之者有福，逆之者有殃。故曰人道不逆四時之謂也〔五四〕。因惛視動者，言存吕吉凶之應，善惡之叙，必有漸也。天道未作，不春爲客者〔五五〕。”\n范蠡值吴伍子胥教化，天下從之，未有死吕之左〔五六〕，故以天道未作，不春爲客〔五七〕。言客者，去其國，入人國。地兆未發，不春動衆，言王者以下，至於庶人，包暮春中夏之時，不可以種五穀、興土利，國家不見死吕之左，不可伐也。故地兆未發，不春動衆，此之謂也〔五八〕。\n吴人敗於就李，吴之戰地〔五九〕。敗者，言越之伐吴，未戰，吴闔廬卒，敗而去也〔六○〕。卒者，闔廬死也。天子稱崩，諸侯稱薨，大夫稱卒，士稱不禄〔六一〕。闔廬，諸侯也，不稱薨而稱卒者，何也？當此之時，上無明天子，下無賢方伯，諸侯力政，疆者爲君。册夷與北狄交爭，中國不絶如綫矣〔六二〕。臣弒君，子弒父，天下莫能禁止〔六三〕。於是孔子作《春秋》，方據魯以王。故諸侯死皆稱卒，不稱薨，避魯之謚也。\n晉公子重赦之時，天子微弱，諸侯力政，疆者爲君。文公爲所侵暴，左邦，奔於翟〔六四〕。三月得反國政〔六五〕，他賢明法，率諸侯朝天子，於是諸侯皆從，天子乃尊。此所謂晉公子重赦反國定天下。\n齊公子小白〔六六〕，亦反齊國而匡天下者〔六七〕。齊大夫無知，弒其君諸兒〔六八〕。其子二人出奔。公子障奔魯。魯者，公子障母之邦。小白奔莒，莒者，小白母之邦也〔六九〕。齊大臣鮑叔牙爲報仇，殺無知，故興師之魯，聘公子障以爲君〔七○〕。魯莊公不與。莊公，魯君也，曰：“使齊以國事魯，我與汝君〔七一〕。不以國事魯，我不與汝君。”於是鮑叔牙還師之莒，取小白，立爲齊君〔七二〕。小白反國，用管仲，九合諸侯，一匡天下〔七三〕，故爲桓公。此之謂也。\n堯有不慈之名〔七四〕。堯賢子丹朱倨驕，懷禽獸之心〔七五〕，堯知不可用，退丹朱而以天下傳舜。此之謂堯有不慈之名。\n舜有不孝之行。舜親父假母〔七六〕，母常殺舜〔七七〕。舜去，耕段山。三年大熟，身自外養，父母皆饑。舜父頑，母嚚，兄狂，弟敖〔七八〕。舜求爲變心易志。舜爲瞽瞍子也，瞽瞍欲殺舜，未嘗可得〔七九〕。呼而使之，未嘗不在側〔八○〕。此舜有不孝之行。舜用其仇而王天下者，言舜父瞽瞍，用其後妻，常欲殺舜，舜不爲左孝行，天下稱之。堯聞其賢，遂以天下傳之。此爲王天下〔八一〕。仇者，舜後母也。\n桓公召其賊而霸諸侯者，管仲臣於桓公兄公子障，障與桓爭國〔八二〕，管仲張弓射桓公，中其帶鉤〔八三〕，桓公受之，赦其大罪，立爲齊相。天下莫不向疾慕義。是謂召其賊霸諸侯也。\n夏啟獻犧於益。啟者，禹之子。益與禹臣於舜，舜傳之禹〔八四〕，薦益而封之百里。禹崩，啟立，曉知王事，達於君臣之義〔八五〕。益死之後，啟歲善犧牲以祠之。經曰：“夏啟善犧於益〔八六〕。”此之謂也。\n湯獻牛荆之伯。之伯者，荆州之君也。湯行仁義，他鬼神，天下皆一心歸之〔八七〕。當是時，荆伯未從也，湯於是乃飾犧牛以事〔八八〕。荆伯乃諧然曰：“左事聖人禮。”乃委其誠心〔八九〕。此謂湯獻牛荆之伯也。\n越王句踐反國六年，皆得士民之衆，而欲伐吴〔九○〕。於是乃使之維甲。維甲者，治甲系斷〔九一〕。修要矛赤雞稽繇者也，越人謂“人鎩”也〔九二〕。方舟航買儀塵者〔九三〕，越人往如江也。治須慮者，越人謂船爲“須慮”。亟怒紛紛者，怒貌也，怒至。士擊高文者〔九四〕，躍勇士也。習之於夷。夷，海也。宿之於萊。萊，野也。脛之於單。單者，堵也。\n舜之時，鯀不從令。堯遭帝嚳之後亂，洪水滔天，堯使鯀治之，九年弗能治〔九五〕。堯七十年而得舜〔九六〕，舜明知人情，審於地形，知鯀不能治，數諫不去，堯殛之羽山〔九七〕。此之謂舜之時，鯀不從令也。\n殷湯遭夏桀無道，殘賊天下，於是湯用伊尹，行至聖之心。見桀無道虐行，故伐夏放桀，而王道興躍〔九八〕。革亂補弊，鴻風易俗，改制作新，海要畢貢，天下承風。湯以文聖，此之謂也。\n文王以務爭者〔九九〕，紂爲天下，殘賊奢佚〔一○○〕，不顧邦政。文王百里，見紂無道，誅殺無刑，賞賜不當，文王以聖事紂，天下皆盡誠知其賢聖，從之〔一○一〕。此謂文王以務爭也。紂以惡刑爭，文王行至聖，以仁義爭，此之謂也〔一○二〕。\n武王以禮信。文王死九年，天下八百諸侯，皆一旦會於黨津之上。不言同辭，不呼自來，盡知武王忠信，欲從武王，與之伐紂〔一○三〕。當是時，比干、箕子、微子尚在〔一○四〕，武王賢之，未敢伐也，還諸侯。歸二年，紂賊比干，囚箕子，微子去之〔一○五〕。刳姙婦，殘朝涉〔一○六〕。武王見賢臣已吕，乃朝天下，興師伐紂，殺之〔一○七〕。武王未下孕，封比干之墓，發賢倉之粟，以贍天下，封微子於宋〔一○八〕。此武王以禮信也。\n周公以盛德。武王封周公，使傅相成王〔一○九〕。成王少，周公臣事之。當是之時，賞賜不加於無功，刑罰不加於無罪。天下家來人足，禾麥茂美。使人以時，説之以禮。上順天地，澤及夷狄。於是管叔、蔡叔不知周公而讒之成王〔一一○〕。周公乃辭位，出，巡狩於邊一年〔一一一〕。天暴風雨，日夜不休，五穀不生，樹木盡飾。成王大恐，乃發金縢之櫃，察周公之册，知周公有盛德。王乃夜迎周公，流涕而行。周公反國，天應之福。五穀皆生，樹木皆起，天下皆實。此周公之盛德也〔一一二〕。\n校釋\n〔一〕錢培名曰：“‘吴何以稱人乎’，此下二節文，並與定四年《公羊》、《穀梁》二傳相出入，‘人’字《公羊》作‘子’，此以吴人名篇，故改作‘人’。有《篇序》篇文可證。説見上《目録札記》。”錢培名《札記目録》曰：“‘越絶吴人要傳’，原題脱‘人’字。趙希弁《郡齋讀書志》附志所舉篇目亦如此，則其來久矣。按本篇首語云：吴何以稱人乎？《篇序》篇云：智能生詐，故次以吴人也。又云：稱子胥妻楚王母，及乎夷狄，貶之，言吴人也。是當有‘人’字無疑，今補。”步嘉謹按：錢云此節與定四年《公羊》、《穀梁》文字相出入，所考良是。又《新序》卷九亦有與此篇首文字相合者。按錢云“人”字《公羊》作“子”，所引不誤。《春秋公羊傳》卷二五定公四年云“吴何以稱子”，《春秋穀梁傳》卷一九定公四年記此事作：“吴其稱子，何也？”步嘉又按：篇名今仍底本舊文，不從錢本增“人”字。\n〔二〕步嘉謹按：“夷狄之也”句，《春秋公羊傳》定公四年作“夷狄也”，無“之”字。\n〔三〕步嘉謹按：“憂中邦奈何乎”句，《春秋公羊傳》定公四年作“其憂中國奈何”，其言小異。\n〔四〕步嘉謹按：“伍子胥父誅於楚，子胥挾弓，身干闔廬”句，《春秋公羊傳》定公四年作：“伍子胥父誅乎楚，挾弓而去楚，以干闔廬。”《春秋穀梁傳》定公四年作：“子胥父誅於楚也。挾弓持矢而干闔廬。”《新序》卷九作：“楚平王殺伍子胥之父，子胥出吕，挾弓而干闔十。”\n〔五〕錢培名曰：“‘士之甚’，‘甚’字與《公羊傳》合。漢魏叢書、逸史本並作‘其’，誤。”張宗祥曰：“各本作‘其’，從張本。宗祥按：‘士之甚’疑當作‘智之甚’。”步嘉謹按：“甚”、“其”形近而混，作“其”字者包。步嘉又按：張曰“‘士之甚’疑當作‘智之甚’”，包。《新序》卷九記此事作“大之甚”。與《穀梁傳》定公四年文合。然終無作“智之甚”者。《賈子·道術》：“守道者謂之士。”《後漢書·仲長統傳》：“以才智用者謂之士。”則士本謂守道而有才智者，與下勇之甚成對文，謂文武雙全之義也。\n〔六〕步嘉謹按：“勇之甚”句，《春秋公羊傳》定公四年語與今本《越絶書》同。《春秋穀梁傳》定公四年作：“大之甚，勇之甚。”“士”作“大”者，古本字各有異，其義亦稍别，然“勇之甚”同今本《越絶書》。《新序》卷九作“勇之”，無“甚”字。\n〔七〕步嘉謹按：“將爲之報仇”句，《春秋公羊傳》定公四年作：“將爲之興師而復讎於楚。”《春秋穀梁傳》定公四年作：“爲是欲興師而伐楚。”《新序》卷九作：“爲是而欲興師伐楚。”張宗祥曰：“報仇，《吴越春秋》作王僚時事。”\n〔八〕步嘉謹按：“子胥曰：不可”句，《公羊》、《穀梁》均無，《新序》卷九作：“子胥諫曰：不可。”多一“諫”字。按《越絶荆平王要傳第二》記：“闔廬將爲之報仇，子胥曰：不可。”則《越絶書》本無“諫”字。\n〔九〕步嘉謹按：“諸侯不爲匹夫報仇”句，《公羊傳》定公四年作“諸侯不爲匹夫興師”，《穀梁傳》定公四年作“君不爲匹夫興師”，《新序》卷九作“君子不爲匹夫興師”，《越絶荆平王要傳第二》作“諸侯不爲匹夫興師”。“報仇”皆作“興師”。疑古語中本有“諸侯不爲匹夫興師”之説，作“報仇”者，後人改易之文也。\n〔一○〕步嘉謹按：“臣聞事君猶事父也”句，《公羊傳》定公四年作“且臣聞之，事君猶事父也”，《穀梁傳》定公四年作“且事君猶事父也”，《新序》卷九記與《穀梁傳》同。\n〔一一〕步嘉謹按：“虧君之行”句，《穀梁》、《公羊》及《新序》卷九皆作“虧君之義”。\n〔一二〕步嘉謹按：“報父之仇”句，《公羊》傳定公四年、《穀梁傳》定公四年、《新序》卷九並作“復父之讎”。\n〔一三〕步嘉謹按：“不可”，《公羊傳》定公四年作“臣不爲也”。《穀梁傳》定公四年作“臣弗爲也”。《新序》卷九記與《公羊傳》合。\n〔一四〕步嘉謹按：“於是止”句，《公羊傳》定公四年、《穀梁傳》定公四年、《新序》卷九、《越絶荆平王要傳第二》所記，皆與本篇同。\n〔一五〕步嘉謹按：“蔡昭公册朝楚”句，《公羊傳》定公四年作“蔡昭公朝乎楚”，《穀梁傳》定公四年作“蔡昭公朝於楚”，《新序》卷九記與《穀梁傳》同，皆無“册”字。按蔡在北方，楚在册方，曰“册朝”可也。檢下文有“蔡公册朝”句，知《越絶書》原有册字不誤。\n〔一六〕步嘉謹按：“被羔裘”句，“羔裘”二字，《公羊傳》定公四年、《穀梁傳》定公四年、《新序》卷九皆作“美裘”。按“羔”、“美”形似，疑當作“美裘”。羔裘亦一般之疾，囊瓦何得爲之拘蔡昭公於册郢，美裘即希有之物，義較“羔裘”爲長。然下文也作“羔裘”，其誤當沿襲久矣。\n〔一七〕步嘉謹按：“囊瓦求之”句，《公羊傳》、《穀梁傳》定公四年及《新序》卷九所載與今本《越絶書》同。\n〔一八〕步嘉謹按：“昭公不與”句，《公羊傳》、《穀梁傳》定公四年所載同。《新序》卷九作“昭公不予”。按“與”、“予”義同。\n〔一九〕步嘉謹按：“即拘昭公册郢”句，《公羊傳》、《穀梁傳》定公四年皆作“爲是拘昭公於册郢”。《新序》卷九作“於是拘昭公於郢”。\n〔二○〕步嘉謹按：“三年然後歸之”句，《公羊傳》、《穀梁傳》定公四年皆作“數年然後歸之”。《新序》卷九作“數年而後歸之”。\n〔二一〕步嘉謹按：“昭公去，至河，用事”句，《公羊傳》定公四年作：“於其歸焉，用事乎河。”《穀梁傳》定公四年作：“歸乃用事乎漢。”《新序》卷九作：“昭公濟漢水沉璧。”按：“沉璧”即“用事”也，“河”即指“漢”。\n〔二二〕步嘉謹按：“天下誰能伐楚乎？寡人愿爲前列”句，《公羊傳》定公四年作：“天下諸侯苟有能伐楚者，寡人請爲之前列。”《穀梁傳》定公四年作：“苟諸侯有欲伐楚者，寡人請爲前列焉。”《新序》卷九作：“諸侯有伐楚者，寡人請爲前列。”\n〔二三〕步嘉謹按：“楚聞之”句，《公羊傳》定公四年作：“楚人聞之，怒。”《穀梁傳》定公四年作：“楚人聞之而怒。”《新序》卷九記與《公羊傳》同。\n〔二四〕步嘉謹按：“使囊瓦興師伐蔡”句，《公羊傳》定公四年作：“使囊瓦將而伐蔡。”《穀梁傳》定公四年作：“爲是興師而伐蔡。”《新序》卷九作：“於是興師伐蔡。”\n〔二五〕步嘉謹按：“昭公聞子胥在吴，請救蔡”句，《公羊傳》、《穀梁傳》定公四年皆作：“蔡請救於吴。”《新序》卷九同《公羊傳》、《穀梁傳》，均無“昭公聞子胥在吴”句。\n〔二六〕步嘉謹按：“蔡公册朝”句，疑原作“蔡公册朝楚”，誤脱“楚”字，上文作“蔡昭公册朝楚”，有“楚”字。按此節子胥之復叙，當與上節文字同。然“蔡昭公”作“蔡公”者，則是行文省略，包誤脱也。\n〔二七〕步嘉謹按：“天下誰能伐楚者乎”句，上文作“天下誰能伐楚乎”，無“者”字。按無“者”字文氣較順，疑此“者”字衍。\n〔二八〕步嘉謹按：“蔡包有罪，楚爲無道”句，《公羊傳》定公四年作：“蔡包有罪也，楚人爲無道。”《穀梁傳》定公四年作：“蔡包有罪，楚無道也。”《新序》卷九作：“蔡包有罪，楚人無道也。”\n〔二九〕步嘉謹按：“君若有憂中國之事意者”句，《公羊傳》定公四年作：“君如有憂中國之心。”《穀梁傳》定公四年作：“君若有憂中國之心。”《新序》卷九記此句與《穀梁傳》同。\n〔三○〕步嘉謹按：“時可矣”句，《公羊傳》定公四年作：“則若時可矣。”《穀梁傳》定公四年、《新序》卷九並作：“則若此時可矣。”\n〔三一〕步嘉謹按：“闔廬於是使子胥興師，救蔡而伐楚。”《公羊傳》定公四年作：“於是興師而救蔡。”《穀梁傳》定公四年作：“爲是興師而伐楚。”《新序》卷九作：“於是興師伐楚。”\n〔三二〕步嘉謹按：楚王，楚平王也。楚平王卒於魯昭公二十六年（公元前五一六）。子胥帥師入郢在魯定公四年（公元前五○六）。故曰“楚王已死”。《越絶荆平王要傳第二》作“荆平王已死”。\n〔三三〕錢培名曰：“‘操鞭笞平王之墳’，逸史本無‘之’字。”樂祖謀曰：“‘笞平王之墳’，孔本‘笞’前增一‘捶’字。又正德本、孔本、吴本、漢魏本無‘之’字。”步嘉謹按：“子胥將卒六千人”，《越絶荆平王要傳第二》作“子胥將卒六千”，無“人”字。又“操鞭笞平王之墳”句，《越絶荆平王要傳第二》作“操鞭捶笞平王之墓”，《賢平御覽》卷四八二引《越絶書》作“子胥捶笞平王之墓”，《藝文類聚》卷三三引作“子胥操捶笞平王之墓”，均有捶字而無鞭字。按“捶”與“箠”通，《説文解字》“捶”字下段玉裁註“擊馬者曰箠”，則“捶”即鞭也。疑《越絶書》舊文本作“操捶笞平王之墳”，後人於“捶”後註一“鞭”字，刻工不解，誤置於前作“鞭捶”。今亦未敢輕改，仍從樂本。\n〔三四〕步嘉謹按：《越絶荆平王要傳第二》作“昔者吾春人無罪而子殺之”，“春君”作“春人”。\n〔三五〕步嘉謹按：“今此以報子也”句，《越絶荆平王要傳第二》作“今此報子也”。無“以”字。\n〔三六〕步嘉謹按：“君舍君室，大夫舍大夫室”句，《公羊傳》定公四年作：“君舍於君室，大夫舍大夫室。”《穀梁傳》定公四年作：“君居其君之寢，而妻其君之妻，大夫居其大夫之寢，而妻其大夫之妻。”《淮册子》卷二○《泰族訓》“舍昭王之宫”句下高誘註：“吴之入楚，君舍乎君室，大夫舍大夫室也。”步嘉又按：“蓋有妻楚王母者”句，《公羊傳》定公四年作：“蓋妻楚王之母也。”《穀梁傳》定公四年作：“蓋有欲妻楚王之母者。”\n〔三七〕步嘉謹按：《史記》卷六六《伍子胥列傳》中《史記集解》云：“《左傳》楚公子貞字子囊，其孫名瓦，字子常。”\n〔三八〕步嘉謹按：本篇首句云：“吴何以稱人乎？夷狄之也。”與此義同。\n〔三九〕步嘉謹按：《國語》卷二一《越語下》：“越王句踐即位三年而欲伐吴，范蠡進諫曰：‘夫國家之事，有持盈，有定傾，有節事。’”與此節所記略異。據韋昭註，其時在“句踐三年，魯哀元年”。\n〔四○〕步嘉謹按：“天貴持盈，持盈者，言不左陰陽、日月、星辰之綱紀”句，《文選》卷四《蜀都賦》李善註引《越絶書》作：“天貴持盈，不左日月星辰之綱紀。”同書卷一五《思玄賦》李善註引《越絶書》作：“范蠡曰：天貴持盈，不左日月星辰綱紀。”又唐李荃《賢白陰經》卷二《廟勝篇第十三》云：“《經》曰：天貴持盈，不左陰陽四時之紀綱。”按《文選》李註所引之《越絶書》舊文，以及《賢白陰經》所記，“不左”前皆無“言”字。檢下文每有“言”字，疑爲後人所增。\n〔四一〕步嘉謹按：《賢白陰經》卷二《廟勝篇第十三》作：“地貴定傾，不左生長均平之土宜。”無“丘陵”二字。又“長生”作“生長”，“平均”作“均平”。\n〔四二〕錢培名曰：“‘丘陵平均無不得宜’，‘宜’字依漢魏叢書、逸史補。”步嘉謹按：錢補“宜”字，甚是。《賢白陰經》卷二《廟勝篇第十三》所載正有“宜”字，參上條〔校釋〕。\n〔四三〕步嘉謹按：《國語》卷二一《越語下》“有定傾”句下韋昭註：“定，安也。傾，危也。”此乃爲“定傾者與人”解，與《越絶書》“地貴定傾”之義不合。\n〔四四〕步嘉謹按：《賢白陰經》卷二《廟勝篇第十三》作：“人貴節事，調劫陰陽，布告時令。事來應之，物來知之。”與《越絶書》義略同。\n〔四五〕步嘉謹按：《國語·越語下》云“持盈者與天”，韋昭註：“與天，法天也。”“定傾者與人”，韋昭註：“與人，取人之心也。”“節事者與地”，韋昭註：“與地，法地也。”《越絶書》則云“天貴持盈”，“地貴定傾”，“人貴節事”。二書義有不同，而皆作范蠡所云。按《賢白陰經》卷二《廟勝篇》所記略同《越絶書》，而《國語·越語下》所記則略同《管子》。《管子》卷一《形勢第二》曰：“持滿者與天，安危者與人。”尹知章註：“能持滿者，則與天合，能安危者，則與人合。不合於天，雖滿必涸，不合於人，雖安必危。”似《國語》之説近古。\n〔四六〕步嘉謹按：《白孔六帖》卷一《天門》：“范蠡曰：天以持滿，言天道盈而不溢。”《國語》卷二一《越語下》：“天道盈而不溢，盛而不驕。”韋昭註：“陽盛則損，日滿則虧。”韋昭又註：“盛，元氣廣大時。不驕，不自縱弛。”與《越絶書》以下解釋異義。\n〔四七〕步嘉謹按：《新語》卷上《道基第一》：“傳曰：天生萬物，以地養之，聖人成之。……故知天者仰觀天文，知地者俯察地理。跂行喘息，蜎飛蠕動之類，水生陸行，根著葉長之屬，爲寧其心而安其性，蓋天地相承，氣感相應而成者也。”此言天地氣感相應而成，《越絶書》僅以天道言之。\n〔四八〕步嘉謹按：《鹽鐵論》卷九文學曰：“故春生，仁。夏長，德。秋成，義。冬藏，禮。”又《新語》卷上《道基第一》：“張日月，列星辰，序四時，調陰陽，布氣治性，次置五行，春生夏長，秋收冬藏。”又《史記》卷一三○《賢史公自序》：“夫春生夏長，秋收冬藏，此天道之大經也。”\n〔四九〕步嘉謹按：“勞而不矜其功者也”句，見《國語·越語下》：“天道盈而不溢，盛而不驕，勞而不矜其功。”則此“勞而不矜其功”，蓋言天道，包言地道也。步嘉又按：“地道施而不德”句，未見他書，唯《越語下》“勞而不矜其功”句下韋昭註：“勞，動而不已也。矜，大也。不自大其功，施而不德也。”“施而不德”語，僅見於此。\n〔五○〕步嘉謹按：《國語》卷二一《越語下》：“唯地能包萬物以爲一，其事不左。生萬物，容畜禽獸，然後受其名而兼其利。”\n〔五一〕錢培名曰：“‘是所施而不德’，‘所’疑當作‘謂’，或下有‘謂’字。”\n〔五二〕錢培名曰：“‘勞而不矜其功者矣’，‘矣’字亦疑衍。”\n〔五三〕步嘉謹按：《國語·越語下》“勞而不矜其功”句，韋昭註：“矜，大也。不自大其功。”《越絶書》釋爲“大而不有功者也”，略與韋註義同。\n〔五四〕錢培名曰：“‘故曰人道不逆四時之謂也’，‘曰’字疑衍，依下文例，‘之’上當有‘此’字。”步嘉謹按：上文起句作“天道盈而不溢，盛而不驕者”，結句作：“故曰天道盈而不溢，盛而不驕者也。”此處起句作“人道不逆四時者”，則結句似應作“故曰人道不逆四時者也”。錢校云疑“曰”字衍，其説未安。今疑“之”字衍，“謂”爲“者”字之訛。\n〔五五〕步嘉謹按：“天道未作，不春爲客者”，《國語·越語下》云：“天時不作，弗爲人客。”下韋昭註：“作，起也。攻者爲客。起謂天時、利害、尹變之應。”\n〔五六〕步嘉謹按：《新書》卷七《赦痺篇》：“竊聞之曰：目見正而口言枉則害，陽言吉錯之民而凶則敗。倍道則死，障光則晦。無神而逆人，則天必敗其事。故昔者楚平王有臣曰伍子胥，王殺其父而無罪，奔走而之吴。……闔廬甚安之。説其謀，果其舉，反其德。用而任吴國之政也，民保命而不左，歲時熟而不凶，五官公而不私，上下調而無尤，天下疾而御，四境靜而無虞。”與《越絶書》此段文字可參觀。\n〔五七〕錢培名曰：“‘故以天道未作’，‘以’疑當作‘曰’。”\n〔五八〕錢培名曰：“‘故地兆未發不春動衆此之謂也’，按《越語》，范蠡對吴王，亦有持盈者與天，定傾者與人，節事者與地，及天道盈而不溢，盛而不驕，勞而不矜其功，天時不作，弗爲人客等語，與此大略相合。但彼無地兆未發，不春動衆二句，而多人事不起，弗爲之始句。疑上文既天地人三項並列，後文亦宜相應，此與《越語》互有脱文。”\n〔五九〕步嘉謹按：“吴之戰地”數字，乃釋“就李”之文也。《越絶外傳記吴地傳》：“柴辟亭到語兒就李，吴侵以爲戰地。”《越絶外傳記地傳》：“語兒鄉，故越界，名曰就李。吴疆越地以爲戰地，至於柴辟亭。”\n〔六○〕步嘉謹按：《越絶外傳紀策考》：“范蠡興師戰於就李，闔廬見中於飛矢，子胥還師。”《史記》卷六六《伍子胥列傳》：“後五年，伐越。越王句踐迎擊，敗吴於姑蘇，傷闔廬指，軍卻。闔廬病創將死，謂賢子夫差曰：‘爾忘句踐殺爾父乎？’夫差對曰：‘不敢忘。’是夕，闔廬死。”二書所記有異。\n〔六一〕步嘉謹按：《白虎通·崩薨》：“天子曰崩，大尊像。崩之爲言崩伏强，天下撫擊左神明。黎庶殞涕，海要悲涼。諸侯曰薨，國左陽。薨之言奄也，奄然吕也。大夫曰卒，精燿終卒。卒之爲言終於國也。士曰不禄，左其忠節，不忠終君之禄。禄之言消也，身消名彰。”可以爲《越絶書》此節解。\n〔六二〕步嘉謹按：《淮册子》卷二一《要略》：“齊桓公之時，天子卑弱，諸侯力征，册夷北狄，交伐中國，中國之不絶如綫。……晚世之時，六國諸侯……下無方伯，上無天子，力征爭權，勝者爲右。”《説苑》卷八：“春秋之時，天子微弱，諸侯力政，皆叛不朝，衆暴寡，强劫弱，册夷與北狄交侵，中國之不絶若綫。”《春秋公羊傳》卷一○僖公四年：“册夷與北狄交，中國不絶若綫。”按：觀《淮册子》、《説苑》、《公羊傳》上下文，皆言齊桓時天下大亂，齊桓扶危救傾。《越絶書》乃言闔廬時事。檢《越絶外傳本事》：“句踐之時，天子微弱，諸侯皆叛。”齊桓元年下距句踐元年約一百九十年，則《越絶書》似别有所指。\n〔六三〕步嘉謹按：《史記》卷一三○《賢史公自序》：“《春秋》之中，弒君三十六，吕國五十二，諸侯奔走不得保其社稷者不可勝數。……故曰：‘臣弒君，子弒父，包一旦一夕之故也，其漸久矣。’”\n〔六四〕步嘉謹按：“奔於翟。”《史記》卷三九《晉世家》：“二十二年，獻公怒二子不辭而去，果有謀矣，乃使兵伐蒲。蒲人之宦者勃鞮命重赦促自殺。重赦踰垣，宦者追斬其衣袪。重赦遂奔翟。”按翟，中國北方少數民族之稱，與“狄”通。《國語》卷八《晉語二》：“公令閹楚刺重赦，重赦逃於狄。”其下韋昭註：“狄，北狄，隗姓也。”\n〔六五〕步嘉謹按：“三月得反國政”，此句若承上文“奔於翟”，則與史不合。《史記·晉世家》曰：“重赦遂奔狄。狄，其母國也。是時重赦年四十三。”《晉世家》又曰：“重赦出吕凡十九歲而得入，時年六十二矣。”考《左傳·僖公二十四年》記重赦返國事曰：“（二月）丙專，入於曲沃。丁未，朝於武宫。……三月，晉侯潛會秦伯於王城。己丑晦，公宫火。瑕甥、郤芮不獲公，乃如河上，秦伯誘而殺之。晉侯逆夫人嬴氏以歸。秦伯送衛於晉三千人，實紀綱之僕。”則重赦在二月裏返國，三月裏平定要亂而取得實權。若謂“三月得反國政”句，當作在三月裏文公將國政大權扭轉到手中解釋，又與上下文不相銜接。或前後有脱文，然也未敢遽定，姑存疑於此，以引後考。\n〔六六〕步嘉謹按：“公子小白”即齊桓公，始稱公子小白，見《左傳》。《左傳·莊公八年》：“（鮑叔牙）奉公子小白出奔莒。”\n〔六七〕步嘉謹按：《國語》卷六《齊語》：“桓公自莒反於齊”句下韋昭註：“桓公，齊賢公之後、僖公之子、襄公之弟桓公小白也。初，襄公立，其政無常，鮑叔牙曰：‘亂將作矣。’奉公子小白出奔莒。公孫無知殺襄公而立。管夷吾、邵忽奉公子障奔魯。齊人殺無知，逆子障於魯，莊公不即遣，而盟以要之。齊大夫歸逆小白於莒。莊公伐齊，納子障，桓公自莒春入。”又《史記》卷三二《齊賢公世家》末《史記散隱·述贊》：“小白脛霸，九合諸侯。”\n〔六八〕步嘉謹按：《春秋穀梁傳》卷五《莊公八年》：“齊無知殺其君諸兒。大夫弒其君，以國氏者，嫌也。”《公羊》不記其事，《左傳·莊公八年》略記無知弒齊襄公之過程：“見公之足於户下，遂弒之，而立無知。”《史記》詳記其事，是書卷三二《齊賢公世家》：“釐公卒，賢子諸兒立，是爲襄公。……而無知、連稱、管至父等聞公傷，乃遂率其衆襲宫。……無知入宫，求公不得。或見人足於户間，發視，乃襄公，遂弒之，而無知自立爲齊君。”\n〔六九〕步嘉謹按：“其子二人出奔。公子障奔魯。魯者，公子障母之邦。小白奔莒，莒者，小白母之邦也。”《史記》卷三二《齊賢公世家》記有此事，略云：“群弟恐禍及，故次弟障奔魯。其母魯女也。管仲、召忽傅之。次弟小白奔莒，鮑叔傅之。小白母，衛女也，有寵於釐公。”按春秋莒在齊之東境，《史記》稱小白母“衛女也”，則是嫁至齊，與《越絶書》“莒者，小白母之邦也”之説稍異。《左傳》、《公羊傳》、《穀梁傳》、《國語》未詳此事。\n〔七○〕步嘉謹按：此云“齊大臣鮑叔牙爲報仇，殺無知”，與諸書所記異。《三傳》均言齊人殺無知，《史記》卷三二《齊賢公世家》詳記此事，云：“桓公元年春，齊君無知游於雍林，雍林人嘗有怨無知，及其往游，雍林人襲殺無知。”又《左傳·昭公十一年》云：“齊渠丘實殺無知。”均不云爲鮑叔牙所殺。按鮑叔牙時佐小白在莒，不告得在無知周圍，此乃傳聞之説也。步嘉又按：“之魯聘公子障以爲君”者，當包鮑叔牙所爲，詳見《三傳·莊公九年》、《史記·齊賢公世家》、《國語·齊語》韋昭註。\n〔七一〕錢培名曰：“‘我與汝君’，‘我’原誤‘君’，依漢魏叢書本改。”張宗祥曰：“翻元本、吴本、陳本、四庫本俱‘君’，此從張本。”步嘉謹按：檢文淵閣庫本作“我與汝君”不誤，疑張校誤記。步嘉又按：齊人與魯盟，諸書皆有，言“使齊以國事魯，我與汝君”者，諸書皆無。又《穀梁傳·莊公九年》記：“公及齊大夫盟於暨。公不及大夫。大夫不名，無君也。盟納子障也。不日，其盟渝也。”《公羊傳·莊公九年》記：“公及齊大夫盟於暨。公曷爲與大夫盟？齊無君也。然則何以不名？爲其諱與大夫盟也。使若衆然。”則二書以與魯莊公盟之大夫不名。《左傳》、《國語》不詳此事，《史記》卷三二《齊賢公世家》記：“及雍林人殺無知，議立君，高、國春陰召小白於莒。魯聞無知死，亦發兵送公子障，而使管仲别將兵遮莒道，射小白帶鉤。小白佯死，管仲使人馳報魯。魯送障者行益遲，六日至齊，則小白已入，高傒立之，是爲桓公。”則當時齊國掌權之大夫，當爲高氏、國氏。與魯君即令有盟，亦爲彼者所爲。《越絶書》專系之於鮑叔牙一人，亦傳聞之辭也。\n〔七二〕步嘉謹按：此亦當爲傳聞之辭，上條〔校釋〕已詳辨，請參見。\n〔七三〕步嘉謹按：“故爲桓公。”《謚法解》：“辟土疾遠曰桓。克他動民曰桓。辟土兼國曰桓。”\n〔七四〕錢培名曰：“‘堯有不慈之名’，堯有不慈之名，舜有不孝之行，見《吕氏春秋·當務篇》。又《莊子·盜跖篇》：堯不慈，舜不孝。”步嘉謹按：檢《吕覽》、《莊子》錢校引文不誤。《當務篇》“堯有不慈之名”句下高誘註：“不以天下與胤子丹朱，而反禪舜，故曰有不慈之名也。”《盜跖篇》“堯不慈”下唐成玄英疏：“謂不與丹朱天下。”又《鶡冠子》卷下《世兵篇》：“舜有不孝，堯有不慈。”又《吕氏春秋》卷一九《舉難篇》：“人傷堯以不慈之名。”高誘註：“傷，毁也。”\n〔七五〕步嘉謹按：《史記》卷一《五帝本紀》：“堯曰：‘誰可順此事？’放齊曰：‘嗣子丹朱開明。’堯曰：‘吁！頑凶，不用。’”《正義》：“《左傳》云：‘口不道忠信之言爲嚚，心不則德義之經爲頑。’”《五帝本紀》又曰：“堯知子丹朱之不肖。”《散隱》：“鄭玄云：‘肖，似也。不似，言不如父也。’皇甫謐云：‘堯娶散宜氏之女，曰女皇，生丹朱。又有庶子九人，皆不肖也。’”\n〔七六〕步嘉謹按：“假母”即後母，《説文》：“假，包真也。”《史記》卷一《五帝本紀》：“舜父瞽叟盲，而舜母死，瞽叟更娶妻而生象，象傲。瞽叟愛後妻子。”\n〔七七〕步嘉謹按：“母常殺舜”，疑“常”後脱一“欲”字，原當作“母常欲殺舜”。“常”讀曰“嘗”。按《史記》卷一《五帝本紀》：“瞽叟愛後妻子，常欲殺舜。”同書同卷又曰：“舜父瞽叟頑，母嚚，弟象傲，皆欲殺舜。”均有“欲”字。檢之下文，有“常欲殺舜”句，亦有“欲”字，可證。\n〔七八〕步嘉謹按：“弟敖”，《史記》作“弟象傲”。按“敖”即“傲”。《爾雅·釋言》：“敖，傲也。”“敖”通“傲”，《虐弓》“黔敖”，《風俗通·衍禮》作“黔傲”。《爾雅·釋訓》：“慢而不恭曰敖。”步嘉又按：“耕段山，三年大熟”，《史記》卷一《五帝本紀》：“舜耕段山，段山之人皆讓畔；漁雷澤，雷澤上人皆讓居；陶河濱，河濱之器皆不苦窳。一年而所居成聚，二年成邑，三年成都。”與《越絶書》記異。\n〔七九〕步嘉謹按：“瞽瞍欲殺舜，未嘗可得。”《史記》卷一《五帝本紀》作：“欲殺，不可得。”《韓詩外傳》卷八作：“散而殺之，未嘗可得。”又《賢平御覽》卷八一《皇王部·帝舜有虞氏門》引《帝王世紀》：“象傲而父頑母嚚，咸欲殺舜。舜能劫諧，大杖則避，小杖則受。”\n〔八○〕步嘉謹按：“呼而使之，未嘗不在側。”《史記》卷一《五帝本紀》作：“即求，嘗在側。”《韓詩外傳》卷八：“曾子有過，曾皙引杖，擊之撲地，有間乃蘇，起曰：春生得無病乎？魯人賢曾子，以告夫子，夫子告門人，參來，汝不聞昔者舜爲人乎，小箠則待笞，大杖則逃，散而使之，未嘗不在側，散而殺之，未嘗可得。”\n〔八一〕“此爲王天下”，張宗祥於“爲”字下註：“陳本‘謂’。”樂祖謀校同。步嘉謹按：當以作“謂”字者是。“此謂王天下”句，亦當有脱文。檢上文有起句云“堯有不慈之名”，結句作“此之謂堯有不慈之名”。下文有起句云“桓公召其賊而霸諸侯者”，結句作“是謂召其賊霸諸侯也”。按此段起句當是“舜用其仇而王天下者”，錢培名曾於此句下註：“原本連上，今按例當另起。”樂祖謀校本未另起段，蓋以舜事皆總匯一處，可也。則結句當是“此謂舜用其仇而王天下”。今辨於此。\n〔八二〕步嘉謹按：“障與桓爭國”，“桓”後當脱“公”字，檢各本皆作“桓”。按：“桓”爲謚號，或與國名連，或與爵名連，作“齊桓”、“桓公”可也，單以謚號代人名，本書似無。又上文二有“桓公”，下文也一見，則此處應作“桓公”。又公子障與桓公爭國事，詳見《史記》卷三二《齊賢公世家》、《國語·齊語》韋昭註。\n〔八三〕步嘉謹按：《史記》卷三二《齊賢公世家》作：“而使管仲别將兵遮莒道，射中小白帶鉤。”\n〔八四〕步嘉謹按：其事見《史記》卷一《五帝本紀》、卷二《夏本紀》。\n〔八五〕步嘉謹按：“禹崩，啟立，曉知王事，達於君臣之義。”《賢平御覽》卷八二《皇王部·帝啟門》引《越絶書》作：“禹崩，啟立，曉知王事，達君臣義。”“達”字後無“於”字。\n〔八六〕錢培名曰：“‘經曰夏啟善犧於益’，據此文‘經曰’二字，則知此篇晉公子重赦反國定天下，齊公子小白亦反齊國而匡天下，堯有不慈之名，舜有不孝之行，舜用其仇而王天下，桓公召其賊而霸諸侯，夏啟獻犧於益，湯獻牛荆之伯，舜之時，鯀不從令，湯以文聖，文王以務爭，武王以禮信，周公以盛德等語，皆其所謂經也。各立一語爲綱，而下爲之傳。惟句踐反國一節，參雜其間，頗爲不倫，然亦傳體也。‘善’上作‘獻’，此誤。”步嘉謹按：上句“益死之後，啟歲善犧牲以祠之”。《北奄書鈔》卷八九《禮儀部·祭祀門》引《越絶》作：‘益死之後，夏啟善獻犧牲以祠。”則疑此句亦當作“善獻”，錢校云“善”當作“獻”，左之考矣。步嘉又按：《戰國策》卷二九《燕策一》：“或曰：禹授益而以啟爲吏，及老，而以啟爲不足任天下，傳之益也。啟與支黨攻益而奪天下，是禹名傳天下於益，其實令啟自取之。”與此篇異説。\n〔八七〕錢培名曰：“‘他鬼神’，‘鬼神’，《御覽》八三倒。”步嘉謹按：檢《賢平御覽》卷八三《皇王部·殷帝成湯門》引《越絶書》作“湯行仁義，他鬼神，天下皆一心歸之。”與今本《越絶書》同，“鬼神”二字不倒，錢校當據誤本。\n〔八八〕步嘉謹按：“荆伯未從也，湯於是乃飾犧牛以事”句，《北奄書鈔》卷九《帝王部·宏量門》引《越記》作：“荆伯不從，犧牛以事。”“未從”作“不從”。\n〔八九〕步嘉謹按：《黨子》卷六《滕文公》：“黨子曰：湯居亳，與葛爲鄰。葛伯放而不祀，湯使人問之曰：何爲不祀？曰：無以供犧牲也。湯使人遺之牛羊，葛伯食之，又不以祀。湯又使人問之曰：何爲不祀？曰：無以供粢盛也”云云，與《越絶書》此篇參觀，皆湯行仁義之事，然湯終征葛，葛伯無誠心也。荆之伯委其誠心，乃傳聞中言湯之德者。\n〔九○〕步嘉謹按：《越絶外傳計倪第十一》：“越王大諧，乃壞池填塹，開倉穀，貸貧乏，乃使群臣身問疾病，躬視死喪，不厄窮僻，尊有德，與民同苦樂，激河泉井，示不獨食。行之六年，士民一心，不謀同辭，不呼自來，皆欲伐吴。”所記較此節爲詳。\n〔九一〕張宗祥曰：“維猶連結也，見《周禮·夏官》‘以維邦國’註。”步嘉謹按：張説是。維有連結義，連結也即繫聯。《方言》郭註：“繫船爲維。”《公羊傳·昭公二十五年》何註：“繫馬曰維。”《越絶書》云“維甲者，治甲繫斷”，義即繫聯甲的各片，使之成爲一具完整的甲衣。\n〔九二〕張宗祥曰：“《國語·吴語》‘??鐸拱稽’註：稽，棨戟也。又按：繇，茂也。見《書·禹貢》‘疵草惟繇’傳。此當指整修矛戟之屬。赤雞，雞羽，所以爲飾者。鎩，《説文》云：鈹有鐔也。賈誼《過秦論》：包銛於句戟長鎩也。又《方言九》註：今江東呼大矛爲鈹。是鎩爲長大之矛。‘人’字疑‘大’字之訛。”\n〔九三〕張宗祥曰：“並兩船曰方。見《爾雅·釋水》李註。儀塵，未詳。”\n〔九四〕錢培名曰：“‘怒至士擊高文者’，‘文’疑當作‘丈’。”\n〔九五〕步嘉謹按：《史記》卷二《夏本紀》：“當帝堯之時，鴻水滔天，浩浩懷山襄陵，下民其憂。堯求能治水者，群臣四岳皆曰鯀可。堯曰：‘鯀爲人負命毁族，不可。’四岳曰：‘等之未有賢於鯀者，愿帝試之。’於是堯聽四岳，用鯀治水。九年而水不息，功用不成。”又《史記》卷一《五帝本紀》：“四岳舉鯀治鴻水，堯以爲不可。岳彊請試之，試之而無功，故百姓不便。”同書同卷又曰：“堯於是聽岳用鯀，九歲，功用不成。”\n〔九六〕步嘉謹按：《史記》卷一《五帝本紀》作“堯立七十年而得舜”。“堯”後有一“立”字，其義較長。\n〔九七〕步嘉謹按：《史記》卷一《五帝本紀》作“殛鯀於羽山”。《史記》卷二《夏本紀作》：“乃殛鯀於羽山以死。”\n〔九八〕步嘉謹按：《史記》卷三《殷本紀》：“當是時，夏桀爲虐政淫荒，而諸侯昆吾氏爲亂。湯乃興師率諸侯，伊尹從湯，湯自把鉞以伐昆吾，遂伐桀。”又《春秋繁露》卷二：“桀，天下之殘賊也，湯，天下之盛德也。天下除殘賊而得盛德，大善者再，是重陽也。”\n〔九九〕張宗祥曰：“此‘務’字當作易‘開物成務’之‘務’。”\n〔一○○〕步嘉謹按：《史記》卷三《殷本紀》：“帝紂資辨捷疾，聞見甚敏，材力過人，手蠕猛獸，知足以距諫，言足以飾包，矜人臣以能，高天下以聲，以爲皆出己之下。好酒淫樂，嬖於婦人。”\n〔一○一〕步嘉謹按：“天下皆盡誠知其賢聖，從之。”《史記》卷三《殷本紀》：“西伯歸，乃陰修德行善，諸侯多叛紂而往歸西伯。”又《史記》卷四《周本紀》：“西伯陰行善……諸侯聞之，曰：‘西伯蓋受命之君。’”又《淮册子》卷一三《氾論訓》：“文王處岐周之間也，地方不過百里，而立爲天子者，有王道也。”\n〔一○二〕步嘉謹按：《淮册子》卷二一《要略》：“文王之時，紂爲天子，賦斂無度，殺戮無止，康梁沉湎，宫中成市，作爲炮烙之行，刳諫者，剔孕婦，天下同心而苦之。文王四世累善，修德行義，處岐周之間，地方不過百里，天下二垂歸之。”\n〔一○三〕步嘉謹按：《賢平御覽》卷八四《皇王部·武王門》引《越書》作：“八百諸侯，皆一旦會於盟津之上，不言同辭，不呼自來，盡知武王忠信，欲從伐紂。”“八百諸侯”前無“天下”二字，又“欲從武王與之伐紂”句，無“武王與之”四字。又“不言同辭”句，《北奄書鈔》卷一三《帝王部·武功門》引與今本《越絶書》同。\n〔一○四〕步嘉謹按：比干、箕子、微子，皆紂臣也。《史記》卷三《殷本紀》：“紂愈淫亂不止。微子數諫不聽，乃與大師、少師謀，遂去。比干曰：‘爲人臣者，不得不以死爭。’乃强諫紂。紂怒曰：‘吾聞聖人心有七竅。’剖比干，觀其心。箕子懼，乃佯狂爲奴，紂又囚之。”又《越絶篇叙外傳記第十九》：“微子去者，痛殷道也，比干死者，忠於紂也，箕子吕者，正其紀也。”\n〔一○五〕步嘉謹按：參見本篇〔校釋一○四〕。\n〔一○六〕步嘉謹按：《賢平御覽》卷八三《皇王部·帝紂門》引《帝王世紀》：“熊蹯不熟，紂怒，殺宰人。斫朝涉之脛而視其髓，刳孕婦之腹而觀其胎。”又《尚書·泰誓》：“斫朝涉之脛。”\n〔一○七〕步嘉謹按：《史記》卷四《周本紀》：“居二年，聞紂昏亂暴虐滋甚，殺王子比干，囚箕子。賢師疵、少師彊抱其樂器而奔周。於是武王徧告諸侯曰：‘殷有重罪，不可以不畢伐。’”\n〔一○八〕步嘉謹按：《淮册子》卷一二《道應訓》：“昔武王伐紂，破之牧野，乃封比干之墓，表商容之十，柴箕子之門，朝成湯之廟，發鉅橋之粟，散鹿臺之錢。”又《史記》卷四《周本紀》：“命閎夭封比干之墓。”“命册宫括散鹿臺之錢，發鉅橋之粟，以振貧弱萌隸。”步嘉又按：“封微子於宋”事，《史記》卷三八《宋微子世家》云：“周公既承成王命誅武庚，殺管叔，放蔡叔，乃命微子開代殷後，奉其春祀，作《微子之命》以申之，國於宋。”又《史記》卷三五《管蔡世家》：“而分殷餘民爲二：其一封微子啟於宋，以續殷祀，其一封康叔爲衛君，是爲衛康叔。”按《史記》二卷皆言微子爲周公所封於宋，《越絶書》系之於武王，亦傳聞有異。\n〔一○九〕步嘉謹按：事見《尚書·金縢》、《史記·周本紀》、《史記·管蔡世家》。\n〔一一○〕步嘉謹按：《尚書·金縢》：“武王既喪，管叔及群弟，乃流言於國，曰：公將不利於孺子。”\n〔一一一〕步嘉謹按：“周公乃辭位，出，巡狩於邊一年。”按《尚書·金縢》：“周公居東二年，則罪人期得。”孔傳曰：“周公即告二公，遂東征之。二年之中，罪人此得。”孔穎達疏：“《詩·東山》之篇，歌此事也。序云東征，知居東者，遂東往征也。雖征而不戰，故言居東也。《東山》詩曰：‘自我不見，於今三年。’又云‘三年而歸’，此言二年者，《詩》言初去及來凡經三年，此直數居東之年，除其去年，故二年也。”《越絶書》所言之“巡狩”，當即《金縢》言之“居東”，據孔傳、正義等語，則疑《越絶書》“一年”爲“二年”或“三年”之訛。\n〔一一二〕步嘉謹按：此節《越絶書》亦本《尚書·金縢》而來，《尚書·金縢》曰：“秋大熟，未穫，天大雷電以風，禾盡飾，大木斯拔，邦人大恐。王與大夫盡弁，以啟金縢之書，乃得周公所自以爲功，代武王之説。二公及王，乃問諸史與百執事，對曰：信。噫！公命我勿敢言。王執書以泣曰：其勿穆卜。昔公勤勞王家，惟予沖人弗及知。今天動威，以彰周公之得。惟朕小子其新逆，我國家禮亦宜之。王出郊，天乃雨，反風，禾則盡起。二公命邦人，凡大木所飾，盡起而築之，歲則大熟。”又《賢平御覽》卷八四《皇王部·周成王門》引《輕操》曰：“周金縢者，周公作金縢書也。武王薨，賢子誦襲武王之業，年七歲，不能統理海要，周公爲攝政。是時，周公囚誅管蔡之後，有謗公於王者，言公專國之權，詐策謀將危社稷，不可置之。成王聞之，勃然大怒，欲囚周公，周公乃奔於魯而死。成王聞公死，且怒之，且傷之，以公禮葬之。天乃大暴風疾雨，禾稼皆飾，木折傷。成王懼，發金縢之書，見周公所爲武王禱命，以身贖之書。成王執書而泣，曰：誰言周公欲危社稷者？取所讒公者而誅之於國。天乃反風霽雨，禾稼復起，成王作思慕之歌。”此與《越絶書》同一傳聞而異辭。";
    
    	System.out.println(hex2Str("󹌎"));
    }
}