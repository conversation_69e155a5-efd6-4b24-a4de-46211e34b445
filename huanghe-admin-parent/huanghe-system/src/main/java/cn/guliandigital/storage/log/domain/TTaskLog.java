package cn.guliandigital.storage.log.domain;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 数据解析日志对象 t_task_log
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
@Data
public class TTaskLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 任务id */
    @Excel(name = "任务id")
    private String taskId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;
    /** 解析开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "解析开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date parseStartTime;

    /** 解析结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "解析结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date parseEndTime;

    /** 解析状态 2-解析成功 3-解析失败 */
    @Excel(name = "解析状态 1-正在解析 2-解析成功 3-解析失败")
    private String dataStatus;

    /** 错误描述 */
    @Excel(name = "错误描述")
    private String errorMsg;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryTimes;

    /** 数据描述 */
    @Excel(name = "数据描述")
    private String dataDesc;

    /** $column.columnComment */
    @Excel(name = "创建人Id" )
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
    @Excel(name = "更新人")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    private Integer pageNum;
    private Integer pageSize;

    /**文本类型*/
    private String imagetextType;

    
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskId", getTaskId())
            .append("fileName", getFileName())
            .append("parseStartTime", getParseStartTime())
            .append("parseEndTime", getParseEndTime())
            .append("dataStatus", getDataStatus())
            .append("errorMsg", getErrorMsg())
            .append("retryTimes", getRetryTimes())
            .append("dataDesc", getDataDesc())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
