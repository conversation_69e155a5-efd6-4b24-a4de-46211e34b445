package cn.guliandigital.storage.storage.service.impl;

import java.awt.Graphics;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.imageio.ImageIO;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;

import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.common.utils.BigwordUtil;
import cn.guliandigital.common.utils.ImgConvert;
import cn.guliandigital.common.utils.TxtUtil;
import cn.guliandigital.common.utils.file.Tif2JpgPythonUtils;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.text.UnicodeUtil;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

@Slf4j
public class Recog3Utils {
	
	
	public static void main(String[] args) throws Exception {
//		System.out.println(IdUtil.fastSimpleUUID());
//		System.out.println(IdUtil.fastSimpleUUID());
//		System.out.println(IdUtil.fastSimpleUUID());
		
		//String jsonPath = "C:\\Users\\<USER>\\Desktop\\ZSK90585 中山蔡太史館閣宏辭\\json\\ZSK90585-000001-L00001.json";
//		String jsonPath = "C:\\Users\\<USER>\\Desktop\\ZHB100935064 周易乾鑿度二卷\\pageimages\\json2\\ZSK10093-000037-L00036.json";
//		
//		String jsonstr = TxtUtil.readTxtFile(jsonPath);
//		
//		JSONObject images  = JSONObject.parseObject(jsonstr);
//		if(images.containsKey("Images")) {
//			Map<String, Object> _map= convert(images.getJSONObject("Images"), null, false);
//			//System.out.println(_map);
//			
//			String string = JSONObject.toJSONString(_map);
//			System.out.println(string);
//		}else {
//			System.out.println(images);
//		}
		File save = new File("C:\\Users\\<USER>\\Desktop\\json2");
		if(!save.exists()) {
			save.mkdirs();
		}else {
			FileUtils.cleanDirectory(save);
		}
		
		File file = new File("C:\\Users\\<USER>\\Desktop\\json");
		File[] fs = file.listFiles();
		int i = 0;
		int total = fs.length;
		for(File f : fs) {
			i++;
			System.out.println("##########"+i+"==>"+total);
			String jsonstr = TxtUtil.readTxtFile(f.getAbsolutePath());
			
			JSONObject images  = JSONObject.parseObject(jsonstr);
			if(images == null) {
				continue;
			}
			if(images.containsKey("Images")) {
				Map<String, Object> _map= convert(images.getJSONObject("Images"), null, false);
				//System.out.println(_map);
				
				String string = JSONObject.toJSONString(_map);
				String newfile = save.getAbsolutePath() + File.separator + f.getName();
				System.out.println(string);
				
				TxtUtil.contentToTxt(newfile, string);
			}
		}
		
		log.info("===========================>");
		
	}

	public static final int PIC_SIZE = 1200;

	public static final int PIC_WIDTH_SIZE = 1000;

	public static final int PIC_HEIGHT_SIZE = 1000;

	public static boolean compressPic(Path source, Path target, int times) throws IOException {
		try {
			log.info("开始第{}次压缩 {} >>> {}", times, source, target);
			BufferedImage bfferedImage = ImageIO.read(source.toFile());
			if (bfferedImage != null && bfferedImage.getWidth() > PIC_SIZE) {
				
				// 压缩文件至指定路径
				Thumbnails.of(source.toFile()) //
						.size(PIC_HEIGHT_SIZE, PIC_WIDTH_SIZE) //
						.toFile(target.toFile());
				return true;// 压缩处理
			} else {
				// 无需压缩
				return false;
			}
		}catch(IllegalArgumentException e) {
			log.error("error,",e);
			/*
			 * 防止进入无限循环
			 */
			if(times > 1) {
				return false;
			}
			//出现异常
			// 1、直接转换
			File inputFile = source.toFile();
			String newfilename = FilenameUtils.getBaseName(inputFile.getName())+"_jpg2jpg";
			String extend = FilenameUtils.getExtension(inputFile.getName());
			
			Path outPath = Paths.get(inputFile.getParent(), newfilename+"."+extend);
			try {
				Tif2JpgPythonUtils.tif2jpgHandler(inputFile.getAbsolutePath(), outPath.toFile().getAbsolutePath(), false);
			} catch (Exception e1) {
				log.error("errore1",e1);
				throw new CustomException("图片压缩异常");
			}
			//log.info(outPath.toFile().getAbsolutePath());
			times++;
			
			boolean compressPic = compressPic(outPath,  target, times);
			if(!compressPic) {
				Files.copy(outPath, target);
			}
			return true;
		}catch(Exception e) {
			log.error("error,",e);
		}
		log.info("======================>压缩完成<===========================");
		return false;
	}

	
	public static boolean compressPic(Path source, Path target) throws IOException {
		return compressPic(source,  target, 1);
	}

//	public static void convertTif2Jpg(String url, Path source, Path target) {
//		Map<String, String> requestMap = new HashMap<String, String>();
//		String encode = Base64.encode(source.toFile());
//		requestMap.put("image", encode);
//		requestMap.put("fileName", source.getFileName().toString());
//		Entry<Boolean, String> post = post(url, JSON.toJSONString(requestMap), ContentType.APPLICATION_JSON);
//		if (post.getKey()) {
//			String value = post.getValue();
//			JSONObject parseObject = JSON.parseObject(value);
//			String jpgBase64 = parseObject.getString("image");
//			// String jpgName = parseObject.getString("imgName");
//			Base64.decodeToFile(jpgBase64, target.toFile());
//		} else {
//			throw new RuntimeException();
//		}
//	}
	
	public static void convertTif2Jpg(Path source, Path target) {
		try {
			ImgConvert.tiffTurnJpg(source.toString(), target.toString());
		} catch (Exception e) {
			throw new RuntimeException();
		}
	}
	
	public static Entry<Boolean, String> post(String url, String text, ContentType contentType, Header... headers) {
		StringEntity stringEntity = new StringEntity(text, contentType);
		return post(url, stringEntity, headers);
	}

	public static Entry<Boolean, String> post(String url, String text, ContentType contentType) {
		StringEntity stringEntity = new StringEntity(text, contentType);
		return post(url, stringEntity);
	}

	public static Entry<Boolean, String> post(String url, HttpEntity httpEntity, Header... headers) {
		try {
			CloseableHttpClient httpClient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(httpEntity);
			if (ArrayUtils.isNotEmpty(headers)) {
				httpPost.setHeaders(headers);
			}

			RequestConfig requestConfig = RequestConfig.custom() //
					.setSocketTimeout(60 * 1000) //
					.setConnectTimeout(60 * 1000).build();// 设置请求和传输超时间
			httpPost.setConfig(requestConfig);

			CloseableHttpResponse response = httpClient.execute(httpPost);
			HttpEntity entity = response.getEntity();
			boolean isSuccess = false;
			String result = EntityUtils.toString(entity, "UTF-8");
			if (response.getStatusLine().getStatusCode() == 200) {
				isSuccess = true;
			}
			Entry<Boolean, String> entry = new AbstractMap.SimpleEntry<>(isSuccess, result);
			response.close();
			httpClient.close();
			return entry;
		} catch (Exception e) {
			log.info("异常 -> {}", e.getMessage());
			log.error("异常 -> ", e);
			Entry<Boolean, String> entry = new AbstractMap.SimpleEntry<>(false, "接口异常未识别出结果！");
			if(e instanceof SocketTimeoutException) {
				entry.setValue("识别超时，请重试！");
			}
			return entry;
		}
	}

	public static Entry<Boolean, String> postOcr(String url, Path imagePath, String filename, String layout) {
		String encode = Base64.encode(imagePath.toFile());
		Map<String, String> requestMap = new HashMap<String, String>();
		requestMap.put("image", encode);
		requestMap.put("imgName", UnicodeUtil.toUnicode(filename));
		requestMap.put("layout", layout);
		String jsonString = JSON.toJSONString(requestMap);
		return post(url, jsonString, ContentType.APPLICATION_JSON);
	}
	
	public static Entry<Boolean, String> postOcr(String url, String imageBase64, String filename, String layout) {
		Map<String, String> requestMap = new HashMap<String, String>();
		requestMap.put("image", imageBase64);
		requestMap.put("imgName", UnicodeUtil.toUnicode(filename));
		requestMap.put("layout", layout);
		String jsonString = JSON.toJSONString(requestMap);
		return post(url, jsonString, ContentType.APPLICATION_JSON);
	}

	/**
	 * 
	 * @param images    ocr接口响应数据
	 * @param imagePath 图片文件路径, 当isInlay = true 时才会使用
	 * @param isInlay   是否嵌入图片(折校)
	 * @return
	 * @throws IOException
	 */
	public static Map<String, Object> convert(JSONObject images, Path imagePath, boolean isInlay) throws IOException {
		Map<String, Object> result = new HashMap<String, Object>();
		Integer width = images.getInteger("width");
		Integer height = images.getInteger("height");
		JSONObject referenceSignInstances = images.getJSONObject("referenceSignInstances");
		JSONObject top = referenceSignInstances.getJSONObject("天头");
		JSONObject layoutAnalysis = images.getJSONObject("layoutAnalysis");
		long totalWords = 0;
		if (layoutAnalysis.containsKey("天头")) {
			result.put("heavenPoints", layoutAnalysis.get("天头"));
			if (top.getJSONArray("original_position") instanceof JSONArray) {
				JSONArray heavenArray = top.getJSONArray("original_position");
				for (int i = 0; i < heavenArray.size(); i++) {
					JSONObject jsonObject = heavenArray.getJSONObject(i);
					totalWords += jsonObject.getString("text").length();
				}
				JSONArray processHeaven = processBody(heavenArray, 0);
				if (isInlay) {
					JSONArray processImageInlay = processImageInlay(processHeaven, imagePath);
					result.put("heavenContent", processImageInlay);
				} else {
					result.put("heavenContent", processHeaven);
				}
			}
		} else {
			result.put("heavenContent", null);
		}

		Object body = referenceSignInstances.get("正文");
		if (body instanceof JSONArray) {
			// 多列
			JSONArray bodyArray = (JSONArray) body;
			List<JSONObject> list = new ArrayList<JSONObject>();
			for (int i = 0; i < bodyArray.size(); i++) {
				JSONObject originalObj = bodyArray.getJSONObject(i);
				JSONArray originalPosition = originalObj.getJSONArray("original_position");
				if (originalPosition != null && originalPosition.size() > 0) {
					for (int j = 0; j < originalPosition.size(); j++) {
						JSONObject jsonObject = originalPosition.getJSONObject(j);
						totalWords += jsonObject.getString("text").length();
					}
					JSONArray processBody = processBody(originalPosition, 0);
					if (isInlay) {
						list.add(processImageInlayMultiColumn(processBody, imagePath));
					} else {
						list.add(processImageMultiColumn(processBody));
					}
				}
			}
			result.put("multiColumn", list);
		} else {
			// 整页
			JSONObject jsonObject = (JSONObject) body;
			JSONArray originalPosition = (JSONArray) jsonObject.get("original_position");
			if (originalPosition != null && originalPosition.size() > 0) {
				for (int i = 0; i < originalPosition.size(); i++) {
					// 当前索引值
					JSONObject currJSONObject = originalPosition.getJSONObject(i);
					totalWords += currJSONObject.getString("text").length();
				}
				JSONArray processBody = processBody(originalPosition, 1);
				if (isInlay) {
					JSONArray processImageSlice = processImageInlay(processBody, imagePath);
					result.put("content", processImageSlice);
				} else {
					result.put("content", processBody);
				}
			} else {
				result.put("content", null);
			}
		}
		Object table = referenceSignInstances.get("表格");
		if (table instanceof JSONArray) {
			JSONArray tableArray = (JSONArray) table;
			for (int i = 0; i < tableArray.size(); i++) {
				JSONObject jsonObject = tableArray.getJSONObject(i);
				JSONArray blocksJsonArray = jsonObject.getJSONArray("blocks");
				for (int k = 0; k < blocksJsonArray.size(); k++) {
					JSONObject blockJsonObject = blocksJsonArray.getJSONObject(k);
					JSONArray originalPositionJSONArray = blockJsonObject.getJSONArray("original_position");
					for (int k1 = 0; k1 < originalPositionJSONArray.size(); k1++) {
						JSONObject originalPositionJsonObject = originalPositionJSONArray.getJSONObject(k1);
						totalWords += originalPositionJsonObject.getString("text").length();
					}
				}
			}
			JSONArray processTable = processTable(tableArray);
			if (isInlay) {
				JSONArray processImageSliceTable = processImageInlayTable(processTable, imagePath);
				result.put("tableContent", processImageSliceTable);
			} else {
				result.put("tableContent", processTable);
			}
		} else {
			// 无
			result.put("tableContent", null);
		}
		result.put("totalWords", totalWords);
		result.put("width", width);
		result.put("height", height);
		return result;
	}

	/**
	 * 
	 * @param images    ocr接口响应数据
	 * @param imagePath 图片文件路径, 当isInlay = true 时才会使用
	 * @param isInlay   是否嵌入图片(折校)
	 * @return 
	 * @return
	 * @throws IOException
	 */
	public static JSONObject inlay(String resultContent, Path imagePath) throws IOException {
		if(StringUtils.isBlank(resultContent)) {
			throw new RuntimeException();
		}
		JSONObject result = JSON.parseObject(resultContent, Feature.OrderedField);
		BufferedImage bi = null;
		try {
			bi = ImageIO.read(imagePath.toFile());
		}catch(IllegalArgumentException e) {
			//出现异常
			// 1、直接转换
			File inputFile = imagePath.toFile();
			String newfilename = FilenameUtils.getBaseName(inputFile.getName())+"_jpg2jpg";
			String extend = FilenameUtils.getExtension(inputFile.getName());
			
			Path outPath = Paths.get(inputFile.getParent(), newfilename+"."+extend);
			try {
				Tif2JpgPythonUtils.tif2jpgHandler(inputFile.getAbsolutePath(), outPath.toFile().getAbsolutePath(), false);
			} catch (Exception e1) {
				log.error("errore1",e1);
			}
			bi = ImageIO.read(outPath.toFile());	
		}
		int w = bi.getWidth();
		int h = bi.getHeight();
		bk0: if (result.containsKey("heavenContent")) {
			JSONArray jsonArray = result.getJSONArray("heavenContent");
			if (jsonArray == null) {
				break bk0;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject obj = (JSONObject) jsonArray.get(i);
				JSONObject points = obj.getJSONObject("points");
				int x = points.getIntValue("left");
				int y = points.getIntValue("top");
				int width = obj.getIntValue("width");
				int height = obj.getInteger("orgin_height");
				if (width + x > w) {
					width = w - x;
				}
				if (height + y > h) {
					height = h - y;
				}
				String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
				obj.put("img", imgStr);
			}
		}

		bk1: if (result.containsKey("multiColumn")) {
			JSONArray jsonArray = result.getJSONArray("multiColumn");
			if (jsonArray == null) {
				break bk1;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject jsonObject = jsonArray.getJSONObject(i);
				JSONArray contentArray = jsonObject.getJSONArray("content");
				processImageInlayMultiColumn(contentArray, imagePath);
				for (int j = 0; j < jsonArray.size(); j++) {
					JSONObject obj = (JSONObject) jsonArray.get(j);
					JSONObject points = obj.getJSONObject("points");
					int x = points.getIntValue("left");
					int y = points.getIntValue("top");
					int width = obj.getIntValue("width");
					int height = obj.getInteger("orgin_height");
					if (width + x > w) {
						width = w - x;
					}
					if (height + y > h) {
						height = h - y;
					}
					String imgStr = subimage2Base64(bi, true, "1", j, 1, x, y, width, height);
					obj.put("img", imgStr);
				}
			}
		}

		bk2: if (result.containsKey("content")) {
			JSONArray jsonArray = result.getJSONArray("content");
			if (jsonArray == null) {
				break bk2;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject obj = (JSONObject) jsonArray.get(i);
				JSONObject points = obj.getJSONObject("points");
				int x = points.getIntValue("left");
				int y = points.getIntValue("top");
				int width = obj.getIntValue("width");
				int height = obj.getInteger("orgin_height");
				if (width + x > w) {
					width = w - x;
				}
				if (height + y > h) {
					height = h - y;
				}
				String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
				obj.put("img", imgStr);
			}

		}

		bk3: if (result.containsKey("tableContent")) {
			JSONArray jsonArray = result.getJSONArray("tableContent");
			if (jsonArray == null) {
				break bk3;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject obj = jsonArray.getJSONObject(i);
				JSONArray blocksArray = obj.getJSONArray("blocks");
				for (int j = 0; j < blocksArray.size(); j++) {
					JSONObject blocksObj = blocksArray.getJSONObject(j);
					JSONObject points = blocksObj.getJSONObject("points");
					String imgStr = subimage2Base64(bi, true, "1", i, 1, points.getIntValue("left"),
							points.getIntValue("top"), blocksObj.getIntValue("width"),
							blocksObj.getInteger("orgin_height"));
					blocksObj.put("img", imgStr);
				}
			}

		}
		return result;
	}
	
	public static Map<String, String> convert2Map(String resultContent) throws IOException {
		if(StringUtils.isBlank(resultContent)) {
			throw new RuntimeException();
		}
		JSONObject jsonObject = JSON.parseObject(resultContent, Feature.OrderedField);
		Map<String, String> result = new HashMap<String, String>();
		bk0: if (jsonObject.containsKey("heavenContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("heavenContent");
			if (jsonArray == null || jsonArray.isEmpty()) {
				break bk0;
			}
			result.put("heavenContent", jsonArray.toJSONString());
		}
		
		bk1: if (jsonObject.containsKey("multiColumn")) {
			JSONArray jsonArray = jsonObject.getJSONArray("multiColumn");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk1;
			}
			result.put("multiColumn", jsonArray.toJSONString());
		}
		
		bk2: if (jsonObject.containsKey("content")) {
			JSONArray jsonArray = jsonObject.getJSONArray("content");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk2;
			}
			result.put("content", jsonArray.toJSONString());
		}
		
		bk3: if (jsonObject.containsKey("tableContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("tableContent");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk3;
			}
			result.put("tableContent", jsonArray.toJSONString());
		}
		return result;
	}
	
	public static void removeImg(JSONArray jsonArray) {
		if(jsonArray == null) {
			return;
		}
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject item = (JSONObject) jsonArray.get(i);
			item.remove("img");
		}
	}
	
	public static void removeImgFurther(JSONArray jsonArray, String property) {
		if(jsonArray == null) {
			return;
		}
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject item = (JSONObject) jsonArray.get(i);
			JSONArray blocksArray = item.getJSONArray(property);
			for (int j = 0; j < blocksArray.size(); j++) {
				JSONObject further = blocksArray.getJSONObject(j);
				further.remove("img");
			}
		}
	}

	/**
	 * 多栏数据结果处理
	 * 
	 * @param json
	 * @param picPath
	 * @return
	 * @throws IOException
	 */
	private static JSONObject processImageMultiColumn(JSONArray jsonArray) throws IOException {
		JSONObject resultJSONObject = new JSONObject();
		JSONObject obj = null;
		Integer height = 0;
		Integer width = 0;
		for (int i = 0; i < jsonArray.size(); i++) {
			obj = (JSONObject) jsonArray.get(i);
			width = obj.getInteger("width");
			if (height < obj.getInteger("height")) {
				height = obj.getInteger("height");
			}
		}
		resultJSONObject.put("width", width);
		resultJSONObject.put("height", height);
		resultJSONObject.put("original_height", height);
		resultJSONObject.put("content", jsonArray);
		return resultJSONObject;
	}

	/**
	 * 多栏数据结果处理
	 * 
	 * @param json
	 * @param picPath
	 * @return
	 * @throws IOException
	 */
	private static JSONObject processImageInlayMultiColumn(JSONArray jsonArray, Path imagePath) throws IOException {
		JSONObject resultJSONObject = new JSONObject();
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		Integer height = 0;
		Integer width = 0;
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = (JSONObject) jsonArray.get(i);
			JSONObject points = obj.getJSONObject("points");
			int x = points.getIntValue("left");
			int y = points.getIntValue("top");
			width = obj.getInteger("width");
			height = obj.getInteger("orgin_height");
			if (width + x > w) {
				width = w - x;
			}
			if (height + y > h) {
				height = h - y;
			}
			String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
			obj.put("img", imgStr);
		}
		resultJSONObject.put("width", width);
		resultJSONObject.put("height", height);
		resultJSONObject.put("original_height", height);
		resultJSONObject.put("content", jsonArray);
		return resultJSONObject;
	}

	private static JSONArray processImageInlayTable(JSONArray jsonArray, Path imagePath) throws IOException {
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = jsonArray.getJSONObject(i);
			JSONArray blocksArray = obj.getJSONArray("blocks");
			for (int j = 0; j < blocksArray.size(); j++) {
				JSONObject blocksObj = blocksArray.getJSONObject(j);
				JSONObject points = blocksObj.getJSONObject("points");
				String imgStr = subimage2Base64(bi, true, "1", i, 1, points.getIntValue("left"),
						points.getIntValue("top"), blocksObj.getIntValue("width"),
						blocksObj.getInteger("orgin_height"));
				blocksObj.put("img", imgStr);
			}
		}
		return jsonArray;
	}

	public static JSONArray processImageInlay(JSONArray jsonArray, Path imagePath) throws IOException {
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = (JSONObject) jsonArray.get(i);
			JSONObject points = obj.getJSONObject("points");
			int x = points.getIntValue("left");
			int y = points.getIntValue("top");
			int width = obj.getIntValue("orgin_width");
			int height = obj.getInteger("orgin_height");
			if (width + x > w) {
				width = w - x;
			}
			if (height + y > h) {
				height = h - y;
			}
			String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
			obj.put("img", imgStr);
		}
		return jsonArray;
	}

	public static JSONArray processBody(String input, int baseLeftMargin) {
		JSONArray inputArray = JSON.parseArray(input);
		return processBody(inputArray, baseLeftMargin);
	}
	
	public static Map<String, Integer> fontSize(JSONArray inputArray) {
		try {
			FileUtils.write(Paths.get("D:\\usr\\tmp\\2023\\08\\18\\1.txt").toFile(), "《ZSK10092-0822》" + "\r\n", StandardCharsets.UTF_8, true);
		} catch (IOException e) {
			e.printStackTrace();
		}
		Map<String, Integer> result = new HashMap<String, Integer>();
		for (int i = inputArray.size() - 1; i >= 0; i--) {
			JSONObject column = inputArray.getJSONObject(i);
			JSONArray charlinesJsonArray = column.getJSONArray("charlines");
			for (int c = 0; c < charlinesJsonArray.size(); c++) {
				JSONObject chunk = charlinesJsonArray.getJSONObject(c);
				String fontSize = chunk.getString("font_size");
				if (StringUtils.equalsAny(fontSize, "large", "media")) {
					JSONArray wordsJSONArray = chunk.getJSONArray("texts");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if(BigwordUtil.isDouble(wordText)) {
							log.info("大字 - {}", wordText);
							try {
								FileUtils.write(Paths.get("D:\\usr\\tmp\\2023\\08\\18\\1.txt").toFile(), wordText + "\r\n", StandardCharsets.UTF_8, true);
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
						if (StringUtils.isBlank(wordText)) {
							continue;
						}
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						int w = wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0);
						if (result.containsKey("large_min")) {
							Integer large = result.get("large_min");
							int min = Math.min(large, w);
							result.put("large_min", min);
						} else {
							result.put("large_min", w);
						}
						if (result.containsKey("large_max")) {
							Integer large = result.get("large_max");
							int min = Math.max(large, w);
							result.put("large_max", min);
						} else {
							result.put("large_max", w);
						}
					}
				} else if (StringUtils.equals(fontSize, "small")) {
					JSONObject jsonObject = chunk.getJSONObject("texts");
					JSONArray wordsJSONArray = jsonObject.getJSONArray("small_print");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if(BigwordUtil.isDouble(wordText)) {
							log.info("大字 - {}", wordText);
							try {
								FileUtils.write(Paths.get("D:\\usr\\tmp\\2023\\08\\18\\1.txt").toFile(), wordText + "\r\n", StandardCharsets.UTF_8, true);
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
						if (StringUtils.isBlank(wordText)) {
							continue;
						}
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						int w = wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0);
						if (result.containsKey("small_min")) {
							Integer small = result.get("small_min");
							int min = Math.min(small, w);
							result.put("small_min", min);
						} else {
							result.put("small_min", w);
						}
						if (result.containsKey("small_max")) {
							Integer small = result.get("small_max");
							int max = Math.max(small, w);
							result.put("small_max", max);
						} else {
							result.put("small_max", w);
						}
					}

				}
			}
		}
		
		
		Integer lmin = result.get("large_min");
		Integer lmax = result.get("large_max");
		Integer smin = result.get("small_min");
		Integer smax = result.get("small_max");
		if (lmin == null && lmax == null) {
			lmin = 0;
			lmax = 0;
		} else if (lmin == null) {
			lmin = lmax;
		} else if (lmax == null) {
			lmax = lmin;
		}
		if (smin == null && smax == null) {
			smin = 0;
			smax = 0;
		} else if (smin == null) {
			smin = smax;
		} else if (smax == null) {
			smax = smin;
		}
		result.put("small", (smin + smax) / 2);
		result.put("large", (lmin + lmax) / 2);
		return result;
	}

	public static JSONArray processBody(JSONArray inputArray, int baseLeftMargin) {
		JSONArray resultArray = new JSONArray();
		Integer width = 0;
		//
		Integer prevRight = null;
		int offset = 0;
		int large = 0;
		int small = 0;
		Map<String, Integer> fontSizeM = fontSize(inputArray);
		// 倒序处理 从左至右
		for (int i = inputArray.size() - 1; i >= 0; i--) {
			JSONObject column = inputArray.getJSONObject(i);
			/* System.out.println(column); */
			JSONArray charlinePointsJsonArray = column.getJSONArray("points");
			JSONObject columnMap = new JSONObject();
			//
			int lineLeft = charlinePointsJsonArray.getJSONArray(0).getInteger(0);
			if (prevRight != null && lineLeft < prevRight) {
				int diff = lineLeft - prevRight;
				if (diff < baseLeftMargin) {
					offset += baseLeftMargin - diff;
				}
			}
			int lineRight = charlinePointsJsonArray.getJSONArray(1).getInteger(0);
			prevRight = lineRight;
			//
			JSONObject columnPointsMap = new JSONObject();
			columnPointsMap.put("top", charlinePointsJsonArray.getJSONArray(0).getInteger(1));
			columnPointsMap.put("leftOffset", charlinePointsJsonArray.getJSONArray(0).getInteger(0) + offset);
			columnPointsMap.put("left", charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnPointsMap.put("right", charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnPointsMap.put("bottom", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
//			columnPointsMap.put("right", charlinePointsJsonArray.getJSONArray(1).getInteger(0) - charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnMap.put("points", columnPointsMap);
			width = charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0);
			columnMap.put("width", width);
			columnMap.put("orgin_width", charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnMap.put("height", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
			columnMap.put("orgin_height", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
			columnMap.put("confidence", column.getBigDecimal("confidence"));
			columnMap.put("isTypePage", column.getBoolean("is_type_page"));

			List<JSONObject> wordOutputs = new ArrayList<JSONObject>();
			JSONArray charlinesJsonArray = column.getJSONArray("charlines");
			for (int c = 0; c < charlinesJsonArray.size(); c++) {
				JSONObject chunk = charlinesJsonArray.getJSONObject(c);
				String fontSize = chunk.getString("font_size");
				JSONArray chunkPointsJsonArray = chunk.getJSONArray("points");
				if (StringUtils.equalsAny(fontSize, "large", "media")) {
					JSONArray wordsJSONArray = chunk.getJSONArray("texts");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if (StringUtils.isBlank(wordText)) {
							continue;
						}
						int ceil = (int) Math.ceil(fontSizeM.get("large") * 0.9);
						int left = (width - ceil) / 2;
						JSONObject wordMap = new JSONObject();
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						JSONObject wordPointsMap = new JSONObject();
						wordPointsMap.put("top", wordPointsJsonArray.getJSONArray(0).getInteger(1)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(1));
						wordPointsMap.put("left", left); //);
								/* wordPointsJsonArray.getJSONArray(0).getInteger(0)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(0));*/
						wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("points", wordPointsMap);
						wordMap.put("color", chunk.getString("color"));
						wordMap.put("type", chunk.getString("font_size"));
						JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
						JSONObject resultTopk = getTopk(topkJsonObject);
						wordMap.put("confidence", resultTopk.get(wordText));
						wordMap.put("topk", resultTopk);// 从大到小排序
						wordMap.put("word", wordText);
						wordMap.put("new_word", false);
						wordMap.put("original_word", wordText);
						Entry<Integer, String> font = font(
								wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0),
								wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						// wordMap.put("height", font.getKey());
						if (font.getKey() > width) {
							columnMap.put("width", font.getKey());
						}

						// wordMap.put("width", font.getKey());
						wordMap.put("fontSize", ceil);//font.getKey());
						wordMap.put("scale", "(1,1)");//font.getValue());
						wordOutputs.add(wordMap);
					}
				} else if (StringUtils.equals(fontSize, "small")) {
					JSONObject jsonObject = chunk.getJSONObject("texts");
					String fontSizeType = jsonObject.getString("font_size_type");
					String type = "small";
					if (StringUtils.isNotBlank(fontSizeType)) {
						type = fontSizeType + type.substring(0, 1).toUpperCase() + type.substring(1);
					}
					String lr = "left";
					if(StringUtils.isNotBlank(fontSizeType)) {
						lr = fontSizeType;
					}
					JSONArray wordsJSONArray = jsonObject.getJSONArray("small_print");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if (StringUtils.isBlank(wordText)) {
							continue;
						}
						JSONObject wordMap = new JSONObject();
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						JSONObject wordPointsMap = new JSONObject();
						wordPointsMap.put("top", wordPointsJsonArray.getJSONArray(0).getInteger(1)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(1));
						if(StringUtils.equals(lr, "left")) {
							wordPointsMap.put("left", 0);
						}else {
							wordPointsMap.put("left", fontSizeM.get("small"));
						}
//						wordPointsMap.put("left", wordPointsJsonArray.getJSONArray(0).getInteger(0)
//								- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("points", wordPointsMap);
						wordMap.put("color", chunk.getString("color"));
						wordMap.put("type", type);
						JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
						JSONObject resultTopk = getTopk(topkJsonObject);
						wordMap.put("confidence", resultTopk.get(wordText));
						wordMap.put("topk", resultTopk);// 从大到小排序
						wordMap.put("word", wordText);
						wordMap.put("new_word", false);
						wordMap.put("original_word", wordText);

						Entry<Integer, String> font = font(
								wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0),
								wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						// wordMap.put("height", font.getKey());
						// wordMap.put("width", font.getKey());
						wordMap.put("height", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordMap.put("width", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						if (font.getKey() > width) {
							columnMap.put("width", font.getKey());
						}
						wordMap.put("fontSize", fontSizeM.get("small")); // font.getKey());
						wordMap.put("scale", "(1,1)");//font.getValue());
						wordOutputs.add(wordMap);
					}
				}
			}
			columnMap.put("words", wordOutputs);
			resultArray.add(columnMap);
		}
		// 将倒叙结果归正 从右至左
		Collections.reverse(resultArray);
		return resultArray;
	}

	public static JSONArray processTable(JSONArray inputArray) {
		JSONArray list = new JSONArray();
		List<Map<String, Object>> wordOutputs = null;
		List<Map<String, Object>> charlinesList = null;
		// 倒序处理 从左至右
		for (int i = 0; i < inputArray.size(); i++) {
			Map<String, Object> columnMap = new HashMap<String, Object>();
			JSONObject columnJSONObject = inputArray.getJSONObject(i);
			BigDecimal columnYmin = columnJSONObject.getBigDecimal("ymin");
			BigDecimal columnXmin = columnJSONObject.getBigDecimal("xmin");
			BigDecimal columnYmax = columnJSONObject.getBigDecimal("ymax");
			BigDecimal columnXmax = columnJSONObject.getBigDecimal("xmax");
			Map<String, Object> columnPointsMap = new HashMap<String, Object>();
			columnPointsMap.put("top", columnYmin);
			columnPointsMap.put("left", columnXmin);
			columnPointsMap.put("right", columnXmax.subtract(columnXmin));
			columnPointsMap.put("bottom", columnYmax.subtract(columnYmin));
			columnMap.put("points", columnPointsMap);
			columnMap.put("width", columnXmax.subtract(columnXmin));
			columnMap.put("orgin_width", columnXmax.subtract(columnXmin));
			columnMap.put("height", columnYmax.subtract(columnYmin));
			columnMap.put("orgin_height", columnYmax.subtract(columnYmin));
			List<Map<String, Object>> columnList = new ArrayList<Map<String, Object>>();
			columnMap.put("blocks", columnList);
			JSONArray blocksJsonArray = columnJSONObject.getJSONArray("blocks");
			for (int b = blocksJsonArray.size() - 1; b >= 0 ; b--) {
				JSONObject blockJSONObject = blocksJsonArray.getJSONObject(b);
				BigDecimal blockYmin = blockJSONObject.getBigDecimal("ymin");
				BigDecimal blockXmin = blockJSONObject.getBigDecimal("xmin");
				BigDecimal blockYmax = blockJSONObject.getBigDecimal("ymax");
				BigDecimal blockXmax = blockJSONObject.getBigDecimal("xmax");
				Map<String, Object> blockMap = new HashMap<String, Object>();
				Map<String, Object> blockPointsMap = new HashMap<String, Object>();
				blockPointsMap.put("top", blockYmin);
				blockPointsMap.put("left", blockXmin);
				blockPointsMap.put("right", blockXmax.subtract(blockXmin));
				blockPointsMap.put("bottom", blockYmax.subtract(blockYmin));
				blockMap.put("points", blockPointsMap);
				blockMap.put("width", blockXmax.subtract(blockXmin));
				blockMap.put("height", blockYmax.subtract(blockYmin));
				blockMap.put("orgin_height", blockYmax.subtract(blockYmin));
				blockMap.put("confidence", blockJSONObject.getBigDecimal("confidence"));
				blockMap.put("isTypePage", blockJSONObject.getBoolean("is_type_page"));
				blockMap.put("type", blockJSONObject.get("type"));// rowspan
				if (blockJSONObject.getInteger("rowspan") > 0) {
					blockMap.put("rowspan", blockJSONObject.getInteger("rowspan"));
				} else {
					blockMap.put("rowspan", null);
				}
				blockMap.put("area_number", blockJSONObject.get("area_number"));
				JSONArray orginsJsonArray = blockJSONObject.getJSONArray("original_position");
				charlinesList = new ArrayList<Map<String, Object>>();
				for (int o = 0; o < orginsJsonArray.size(); o++) {
					JSONObject orgin = orginsJsonArray.getJSONObject(o);
					JSONArray charlinesJsonArray = orgin.getJSONArray("charlines");
					Map<String, Object> charlinesMap = new HashMap<String, Object>();

					wordOutputs = new ArrayList<Map<String, Object>>();
					charlinesMap.put("words", wordOutputs);

					for (int c = 0; c < charlinesJsonArray.size(); c++) {
						JSONObject chunk = charlinesJsonArray.getJSONObject(c);
						String fontSize = chunk.getString("font_size");
						JSONArray chunkPointsJsonArray = chunk.getJSONArray("points");
						charlinesMap.put("width", chunk.get("width"));
						charlinesMap.put("height", chunk.get("height"));
						charlinesMap.put("top", chunk.get("top"));
						charlinesMap.put("left", chunk.get("left"));
						if (StringUtils.equalsAny(fontSize, "large", "media")) {
							JSONArray wordsJSONArray = chunk.getJSONArray("texts");
							for (int j = 0; j < wordsJSONArray.size(); j++) {
								JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
								String wordText = wordJSONObject.getString("word");
								if (StringUtils.isBlank(wordText)) {
									continue;
								}
								Map<String, Object> wordMap = new HashMap<String, Object>();
								JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
								Map<String, Object> wordPointsMap = new HashMap<String, Object>();
								wordPointsMap.put("top",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(1).subtract(blockYmin));
								wordPointsMap.put("left",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(0).subtract(blockXmin));
								wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("points", wordPointsMap);
								wordMap.put("color", chunk.getString("color"));
								wordMap.put("type", chunk.getString("font_size"));
								JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
								JSONObject resultTopk = getTopk(topkJsonObject);
								wordMap.put("confidence", resultTopk.get(wordText));
								wordMap.put("topk", resultTopk);// 从大到小排序
								wordMap.put("word", wordText);
								wordMap.put("new_word", false);
								wordMap.put("original_word", wordText);
								Entry<Integer, String> font = font(
										wordPointsJsonArray.getJSONArray(1).getInteger(0)
												- wordPointsJsonArray.getJSONArray(0).getInteger(0),
										wordPointsJsonArray.getJSONArray(3).getInteger(1)
												- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordMap.put("fontSize", font.getKey());
								wordMap.put("scale", font.getValue());
								wordOutputs.add(wordMap);
							}
						} else if (StringUtils.equals(fontSize, "small")) {
							JSONObject jsonObject = chunk.getJSONObject("texts");
							String fontSizeType = jsonObject.getString("font_size_type");
							String type = "small";
							if (StringUtils.isNotBlank(fontSizeType)) {
								type = fontSizeType + type.substring(0, 1).toUpperCase() + type.substring(1);
							}
							JSONArray wordsJSONArray = jsonObject.getJSONArray("small_print");
							for (int j = 0; j < wordsJSONArray.size(); j++) {
								JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
								String wordText = wordJSONObject.getString("word");
								if (StringUtils.isBlank(wordText)) {
									continue;
								}
								Map<String, Object> wordMap = new HashMap<String, Object>();
								JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
								Map<String, Object> wordPointsMap = new HashMap<String, Object>();
								wordPointsMap.put("top",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(1).subtract(blockYmin));
								wordPointsMap.put("left",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(0).subtract(blockXmin));
								wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("points", wordPointsMap);
								wordMap.put("color", chunk.getString("color"));
								wordMap.put("type", type);
								JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
								JSONObject resultTopk = getTopk(topkJsonObject);
								wordMap.put("confidence", resultTopk.get(wordText));
								wordMap.put("topk", resultTopk);// 从大到小排序
								wordMap.put("word", wordText);
								wordMap.put("new_word", false);
								wordMap.put("original_word", wordText);
								Entry<Integer, String> font = font(
										wordPointsJsonArray.getJSONArray(1).getInteger(0)
												- wordPointsJsonArray.getJSONArray(0).getInteger(0),
										wordPointsJsonArray.getJSONArray(3).getInteger(1)
												- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordMap.put("fontSize", font.getKey());
								wordMap.put("scale", font.getValue());
								wordOutputs.add(wordMap);
							}
						}
					}
					charlinesList.add(charlinesMap);

				}
				blockMap.put("charlines", charlinesList);
				columnList.add(blockMap);
			}
			list.add(columnMap);
		}

		return list;
	}

	private static Entry<Integer, String> font(int w, int h) {
		Integer key = w;
		String value = "(1, 1)";
		if (w < h) {
			key = h;
			double floor = Math.floor((Double.valueOf(w) / Double.valueOf(h)) * 10) / 10;
			if (floor < 0.8) {
				floor = 0.8;
			}
			value = "(" + floor + ", 1)";
		} else if (w > h) {
			key = w;
			double floor = Math.floor((Double.valueOf(h) / Double.valueOf(w)) * 10) / 10;
			value = "(1, " + floor + ")";
		}
		return new AbstractMap.SimpleEntry<>(key, value);
	}

	final static BigDecimal HUNDRED = new BigDecimal(100);

	public static JSONObject getTopk(JSONObject topkJsonObject) {
		Map<String, BigDecimal> topk = new LinkedHashMap<String, BigDecimal>();
		for (Entry<String, Object> entry : topkJsonObject.entrySet()) {
			topk.put(entry.getKey(), (BigDecimal) entry.getValue());
		}
		List<Entry<String, BigDecimal>> topkList = new ArrayList<>(topk.entrySet());
		topkList.sort(Entry.comparingByValue());
		Collections.reverse(topkList);
		JSONObject resultTopk = new JSONObject(true);
		for (Entry<String, BigDecimal> entry : topkList) {
			resultTopk.put(entry.getKey(), entry.getValue().multiply(HUNDRED));
		}
		return resultTopk;
	}

	public static String subimage2Base64(BufferedImage bi, boolean cutType, String imgKey, int each, Integer cutNum,
			int x, int y, final int imgWidth, final Integer imgHeight) throws IOException {
		ByteArrayOutputStream byOut = new ByteArrayOutputStream();
		try {
			if (x < 0) {
				x = 0;
			}
			if (y < 0) {
				y = 0;
			}
			Integer destHeight = imgHeight;
			Integer destWidth = imgWidth;

			if (cutType) {
				// 纵向切图
				destWidth = destWidth / cutNum;
			} else {
				// 横向切图
				destHeight = destHeight / cutNum;
			}
			// 用来处理最后一次的图片精度问题
			Integer w = destWidth;
			Integer h = destHeight;
			// 用来处理最后一次的图片精度问题
			for (Integer i = 0; i < cutNum; i++) {
				Image image;
				if (cutType) {
					// 纵向切割。
					if (cutNum - 1 == i) {
						w = destWidth;
						destWidth = imgWidth - i * destWidth;
					}
					image = bi.getSubimage(x + w * i, y, destWidth, destHeight);
				} else {
					if (cutNum - 1 == i) {
						h = destHeight;
						destHeight = imgHeight - i * destHeight;
					}
					image = bi.getSubimage(x, y + h * i, destWidth, destHeight);
				}
//              System.out.println("destWidth = " + destWidth + "，destHeight = " + destHeight);
				BufferedImage tag = new BufferedImage(destWidth, destHeight, BufferedImage.TYPE_INT_RGB);
				Graphics g = tag.getGraphics();
				g.drawImage(image, 0, 0, destWidth, destHeight, null);
				g.dispose();
				ImageIO.write(tag, "JPEG", byOut);
				// 将byte数组保存成本地文件或者上传到图床均可
				byte[] imgBytes = byOut.toByteArray();
				return Base64.encode(imgBytes);
			}

			return null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			byOut.close();
		}

	}
	
	
	public static Map<String, String> convert(String resultContent) throws IOException {
		if(StringUtils.isBlank(resultContent)) {
			throw new RuntimeException();
		}
		JSONObject jsonObject = JSON.parseObject(resultContent, Feature.OrderedField);
		Map<String, String> result = new HashMap<String, String>();
		bk0: if (jsonObject.containsKey("heavenContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("heavenContent");
			if (jsonArray == null || jsonArray.isEmpty()) {
				break bk0;
			}
			result.put("heavenContent", jsonArray.toJSONString());
		}
		
		bk1: if (jsonObject.containsKey("multiColumn")) {
			JSONArray jsonArray = jsonObject.getJSONArray("multiColumn");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk1;
			}
			result.put("multiColumn", jsonArray.toJSONString());
		}
		
		bk2: if (jsonObject.containsKey("content")) {
			JSONArray jsonArray = jsonObject.getJSONArray("content");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk2;
			}
			result.put("content", jsonArray.toJSONString());
		}
		
		bk3: if (jsonObject.containsKey("tableContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("tableContent");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk3;
			}
			result.put("tableContent", jsonArray.toJSONString());
		}
		return result;
	}

	public static boolean verifyMap(Map<String, String> map) {
		boolean flag = false;
		String headContent = map.get("heavenContent");
		if(headContent != null) {
			flag = true;
		}
		String multiContent = map.get("multiColumn");
		if(multiContent != null) {
			flag = true;
		}
		String bodyContent = map.get("content");
		if(bodyContent != null) {
			flag = true;
		}
		String tableContent = map.get("tableContent");
		if(tableContent != null) {
			flag = true;
		}
		return flag;
	}
}
