package cn.guliandigital.storage.storage.service.impl;


import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.guliandigital.common.config.HuangHeConfig;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.jsoup.Jsoup;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.BigwordUtil;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.TransferUtils;
import cn.guliandigital.common.utils.file.FileUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.storage.storage.domain.XmlBean;
import cn.guliandigital.storage.storage.domain.XmlBeanZuobiao;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * 解析xml工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class XmlToolZuobiaoUtils {

	
	
	
	
	/**
	 * 解析xml，获取标题与内容
	 * @param book
	 * @param
	 * @return
	 * @throws Exception
	 */
	public static Map<String,XmlBeanZuobiao> parseBookMenuContentXml(TProBooks book,String xmlPath) throws Exception {
		try {
									
			Map<String,String> zhuhaoRefMap = Maps.newHashMap();
			Map<String,XmlBeanZuobiao> pageNoMap = Maps.newHashMap();
						
			log.info("==>开始解析文件{}",xmlPath);
			
			File xmlFile = new File(xmlPath);
			if(!xmlFile.exists()) {
				throw new ServiceException("xml文件不存在");
			}
			String xmlStr = FileUtil.readFileToString(xmlFile, "UTF-8");

			xmlStr = StringUtils.remove(xmlStr, "xmlns=\"http://shangyuan/shuju_yuliao\"");
			xmlStr = StringUtils.remove(xmlStr, "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
			
			if(StringUtils.contains(xmlStr, "shuju_yuliao")) {
				throw new ServiceException("xml里含有shuju_yuliao");
			}
									
			Document document = DocumentHelper.parseText(xmlStr);
			
			Element root = document.getRootElement();//获取根元素
		    List<Element> childElements = root.elements();//
		    StringBuffer zwbuf = new StringBuffer();
		    		   		   		    
		    String picId = null;
		    String size = null;
		    
		    for(Element content : childElements) {
		    	
		    	String tagName = content.getName();
		    			    	
		    	if("table".equals(tagName)) {
		    		log.info("==>开始处理table标签......");
		    		
		    		String tableContent = parseTableTag(book, xmlPath, content, zwbuf, zhuhaoRefMap);				    		
		    		zwbuf.append(tableContent);
		    	}
		    			    	
		    	if(tagName.equals("插图")) {
	    			String picsrc = content.attributeValue("src");
	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            String uploadpath = HuangHeConfig.getUploadPath();
		            String extension = FilenameUtils.getExtension(picsrc);
		            String fileName = DateUtil.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
		            String thumbPath =uploadpath+File.separator+fileName;
		            File destFile = new File(FilenameUtils.normalize(thumbPath));
		            if(!destFile.getParentFile().exists()) {
		            	destFile.getParentFile().mkdirs();
		            	log.info("==>创建文件夹成功 {}",destFile.getAbsolutePath());
		            }
		           
		            if(srcFile.exists()) {
				    	log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
				    }else {
				    	//排查是否路径有问题
				    	if(StringUtils.contains(picsrc, "items")) {									    		
				    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
				    		log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    		if(!srcFile.exists()) {
				    			throw new ServiceException(picsrc+"文件不存在");
				    		}
				    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
				    	}
				    }
		          //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(), true), FilenameUtils.normalize(uploadpath, true));
				    String domain = "%domain";
				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
		            //log.info(httpUrl);
		            
		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("</img>");				            
		            
	    		}
		    	if("原书页面".equals(tagName)) {
		    		
		    		if(Strings.isNullOrEmpty(picId)) {
		    			log.info("==>第一个page标签，不做任何处理，继续");
		    		}else {
		    			log.info("==>page标签，做任何处理，继续");
		    			
		    			
		    			XmlBeanZuobiao zuobiao = new XmlBeanZuobiao();
		    			zuobiao.setPicId(picId);
		    			zuobiao.setSize(size);
		    			zuobiao.setText(zwbuf.toString());
		    			
		    			pageNoMap.put(picId, zuobiao);
		    			zwbuf.setLength(0);		    			
		    		}
		    		String ysym = content.getText();
		    		picId = StringUtil.trimAll(ysym);
		    		
		    		Attribute sizeAttri = content.attribute("size");
		    		size = sizeAttri.getText();
		    		log.info("==>{}={}",ysym, size);
		    	}
		    	
		    	if("段落".equals(tagName)) {	    
		    		//log.info("进入段落解析"+xmlPath+"====="+content);
		    		parseDuanluo(book, xmlPath, content, tagName, zwbuf, zhuhaoRefMap);
		    	}			    	
		    }
		    
		    XmlBeanZuobiao zuobiao = new XmlBeanZuobiao();
			zuobiao.setPicId(picId);
			zuobiao.setSize(size);
			zuobiao.setText(zwbuf.toString());
			
			pageNoMap.put(picId, zuobiao);
		   
		   		
		    log.info("pageNoMap={}", pageNoMap);
	        
			
			log.info("==>解析处理完成，生成菜单数据......");
			
		   
		    return pageNoMap;
		}catch(Exception e) {
			log.error("error,", e);
			throw e;
		}
	}
	
	
	
	
	public static TProBookMenu getParentMenu(TProBookMenu bookmenu , XmlBean bean) {
		TProBookMenu result = bookmenu;
		//从1级开始
		List<TProBookMenu> list = bookmenu.getChildren();
		for(TProBookMenu menu : list) {
			int level = menu.getLevel();
			int _level = bean.getLevel();
			//log.info("<==>level:"+menu.getMenuName()+"=="+level);
			//log.info("<==>_level:"+bean.getBiaoti()+"=="+_level);
			if(level < _level) {
				result = getParentMenu(menu , bean);
			}else {
				continue;
			}
		}
		//log.info("==>获取的上级："+result.getMenuName()+" ==>"+bean.getBiaoti());
		return result;
	}
	
	public static String parseTableTag(TProBooks book,String xmlPath,Element tableEle,
			StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		//log.info(tableEle.asXML());
		StringBuffer tablebuf = new StringBuffer();
		tablebuf.append("<table>");
				
		String row_xpath = tableEle.getUniquePath()+"/tgroup/tbody/row";
		List<Node> selectNodes_row = tableEle.selectNodes(row_xpath);
		//按行处理
		for (Node trnode : selectNodes_row) {		
			Element tr = (Element)trnode;
			//log.info(tr.asXML());
			//取出段落级别
			String tagName = "段落";
			tablebuf.append("<tr>");
			String td_xpath = tr.getUniquePath()+"/entry";
			List<Node> selectNodes_td = tableEle.selectNodes(td_xpath);
			for (Node tdnode : selectNodes_td) {				
				Element duanluo = (Element)tdnode;
				//log.info(duanluo.asXML());
				String rowspan = duanluo.attributeValue("rowspan");
				String colspan = duanluo.attributeValue("colspan");
				if(!Strings.isNullOrEmpty(rowspan)&&!Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td rowspan=\""+rowspan+"\" colspan=\""+colspan+"\">");
				}
				if(!Strings.isNullOrEmpty(rowspan)&&Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td rowspan=\""+rowspan+">\"");
				}
				if(Strings.isNullOrEmpty(rowspan)&&!Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td colspan=\""+colspan+"\">");
				}
				if(Strings.isNullOrEmpty(rowspan)&&Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td>");
				}
				//可能有多个段落
				List<Element> duanluos = duanluo.elements();
				for(Element _duanluo : duanluos) {
					String tagname = _duanluo.getName();
					if("段落".equals(tagname)) {
						parseDuanluo(book, xmlPath, _duanluo, tagName, tablebuf,zhuhaoRefMap);
					}else if("原书页面".equals(tagname)) {
						//log.info("==>");
						Element _e = (Element)_duanluo;
	    				String ysym = _e.getText();
	    				ysym = StringUtil.trimAll(ysym);
	    				_duanluo.setName("a");		    	
	    				 //删除多余属性
			    		Attribute deleteAttri = _duanluo.attribute("destsrc");
			    		if(deleteAttri != null) {
			    			_duanluo.remove(deleteAttri);
			    		}
	    				Attribute newattri = DocumentHelper.createAttribute(_duanluo, "class", "page");
	    				_duanluo.add(newattri);	 
	    				Attribute idattri = DocumentHelper.createAttribute(_duanluo, "id", ysym);
	    				_duanluo.add(idattri);	 
	    				//log.info(content.asXML());
	    				//增加调动函数
	    				String hanshu = "openReader('"+ysym+"')";
	    				Attribute clickatt = DocumentHelper.createAttribute(_duanluo, "onclick", hanshu);
	    				_duanluo.add(clickatt);	
	    				//log.info("==>原书页面{}，=={}",ysym,tr.asXML());
	    				
	    				//截取最后5位
	    				String ym = StringUtil.substring(ysym,ysym.length()-5);
	    				ym = StringUtil.trimAll(ym);
	    				ym = StringUtils.replace(ym, " ", "");
	    				int yamaInt = 0;
	    				try {
		    				yamaInt = Integer.parseInt(ym);			    				
	    				}catch(Exception e) {
	    					throw new ServiceException("原书页面"+ysym+"不符合规范");
	    				}
	    				_duanluo.setText("P"+String.valueOf(yamaInt));
	    				
	    				tablebuf.append(_duanluo.asXML());
					}
				}
				tablebuf.append("</td>");
				//log.info(tablebuf.toString());
			}	
						
			tablebuf.append("</tr>");
		}
		
		tablebuf.append("</table>");
		return tablebuf.toString();
	}
	
	
	/**
	 * 解析段落
	 * @param book
	 * @param xmlPath
	 * @param content
	 * @param tagName
	 * @param zwbuf
	 * @return
	 */
	public static String parseDuanluo(TProBooks book,String xmlPath,Element content,String tagName,StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		String uploadpath = HuangHeConfig.getUploadPath();
		if("段落".equals(tagName)) {	    		
    		
			zwbuf.append("<p>");
    		//可能会有多个正文
    		List<Element> duanluoelements = content.elements();
    		for(Element zw_element : duanluoelements) {
    			//正文中会有如下标签： 字体、原书页面、注释
    			//log.info("==>"+zw_element.asXML());
//    			if(zw_element.asXML().contains("成都處士廖吉人先生，凡事")) {
//    				log.info("=====");
//    			}
    			String duanTagName = zw_element.getName();
    			Attribute zwattri = zw_element.attribute("type");
    			String zwtype  = null;
    			if(zwattri != null) {
    				zwtype = zwattri.getValue();
    			}
    			String tempDiv = "";
    			//log.info("==>正文type={}",zwtype);
    			if(!Strings.isNullOrEmpty(zwtype)) {
    				if("正文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhengwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhengwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhengwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhengwen-jiao\">";
	    				}else {
//	    					if(StringUtil.in)
//	    					xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    					//tempDiv = "<span class=\"zhengwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhengwen\">";
	    				}
    				}
    				
    				if("注文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhuwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhuwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhuwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhuwen-jiao\">";
	    				}else {
	    					//tempDiv = "<span class=\"zhuwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhuwen\">";
	    				}
    				}
    				
    			}
    			zwbuf.append(tempDiv);
    			
    			if("注号".equals(duanTagName)){
    				String id = zw_element.attributeValue("id");   				
    				String refid = zw_element.attributeValue("refid");
    				String hexstr = zw_element.getText();      
    				if(Strings.isNullOrEmpty(refid)) {
    					
    					String _refid = zhuhaoRefMap.get(id);
    					if(Strings.isNullOrEmpty(_refid)) {
    						//说明是目标注
        					Element span = DocumentHelper.createElement("span");	    				
    	    				span.addAttribute("class", "zhuhao");   	    				
    	    				 				
    	    				span.addText(hexstr);

    	    				zw_element = null;
    	    				zwbuf.append(span.asXML());
    					}else {
    						Element atag = DocumentHelper.createElement("a");
        					atag.addAttribute("class", "zhuhao");
        					atag.addAttribute("id", id);
        					atag.addAttribute("href", "#"+_refid);
    	    				
    	    				atag.addText(hexstr);
    	    				
    	    				zw_element = null;
    	    				zwbuf.append(atag.asXML());
    					}
    					
    				}else {
    					
    					zhuhaoRefMap.put(refid, id);
    					
    					Element atag = DocumentHelper.createElement("a");
    					atag.addAttribute("class", "zhuhao");
    					atag.addAttribute("id", id);
    					atag.addAttribute("href", "#"+refid);
	    				
	    				atag.addText(hexstr);
	    				
	    				zw_element = null;
	    				zwbuf.append(atag.asXML());
    				}
				}
    			if(zw_element == null) {
    				continue;
    			}
    			if("正文".equals(duanTagName) || "注文".equals(duanTagName)) {
    				
//    				String xpath = zw_element.getUniquePath()+"/word";
//	    			//String xpath2 = zw_element.getUniquePath()+"/b/字体";
//	    			List<Node> selectNodes = zw_element.selectNodes(xpath);
//
//	    			for (Node node : selectNodes) {
//	    				Element _e = (Element)node;
//	    				String bigtext = _e.getText();
//	    				String pos = _e.attributeValue("pos");
//	    				String picpos = _e.attributeValue("picpos");
//	    				//log.info(node.getText()+","+ftype);
//	    				
//	    				Element span = DocumentHelper.createElement("span");
//	    				
//	    				String hexstr = null;
//	    				
//    					span.addAttribute("pos", pos);
//    					span.addAttribute("picpos", picpos);    					
//    					hexstr = BigwordUtil.hex2word(bigtext);
//		    					    				
//	    				span.addText(hexstr);
//	    				List elepar = node.getParent().content();
//	    				// 用content标签替换文本节点
//	    				elepar.set(elepar.indexOf(node), span);
//	    			}
	    			
	    			String xpathword = zw_element.getUniquePath()+"/字体";
	    			//String xpath2 = zw_element.getUniquePath()+"/b/字体";
	    			List<Node> selectNodesword = zw_element.selectNodes(xpathword);

	    			for (Node node : selectNodesword) {
	    				Element _e = (Element)node;
	    				String bigtext = _e.getText();
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				String zhuanming_xpath = _e.getUniquePath()+"/专名";
		    			List<Node> selectNodes_zhuanming = _e.selectNodes(zhuanming_xpath);
		    			for (Node _node : selectNodes_zhuanming) {		
		    				Element _e1 = (Element)_node;   				
		    			
		    				String tagname = _e1.getName();
		    					
		    				Element span = DocumentHelper.createElement("span");
		    				span.addAttribute("class", "zhuanming");
		    				String hexstr = _e1.getText();;
		    				
		    				span.addText(hexstr);
//		    				List elepar = _e1.getParent().content();
//		    				// 用content标签替换文本节点
//		    				elepar.set(elepar.indexOf(_e1), span);		    				
		    				bigtext = span.asXML();
		    			}
	    				Element span = DocumentHelper.createElement("span");
	    				
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}		
	    				}
	    				
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}
	    			String xpath2 = zw_element.getUniquePath()+"/b/字体";	    			
	    			List<Node> selectNodes = zw_element.selectNodes(xpath2);
	    			
	    			for (Node node : selectNodes) {
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				
	    				Element span = DocumentHelper.createElement("span");
	    				String bigtext = node.getText();
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}				    				
	    				}
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}    			
	    			
	    			
	    			String ysym_xpath = zw_element.getUniquePath()+"/原书页面";
	    			List<Node> selectNodes_ysym = zw_element.selectNodes(ysym_xpath);
	    			for (Node node : selectNodes_ysym) {
	    				Element _e = (Element)node;
	    				String yema = node.getText();
	    				yema = StringUtil.trimAll(yema);
	    				//截取最后5位
	    				String ym = StringUtil.substring(yema,yema.length()-5);
	    				ym = StringUtil.trimAll(ym);
	    				ym = StringUtils.replace(ym, " ", "");
	    				int yamaInt = 0;
	    				try {
		    				yamaInt = Integer.parseInt(ym);			    				
	    				}catch(Exception e) {
	    					throw new ServiceException("原书页面"+yema+"不符合规范");
	    				}
	    				
	    				Element pica = DocumentHelper.createElement("a");					    				
	    				pica.addAttribute("class", "page");
	    				pica.addAttribute("id", _e.getText());
	    				
	    				//增加调动函数
	    				String hanshu = "openReader('"+yema+"')";	    				
	    				pica.addAttribute("onclick", hanshu);	
	    				
	    				
	    				pica.addText("P"+String.valueOf(yamaInt));
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    					 
	    			String zhuhao_xpath = zw_element.getUniquePath()+"/注号";
	    			List<Node> selectNodes_zhuhao = zw_element.selectNodes(zhuhao_xpath);
	    			for (Node node : selectNodes_zhuhao) {
	    				Element _e = (Element)node;
	    				String hexstr = node.getText();
	    				
	    				String id = _e.attributeValue("id");
	    				String refid = _e.attributeValue("refid");
	    				if(Strings.isNullOrEmpty(refid)) {   						    					
	    						    			
	    					String _refid = zhuhaoRefMap.get(id);
	    					if(Strings.isNullOrEmpty(_refid)) {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("span");
			    				
			    				span.addAttribute("class", "zhuhao");
			    				span.addAttribute("id", ""+id);
			    				span.addAttribute("name", ""+id);
			    					    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);
	    					}else {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+_refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);    		
	    					}   					
	    					
	    				}else {	    					
	    					
	    					zhuhaoRefMap.put(refid, id);
	    					
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("a");
		    				
		    				span.addAttribute("class", "zhuhao");
			    			span.addAttribute("id", ""+id);
			    			span.addAttribute("href", "#"+refid);
		    				
		    				span.addText(hexstr);
		    				List elepar = node.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(node), span);    					
	    				}
	    			}
	    			
	    			String zhu_xpath = zw_element.getUniquePath()+"/注释";
	    			List<Node> selectNodes_zhu = zw_element.selectNodes(zhu_xpath);
	    			for (Node node : selectNodes_zhu) {		
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				String divtype = null;
	    				if("注".equals(ftype)) {
	    					divtype = "zhu";
	    				}else if("小注".equals(ftype)) {
	    					divtype = "xiaozhu";
	    				}else if("落款".equals(ftype)) {
	    					divtype = "luokuan";
	    				}else if("眉批".equals(ftype)) {
	    					divtype = "meipi";
	    				}else if("脚注-注".equals(ftype)) {
	    					divtype = "jiaozhu-zhu";
	    				}else if("脚注-校".equals(ftype)) {
	    					divtype = "jiaozhu-jiao";
	    				}else if("疏".equals(ftype)){
	    					divtype = "shu";
	    				}else {
	    					divtype = "zhu";
	    				}
	    				Element pica = DocumentHelper.createElement("span");
	    				pica.addAttribute("class", divtype);
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			String zhuanming_xpath = zw_element.getUniquePath()+"/专名";
	    			List<Node> selectNodes_zhuanming = zw_element.selectNodes(zhuanming_xpath);
	    			for (Node node : selectNodes_zhuanming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				//log.info(tagname);
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element _epage = (Element)bigelement;
		        				String ysym = _epage.getText();
		        				ysym = StringUtil.trimAll(ysym);
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trimAll(ym);
		        				ym = StringUtils.replace(ym, " ", "");
		        				int yamaInt = 0;
			    				try {
				    				yamaInt = Integer.parseInt(ym);			    				
			    				}catch(Exception e) {
			    					throw new ServiceException("原书页面"+ysym+"不符合规范");
			    				}
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zitu_e = (Element)bigelement;
			    				String picsrc = zitu_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
								log.info("FilenameUtils.getFullPath(xmlPath)==="+FilenameUtils.getFullPath(xmlPath));
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));

					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								//log.info("==>拷贝图片：{}",destPath);

							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }

							    if(srcFile.exists()) {
							    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }else {
							    	//排查是否路径有问题
							    	if(StringUtils.contains(picsrc, "items")) {									    		
							    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
							    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    		if(!srcFile.exists()) {
							    			throw new ServiceException(picsrc+"文件不存在");
							    		}
							    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
							    	}
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));

							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}		
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");	    				
	    				Attribute deleteAttri = _e.attribute("type");
    		    		if(deleteAttri != null) {
    		    			_e.remove(deleteAttri);
    		    		}
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String shuming_xpath = zw_element.getUniquePath()+"/书名";
	    			List<Node> selectNodes_shuming = zw_element.selectNodes(shuming_xpath);
	    			for (Node node : selectNodes_shuming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element b_e = (Element)bigelement;
		        				String ysym = b_e.getText();
		        				ysym = StringUtil.trimAll(ysym);
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trimAll(ym);
		        				ym = StringUtils.replace(ym, " ", "");
		        				int yamaInt = 0;
			    				try {
				    				yamaInt = Integer.parseInt(ym);			    				
			    				}catch(Exception e) {
			    					throw new ServiceException("原书页面"+ysym+"不符合规范");
			    				}
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zm_e = (Element)bigelement;
			    				String picsrc = zm_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
					            
					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
							    
							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }									   
							    if(srcFile.exists()) {
							    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }else {
							    	//排查是否路径有问题
							    	if(StringUtils.contains(picsrc, "items")) {									    		
							    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
							    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    		if(!srcFile.exists()) {
							    			throw new ServiceException(picsrc+"文件不存在");
							    		}
							    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
							    	}
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//							    String domain = picHttpUrl;
////							    if(Strings.isNullOrEmpty(domain)) {
////							    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////							    }
							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}	
		    				
		    					
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String zitu_xpath = zw_element.getUniquePath()+"/字图";
	    			List<Node> selectNodes_zitu = zw_element.selectNodes(zitu_xpath);
	    			for (Node node : selectNodes_zitu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			            
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//					    String domain = picHttpUrl;
////					    if(Strings.isNullOrEmpty(domain)) {
////					    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////					    }
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			//外字
	    			String waizi_xpath = zw_element.getUniquePath()+"/外字";
	    			List<Node> selectNodes_waizi = zw_element.selectNodes(waizi_xpath);
	    			for (Node node : selectNodes_waizi) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
						//log.info("获取资源的目录部分路径：{}===="+FilenameUtils.getFullPath(xmlPath));
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    			            
			           
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    File parentdir = destDir.getParentFile();
					    log.info("==>文件夹成功 {}",parentdir.getAbsolutePath());
					    log.info("==>exist={}",parentdir.exists());
					    if(!parentdir.exists()) {
					    	parentdir.mkdirs();					    	
					    	log.info("==>创建文件夹成功 {}",parentdir.getAbsolutePath());
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));

					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			
	    			String chatu_xpath = zw_element.getUniquePath()+"/插图";
	    			List<Node> selectNodes_chatu = zw_element.selectNodes(chatu_xpath);
	    			for (Node node : selectNodes_chatu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));

			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));

					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "chatu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    				    			
    			}
    			if("原书页面".equals(duanTagName)) {
    							    				
    				Element _e = (Element)zw_element;
    				String ysym = _e.getText();
    				log.info(ysym);
    				ysym = StringUtil.trimAll(ysym);
    				zw_element.setName("a");		 
    				//删除多余属性
		    		Attribute deleteAttri = zw_element.attribute("destsrc");
		    		if(deleteAttri != null) {
		    			zw_element.remove(deleteAttri);
		    		}
		    		
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
    				zw_element.add(newattri);	 
    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
    				zw_element.add(idattri);	 
    				//log.info(content.asXML());
    				String hanshu = "openReader('"+ysym+"')";
    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
    				zw_element.add(clickatt);	
    				//截取最后5位
    				String ym = StringUtil.substring(ysym,ysym.length()-5);
    				ym = StringUtil.trimAll(ym);
    				ym = StringUtils.replace(ym, " ", "");
    				int yamaInt = 0;
    				try {
	    				yamaInt = Integer.parseInt(ym);			    				
    				}catch(Exception e) {
    					throw new ServiceException("原书页面"+ysym+"不符合规范");
    				}
    				zw_element.setText("P"+String.valueOf(yamaInt));
    				//zwbuf.append(zw_element.asXML());	    			
    			}
    			//插图
    			if("插图".equals(duanTagName)) {
    				String picsrc = zw_element.attributeValue("src");
	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            //String uploadpath = baseParms.getUploadPath();
		            String extension = FilenameUtils.getExtension(picsrc);
		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
		            String thumbPath =uploadpath+File.separator+fileName;
		            File destFile = new File(FilenameUtils.normalize(thumbPath));
		            if(!destFile.getParentFile().exists()) {
		            	destFile.getParentFile().mkdirs();
		            }
		           
		            if(srcFile.exists()) {
				    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
				    }else {
				    	//排查是否路径有问题
				    	if(StringUtils.contains(picsrc, "items")) {									    		
				    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
				    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    		if(!srcFile.exists()) {
				    			throw new ServiceException(picsrc+"文件不存在");
				    		}
				    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
				    	}
				    }

		          //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));

				    String domain = "%domain";
				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           

		            zw_element = null;
		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("</img>");
		            
    			}
    			if(zw_element == null) {
    				continue;
    			}
    			if("注释".equals(duanTagName)) {
    				//log.info("==>"+zw_element.asXML());
    				String ftype = zw_element.attributeValue("type");
    				String divtype = null;
    				if("注".equals(ftype)) {
    					divtype = "zhu";
    				}else if("小注".equals(ftype)) {
    					divtype = "xiaozhu";
    				}else if("落款".equals(ftype)) {
    					divtype = "luokuan";
    				}else if("眉批".equals(ftype)) {
    					divtype = "meipi";
    				}else if("脚注-注".equals(ftype)) {
    					divtype = "jiaozhu-zhu";
    				}else if("脚注-校".equals(ftype)) {
    					divtype = "jiaozhu-jiao";
    				}else if("疏".equals(ftype)){
    					divtype = "shu";
    				}else {
    					divtype = "zhu";
    				}
    				
    				//删除原有的标签
    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
    				zw_element.remove(delattri);
    				
    				//增加新标签
    				zw_element.setName("span");
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
    				zw_element.add(newattri);		
    				
    				//log.info(zw_element.asXML());
    				/**
    				 * 可能会有大字
    				 */
    				List<Element> bigwordelements = zw_element.elements();
    				for(Element bigelement : bigwordelements) {
	    				String tagname = bigelement.getName();
	    				//log.info(tagname);
	    				if("注号".equals(tagname)){
		    				String id = bigelement.attributeValue("id");
		    				String refid = bigelement.attributeValue("refid");
		    				String hexstr = bigelement.getText();
		    				
		    				if(Strings.isNullOrEmpty(refid)) {   						    					
		    						    			
		    					String _refid = zhuhaoRefMap.get(id);
		    					if(Strings.isNullOrEmpty(_refid)) {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("span");
				    				
				    				span.addAttribute("class", "zhuhao");
				    				span.addAttribute("id", ""+id);
				    				span.addAttribute("name", ""+id);
				    					    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);
		    					}else {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+_refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    		
		    					}   					
		    					
		    				}else {	    					
		    					
		    					zhuhaoRefMap.put(refid, id);
		    					
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);    					
		    				}
		    				
	    				}
	    				
	    				if("字体".equals(tagname)){
		    				String _ftype = bigelement.attributeValue("type");
		    				//log.info(node.getText()+","+ftype);
		    				
		    				Element span = DocumentHelper.createElement("span");
		    				String hexstr = null;
		    				if(Strings.isNullOrEmpty(_ftype)) {
		    					span.addAttribute("class", "no");
		    					hexstr = bigelement.getText();
		    				}else {
			    				if(_ftype.contains("超大字2")) {
			    					span.addAttribute("class", "big02");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字3")){
			    					span.addAttribute("class", "big15");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字1")) {
			    					span.addAttribute("class", "big01");
			    					hexstr = zw_element.getText();
			    					hexstr = BigwordUtil.hex2word(hexstr);
			    				}else {
			    					span.addAttribute("class", _ftype);
			    					hexstr = bigelement.getText();
			    				}
		    				}
		    				
		    				span.addText(hexstr);
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), span);
	    				}
	    				//log.info(zw_element.asXML());
	    				if("原书页面".equals(tagname)) {
				    		String ysym = bigelement.getText();
				    		ysym = StringUtil.trimAll(ysym);
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		bigelement.setName("a");		
				    		//删除多余属性
				    		Attribute deleteAttri = bigelement.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			bigelement.remove(deleteAttri);
				    		}
				    		
		    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		    				bigelement.add(zspageattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		    				bigelement.add(idattri);	 
		    				//log.info(content.asXML());
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		    				bigelement.add(clickatt);	
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trimAll(ym);
		    				ym = StringUtils.replace(ym, " ", "");
		    				int yamaInt = 0;
		    				try {
			    				yamaInt = Integer.parseInt(ym);			    				
		    				}catch(Exception e) {
		    					throw new ServiceException("原书页面"+ysym+"不符合规范");
		    				}
		    				bigelement.setText("P"+String.valueOf(yamaInt));
		    				//zwbuf.append(bigelement.asXML());
				    	}
	    				//插图
	        			if("插图".equals(tagname)) {
	        				String picsrc = bigelement.attributeValue("src");
	    	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
	    		            //String uploadpath = baseParms.getUploadPath();
	    		            String extension = FilenameUtils.getExtension(picsrc);
	    		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
	    		            String thumbPath =uploadpath+File.separator+fileName;
	    		            File destFile = new File(FilenameUtils.normalize(thumbPath));
	    		            if(!destFile.getParentFile().exists()) {
	    		            	destFile.getParentFile().mkdirs();
	    		            }
	    		           
	    		            if(srcFile.exists()) {
						    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
						    	}
						    }

	    		          //组装http链接
	    				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));
//	    				    String domain = picHttpUrl;
////	    				    if(Strings.isNullOrEmpty(domain)) {
////	    				    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////	    				    }
	    				    String domain = "%domain";
	    				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
	    		            //log.info(httpUrl);
	        				Element pica = DocumentHelper.createElement("img");
	        				pica.addAttribute("src", httpUrl);
	        				pica.addText(bigelement.getText());
	        				List elepar = bigelement.getParent().content();
	        				// 用content标签替换文本节点
	        				elepar.set(elepar.indexOf(bigelement), pica);          
//	    		            log.info(content.asXML());
	    		            //删除该标签
	    		            //content.remove(zw_element);
//	    				    bigelement = null;
//	    		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("");
	    		            
	        			}
	        			
	    				//log.info(bigelement.asXML());
	    				if("字图".equals(tagname)||"外字".equals(tagname)) {
		    				Element _e = (Element)bigelement;
		    				String picsrc = _e.attributeValue("src");
		    				//转换为http:// 形式
		    				String httpUrl = null;
		    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		    				//log.info("src:"+srcFile.getAbsolutePath());
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            //String uploadpath = baseParms.getUploadPath();
				            
				            
				            uploadpath = FilenameUtils.normalize(uploadpath);
				            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
						    
						    File destDir = new File(destPath);
						    if(!destDir.getParentFile().exists()) {
						    	destDir.getParentFile().mkdirs();
						    }									   
						    if(srcFile.exists()) {
						    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
						    	}
						    }
						    
						    //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//						    String domain = picHttpUrl;
////						    if(Strings.isNullOrEmpty(domain)) {
////						    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////						    }
						    String domain = "%domain";
						    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
		    				Element pica = DocumentHelper.createElement("img");
		    				pica.addAttribute("src", httpUrl);
		    				pica.addAttribute("class", "zitu");
		    				pica.addText(bigelement.getText());
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), pica);		    			
	    				}	
	    				
	    				if("书名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");			    				
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trimAll(ysym);
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trimAll(ym);
				    				ym = StringUtils.replace(ym, " ", "");
				    				int yamaInt = 0;
				    				try {
					    				yamaInt = Integer.parseInt(ym);			    				
				    				}catch(Exception e) {
				    					throw new ServiceException("原书页面"+ysym+"不符合规范");
				    				}
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						            
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }else {
								    	//排查是否路径有问题
								    	if(StringUtils.contains(picsrc, "items")) {									    		
								    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
								    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    		if(!srcFile.exists()) {
								    			throw new ServiceException(picsrc+"文件不存在");
								    		}
								    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
								    	}
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//								    String domain = picHttpUrl;
////								    if(Strings.isNullOrEmpty(domain)) {
////								    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////								    }
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
		    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("专名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trimAll(ysym);
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trimAll(ym);
				    				ym = StringUtils.replace(ym, " ", "");
				    				int yamaInt = 0;
				    				try {
					    				yamaInt = Integer.parseInt(ym);			    				
				    				}catch(Exception e) {
				    					throw new ServiceException("原书页面"+ysym+"不符合规范");
				    				}
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						           
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }else {
								    	//排查是否路径有问题
								    	if(StringUtils.contains(picsrc, "items")) {									    		
								    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
								    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    		if(!srcFile.exists()) {
								    			throw new ServiceException(picsrc+"文件不存在");
								    		}
								    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
								    	}
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//								    String domain = picHttpUrl;
////								    if(Strings.isNullOrEmpty(domain)) {
////								    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////								    }
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
			    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute deleteAttri = _e.attribute("type");
	    		    		if(deleteAttri != null) {
	    		    			_e.remove(deleteAttri);
	    		    		}
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}	
    				}
    			}				    					    			
    			if(zw_element != null) {
	    			String xmlstr = zw_element.asXML();
	    			xmlstr = StringUtil.replace(xmlstr, "&lt;", "<");
	    			xmlstr = StringUtil.replace(xmlstr, "&gt;", ">");
	    			
	    			//log.info(xmlstr);
	    			xmlstr = StringUtil.remove(xmlstr, "<正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"\">");	    			
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"引文\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"注\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"校\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"疏\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<注文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.remove(xmlstr, "</注文>");
	    			xmlstr = StringUtil.remove(xmlstr, "</正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"2\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文/>");   			
	    			zwbuf.append(xmlstr);
    			}
    			if(!Strings.isNullOrEmpty(tempDiv)) {
	    			if(tempDiv.indexOf("<span class=\"zhengwen-") != -1||tempDiv.indexOf("<span class=\"zhuwen-") != -1) {
	    				//xmlstr = xmlstr +"</div>";
	    				zwbuf.append("</span>");
	    			}
    			}
    			
    		}
    		zwbuf.append("</p>");
    		
    	}
		//log.info("==>解析结果：{}",zwbuf.toString());
		return zwbuf.toString();
	}
	
	
	public static String findLastPage(String str) {
		String result = null;
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		    	   
	    while(m.find()){	      
	       String pagestr = m.group();       
	       result = pagestr;
	    }	
		
		return result;
	}
	
	public static String findLastPageNoTag(String str) {
		String result = null;
		String patstr = "(P[0-9]*)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		    	   
	    while(m.find()){	      
	       String pagestr = m.group();       
	       result = pagestr;
	    }	
		
		return result;
	}
	

	
	public static String  removePage(String content) {
		//String patstr = "(P[0-9]*)";
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(content);		    
	    while(m.find()){	 
	    	content = m.replaceAll("");	    	
	    }
	    return content;
	}
	
	public static String removeBigwordStr(String str,String classname) {

		String pattern= "(<span class=\""+classname+"\">[\u4e00-\u9fff]{1,2}</span>)";
		Pattern r = Pattern.compile(pattern); 
		
		int laststart = 0;
		int lastend = 0;
		Matcher m = r.matcher(str);
		while (m.find()) { 
			String pattStr = m.group();
			//System.out.println(pattStr);
			String _pattStr = StringUtil.replace(pattStr, "<span class=\""+classname+"\">", "");
			String bigword = StringUtil.replace(_pattStr, "</span>", "");
			
			str = StringUtil.replace(str, pattStr, bigword);
		}
		
		return str.toString();
	}
	
	
	
	
	/**
	 * 格式化html
	 * @param xmlcontent
	 * @return
	 */
	public static String formatHtml(String xmlcontent) {
		if(Strings.isNullOrEmpty(xmlcontent)) {
			return xmlcontent;
		}
//		xmlcontent = StringUtil.replace(xmlcontent, "<div class=\"mlbt1\">", "<p>");
//		xmlcontent = StringUtil.replace(xmlcontent, "<div class=\"mlbt2\">", "<p>");
//		xmlcontent = StringUtil.replaceAll(xmlcontent, "<div class=\"mlbt[\\d]+\">", "<p>");
		
		Pattern p1 = Pattern.compile("<div class=\"mlbt[1-9]+\">");
		Matcher m1 = p1.matcher(xmlcontent);		
		// 先找出不配对得标签，记录位置下表
		while (m1.find()) {
			String tag = m1.group();
			log.info(tag);
			if(StringUtils.contains(tag, "mlbt1")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti1\">");
			}else if(StringUtils.contains(tag, "mlbt2")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti2\">");
			}else if(StringUtils.contains(tag, "mlbt3")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti3\">");
			}else if(StringUtils.contains(tag, "mlbt4")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti4\">");
			}else if(StringUtils.contains(tag, "mlbt5")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti5\">");
			}else if(StringUtils.contains(tag, "mlbt6")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti6\">");
			}
		}
		
		xmlcontent = StringUtil.replace(xmlcontent, "</div>", "</p>");
		
		//xmlcontent = StringUtil.replaceAll(xmlcontent, "<div class=\"biaoti[\\d]+\">", "<p>");
		//正则匹配标题
		Pattern p = Pattern.compile("<div class=\"biaoti[1-9]+\">");
		Matcher m = p.matcher(xmlcontent);		
		// 先找出不配对得标签，记录位置下表
		while (m.find()) {
			String tag = m.group();
			log.info(tag);
			if(StringUtils.contains(tag, "biaoti1")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti1\">");
			}else if(StringUtils.contains(tag, "biaoti2")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti2\">");
			}else if(StringUtils.contains(tag, "biaoti3")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti3\">");
			}else if(StringUtils.contains(tag, "biaoti4")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti4\">");
			}else if(StringUtils.contains(tag, "biaoti5")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti5\">");
			}else if(StringUtils.contains(tag, "biaoti6")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti6\">");
			}
		}
			
		
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号\">", "<span class=\"zhuozhonghao\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号1\">", "<span class=\"zhuozhonghao1\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号2\">", "<span class=\"zhuozhonghao2\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号3\">", "<span class=\"zhuozhonghao3\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号4\">", "<span class=\"zhuozhonghao4\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号5\">", "<span class=\"zhuozhonghao5\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"超大字1\">", "<span class=\"big01\">");
		//temp_result = StringUtil.replace(temp_result, "<span class=\"楷体\">", "<span class=\"kaiti\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"楷体\">", "");
		//去掉表格
		xmlcontent = StringUtil.replace(xmlcontent, "<table>","");
    	xmlcontent = StringUtil.replace(xmlcontent, "</table>","");
    	xmlcontent = StringUtil.replace(xmlcontent, "<tr>","");	        	
    	xmlcontent = StringUtil.replace(xmlcontent, "</tr>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td rowspan=[0-9]{1,2} colspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td rowspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td colspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replace(xmlcontent, "</td>","");
		xmlcontent = StringUtil.replace(xmlcontent, "<td>","");

		//log.info("==>"+xmlcontent);	
		return xmlcontent;
	}
	
	public static String getPageHtmlContent(List<String> pageTaglist, int i, String xmlcontent,String pageContent) {
		String pageStr = pageTaglist.get(i);
		String direct = "both";
    	if(i == 0) {
    		direct = "after";
    		if(pageTaglist.size() > 1) {
    			pageStr = pageTaglist.get(i+1);
    		}else {
    			direct = "no";
    		}
    	}
    	if(i == pageTaglist.size() -1) {
    		direct = "before";
    	}
    	String pagehtmlstr = completeHtmlTag(xmlcontent , pageStr, direct, pageContent);
    	//log.info("==>"+pagehtmlstr);	 
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p></p>","</p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p><p>","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p class=\"bt\"><p class=\"bt\">","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<span class=\"zhu\"/></span>","");
    	if(StringUtil.endsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p><p class=\"bt\">","<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p>","");
    	}
    	if(StringUtil.endsWith(pagehtmlstr, "<tr>")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<tr>");
    	}
    	if(!StringUtil.endsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = pagehtmlstr + "</p>";
    	}
    	pagehtmlstr = StringUtil.replaceAll(pagehtmlstr, "(<p class=\"bt\">){2,10}","<p class=\"bt\">");
    	if(StringUtils.equalsAny(pagehtmlstr, "<p>","</p>")) {
    		pagehtmlstr = "<p></p>";
    	}
    	
    	return pagehtmlstr;
	}
	
	public static String lastFormat(String pagehtmlstr) {
		
    	//log.info("==>"+pagehtmlstr);	 
		int lastlen = 0;
		while(true) {
			pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p></p>","</p>");
			int len = pagehtmlstr.length();
			if(lastlen == len) {
				break;
			}
			lastlen = len;
		}
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p><p>","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p class=\"bt\"><p class=\"bt\">","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<span class=\"zhu\"/></span>","");
    	if(StringUtil.endsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p><p class=\"bt\">","<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p>","");
    	}
    	if(StringUtil.endsWith(pagehtmlstr, "<tr>")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<tr>");
    	}
    	if(!StringUtil.endsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = pagehtmlstr + "</p>";
    	}
    	pagehtmlstr = StringUtil.replaceAll(pagehtmlstr, "(<p class=\"bt\">){2,10}","<p class=\"bt\">");
    	if(StringUtils.equalsAny(pagehtmlstr, "<p>","</p>")) {
    		pagehtmlstr = "<p></p>";
    	}
    	
    	return pagehtmlstr;
	}
	/**
	 * 
	 * @param menuHtmlStr 本章节的全部html内容
	 * @param pageStr  页码
	 * @param direct
	 * @param pageContent
	 * @return
	 * @throws Exception
	 */
	private static String completeHtmlTag(String menuHtmlStr,String pageStr, String direct, String pageContent) {
		String result = "";
		
		if(StringUtil.equals(direct, "no")) {
			return pageContent;
		}
		if(StringUtil.equals(direct, "before")) {
	
			String _str = StringUtil.substringBefore(menuHtmlStr, pageStr);
			_str = removePage(_str);
			//log.info("_str="+_str);			
			
			List<String> beforeclosetags = lastUncloseTags(_str);
			//log.info(StringUtil.join(beforeclosetags, "|||||"));
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = pageContent;
			}else {
				result = StringUtil.join(beforeclosetags,"") + pageContent;
				if(StringUtils.startsWith(result.toString(), "<p class=\"bt\"")) {
					result = StringUtils.replace(result, "<p class=\"bt\"", "<p");
				}
			}
			
		}else if(StringUtil.equals(direct, "after")) {			

			List<String> afterclosetags = lastUncloseTags(pageContent);
			StringBuilder buff = new StringBuilder();
			Collections.reverse(afterclosetags);
			for(String tag : afterclosetags) {
				if(StringUtil.startsWith(tag, "<p")) {
					buff.append("</p>");
				}
				if(StringUtil.startsWith(tag, "<tr")) {
					buff.append("</tr>");
				}
				if(StringUtil.startsWith(tag, "</tr")) {
					buff.append("<tr>");
				}
				if(StringUtil.startsWith(tag, "<td")) {
					buff.append("</td>");
				}
				if(StringUtil.startsWith(tag, "<span")) {
					buff.append("</span>");
				}
			}
			result =  pageContent + buff.toString();
			
		}else {
//			if(StringUtil.contains(pageStr, "L00007")) {
//				log.info("debug");
//			}
			//中间部分处理
			String _str = StringUtil.substringBefore(menuHtmlStr, pageStr);
			_str = removePage(_str);
			//log.info("_str="+_str);		
			List<String> beforeclosetags = lastUncloseTags(_str);
			//log.info(StringUtil.join(beforeclosetags, "|||||"));
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = "";
			}else {
				result = StringUtil.join(beforeclosetags,"");
				if(StringUtils.startsWith(result.toString(), "<p class=\"bt\"")) {
					result = StringUtils.replace(result, "<p class=\"bt\"", "<p");
				}
			}
			//本身
			List<String> closetags = lastUncloseTags(pageContent);
			//log.info(StringUtil.join(closetags, "|||||"));
			StringBuilder buff = new StringBuilder();
			//Collections.sort(closetags, Collections.reverseOrder());
			Collections.reverse(closetags);
			for(String tag : closetags) {
				if(StringUtil.startsWith(tag, "<span")) {
					buff.append("</span>");
				}
				if(StringUtil.startsWith(tag, "<p")) {
					buff.append("</p>");
				}				
			}
			String lostTags = StringUtil.join(closetags, "");
			
			result =  result + pageContent + buff.toString();
			//再次校验
			List<String> checktags = lastUncloseTags(result);
			if(checktags.size() == 0) {
				return result;
			}
			
			//后面
			String _str1 = StringUtil.substringAfter(menuHtmlStr, pageStr);
			_str1 = removePage(_str1);
			//log.info("_str1="+_str1);		
			List<String> afterclosetags = lastUncloseTags(_str1);
			//log.info(StringUtil.join(afterclosetags, "|||||"));
			//中间和最后比较 是否一致
			String lost2Tags = StringUtil.join(afterclosetags, "");//lost2Tags = </span></p>  
			for(String tags1 : closetags) {
				afterclosetags.remove(tags1);
			}
			
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = result;
			}else {
				result = result + StringUtil.join(afterclosetags,"");	
			}
		}
		
		return result;
	}
	
	/**
	 * 
	 * @param htmlStr
	 * @return
	 */
	private static List<String> lastUncloseTags(String htmlStr){
		List<String> list = Lists.newCopyOnWriteArrayList();
		Stack<String> statck = new Stack<String>();
		
		//Pattern p = Pattern.compile("<\\/[a-zA-Z]*>");
		//Pattern p = Pattern.compile("<p>|<span class=\"[a-zA-Z0-9]*\"[^<>]*>|</span>|</p>");
		Pattern p = Pattern.compile("<p>|<tr>|<td>|</tr>|</td>|<p class=\"bt\">|<span class=\"[a-zA-Z0-9]*\"[^<>]*>|<span>|</span>|</p>");
		Matcher m = p.matcher(htmlStr);		
		// 先找出不配对得标签，记录位置下表
		while (m.find()) {
			String tag = m.group();
			//System.out.println(tag);
			if(StringUtil.startsWith(tag, "<")&&!StringUtil.startsWith(tag, "</")) {
				statck.add(tag);
			}
			if(StringUtil.startsWith(tag, "</")) {	
				if(!statck.empty()) {
					String tags = statck.pop();
					boolean isdelete = list.remove(tags);
					if(!isdelete) {
						list.add(tag);
					}
				}else {
					list.add(tag);
				}
			}else {
				list.add(tag);
			}
		}
		return list;
	}

	public static Element element(Element e, String...ename) {
		for (String name : ename) {
			Element element = e.element(name);
			if(element != null) {
				return element;
			}
		}
		return null;
	}
	
	public static String formatP(String enccontent) {
		if(Strings.isNullOrEmpty(enccontent)) {
			return enccontent;
		}
		if(StringUtils.startsWith(enccontent, "<p></p>")) {
      	  enccontent = StringUtil.replaceOnce(enccontent, "<p></p>", "");
        }
		if(StringUtils.startsWith(enccontent, "</p></p>")) {
	      	  enccontent = StringUtil.replace(enccontent, "</p></p>", "</p>");
	        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti1\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti1\"><p>", "<p class=\"biaoti1\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti2\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti2\"><p>", "<p class=\"biaoti2\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti3\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti3\"><p>", "<p class=\"biaoti3\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti4\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti4\"><p>", "<p class=\"biaoti4\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti5\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti5\"><p>", "<p class=\"biaoti5\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti6\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti6\"><p>", "<p class=\"biaoti6\">");
        }
        
        return enccontent;
	}
	
	
	public static void main(String[] args) throws Exception {
		
		
		String xmlpath1 = "C:\\Users\\<USER>\\Desktop\\json2xml\\DJB00005 平叛記二卷\\test.xml";		
		TProBooks book = new TProBooks();		
		book.setId("123328493483948");		
		book.setBookName("黨員讀本");
	
		parseBookMenuContentXml(book,xmlpath1);

	}


}
