package cn.guliandigital.storage.storage.service.impl;

import java.io.File;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.constant.Constants;
import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.CheckSumUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.file.Tif2JpgPythonUtils;
import cn.guliandigital.common.utils.file.TifCompressPythonUtils;
import cn.guliandigital.product.book.service.IPicService;
import cn.guliandigital.storage.storage.service.IAsyncEncPicsService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/10/28 21:40
 * 
 */
@Slf4j
//@Transactional
@Service("asyncEncPicsService")
public class AsyncEncPicsServiceImpl implements IAsyncEncPicsService {

	
	
	@Autowired
    private RedisCache redisCache;
	
	@Autowired
	private IPicService picService;
	
	
	
	//@Async(value="picencodeExecutor")
	@Override
	public void encImages(String imagePath) {
		log.info("==>暂时是空方法......");
		String localPath = HuangHeConfig.getUploadPath();
		String imagesPath = imagePath;
		log.info("==>转换tif图书   {}",imagesPath);
		String outPath = FilenameUtils.normalize(localPath + File.separator + "tif2jpg"+ File.separator);
		File ff = new File(outPath);
		if (!ff.exists()) {
			return;
		}
		File dir = new File(imagesPath);
		if(dir.isDirectory()) {			
			File[] files = dir.listFiles();
			int pictotal = files.length;
			int picindex = 0;
			for(File file : files) {
				try {
					picindex++;
					String basename = FilenameUtils.getBaseName(file.getName());
					if(StringUtil.equalsAnyIgnoreCase(basename, "thumbs")) {
						continue;
					}
					log.info("==>转换图书，图片进度:{}->{}  picpath={}", picindex, pictotal, file.getAbsolutePath());
					
					String md5str = CheckSumUtil.MD5(file);
					//对图片生成加密图片
					Map<String,Object> exist = redisCache.getCacheObject(RedisConstants.HUANGHE_PAGEIMAGES_SPAN+":"+md5str);
					if(exist != null) {
						log.info("==>{} 已生成 ", file.getAbsolutePath());
						continue;
					}
					
					String picPath = file.getAbsolutePath();
					if (StringUtil.endsWithAny(file.getName(), ".tif", ".tiff", ".png")) {
						String jpgname = FilenameUtils.getBaseName(file.getName());
						String _outPath = FilenameUtils.normalize(outPath + File.separator + md5str+"_"+jpgname + "_tif2jpg.jpg", true);
						File jpgf = new File(_outPath);
						if (!jpgf.exists() || jpgf.length() == 0) {								
							// 不存在，需要转换
							try {
								//ImgConvert.tiffTurnJpg(file.getPath(), _outPath);
								if(jpgf.length() > 10 * 1024 * 1024) {
			                		TifCompressPythonUtils.tifCompressHandler(jpgf.getAbsolutePath(), _outPath, 0, 0);
			                	}else {
			                		Tif2JpgPythonUtils.tif2jpgHandler(jpgf.getAbsolutePath(), _outPath, false);
			                	}
							} catch (Exception e) {
								log.error("error,",e);
							}	
						} else {
							log.info("==>{}图片已存在，不需要转换...", jpgf.getName());
						}		
						picPath = jpgf.getAbsolutePath();
					}else {
						log.info("==>不是tif图片，不需要转换");
					}
					
					
					Map<String,Object> spanMap = picService.makePicV2(picPath, Constants.PIC_HEIGHT, md5str);
					if(spanMap != null) {
						log.info("===========>保存到redis中<================");
						redisCache.setCacheObject(RedisConstants.HUANGHE_PAGEIMAGES_SPAN+":"+md5str, spanMap);
					}
				
				}catch(Exception e) {
			    	log.error("error,{}",e, file.getAbsolutePath());
			    }
			}
		}
		
	}
	
}
