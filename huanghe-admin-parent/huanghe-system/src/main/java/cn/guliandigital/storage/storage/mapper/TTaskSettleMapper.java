package cn.guliandigital.storage.storage.mapper;

import cn.guliandigital.storage.storage.domain.TTaskSettle;

import java.util.List;


/**
 * 数据入库配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
public interface TTaskSettleMapper 
{
    /**
     * 查询数据入库配置
     * 
     * @param id 数据入库配置ID
     * @return 数据入库配置
     */
        TTaskSettle selectTTaskSettleById(String id);

    /**
     * 查询数据入库配置列表
     * 
     * @param tTaskSettle 数据入库配置
     * @return 数据入库配置集合
     */
    List<TTaskSettle> selectTTaskSettleList(TTaskSettle tTaskSettle);

    /**
     * 新增数据入库配置
     * 
     * @param tTaskSettle 数据入库配置
     * @return 结果
     */
    int insertTTaskSettle(TTaskSettle tTaskSettle);

    /**
     * 修改数据入库配置
     * 
     * @param tTaskSettle 数据入库配置
     * @return 结果
     */
    int updateTTaskSettle(TTaskSettle tTaskSettle);

    /**
     * 删除数据入库配置
     * 
     * @param id 数据入库配置ID
     * @return 结果
     */
    int deleteTTaskSettleById(String id);

    /**
     * 批量删除数据入库配置
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteTTaskSettleByIds(String[] ids);

    int updateSettle(String[] ids);

    int updateStatus(String id);



}
