package cn.guliandigital.storage.storage.service.impl;


import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.guliandigital.common.config.HuangHeConfig;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.jsoup.Jsoup;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.BigwordUtil;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.storage.storage.domain.XmlBean;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * 解析xml工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class StyleRestoreV3ToolUtils {

	
	
	/**
	 * 解析xml，获取标题与内容
	 * @param book
	 * @param fileMap
	 * @return
	 * @throws Exception
	 */
	public static TProBookMenu parseBookMenuPic(TProBooks book,Map<String,String> fileMap,String bookRootPath) throws Exception {
		try {
			List<XmlBean> xmlContentlist = Lists.newArrayList();
			Map<String,String> zhuhaoRefMap = Maps.newHashMap();
			//去掉空间
			List<String> _xmllist = book.getXmlFileList();
			log.info("fileMap==>"+fileMap);
			for(String xmlname : _xmllist) {
				log.info("==>"+xmlname);
				if(fileMap.containsKey(xmlname)) {
					String xmlPath = fileMap.get(xmlname);
					log.info("==>开始解析文件{}",xmlPath);
					String xmlStr = FileUtils.readFileToString(new File(xmlPath), "UTF-8");
					//String xmlStr = TxtUtil.readTxtFile(xmlPath);
					xmlStr = StringUtils.trim(xmlStr);
					xmlStr = StringUtil.remove(xmlStr, " xmlns=\"http://shangyuan/shuju_yuliao\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
					xmlStr = StringUtils.remove(xmlStr, "xmlns=\"http://shangyuan/shuju_yuliao\"");
					xmlStr = StringUtils.remove(xmlStr, "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
					//xmlStr = StringUtils.remove(xmlStr, " ");
					if(StringUtils.contains(xmlStr, "shuju_yuliao")) {
						throw new ServiceException("xml里含有shuju_yuliao");
					}
					/*
					 * 使用正则表达式替换  专有名词 <专有名词 type="1">宏</专有名词>   ==》 <div class=”zhuanming”></div>
					 */
//					xmlStr=StringUtil.replace(xmlStr, "<专有名词", "<span class=\"zhuanming\" ");
//					xmlStr=StringUtil.replace(xmlStr, "</专有名词>", "</span>");
					
					xmlStr=StringUtil.replace(xmlStr, "<专有名词", "<专名");
					xmlStr=StringUtil.replace(xmlStr, "</专有名词>", "</专名>");
					
					xmlStr=StringUtil.replace(xmlStr, "<b>", "");					
					xmlStr=StringUtil.replace(xmlStr, "</b>", "");
					xmlStr=StringUtil.replace(xmlStr, "<文字>", "");
					xmlStr=StringUtil.replace(xmlStr, "</文字>", "");
					xmlStr=StringUtil.replace(xmlStr, "<字词注>", "");
					xmlStr=StringUtil.replace(xmlStr, "</字词注>", "");
					xmlStr=StringUtil.replace(xmlStr, "<特殊数字>", "");
					xmlStr=StringUtil.replace(xmlStr, "</特殊数字>", "");
					
					xmlStr=StringUtil.replace(xmlStr, "<书签 id=\"[0-9]{1,6}\"/>", "");
					xmlStr=StringUtil.replace(xmlStr, "<书签 id=\"[0-9]{1,6}\" />", "");
					
					Document document = DocumentHelper.parseText(xmlStr);
					LinkedHashMap<String,String> mainMap = Maps.newLinkedHashMap();
					
					Element root = document.getRootElement();//获取根元素
				    List<Element> childElements = root.elements();//
				    StringBuffer zwbuf = new StringBuffer();
				    String parentBT = null;
				    String parentLevel = null;
				    String parentId = null;
				    int totalcount = childElements.size();
				    int currentcount = 0;
				    int biaoticount = 0;
				    List<String> biaotilist = Lists.newArrayList();
				    for(Element content : childElements) {
				    	currentcount++;
				    	String tagName = content.getName();
				    	//log.info("==>"+content.asXML());
				    	if("标题".equals(tagName)) {
				    		
				    		String level = content.attributeValue("level");
				    		String isget = content.attributeValue("是否提取");
				    		String biaoti = content.attributeValue("提取标题");
				    		String id = content.attributeValue("id");
				    		String isshow = content.attributeValue("是否显示");
				    		
				    		//isget = "是";
				    		StringBuffer tempbtbuf = new StringBuffer();
				    		parseBiaoti(book, xmlPath, content, tagName, tempbtbuf, zhuhaoRefMap);
				    		
				    		//String bookviewbt = content.getText();	
				    		if(Strings.isNullOrEmpty(biaoti)) {
				    			log.info("==>没有提取标题{}",tempbtbuf);
				    			//没有提取标题字段				    			
				    			String tempbt = tempbtbuf.toString();
				    			tempbt = StringUtil.replace(tempbt, "<div class=\"biaoti"+level+"\">", "");
				    			tempbt = StringUtil.removeEnd(tempbt, "</div>");
				    			biaoti = tempbt;
				    		}
				    		
				    		int int_level = Integer.parseInt(level);
				    		//log.info("==>"+bookviewbt+", "+level + ","+ isget+","+biaoti);  					    		
				    		if("是".equals(isget)) {	
				    			
				    			if(int_level <=3 ) {
					    			parentBT = biaoti; 
						    		parentLevel = level;	
						    		parentId = id;
					    		}	
				    			String mapKey = parentBT+"_"+parentLevel+"_"+parentId;
				    			//取出上一个标题
				    			String biaotiKey = mapKey;
				    			if(biaoticount >0) {
				    				biaotiKey = biaotilist.get(biaoticount-1);
				    				mainMap.put(biaotiKey, zwbuf.toString());
				    				if(int_level <= 3 ) {
				    					zwbuf.setLength(0);
				    				}
				    			}
				    			biaoticount ++;				    			
				    			//增加到标题
				    			biaotilist.add(mapKey);

					    		//parseBiaoti(book, xmlPath, content, tagName, zwbuf, picHttpUrl);'
				    			if(StringUtil.equalsIgnoreCase(isshow, "是")) {
				    				zwbuf.append(tempbtbuf);
				    			}					    		
					    		
					    		mainMap.put(mapKey, zwbuf.toString());
				    		}else {
				    			Iterator<Entry<String,String>> iterator = mainMap.entrySet().iterator();
				    		    Entry<String, String> tail = null;
				    		    while (iterator.hasNext()) {
				    		        tail = iterator.next();
				    		    }
				    		    if(StringUtil.equalsIgnoreCase(isshow, "是")) {
				    				zwbuf.append(tempbtbuf);
				    			}
				    		    if(tail != null) {
					    		    String key = tail.getKey();
					    		    String val = tail.getValue();
					    		    
					    			mainMap.put(key, zwbuf.toString());
				    		    }
				    		}
				    	}
				    	
				    	if(tagName.equals("插图")) {
			    			String picsrc = content.attributeValue("src");
			    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            String uploadpath = HuangHeConfig.getUploadPath();
				            String extension = FilenameUtils.getExtension(picsrc);
				            String fileName = DateUtil.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
				            String thumbPath =uploadpath+File.separator+fileName;
				            File destFile = new File(FilenameUtils.normalize(thumbPath));
				            if(!destFile.getParentFile().exists()) {
				            	destFile.getParentFile().mkdirs();
				            }
				           
				            FileUtil.copyFile(srcFile, destFile);

				          //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(), true), FilenameUtils.normalize(uploadpath, true));

						    String domain = "%domain";
						    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
//		    				Element pica = DocumentHelper.createElement("img");
//		    				pica.addAttribute("src", httpUrl);
//		    				pica.addText(content.getText());
//		    				List elepar = content.getParent().content();
//		    				// 用content标签替换文本节点
//		    				elepar.set(elepar.indexOf(content), pica);          
//				            log.info(content.asXML());
				            
				            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("");				            
				            
			    		}
				    	if("原书页面".equals(tagName)) {
				    		String ysym = content.getText();
				    		
				    		ysym = StringUtil.trim(ysym);
				    		String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//				    		if(!StringUtil.startsWith(ysymLast, "L")) {
//				    			continue;
//				    		}
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		content.setName("a");
				    	    //删除多余属性
				    		Attribute deleteAttri = content.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			content.remove(deleteAttri);
				    		}
				    		//添加一个class属性，值为page
		    				Attribute newattri = DocumentHelper.createAttribute(content, "class", "page");
		    				content.add(newattri);
							//添加一个id属性，值为picId
		    				Attribute idattri = DocumentHelper.createAttribute(content, "id", ysym);
		    				content.add(idattri);	 
		    				//log.info(content.asXML());
		    				//增加调动函数
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(content, "onclick", hanshu);
		    				content.add(clickatt);		
		    				
		    				Attribute sizeAttri = content.attribute("size");
				    		String size = "";
				    		if(sizeAttri != null) {
				    			size = sizeAttri.getText();
				    			content.remove(sizeAttri);
				    		}
		    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
		    				content.add(sizeatt);	
		    				
		    				Attribute textsizeAttri = content.attribute("textsize");
				    		String textsize = "";
				    		if(textsizeAttri != null) {
				    			textsize = textsizeAttri.getText();
				    			content.remove(textsizeAttri);
				    		}
		    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
		    				content.add(textsizeatt);	
		    				
		    				// 截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trim(ym);
		    				int yamaInt = Integer.parseInt(ym);
		    				content.setText("P"+String.valueOf(yamaInt));
		    				//将 XML 元素 content 转换为其 XML 字符串表示形式
		    				zwbuf.append(content.asXML());
				    	}
				    	
				    	if("段落".equals(tagName)) {	    
				    		
				    		parseDuanluo(book, xmlPath, content, tagName, zwbuf, zhuhaoRefMap);
				    	}			    	
				    }
				    
				    if(currentcount == totalcount) {
				    	if(Strings.isNullOrEmpty(parentBT)) {
				    		
				    	}else {
				    		//log.info(zwbuf.toString());
				    		mainMap.put(parentBT+"_"+parentLevel+"_"+parentId, zwbuf.toString());
				    	}
				    }				
				    //log.info("==>"+mainMap);
				    String lastPage = null;
				   
				    List<Map.Entry<String, String>> newlist = new ArrayList<>(mainMap.size());
				    List<Map.Entry<String, String>> list = new ArrayList<>(mainMap.entrySet());
			        			 
			        for(int i = 0;i < list.size(); i++) {
			            Map.Entry<String, String> entry = list.get(i);
			            
			            String biaoti = entry.getKey();//重刻景㝎建康志序_1  标题_level
				    	String duanluo = entry.getValue();
//				    	log.info(biaoti);
//				    	log.info(duanluo);
				    	
				    	if(!StringUtil.startsWith(duanluo, "<a class=\"page\"")&&!Strings.isNullOrEmpty(lastPage)) {
				    		duanluo = lastPage + duanluo;			    		
				    	}
				    	lastPage = findLastPage(duanluo);
				    	entry.setValue(duanluo);
				    	newlist.add(entry);
			        }
			        for(int i = 0;i < newlist.size(); i++) {
			        	Map.Entry<String, String> entry = newlist.get(i);
			            
			            String biaoti = entry.getKey();//重刻景㝎建康志序_1  标题_level
				    	String duanluo = entry.getValue();
				    	//log.info(biaoti);
				    	//log.info(duanluo);
				    	
			    		lastPage = findLastPage(duanluo);
			    		//log.info("lastPage=>"+lastPage);
			    		if(!Strings.isNullOrEmpty(lastPage)) {
				    		if(duanluo.endsWith(lastPage)) {
				    			Map.Entry<String, String> nextentry = null;
				    			if(i < list.size()-1) {
				    				nextentry = newlist.get(i+1);
				    			}
				    			if(nextentry != null) {
					    			String nextduanluo = nextentry.getValue();
					    			if(nextduanluo.contains(lastPage)) {
					    				duanluo = StringUtil.removeEnd(duanluo, lastPage);
					    			}
				    			}
				    		}
				    		
			    		}	   
			    		String[] bt = StringUtil.split(biaoti, "_");
				    	String level = bt[1];
				    	String btstr = bt[0];
				    	int _level = Integer.parseInt(level);
				    	
				    	XmlBean bean = new XmlBean();
				    	bean.setBiaoti(btstr);
				    	bean.setLevel(_level);
				    	bean.setZhengwen(duanluo);
				    	xmlContentlist.add(bean);
			        }
				    mainMap.clear();
				}
			}
			
			//赋值图书
			book.setXmlMenuContentList(xmlContentlist);
		    //对章节进行处理
			long display = 1;
			
		    TProBookMenu bookmenu = new TProBookMenu();
		    bookmenu.setBookId(book.getId());
		    bookmenu.setId(IdUtil.simpleUUID());
		    bookmenu.setMenuName(book.getBookName());
		    bookmenu.setPid("0");	
		    bookmenu.setDisplay(display);
		    bookmenu.setLevel(0);
		    bookmenu.setFullMenuName(book.getBookName());
		    TProBookMenu pmenu = null;	    
		  		    
		    for(int i = 0;i < xmlContentlist.size(); i++) {
		    	display ++;
		    	XmlBean bean =  xmlContentlist.get(i);
		    	String biaoti = bean.getBiaoti();//重刻景㝎建康志序_1  标题_level
		    	String duanluo = bean.getZhengwen();
		    	duanluo = StringEscapeUtils.unescapeXml(duanluo);
		    	int _level = bean.getLevel();
		    	
		    	if(_level == 1) {
		    		//fullMenuBuff.setLength(0);
		    		//全路径		    				    		
		    		TProBookMenu menu = new TProBookMenu();
		    		menu.setBookId(book.getId());
		    		menu.setId(IdUtil.simpleUUID());
		    		menu.setMenuName(biaoti);
		    		menu.setPid(bookmenu.getId());		    	
		    		menu.setZwContent(duanluo);
		    		menu.setDisplay(display);
		    		menu.setLevel(bean.getLevel());
		    		menu.setFullMenuName(biaoti);
		    		List<TProBookMenu> children = bookmenu.getChildren();
		    		if(children == null) {
		    			children = new ArrayList<TProBookMenu>();
		    		}
		    		children.add(menu);
		    		bookmenu.setChildren(children);
		    		
		    		pmenu = menu;
		    		
		    	}else {
		    		//log.info("_level="+_level+"  "+biaoti);
		    		
		    		TProBookMenu parent = getParentMenu(bookmenu, bean);
		    		TProBookMenu menu = new TProBookMenu();
		    		menu.setBookId(book.getId());
		    		menu.setId(IdUtil.simpleUUID());
		    		menu.setMenuName(biaoti);
		    		menu.setPid(parent.getId());	
		    		menu.setZwContent(duanluo);
		    		menu.setDisplay(display);
		    		menu.setLevel(bean.getLevel());
		    		menu.setFullMenuName(parent.getFullMenuName()+">"+menu.getMenuName());
		    		List<TProBookMenu> children = parent.getChildren();
		    		if(children == null) {
		    			children = new ArrayList<TProBookMenu>();
		    		}
		    		children.add(menu);
		    		parent.setChildren(children);
		    		
//		    		if(i< xmlContentlist.size() -1) {
//			    		if(xmlContentlist.get(i+1).getLevel() != _level) {//下级
//			    			pmenu = menu;		    			
//			    		}
//		    		}		    		
		    	}		    	
		    }
		   
		    return bookmenu;
		}catch(Exception e) {
			throw e;
		}
	}
	
	public static String parseBiaoti(TProBooks book,String xmlPath,Element content,String tagName,StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		String uploadpath = HuangHeConfig.getUploadPath();
		if("标题".equals(tagName)) {	
			String level = content.attributeValue("level");
    		//String isget = content.attributeValue("是否提取");
    		//String biaoti = content.attributeValue("提取标题");
			String isshow = content.attributeValue("是否显示");
			//log.info(content.asXML());
    		//String id = content.attributeValue("id");
			zwbuf.append("<div class=\"biaoti"+level+"\">");
			
    		//可能会有多个正文
    		List<Element> duanluoelements = content.elements();

    		for(Element zw_element : duanluoelements) {
    			//正文中会有如下标签： 字体、原书页面、注释
    			//log.info("==>"+zw_element.asXML());
    			
    			String duanTagName = zw_element.getName();
//    			if(zw_element.getText().contains("府治洪武初自集慶路徙古")) {
//    				log.info("==>test");
//    			}
    			if("注号".equals(duanTagName)){
    				String id = zw_element.attributeValue("id");   				
    				String refid = zw_element.attributeValue("refid");
    				String hexstr = zw_element.getText();
    				
    				if(Strings.isNullOrEmpty(refid)) {   						    					
    						    			
    					String _refid = zhuhaoRefMap.get(id);
    					if(Strings.isNullOrEmpty(_refid)) {
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("span");
		    				
		    				span.addAttribute("class", "zhuhao");
		    				span.addAttribute("id", ""+id);
		    				span.addAttribute("name", ""+id);
		    					    				
		    				span.addText(hexstr);
		    				List elepar = zw_element.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(zw_element), span);
    					}else {
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("a");
		    				
		    				span.addAttribute("class", "zhuhao");
			    			span.addAttribute("id", ""+id);
			    			span.addAttribute("href", "#"+_refid);
		    				
		    				span.addText(hexstr);
		    				List elepar = zw_element.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(zw_element), span);    		
    					}   					
    					
    				}else {	    					
    					
    					zhuhaoRefMap.put(refid, id);
    					
						//说明是目标注
    					Element span = DocumentHelper.createElement("a");
	    				
	    				span.addAttribute("class", "zhuhao");
		    			span.addAttribute("id", ""+id);
		    			span.addAttribute("href", "#"+refid);
	    				
	    				span.addText(hexstr);
	    				List elepar = zw_element.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(zw_element), span);    					
    				}
				}
    			
    				//log.info(tagname);
				if("字体".equals(duanTagName)){
    				String _ftype = zw_element.attributeValue("type");
    				//log.info(node.getText()+","+ftype);
    				
    				Element span = DocumentHelper.createElement("span");
    				String hexstr = null;
    				if(Strings.isNullOrEmpty(_ftype)) {
    					span.addAttribute("class", "no");
    					hexstr = zw_element.getText();
    				}else {
	    				if(_ftype.contains("超大字2")) {
	    					span.addAttribute("class", "big02");
	    					//大字统一转&#x码
		    				String bigtext = zw_element.getText();
		    				//hexstr = BigwordUtil.str2Hex(bigtext);
		    				hexstr = BigwordUtil.hex2word(bigtext);
		    				//hexstr = BigwordUtil.hex2word(bigtext);
	    				}else if(_ftype.contains("超大字3")){
	    					span.addAttribute("class", "big15");
	    					//大字统一转&#x码
		    				String bigtext = zw_element.getText();
		    				//hexstr = BigwordUtil.str2Hex(bigtext);
		    				hexstr = BigwordUtil.hex2word(bigtext);
		    				//hexstr = BigwordUtil.hex2word(bigtext);
	    				}else if(_ftype.contains("超大字1")) {
	    					span.addAttribute("class", "big01");
	    					hexstr = zw_element.getText();
	    					hexstr = BigwordUtil.hex2word(hexstr);
	    				}else {
	    					span.addAttribute("class", _ftype);
	    					hexstr = zw_element.getText();
	    				}
    				}
    				
    				span.addText(hexstr);
    				
    				List elepar = zw_element.getParent().content();
    				// 用content标签替换文本节点
    				elepar.set(elepar.indexOf(zw_element), span);
				}
				
    			
    			if("原书页面".equals(duanTagName)) {
    							    				
    				Element _e = (Element)zw_element;
    				String ysym = _e.getText();
    				ysym = StringUtil.trim(ysym);
    				String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//		    		if(!StringUtil.startsWith(ysymLast, "L")) {
//		    			continue;
//		    		}
    				zw_element.setName("a");		 
    				//删除多余属性
		    		Attribute deleteAttri = zw_element.attribute("destsrc");
		    		if(deleteAttri != null) {
		    			zw_element.remove(deleteAttri);
		    		}
		    		
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
    				zw_element.add(newattri);	 
    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
    				zw_element.add(idattri);	 
    				//log.info(content.asXML());
    				String hanshu = "openReader('"+ysym+"')";
    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
    				zw_element.add(clickatt);	
    				
    				Attribute sizeAttri = content.attribute("size");
		    		String size = "";
		    		if(sizeAttri != null) {
		    			size = sizeAttri.getText();
		    			content.remove(sizeAttri);
		    		}
    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
    				content.add(sizeatt);	
    				Attribute textsizeAttri = content.attribute("textsize");
		    		String textsize = "";
		    		if(textsizeAttri != null) {
		    			textsize = textsizeAttri.getText();
		    			content.remove(textsizeAttri);
		    		}
    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
    				content.add(textsizeatt);
    				
    				//截取最后5位
    				String ym = StringUtil.substring(ysym,ysym.length()-5);
    				ym = StringUtil.trim(ym);
    				int yamaInt = Integer.parseInt(ym);
    				zw_element.setText("P"+String.valueOf(yamaInt));
    				//zwbuf.append(zw_element.asXML());	    			
    			}
    		   	
    			if("字图".equals(duanTagName)||"外字".equals(duanTagName)) {
    				Element _e = (Element)zw_element;
    				String picsrc = _e.attributeValue("src");
    				//转换为http:// 形式
    				String httpUrl = null;
    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
    				//log.info("src:"+srcFile.getAbsolutePath());
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            
		            
		            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
		            	uploadpath = "D:/huanghe/uploadPath/upload";
		            }
		            uploadpath = FilenameUtils.normalize(uploadpath);
		            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
				    
				    File destDir = new File(destPath);
				    if(!destDir.getParentFile().exists()) {
				    	destDir.getParentFile().mkdirs();
				    }									   
				    if(srcFile.exists()) {
				    	//log.info("==>拷贝图片：{}",destPath);
				    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
				    }
				    
				    //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//				    String domain = picHttpUrl;
////				    if(Strings.isNullOrEmpty(domain)) {
////				    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////				    }
				    String domain = "%domain";
				    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
		            //log.info(httpUrl);
    				Element pica = DocumentHelper.createElement("img");
    				pica.addAttribute("src", httpUrl);
    				pica.addAttribute("class", "zitu");
    				pica.addText(zw_element.getText());
    				List elepar = zw_element.getParent().content();
    				// 用content标签替换文本节点
    				elepar.set(elepar.indexOf(zw_element), pica);		    			
				}			
    			
    			if("注释".equals(duanTagName)) {
    				//log.info("==>"+zw_element.asXML());
    				String ftype = zw_element.attributeValue("type");
    				String divtype = null;
    				if("注".equals(ftype)) {
    					divtype = "zhu";
    				}else if("小注".equals(ftype)) {
    					divtype = "xiaozhu";
    				}else if("落款".equals(ftype)) {    			
    					divtype = "luokuan";
    				}else if("眉批".equals(ftype)) {
    					divtype = "meipi";
    				}else if("脚注-注".equals(ftype)) {
    					divtype = "jiaozhu-zhu";
    				}else if("脚注-校".equals(ftype)) {
    					divtype = "jiaozhu-jiao";
    				}else if("疏".equals(ftype)){
    					divtype = "shu";
    				}
    				log.info("==>"+ftype);
    				//删除原有的标签
    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
    				zw_element.remove(delattri);
    				
//    				if("meipi".equalsIgnoreCase(divtype)) {
//    					//增加新标签
//	    				zw_element.setName("span");
//	    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
//	    				zw_element.add(newattri);	
//    				}else {
	    				//增加新标签
	    				zw_element.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
	    				zw_element.add(newattri);		
    				//}
    				//log.info(zw_element.asXML());
    				/**
    				 * 可能会有大字
    				 */
    				List<Element> bigwordelements = zw_element.elements();
    				for(Element bigelement : bigwordelements) {
	    				String tagname = bigelement.getName();
	    				//log.info(tagname);
	    				if("字体".equals(tagname)){
		    				String _ftype = bigelement.attributeValue("type");
		    				//log.info(node.getText()+","+ftype);
		    				
		    				Element span = DocumentHelper.createElement("span");
		    				String hexstr = bigelement.getText();
		    				if(Strings.isNullOrEmpty(_ftype)) {
		    					span.addAttribute("class", "no");
		    					//hexstr = bigelement.getText();
		    				}else {
			    				if(_ftype.contains("超大字2")) {
			    					span.addAttribute("class", "big02");
			    					//大字统一转&#x码
				    				//String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(hexstr);
				    				hexstr = BigwordUtil.hex2word(hexstr);
			    				}else if(_ftype.contains("超大字3")){
			    					span.addAttribute("class", "big15");
			    					//大字统一转&#x码
				    				//String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(hexstr);
				    				hexstr = BigwordUtil.hex2word(hexstr);
			    				}else if(_ftype.contains("超大字1")) {
			    					span.addAttribute("class", "big01");
			    					hexstr = BigwordUtil.hex2word(hexstr);
			    				}else {
			    					span.addAttribute("class", _ftype);
			    					//hexstr = bigelement.getText();
			    				}
		    				}
		    				
		    				span.addText(hexstr);
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), span);
	    				}
	    				//log.info(zw_element.asXML());
	    				if("原书页面".equals(tagname)) {
				    		String ysym = bigelement.getText();
				    		ysym = StringUtil.trim(ysym);
				    		String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//				    		if(!StringUtil.startsWith(ysymLast, "L")) {
//				    			continue;
//				    		}
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		bigelement.setName("a");		
				    		//删除多余属性
				    		Attribute deleteAttri = bigelement.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			bigelement.remove(deleteAttri);
				    		}
				    		
		    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		    				bigelement.add(zspageattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		    				bigelement.add(idattri);	 
		    				//log.info(content.asXML());
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		    				bigelement.add(clickatt);	
		    				
		    				Attribute sizeAttri = content.attribute("size");
				    		String size = "";
				    		if(sizeAttri != null) {
				    			size = sizeAttri.getText();
				    			content.remove(sizeAttri);
				    		}
		    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
		    				content.add(sizeatt);	
		    				Attribute textsizeAttri = content.attribute("textsize");
				    		String textsize = "";
				    		if(textsizeAttri != null) {
				    			textsize = textsizeAttri.getText();
				    			content.remove(textsizeAttri);
				    		}
		    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
		    				content.add(textsizeatt);
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trim(ym);
		    				int yamaInt = Integer.parseInt(ym);
		    				bigelement.setText("P"+String.valueOf(yamaInt));
		    				//zwbuf.append(bigelement.asXML());
				    	}
	    				//log.info(bigelement.asXML());
	    				if("字图".equals(tagname)||"外字".equals(tagname)) {
		    				Element _e = (Element)bigelement;
		    				String picsrc = _e.attributeValue("src");
		    				//转换为http:// 形式
		    				String httpUrl = null;
		    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		    				//log.info("src:"+srcFile.getAbsolutePath());
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            
				            
				            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
				            	uploadpath = "D:/huanghe/uploadPath/upload";
				            }
				            uploadpath = FilenameUtils.normalize(uploadpath);
				            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
						    
						    File destDir = new File(destPath);
						    if(!destDir.getParentFile().exists()) {
						    	destDir.getParentFile().mkdirs();
						    }									   
						    if(srcFile.exists()) {
						    	//log.info("==>拷贝图片：{}",destPath);
						    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
						    }
						    
						    //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
						    String domain = "%domain";
						    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
		    				Element pica = DocumentHelper.createElement("img");
		    				pica.addAttribute("src", httpUrl);
		    				pica.addAttribute("class", "zitu");
		    				pica.addText(bigelement.getText());
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), pica);		    			
	    				}			
	    				if("书名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> bigword_elements = bigelement.elements();
		    				for(Element big_element : bigword_elements) {
			    				String tag_name = big_element.getName();
			    				//log.info(tagname);
			    				if("字体".equals(tag_name)){
				    				String _ftype = big_element.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = big_element.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = big_element.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = big_element.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = big_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = big_element.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = big_element.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(big_element), span);
			    				}
		    				}
		    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("专名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("注号".equals(tagname)){
	        				String id = bigelement.attributeValue("id");	        				
	        				String refid = bigelement.attributeValue("refid");
	        				String hexstr = bigelement.getText();
	        				
	        				if(Strings.isNullOrEmpty(refid)) {   						    					
	        						    			
	        					String _refid = zhuhaoRefMap.get(id);
	        					if(Strings.isNullOrEmpty(_refid)) {
	        						//说明是目标注
	    	    					Element span = DocumentHelper.createElement("span");
	    		    				
	    		    				span.addAttribute("class", "zhuhao");
	    		    				span.addAttribute("id", ""+id);
	    		    				span.addAttribute("name", ""+id);
	    		    					    				
	    		    				span.addText(hexstr);
	    		    				List elepar = bigelement.getParent().content();
	    		    				// 用content标签替换文本节点
	    		    				elepar.set(elepar.indexOf(bigelement), span);
	        					}else {
	        						//说明是目标注
	    	    					Element span = DocumentHelper.createElement("a");
	    		    				
	    		    				span.addAttribute("class", "zhuhao");
	    			    			span.addAttribute("id", ""+id);
	    			    			span.addAttribute("href", "#"+_refid);
	    		    				
	    		    				span.addText(hexstr);
	    		    				List elepar = bigelement.getParent().content();
	    		    				// 用content标签替换文本节点
	    		    				elepar.set(elepar.indexOf(bigelement), span);    		
	        					}   					
	        					
	        				}else {	    					
	        					
	        					zhuhaoRefMap.put(refid, id);
	        					
	    						//说明是目标注
	        					Element span = DocumentHelper.createElement("a");
	    	    				
	    	    				span.addAttribute("class", "zhuhao");
	    		    			span.addAttribute("id", ""+id);
	    		    			span.addAttribute("href", "#"+refid);
	    	    				
	    	    				span.addText(hexstr);
	    	    				List elepar = bigelement.getParent().content();
	    	    				// 用content标签替换文本节点
	    	    				elepar.set(elepar.indexOf(bigelement), span);    					
	        				}
	    				}
    				}
    				
    				
    			}	
    			if("书名".equals(duanTagName)) {
    				Element _e = (Element)zw_element;

    				//增加新标签
    				_e.setName("span");
    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
    				_e.add(_newattri);		
    				//log.info(zw_element.asXML());
    			}
				if("专名".equals(duanTagName)) {
    				Element _e = (Element)zw_element;

    				//增加新标签
    				_e.setName("span");
    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
    				_e.add(_newattri);		
    				//log.info(zw_element.asXML());
    			}	
			}
    		
    		List<Attribute> attlist = content.attributes();
    		//log.info("==>"+attlist.size());
    		
    		List<Attribute> alllist = Lists.newArrayList();
    		alllist.addAll(attlist);
    		for(int i = 0;i < alllist.size(); i++) {
    			//log.info(alllist.get(i).getName()+"==>"+alllist.size());    			
    			content.remove(alllist.get(i));
    		}
    		String xmlstr = content.asXML();
    		xmlstr = StringUtil.replace(xmlstr, "<标题>", "");
    		xmlstr = StringUtil.replace(xmlstr, "</标题>", "");
    		//if(StringUtil.equalsIgnoreCase(isshow, "是")) {
    			zwbuf.append(xmlstr);
    		//}
    		zwbuf.append("</div>");
    		//log.info("====>"+zwbuf.toString());
    	}
		return zwbuf.toString();
	}
	
	
	public static TProBookMenu getParentMenu(TProBookMenu bookmenu , XmlBean bean) {
		TProBookMenu result = bookmenu;
		//从1级开始
		List<TProBookMenu> list = bookmenu.getChildren();
		for(TProBookMenu menu : list) {
			int level = menu.getLevel();
			int _level = bean.getLevel();
			//log.info("<==>level:"+menu.getMenuName()+"=="+level);
			//log.info("<==>_level:"+bean.getBiaoti()+"=="+_level);
			if(level < _level) {
				result = getParentMenu(menu , bean);
			}else {
				continue;
			}
		}
		//log.info("==>获取的上级："+result.getMenuName()+" ==>"+bean.getBiaoti());
		return result;
	}
	
		
	
	/**
	 * 解析段落
	 * @param book
	 * @param xmlPath
	 * @param content
	 * @param tagName
	 * @param zwbuf
	 * @return
	 */
	public static String parseDuanluo(TProBooks book,String xmlPath,Element content,String tagName,StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		String uploadpath = HuangHeConfig.getUploadPath();
		if("段落".equals(tagName)) {	    		
    		
			zwbuf.append("<p>");
    		//可能会有多个正文
    		List<Element> duanluoelements = content.elements();
    		for(Element zw_element : duanluoelements) {
    			//正文中会有如下标签： 字体、原书页面、注释
    			//log.info("==>"+zw_element.asXML());
    			
    			String duanTagName = zw_element.getName();
    			Attribute zwattri = zw_element.attribute("type");
    			String zwtype  = null;
    			if(zwattri != null) {
    				zwtype = zwattri.getValue();
    			}
    			String tempDiv = "";
    			//log.info("==>正文type={}",zwtype);
    			if(!Strings.isNullOrEmpty(zwtype)) {
    				if("正文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhengwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhengwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhengwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhengwen-jiao\">";
	    				}else {
//	    					if(StringUtil.in)
//	    					xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    					//tempDiv = "<span class=\"zhengwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhengwen\">";
	    				}
    				}
    				
    				if("注文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhuwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhuwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhuwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhuwen-jiao\">";
	    				}else {
	    					//tempDiv = "<span class=\"zhuwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhuwen\">";
	    				}
    				}
    				
    			}
    			zwbuf.append(tempDiv);
    			
    			if("注号".equals(duanTagName)){
    				String id = zw_element.attributeValue("id");   				
    				String refid = zw_element.attributeValue("refid");
    				String hexstr = zw_element.getText();      
    				if(Strings.isNullOrEmpty(refid)) {
    					
    					String _refid = zhuhaoRefMap.get(id);
    					if(Strings.isNullOrEmpty(_refid)) {
    						//说明是目标注
        					Element span = DocumentHelper.createElement("span");	    				
    	    				span.addAttribute("class", "zhuhao");   	    				
    	    				 				
    	    				span.addText(hexstr);

    	    				zw_element = null;
    	    				zwbuf.append(span.asXML());
    					}else {
    						Element atag = DocumentHelper.createElement("a");
        					atag.addAttribute("class", "zhuhao");
        					atag.addAttribute("id", id);
        					atag.addAttribute("href", "#"+_refid);
    	    				
    	    				atag.addText(hexstr);
    	    				
    	    				zw_element = null;
    	    				zwbuf.append(atag.asXML());
    					}
    					
    				}else {
    					
    					zhuhaoRefMap.put(refid, id);
    					
    					Element atag = DocumentHelper.createElement("a");
    					atag.addAttribute("class", "zhuhao");
    					atag.addAttribute("id", id);
    					atag.addAttribute("href", "#"+refid);
	    				
	    				atag.addText(hexstr);
	    				
	    				zw_element = null;
	    				zwbuf.append(atag.asXML());
    				}
				}
    			if(zw_element == null) {
    				continue;
    			}
    			if("正文".equals(duanTagName) || "注文".equals(duanTagName)) {
	    			String xpath = zw_element.getUniquePath()+"/字体";
	    			//String xpath2 = zw_element.getUniquePath()+"/b/字体";
	    			List<Node> selectNodes = zw_element.selectNodes(xpath);
//	    			if(selectNodes == null || selectNodes.size() ==0) {
//	    				selectNodes = zw_element.selectNodes(xpath2);
//	    			}
	    			for (Node node : selectNodes) {
	    				Element _e = (Element)node;
	    				String bigtext = _e.getText();
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				String zhuanming_xpath = _e.getUniquePath()+"/专名";
		    			List<Node> selectNodes_zhuanming = _e.selectNodes(zhuanming_xpath);
		    			for (Node _node : selectNodes_zhuanming) {		
		    				Element _e1 = (Element)_node;   				
		    			
		    				String tagname = _e1.getName();
		    					
		    				Element span = DocumentHelper.createElement("span");
		    				span.addAttribute("class", "zhuanming");
		    				String hexstr = _e1.getText();;
		    				
		    				span.addText(hexstr);
//		    				List elepar = _e1.getParent().content();
//		    				// 用content标签替换文本节点
//		    				elepar.set(elepar.indexOf(_e1), span);		    				
		    				bigtext = span.asXML();
		    			}
	    				Element span = DocumentHelper.createElement("span");
	    				
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}		
	    				}
	    				
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}
	    			String xpath2 = zw_element.getUniquePath()+"/b/字体";	    			
	    			selectNodes = zw_element.selectNodes(xpath2);
	    			
	    			for (Node node : selectNodes) {
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				
	    				Element span = DocumentHelper.createElement("span");
	    				String bigtext = node.getText();
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}				    				
	    				}
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}    			
	    			
	    			
	    			String ysym_xpath = zw_element.getUniquePath()+"/原书页面";
	    			List<Node> selectNodes_ysym = zw_element.selectNodes(ysym_xpath);
	    			for (Node node : selectNodes_ysym) {
	    				Element _e = (Element)node;
	    				String yema = node.getText();
	    				yema = StringUtil.trim(yema);
	    				String ysymLast = StringUtil.substringAfterLast(yema, "-");
//			    		if(!StringUtil.startsWith(ysymLast, "L")) {
//			    			continue;
//			    		}
	    				//截取最后5位
	    				String ym = StringUtil.substring(yema,yema.length()-5);
	    				ym = StringUtil.trim(ym);
	    				int yamaInt = Integer.parseInt(ym);
	    				
	    				Element pica = DocumentHelper.createElement("a");					    				
	    				pica.addAttribute("class", "page");
	    				pica.addAttribute("id", _e.getText());
	    				
	    				//增加调动函数
	    				String hanshu = "openReader('"+yema+"')";	    				
	    				pica.addAttribute("onclick", hanshu);	
	    				
	    				
	    				pica.addText("P"+String.valueOf(yamaInt));
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    					 
	    			String zhuhao_xpath = zw_element.getUniquePath()+"/注号";
	    			List<Node> selectNodes_zhuhao = zw_element.selectNodes(zhuhao_xpath);
	    			for (Node node : selectNodes_zhuhao) {
	    				Element _e = (Element)node;
	    				String hexstr = node.getText();
	    				
	    				String id = _e.attributeValue("id");
	    				String refid = _e.attributeValue("refid");
	    				if(Strings.isNullOrEmpty(refid)) {   						    					
	    						    			
	    					String _refid = zhuhaoRefMap.get(id);
	    					if(Strings.isNullOrEmpty(_refid)) {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("span");
			    				
			    				span.addAttribute("class", "zhuhao");
			    				span.addAttribute("id", ""+id);
			    				span.addAttribute("name", ""+id);
			    					    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);
	    					}else {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+_refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);    		
	    					}   					
	    					
	    				}else {	    					
	    					
	    					zhuhaoRefMap.put(refid, id);
	    					
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("a");
		    				
		    				span.addAttribute("class", "zhuhao");
			    			span.addAttribute("id", ""+id);
			    			span.addAttribute("href", "#"+refid);
		    				
		    				span.addText(hexstr);
		    				List elepar = node.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(node), span);    					
	    				}
	    			}
	    			
	    			String zhu_xpath = zw_element.getUniquePath()+"/注释";
	    			List<Node> selectNodes_zhu = zw_element.selectNodes(zhu_xpath);
	    			for (Node node : selectNodes_zhu) {		
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				String divtype = null;
	    				if("注".equals(ftype)) {
	    					divtype = "zhu";
	    				}else if("小注".equals(ftype)) {
	    					divtype = "xiaozhu";
	    				}else if("落款".equals(ftype)) {
	    					divtype = "luokuan";
	    				}else if("眉批".equals(ftype)) {
	    					divtype = "meipi";
	    				}else if("脚注-注".equals(ftype)) {
	    					divtype = "jiaozhu-zhu";
	    				}else if("脚注-校".equals(ftype)) {
	    					divtype = "jiaozhu-jiao";
	    				}else if("疏".equals(ftype)){
	    					divtype = "shu";
	    				}
	    				Element pica = DocumentHelper.createElement("span");
	    				pica.addAttribute("class", divtype);
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			String zhuanming_xpath = zw_element.getUniquePath()+"/专名";
	    			List<Node> selectNodes_zhuanming = zw_element.selectNodes(zhuanming_xpath);
	    			for (Node node : selectNodes_zhuanming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				//log.info(tagname);
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element _epage = (Element)bigelement;
		        				String ysym = _epage.getText();
		        				ysym = StringUtil.trim(ysym);
		        				String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//					    		if(!StringUtil.startsWith(ysymLast, "L")) {
//					    			continue;
//					    		}
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				
		        				Attribute sizeAttri = content.attribute("size");
					    		String size = "";
					    		if(sizeAttri != null) {
					    			size = sizeAttri.getText();
					    			content.remove(sizeAttri);
					    		}
			    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
			    				content.add(sizeatt);	
			    				Attribute textsizeAttri = content.attribute("textsize");
					    		String textsize = "";
					    		if(textsizeAttri != null) {
					    			textsize = textsizeAttri.getText();
					    			content.remove(textsizeAttri);
					    		}
			    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
			    				content.add(textsizeatt);
			    				
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trim(ym);
		        				int yamaInt = Integer.parseInt(ym);
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zitu_e = (Element)bigelement;
			    				String picsrc = zitu_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
			    				//log.info("src:"+srcFile.getAbsolutePath());
					            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
					            //String uploadpath = baseParms.getUploadPath();
					            
					            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
					            	uploadpath = "D:/huanghe/uploadPath/upload";
					            }
					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
							    
							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }									   
							    if(srcFile.exists()) {
							    	//log.info("==>拷贝图片：{}",destPath);
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));

							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}		
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String shuming_xpath = zw_element.getUniquePath()+"/书名";
	    			List<Node> selectNodes_shuming = zw_element.selectNodes(shuming_xpath);
	    			for (Node node : selectNodes_shuming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element b_e = (Element)bigelement;
		        				String ysym = b_e.getText();
		        				ysym = StringUtil.trim(ysym);
		        				String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//					    		if(!StringUtil.startsWith(ysymLast, "L")) {
//					    			continue;
//					    		}
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				
		        				Attribute sizeAttri = content.attribute("size");
					    		String size = "";
					    		if(sizeAttri != null) {
					    			size = sizeAttri.getText();
					    			content.remove(sizeAttri);
					    		}
			    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
			    				content.add(sizeatt);	
			    				Attribute textsizeAttri = content.attribute("textsize");
					    		String textsize = "";
					    		if(textsizeAttri != null) {
					    			textsize = textsizeAttri.getText();
					    			content.remove(textsizeAttri);
					    		}
			    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
			    				content.add(textsizeatt);
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trim(ym);
		        				int yamaInt = Integer.parseInt(ym);
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zm_e = (Element)bigelement;
			    				String picsrc = zm_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
			    				//log.info("src:"+srcFile.getAbsolutePath());
					            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
					            //String uploadpath = baseParms.getUploadPath();
					            
					            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
					            	uploadpath = "D:/huanghe/uploadPath/upload";
					            }
					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
							    
							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }									   
							    if(srcFile.exists()) {
							    	//log.info("==>拷贝图片：{}",destPath);
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//							    String domain = picHttpUrl;
////							    if(Strings.isNullOrEmpty(domain)) {
////							    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////							    }
							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}	
		    				
		    					
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String zitu_xpath = zw_element.getUniquePath()+"/字图";
	    			List<Node> selectNodes_zitu = zw_element.selectNodes(zitu_xpath);
	    			for (Node node : selectNodes_zitu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
			            	uploadpath = "D:/huanghe/uploadPath/upload";
			            }
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	//log.info("==>拷贝图片：{}",destPath);
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			//外字
	    			String waizi_xpath = zw_element.getUniquePath()+"/外字";
	    			List<Node> selectNodes_waizi = zw_element.selectNodes(waizi_xpath);
	    			for (Node node : selectNodes_waizi) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
			            	uploadpath = "D:/huanghe/uploadPath/upload";
			            }
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	//log.info("==>拷贝图片：{}",destPath);
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			
	    			String chatu_xpath = zw_element.getUniquePath()+"/插图";
	    			List<Node> selectNodes_chatu = zw_element.selectNodes(chatu_xpath);
	    			for (Node node : selectNodes_chatu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
			            	uploadpath = "D:/huanghe/uploadPath/upload";
			            }
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	//log.info("==>拷贝图片：{}",destPath);
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "chatu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    				    			
    			}
    			if("原书页面".equals(duanTagName)) {
    							    				
    				Element _e = (Element)zw_element;
    				String ysym = _e.getText();
    				ysym = StringUtil.trim(ysym);
    				String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//		    		if(!StringUtil.startsWith(ysymLast, "L")) {
//		    			continue;
//		    		}
    				zw_element.setName("a");		 
    				//删除多余属性
		    		Attribute deleteAttri = zw_element.attribute("destsrc");
		    		if(deleteAttri != null) {
		    			zw_element.remove(deleteAttri);
		    		}
		    		
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
    				zw_element.add(newattri);	 
    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
    				zw_element.add(idattri);	 
    				//log.info(content.asXML());
    				String hanshu = "openReader('"+ysym+"')";
    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
    				zw_element.add(clickatt);	
    				
    				Attribute sizeAttri = content.attribute("size");
		    		String size = "";
		    		if(sizeAttri != null) {
		    			size = sizeAttri.getText();
		    			content.remove(sizeAttri);
		    		}
    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
    				content.add(sizeatt);	
    				Attribute textsizeAttri = content.attribute("textsize");
		    		String textsize = "";
		    		if(textsizeAttri != null) {
		    			textsize = textsizeAttri.getText();
		    			content.remove(textsizeAttri);
		    		}
    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
    				content.add(textsizeatt);
    				//截取最后5位
    				String ym = StringUtil.substring(ysym,ysym.length()-5);
    				ym = StringUtil.trim(ym);
    				int yamaInt = Integer.parseInt(ym);
    				zw_element.setText("P"+String.valueOf(yamaInt));
    				//zwbuf.append(zw_element.asXML());	    			
    			}
    			//插图
    			if("插图".equals(duanTagName)) {
    				String picsrc = zw_element.attributeValue("src");
	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            //String uploadpath = baseParms.getUploadPath();
		            String extension = FilenameUtils.getExtension(picsrc);
		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
		            String thumbPath =uploadpath+File.separator+fileName;
		            File destFile = new File(FilenameUtils.normalize(thumbPath));
		            if(!destFile.getParentFile().exists()) {
		            	destFile.getParentFile().mkdirs();
		            }
		           
		            try {
						FileUtil.copyFile(srcFile, destFile);
					} catch (Exception e) {					
						log.error("",e);
					}

		          //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));
				    String domain = "%domain";
				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
		            //log.info(httpUrl);
//    				Element pica = DocumentHelper.createElement("img");
//    				pica.addAttribute("src", httpUrl);
//    				pica.addText(content.getText());
//    				List elepar = content.getParent().content();
//    				// 用content标签替换文本节点
//    				elepar.set(elepar.indexOf(content), pica);          
//		            log.info(content.asXML());
		            //删除该标签
		            //content.remove(zw_element);
		            zw_element = null;
		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("");
		            
    			}
    			if(zw_element == null) {
    				continue;
    			}
    			if("注释".equals(duanTagName)) {
    				//log.info("==>"+zw_element.asXML());
    				String ftype = zw_element.attributeValue("type");
    				String divtype = null;
    				if("注".equals(ftype)) {
    					divtype = "zhu";
    				}else if("小注".equals(ftype)) {
    					divtype = "xiaozhu";
    				}else if("落款".equals(ftype)) {
    					divtype = "luokuan";
    				}else if("眉批".equals(ftype)) {
    					divtype = "meipi";
    				}else if("脚注-注".equals(ftype)) {
    					divtype = "jiaozhu-zhu";
    				}else if("脚注-校".equals(ftype)) {
    					divtype = "jiaozhu-jiao";
    				}else if("疏".equals(ftype)){
    					divtype = "shu";
    				}
    				
    				//删除原有的标签
    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
    				zw_element.remove(delattri);
    				
    				//增加新标签
    				zw_element.setName("span");
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
    				zw_element.add(newattri);		
    				
    				//log.info(zw_element.asXML());
    				/**
    				 * 可能会有大字
    				 */
    				List<Element> bigwordelements = zw_element.elements();
    				for(Element bigelement : bigwordelements) {
	    				String tagname = bigelement.getName();
	    				//log.info(tagname);
	    				if("注号".equals(tagname)){
		    				String id = bigelement.attributeValue("id");
		    				String refid = bigelement.attributeValue("refid");
		    				String hexstr = bigelement.getText();
		    				
		    				if(Strings.isNullOrEmpty(refid)) {   						    					
		    						    			
		    					String _refid = zhuhaoRefMap.get(id);
		    					if(Strings.isNullOrEmpty(_refid)) {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("span");
				    				
				    				span.addAttribute("class", "zhuhao");
				    				span.addAttribute("id", ""+id);
				    				span.addAttribute("name", ""+id);
				    					    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);
		    					}else {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+_refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    		
		    					}   					
		    					
		    				}else {	    					
		    					
		    					zhuhaoRefMap.put(refid, id);
		    					
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);    					
		    				}
		    				
	    				}
	    				
	    				if("字体".equals(tagname)){
		    				String _ftype = bigelement.attributeValue("type");
		    				//log.info(node.getText()+","+ftype);
		    				
		    				Element span = DocumentHelper.createElement("span");
		    				String hexstr = null;
		    				if(Strings.isNullOrEmpty(_ftype)) {
		    					span.addAttribute("class", "no");
		    					hexstr = bigelement.getText();
		    				}else {
			    				if(_ftype.contains("超大字2")) {
			    					span.addAttribute("class", "big02");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字3")){
			    					span.addAttribute("class", "big15");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字1")) {
			    					span.addAttribute("class", "big01");
			    					hexstr = zw_element.getText();
			    					hexstr = BigwordUtil.hex2word(hexstr);
			    				}else {
			    					span.addAttribute("class", _ftype);
			    					hexstr = bigelement.getText();
			    				}
		    				}
		    				
		    				span.addText(hexstr);
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), span);
	    				}
	    				//log.info(zw_element.asXML());
	    				if("原书页面".equals(tagname)) {
				    		String ysym = bigelement.getText();
				    		ysym = StringUtil.trim(ysym);
				    		String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//				    		if(!StringUtil.startsWith(ysymLast, "L")) {
//				    			continue;
//				    		}
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		bigelement.setName("a");		
				    		//删除多余属性
				    		Attribute deleteAttri = bigelement.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			bigelement.remove(deleteAttri);
				    		}
				    		
		    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		    				bigelement.add(zspageattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		    				bigelement.add(idattri);	 
		    				//log.info(content.asXML());
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		    				bigelement.add(clickatt);	
		    				
		    				Attribute sizeAttri = content.attribute("size");
				    		String size = "";
				    		if(sizeAttri != null) {
				    			size = sizeAttri.getText();
				    			content.remove(sizeAttri);
				    		}
		    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
		    				content.add(sizeatt);	
		    				Attribute textsizeAttri = content.attribute("textsize");
				    		String textsize = "";
				    		if(textsizeAttri != null) {
				    			textsize = textsizeAttri.getText();
				    			content.remove(textsizeAttri);
				    		}
		    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
		    				content.add(textsizeatt);
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trim(ym);
		    				int yamaInt = Integer.parseInt(ym);
		    				bigelement.setText("P"+String.valueOf(yamaInt));
		    				//zwbuf.append(bigelement.asXML());
				    	}
	    				//插图
	        			if("插图".equals(tagname)) {
	        				String picsrc = bigelement.attributeValue("src");
	    	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
	    		            //String uploadpath = baseParms.getUploadPath();
	    		            String extension = FilenameUtils.getExtension(picsrc);
	    		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
	    		            String thumbPath =uploadpath+File.separator+fileName;
	    		            File destFile = new File(FilenameUtils.normalize(thumbPath));
	    		            if(!destFile.getParentFile().exists()) {
	    		            	destFile.getParentFile().mkdirs();
	    		            }
	    		           
	    		            try {
	    						FileUtil.copyFile(srcFile, destFile);
	    					} catch (Exception e) {	    						
	    						log.error("",e);
	    						
	    					}

	    		          //组装http链接
	    				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));
	    				    String domain = "%domain";
	    				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
	    		            //log.info(httpUrl);
	        				Element pica = DocumentHelper.createElement("img");
	        				pica.addAttribute("src", httpUrl);
	        				pica.addText(bigelement.getText());
	        				List elepar = bigelement.getParent().content();
	        				// 用content标签替换文本节点
	        				elepar.set(elepar.indexOf(bigelement), pica);          
//	    		            log.info(content.asXML());
	    		            //删除该标签
	    		            //content.remove(zw_element);
//	    				    bigelement = null;
//	    		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("");
	    		            
	        			}
	        			
	    				//log.info(bigelement.asXML());
	    				if("字图".equals(tagname)||"外字".equals(tagname)) {
		    				Element _e = (Element)bigelement;
		    				String picsrc = _e.attributeValue("src");
		    				//转换为http:// 形式
		    				String httpUrl = null;
		    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		    				//log.info("src:"+srcFile.getAbsolutePath());
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            //String uploadpath = baseParms.getUploadPath();
				            
				            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
				            	uploadpath = "D:/huanghe/uploadPath/upload";
				            }
				            uploadpath = FilenameUtils.normalize(uploadpath);
				            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
						    
						    File destDir = new File(destPath);
						    if(!destDir.getParentFile().exists()) {
						    	destDir.getParentFile().mkdirs();
						    }									   
						    if(srcFile.exists()) {
						    	//log.info("==>拷贝图片：{}",destPath);
						    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
						    }
						    
						    //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
						    String domain = "%domain";
						    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
		    				Element pica = DocumentHelper.createElement("img");
		    				pica.addAttribute("src", httpUrl);
		    				pica.addAttribute("class", "zitu");
		    				pica.addText(bigelement.getText());
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), pica);		    			
	    				}	
	    				
	    				if("书名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");			    				
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trim(ysym);
						    		String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//						    		if(!StringUtil.startsWith(ysymLast, "L")) {
//						    			continue;
//						    		}
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				
				    				Attribute sizeAttri = content.attribute("size");
						    		String size = "";
						    		if(sizeAttri != null) {
						    			size = sizeAttri.getText();
						    			content.remove(sizeAttri);
						    		}
				    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
				    				content.add(sizeatt);	
				    				Attribute textsizeAttri = content.attribute("textsize");
						    		String textsize = "";
						    		if(textsizeAttri != null) {
						    			textsize = textsizeAttri.getText();
						    			content.remove(textsizeAttri);
						    		}
				    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
				    				content.add(textsizeatt);
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trim(ym);
				    				int yamaInt = Integer.parseInt(ym);
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
						            	uploadpath = "D:/huanghe/uploadPath/upload";
						            }
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	//log.info("==>拷贝图片：{}",destPath);
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//								    String domain = picHttpUrl;
////								    if(Strings.isNullOrEmpty(domain)) {
////								    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////								    }
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
		    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("专名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trim(ysym);
						    		String ysymLast = StringUtil.substringAfterLast(ysym, "-");
//						    		if(!StringUtil.startsWith(ysymLast, "L")) {
//						    			continue;
//						    		}
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				
				    				Attribute sizeAttri = content.attribute("size");
						    		String size = "";
						    		if(sizeAttri != null) {
						    			size = sizeAttri.getText();
						    			content.remove(sizeAttri);
						    		}
				    				Attribute sizeatt = DocumentHelper.createAttribute(content, "size", size);
				    				content.add(sizeatt);	
				    				Attribute textsizeAttri = content.attribute("textsize");
						    		String textsize = "";
						    		if(textsizeAttri != null) {
						    			textsize = textsizeAttri.getText();
						    			content.remove(textsizeAttri);
						    		}
				    				Attribute textsizeatt = DocumentHelper.createAttribute(content, "textsize", textsize);
				    				content.add(textsizeatt);
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trim(ym);
				    				int yamaInt = Integer.parseInt(ym);
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
						            	uploadpath = "D:/huanghe/uploadPath/upload";
						            }
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	//log.info("==>拷贝图片：{}",destPath);
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
			    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}	
    				}
    			}				    					    			
    			if(zw_element != null) {
	    			String xmlstr = zw_element.asXML();
	    			xmlstr = StringUtil.replace(xmlstr, "&lt;", "<");
	    			xmlstr = StringUtil.replace(xmlstr, "&gt;", ">");
	    			
	    			//log.info(xmlstr);
	    			xmlstr = StringUtil.remove(xmlstr, "<正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"\">");	    			
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"引文\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"注\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"校\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"疏\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<注文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.remove(xmlstr, "</注文>");
	    			xmlstr = StringUtil.remove(xmlstr, "</正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"2\">");
	    				    			
	    			zwbuf.append(xmlstr);
    			}
    			if(!Strings.isNullOrEmpty(tempDiv)) {
	    			if(tempDiv.indexOf("<span class=\"zhengwen-") != -1||tempDiv.indexOf("<span class=\"zhuwen-") != -1) {
	    				//xmlstr = xmlstr +"</div>";
	    				zwbuf.append("</span>");
	    			}
    			}
    			
    		}
    		zwbuf.append("</p>");
    		
    	}
		//log.info("==>解析结果：{}",zwbuf.toString());
		return zwbuf.toString();
	}
	
	
	public static String findLastPage(String str) {
		String result = null;
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\" size=\"([0-9]{0,5},?[0-9]{0,5})\" textsize=\"([0-9]{0,5},?[0-9]{0,5})\">P[0-9]*</a>)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		    	   
	    while(m.find()){	      
	       String pagestr = m.group();       
	       result = pagestr;
	    }	
		
		return result;
	}
	
	public static String  removePage(String content) {
		//String patstr = "(P[0-9]*)";
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\" size=\"([0-9]{0,5},?[0-9]{0,5})\" textsize=\"([0-9]{0,5},?[0-9]{0,5})\">P[0-9]*</a>)";
		
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(content);		    
	    while(m.find()){	 
	    	content = m.replaceAll("");	    	
	    }
	    return content;
	}
	
	
	public static void main(String[] args) throws Exception {
		
//		String str = "<a class=\"page\" id=\"DJB00027-00001-A00001\" onclick=\"openReader('DJB00027-00001-A00001')\" size=\"\">P1</a><a class=\"page\" id=\"DJB00027-00002-L00001\" onclick=\"openReader('DJB00027-00002-L00001')\" size=\"3667,5209\">P1</a>";
//		findLastPage(str);
		String bookRootPath = "";
			
//		TProBooks book = new TProBooks();
//		book.setId("21323");
//		List<String> xmlFileList = Lists.newArrayList();
//		xmlFileList.add("正文.xml");
//		book.setXmlFileList(xmlFileList);
//		
//		Map<String,String> fileMap = Maps.newHashMap();
//		fileMap.put("正文.xml", "C:\\Users\\<USER>\\Desktop\\DJB00000027 聊齋四六文集八卷（测试）\\items\\正文.xml");
//		
//		parseBookMenuPic(book, fileMap, bookRootPath);
		
//		String zw = FileUtils.readFileToString(new File("C:\\Users\\<USER>\\Desktop\\2222222222222.txt"), "UTF-8");
//		String fullText = zw.replaceAll("<{1}[^<>]*>{1}", "");
//		fullText = StringUtil.replaceAllBlank(fullText);
//		// 大字转码
//		fullText = BigwordUtil.hexs2words(fullText);	
//		System.out.println(fullText);
		String pageStr = "<a class=\"page\" id=\"DJB00027-00002-L00001\" onclick=\"openReader('DJB00027-00002-L00001')\" size=\"3667,5209\" textsize=\"3667,5209\">P1</a>";
		org.jsoup.nodes.Document document = Jsoup.parse(pageStr);
    	//log.info(document.html());
    	String picId = null;
    	String pageNostr = null;
    	org.jsoup.select.Elements ides = document.getElementsByClass("page");
		if(ides != null) {
			org.jsoup.nodes.Element _ele = ides.first();
			pageNostr = _ele.text();
			picId = _ele.id();
			String size = _ele.attr("size");
			String textsize = _ele.attr("textsize");
		}		
	}
}
