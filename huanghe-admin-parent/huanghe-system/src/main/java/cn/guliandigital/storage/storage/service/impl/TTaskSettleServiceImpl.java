package cn.guliandigital.storage.storage.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.guliandigital.analysis.domain.ImportPDF;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Strings;

import cn.guliandigital.common.core.domain.model.LoginUser;
import cn.guliandigital.common.enums.DataStatusEnum;
import cn.guliandigital.common.enums.InStatusEnum;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.storage.storage.domain.TTaskSettle;
import cn.guliandigital.storage.storage.mapper.TTaskSettleMapper;
import cn.guliandigital.storage.storage.service.ITTaskSettleService;


/**
 * 数据入库配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
@Service
public class TTaskSettleServiceImpl implements ITTaskSettleService
{
    @Autowired
    private TTaskSettleMapper tTaskSettleMapper;
    
  
    

    /**
     * 查询数据入库配置
     * 
     * @param id 数据入库配置ID
     * @return 数据入库配置
     */
    @Override
    public TTaskSettle selectTTaskSettleById(String id)
    {
        return tTaskSettleMapper.selectTTaskSettleById(id);
    }

    /**
     * 查询数据入库配置列表
     * 
     * @param tTaskSettle 数据入库配置
     * @return 数据入库配置
     */
    @Override
    public List<TTaskSettle> selectTTaskSettleList(TTaskSettle tTaskSettle)
    {

        return tTaskSettleMapper.selectTTaskSettleList(tTaskSettle);
    }

    /**
     * 新增数据入库配置
     * 
     * @param tTaskSettle 数据入库配置
     * @return 结果
     */
    @Override
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    public int insertTTaskSettle(TTaskSettle tTaskSettle)
    {
    	if(Strings.isNullOrEmpty(tTaskSettle.getId())) {
    		tTaskSettle.setId(IdUtils.simpleUUID());
    	}
        if(Strings.isNullOrEmpty(tTaskSettle.getDataStatus())) {
        	tTaskSettle.setDataStatus(DataStatusEnum.READY.getCode());
        }
        if(Strings.isNullOrEmpty(tTaskSettle.getInStatus())) {
        	tTaskSettle.setInStatus(InStatusEnum.ING.getCode());
        }
        tTaskSettle.setDelFlag(0);        
        
        if(Strings.isNullOrEmpty(tTaskSettle.getCreatebyName())) {
        	LoginUser user = SecurityUtils.getLoginUser();
        	tTaskSettle.setCreatebyName(user.getUser().getNickName());       
        	tTaskSettle.setCreatebyId(user.getUser().getUserId().toString());
        }
        tTaskSettle.setCreateTime(DateUtil.getCuurentDate());
        //获取资源量
        try {
            getResourceCount(tTaskSettle);
        } catch (Exception e) {
            tTaskSettle.setFullCount(0);
            tTaskSettle.setPdfCount(0);
        }
        return tTaskSettleMapper.insertTTaskSettle(tTaskSettle);
    }

    /**
     * 获取资源量
     * @param settle
     */
    private void getResourceCount(TTaskSettle settle) throws Exception {
        File file=new File(settle.getDataPath());
        int pdf = 0;
        int full=0;
        File[] files = file.listFiles();
        assert files != null;
        for (File f:files){
            if (f.isDirectory()){
                if ("全文".equals(f.getName())){
                    //全文数据量
                    long count = Arrays.stream(Objects.requireNonNull(f.listFiles())).filter(File::isDirectory).count();
                    full+=(int) count;
                }else if ("pdf".equalsIgnoreCase(f.getName())){
                    //解析pdf
                    ExcelUtil<ImportPDF> excelUtil=new ExcelUtil<>(ImportPDF.class);
                    List<File> collect = Arrays.stream(Objects.requireNonNull(f.listFiles()))
                            .filter(fl->StringUtil.endsWithAnyIgnoreCase(FilenameUtils.getExtension(fl.getName()), "xlsx", "xls")).collect(Collectors.toList());
                    for (File excel:collect){
                        List<ImportPDF> list = excelUtil.importExcel(new FileInputStream(excel));
                        pdf+=list.size();
                    }
                }
            }
        }
        settle.setFullCount(full);
        settle.setPdfCount(pdf);
    }

    /**
     * 修改数据入库配置
     * 
     * @param tTaskSettle 数据入库配置
     * @return 结果
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    @Override
    public int updateTTaskSettle(TTaskSettle tTaskSettle)
    {
        tTaskSettle.setUpdateTime(DateUtil.getCuurentDate());
        return tTaskSettleMapper.updateTTaskSettle(tTaskSettle);
    }

    /**
     * 批量删除数据入库配置
     * 
     * @param ids 需要删除的数据入库配置ID
     * @return 结果
     */
    @Override
    public int deleteTTaskSettleByIds(String[] ids)
    {
        return tTaskSettleMapper.deleteTTaskSettleByIds(ids);
    }

    /**
     * 删除数据入库配置信息
     * 
     * @param id 数据入库配置ID
     * @return 结果
     */
    @Override
    public int deleteTTaskSettleById(String id)
    {
        return tTaskSettleMapper.deleteTTaskSettleById(id);
    }

    @Override
    public int updateStartTask(String id) {
    	
        return tTaskSettleMapper.updateStatus(id);
    }
/**
 * <AUTHOR>
 * @Description 删除
 * @Date 2020/10/15 16:28
 * @param ids:
 **/
    @Override
    public int updateSettle(String[] ids) {
        return tTaskSettleMapper.updateSettle(ids);
    }
}
