package cn.guliandigital.storage.storage.service.impl;


import java.io.File;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.guliandigital.common.config.HuangHeConfig;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.jsoup.Jsoup;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.guliandigital.common.enums.ProStatusEnum;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.BigwordUtil;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.TransferUtils;
import cn.guliandigital.common.utils.TxtUtil;
import cn.guliandigital.common.utils.file.FileUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.storage.storage.domain.XmlBean;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * 解析xml工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class XmlToolUtils {

	
	
	/**
	 * 根据目录获取所有相关文件
	 * @param rootPath
	 * @param dirPath
	 * @param fileMap
	 */
	public static void getAllFileByPerBook(String rootPath,String dirPath ,Map<String,String> fileMap){
		
		File dir = new File(dirPath);
		if(dir.isDirectory()) {
			File[] files = dir.listFiles(new FilenameFilter() {				
	            @Override
	            public boolean accept(File dir, String name) {
	                String fileName = name;
	                File path = new File(dir+File.separator+name);
	                //log.info(path.getAbsolutePath());
	                if(path.isDirectory()) {
	                	return true;
	                }
	                if (fileName.endsWith(".xml")||fileName.endsWith(".xpro")) {
	                	log.info("==>匹配到文件：{}",fileName);
	                    return true;	                    
	                }
	                return false;
	            }
	        });
			for(File ff : files) {
				if(ff.isDirectory()) {
					getAllFileByPerBook(rootPath, ff.getAbsolutePath(),fileMap);
				}else {
					String key = StringUtil.replace(ff.getAbsolutePath(), rootPath, "");
					key = FilenameUtils.normalize(key, true);
					key = StringUtil.removeStart(key, "/");
					fileMap.put(key, ff.getAbsolutePath());
				}
			}			
		}
	}
	
	/**
	 * 解析工程文件
	 * @param fileMap
	 * @throws Exception 
	 */
	public static TProBooks parseMainXpro4Meta(Map<String,String> fileMap) throws Exception {
		TProBooks bean = new TProBooks();
		try {
			String xproPath = fileMap.get("main.xpro");
			File xmlfile = new File(xproPath);
			if (!xmlfile.exists()) {
				throw new Exception("main.xpro工程文件不存在");
			}
			SAXReader reader = new SAXReader();
			Document document = reader.read(xmlfile);
			Element root = document.getRootElement();// 获取根元素
			List<Element> childElements = root.elements();//

			Element bookE = root.element("书籍");
			if (bookE == null) {
				throw new Exception("<书籍>不存在");
			}
			Element bookInfoE = bookE.element("书籍信息");
			if (bookInfoE == null) {
				throw new Exception("<书籍信息>不存在");
			}
			Element bookNameE = element(bookInfoE, "题名", "題名");
			if (bookNameE == null) {
				throw new Exception("<题名>不存在");
			}
			String bookName = bookNameE.getText();
			Element coverPathE = bookInfoE.element("缩微图");
			if (coverPathE == null) {
				throw new Exception("<缩微图>不存在");
			}
			String coverPath = coverPathE.getText();
			/*
			 * 拷贝文件到图片路径下
			 */

			File srcFile = new File(FilenameUtils.getFullPath(xproPath) + File.separator + coverPath);
			/// profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			String uploadpath = HuangHeConfig.getUploadPath();
			String extension = FilenameUtils.getExtension(coverPath);
			String fileName = DateUtil.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
			String thumbPath = uploadpath + File.separator + fileName;
			File destFile = new File(FilenameUtils.normalize(thumbPath));
			if (!destFile.getParentFile().exists()) {
				destFile.getParentFile().mkdirs();
			}

			FileUtil.copyFile(srcFile, destFile);
			String thumbPathSave = StringUtil.remove(thumbPath, uploadpath);
			thumbPathSave = "/profile/upload/" + thumbPathSave;
			thumbPathSave = FilenameUtils.normalize(thumbPathSave, true);

			log.info("==>bookName:" + bookName + ",coverPath=" + coverPath);
			// <其他信息>
			Element otherinfo = bookInfoE.element("其他信息");
			if (otherinfo != null) {
				String resourceType = otherinfo.element("资源类型") != null ? otherinfo.element("资源类型").getText() : "";
				String collection = otherinfo.element("馆藏") != null ? otherinfo.element("馆藏").getText() : "";
				String siClassification = otherinfo.element("四部分类法") != null ? otherinfo.element("四部分类法").getText()
						: "";
				String ztfenlei = otherinfo.element("中图分类法") != null ? otherinfo.element("中图分类法").getText() : "";
				// String descriptionNo = otherinfo.element("著录号").getText();
				String uniqueId = otherinfo.element("唯一标识符") != null ? otherinfo.element("唯一标识符").getText() : "";
				// String isbn = otherinfo.element("标准编号").getText();
				// String language = otherinfo.element("语种").getText();
				String congbian = otherinfo.element("丛编")!=null ? otherinfo.element("丛编").getText():"";
				// String dbName = otherinfo.element("丛编") != null ? otherinfo.element("丛编").getText() : "";
				String summary = otherinfo.element("提要") != null ? otherinfo.element("提要").getText() : "";
				// String yinci = otherinfo.element("印次").getText();
				String banci = otherinfo.element("版次") != null ? otherinfo.element("版次").getText() : "";
				// String kaiben = otherinfo.element("开本信息").getText();
				// String zhuangding = otherinfo.element("装订形式").getText();
				String banben = otherinfo.element("版本") != null ?otherinfo.element("版本").getText(): "";
				String price = otherinfo.element("定价") != null ? otherinfo.element("定价").getText() : "";
				// 其他分类 -方志編/縣志
				String otherclass = otherinfo.element("其他分类") != null ? otherinfo.element("其他分类").getText() : "";

				String subclass = otherinfo.element("分类") != null ? otherinfo.element("分类").getText() : "";
				String guancang = otherinfo.element("馆藏") != null ? otherinfo.element("馆藏").getText() : "";
				
				otherclass = StringUtil.defaultIfBlank(otherclass, subclass);
				// 未读取到分类使用其他编
				otherclass = StringUtil.defaultIfBlank(otherclass, "其他编");

				// 转简体
				if (!Strings.isNullOrEmpty(otherclass)) {
					String otherclassS = ZhConverterUtil.convertToSimple(otherclass);
					log.info("==>转简体：{} TO {}", otherclass, otherclassS);
					otherclass = otherclassS;
					List<String> oclist = StringUtil.str2List(otherclass, "/", true, true);
					//
					bean.setDbId("");
					String _dbName = oclist.get(0);
					if (StringUtil.contains(_dbName, "类")) {
						_dbName = StringUtil.replace(_dbName, "类", "编");
					}
					if(StringUtil.isNotBlank(_dbName)) {
						_dbName = ZhConverterUtil.convertToSimple(_dbName);
					}
					bean.setDbName(_dbName);

//					if (otherclass.contains("方志")) {
//						otherclass = "甲编." + otherclass;
//					} else if (otherclass.contains("史料")) {
//						otherclass = "乙编." + otherclass;
//					} else if (otherclass.contains("档案")) {
//						otherclass = "丙编." + otherclass;
//					} else if (otherclass.contains("文献 ")) {
//						otherclass = "丁编." + otherclass;
//					}

					otherclass = StringUtil.replace(otherclass, "編", "类");
				}
				bean.setConglomeration(congbian);
				//bean.setCollection(guancang);

				bean.setSiClassification(siClassification);
				// 查询四部分类ID
				bean.setSiClassificationId("-");

				bean.setResourceType(resourceType);

				bean.setResourceClasses(otherclass);

				// bean.setPublishYear(publishYear);
				// bean.setClassification(classification);//其他分类
				bean.setBookDesc(summary);
				bean.setEdition(banben);
				bean.setRevision(banci);

				// 价格 1800.00元 1000.00元（合刊）
				// price = StringUtil.remove(price, "元");
				bean.setPrice(price);
				bean.setClassiMethodCode(ztfenlei);
				
//				if(Strings.isNullOrEmpty(uniqueId)) {
//					uniqueId = "ZHB"+"";
//				}
				bean.setUniqueId(uniqueId);
				//bean.setCollection(collection);
			}
			Element zereninfo = bookInfoE.element("责任信息");
			if (zereninfo != null) {
				String mainResponsibility = zereninfo.element("主要责任者") != null ? zereninfo.element("主要责任者").getText()
						: "";
				bean.setMainResponsibility(mainResponsibility);
			}

			Element publishinfo = bookInfoE.element("出版信息");
			if (publishinfo != null) {
				String publishDate = publishinfo.element("出版日期") != null ? publishinfo.element("出版日期").getText() : "";
				String publisher = publishinfo.element("出版者") != null ? publishinfo.element("出版者").getText() : "";
				String publishland = publishinfo.element("出版地") != null ? publishinfo.element("出版地").getText() : "";
				// String printDate = publishinfo.element("印刷日期").getText();
				bean.setPublishDate(publishDate);
				bean.setPublisher(publisher);
				bean.setPublishland(publishland);
			}
			// log.info("==>resourceType:"+resourceType+",descriptionNo="+descriptionNo);
			bean.setId(IdUtil.simpleUUID());

			bean.setBookName(bookName);

			bean.setThumbCoverUrl(thumbPathSave);

			bean.setCoverUrl(thumbPathSave);

			bean.setProStatus(ProStatusEnum.OFFLINE.getCode());

			bean.setDelFlag(0);

			bean.setDisplay(1);

			List<String> xmllist = Lists.newArrayList();
			Element files = bookE.element("文件目录");

			if(files==null){
				throw new ServiceException("文件目录不存在！");
			}
			Element bufen = files.element("部分");
			if(bufen==null){
				throw new ServiceException("部分不存在！");
			}
			String name = bufen.attribute("名称").getText();
			if(StringUtils.equals(name, "正文")) {
				List<Element> fs = bufen.elements();
				for (Element fe : fs) {
					if(fe!=null){
						String xmlpath = fe.attributeValue("路径");
						xmllist.add(FilenameUtils.normalize(xmlpath, true));
					}
				}
			}
			List<Element> _fs = files.elements();
			for (Element fe : _fs) {
				String _name = fe.attribute("名称").getText();
				if(StringUtils.equals(_name, "正文")) {
					List<Element> _fss = fe.elements();
					for (Element _fe : _fss) {
						String xmlpath = _fe.attributeValue("路径");
						// log.info(xmlpath);
						xmllist.add(FilenameUtils.normalize(xmlpath, true));
					}
				}
			}

			List<String> _list = xmllist.stream().distinct().collect(Collectors.toList());
			bean.setXmlFileList(_list);

		}catch(Exception e) {
			log.error("error,",e);
			throw e;
		}
		
		return bean;
	}

	/**
	 * 解析xml，获取标题与内容
	 * @param book
	 * @param fileMap
	 * @return
	 * @throws Exception
	 */
	public static TProBookMenu parseBookMenuContentXml(TProBooks book,Map<String,String> fileMap,String bookRootPath,String picHttpUrls) throws Exception {
		try {
			
			//RedisConstants.HUANGHE_TEMP_MAP.clear();
			//xmlPath = "D:\\中华经典古籍库-工程文件\\金陵全书17种（入库修订完成）20200220\\ZSK12445 景定建康志[刘玉娇][20191216-20200102]\\items\\正文1.xml";
			//xmlPath = "C:\\Users\\<USER>\\Desktop\\正文1-test.xml";
			List<XmlBean> xmlContentlist = Lists.newArrayList();
			Map<String,String> zhuhaoRefMap = Maps.newHashMap();
			//去掉空间
			List<String> _xmllist = book.getXmlFileList();
			log.info("fileMap==>"+fileMap);
			log.info("==>_xmllist={}",_xmllist.size());
			for(String xmlname : _xmllist) {
				log.info("==>开始解析文件：{}", xmlname);
				if(fileMap.containsKey(xmlname)) {
					String xmlPath = fileMap.get(xmlname);
					log.info("==>开始解析文件{}",xmlPath);
					String xmlStr = TxtUtil.readTxtFile(xmlPath);
//					xmlStr = StringUtil.remove(xmlStr, " xmlns=\"http://shangyuan/shuju_yuliao\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
//					xmlStr = StringUtil.remove(xmlStr, " xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns=\"http://shangyuan/shuju_yuliao\"");
//					if(StringUtils.contains(xmlStr, "shuju_yuliao")) {
//						throw new ServiceException("xml里含有shuju_yuliao");
//					}
					xmlStr = StringUtils.remove(xmlStr, "xmlns=\"http://shangyuan/shuju_yuliao\"");
					xmlStr = StringUtils.remove(xmlStr, "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
					//xmlStr = StringUtils.remove(xmlStr, " ");
					if(StringUtils.contains(xmlStr, "shuju_yuliao")) {
						throw new ServiceException("xml里含有shuju_yuliao");
					}
					/*
					 * 使用正则表达式替换  专有名词 <专有名词 type="1">宏</专有名词>   ==》 <div class=”zhuanming”></div>
					 */
//					xmlStr=StringUtil.replace(xmlStr, "<专有名词", "<span class=\"zhuanming\" ");
//					xmlStr=StringUtil.replace(xmlStr, "</专有名词>", "</span>");
					
					xmlStr=StringUtil.replace(xmlStr, "<专有名词", "<专名");
					xmlStr=StringUtil.replace(xmlStr, "</专有名词>", "</专名>");
					
					xmlStr=StringUtil.replace(xmlStr, "<b>", "");					
					xmlStr=StringUtil.replace(xmlStr, "</b>", "");
					xmlStr=StringUtil.replace(xmlStr, "<文字>", "");
					xmlStr=StringUtil.replace(xmlStr, "</文字>", "");
					xmlStr=StringUtil.replace(xmlStr, "<字词注>", "");
					xmlStr=StringUtil.replace(xmlStr, "</字词注>", "");
					xmlStr=StringUtil.replace(xmlStr, "<特殊数字>", "");
					xmlStr=StringUtil.replace(xmlStr, "</特殊数字>", "");
					
					xmlStr=StringUtil.replace(xmlStr, "<书签 id=\"[0-9]{1,6}\"/>", "");
					xmlStr=StringUtil.replace(xmlStr, "<书签 id=\"[0-9]{1,6}\" />", "");
					xmlStr = StringUtils.replaceAll(xmlStr, "\\n\\s*", "\n");					
					xmlStr=StringUtil.replace(xmlStr, "\r\n", "");
					xmlStr=StringUtil.replace(xmlStr, "\n", "");
					
					Document document = DocumentHelper.parseText(xmlStr);
					LinkedHashMap<String,String> mainMap = Maps.newLinkedHashMap();
					
					Element root = document.getRootElement();//获取根元素
				    List<Element> childElements = root.elements();//
				    StringBuffer zwbuf = new StringBuffer();
				    String parentBT = null;
				    String parentLevel = null;
				    String parentId = null;
				    int totalcount = childElements.size();
				    int currentcount = 0;
				    int biaoticount = 0;
				    List<String> biaotilist = Lists.newArrayList();
				    for(Element content : childElements) {
				    	currentcount++;
				    	String tagName = content.getName();
				    	//log.info("==>"+content.asXML());
				    	if("标题".equals(tagName)) {
				    		
				    		String level = content.attributeValue("level");
				    		String isget = content.attributeValue("是否提取");
				    		String biaoti = content.attributeValue("提取标题");
				    		String id = content.attributeValue("id");
				    		String isshow = content.attributeValue("是否显示");
				    		
				    		//isget = "是";
				    		StringBuffer tempbtbuf = new StringBuffer();
				    		parseBiaoti(book, xmlPath, content, tagName, tempbtbuf,zhuhaoRefMap);
				    		
				    		//String bookviewbt = content.getText();	
				    		if(Strings.isNullOrEmpty(biaoti)) {
				    			log.info("==>没有提取标题:{}",tempbtbuf);
				    			//没有提取标题字段				    			
				    			String tempbt = tempbtbuf.toString();
				    			tempbt = StringUtil.replace(tempbt, "<div class=\"biaoti"+level+"\">", "");
				    			tempbt = StringUtil.removeEnd(tempbt, "</div>");
				    			biaoti = tempbt;
				    		}
//				    		if(StringUtils.equals(biaoti, "黨員讀本")) {
//				    			log.info("黨員讀本");
//				    		}
				    		int int_level = Integer.parseInt(level);
				    		//log.info("==>"+bookviewbt+", "+level + ","+ isget+","+biaoti);  					    		
				    		if("是".equals(isget)) {	
				    			
				    			if(int_level <=3 ) {
					    			parentBT = biaoti; 
						    		parentLevel = level;	
						    		parentId = id;
					    		}	
				    			String mapKey = parentBT+"_"+parentLevel+"_"+parentId;
				    			//取出上一个标题
				    			String biaotiKey = mapKey;
				    			if(biaoticount >0) {
				    				biaotiKey = biaotilist.get(biaoticount-1);
				    				mainMap.put(biaotiKey, zwbuf.toString());
				    				if(int_level <= 3 ) {
				    					zwbuf.setLength(0);
				    				}
				    			}
				    			biaoticount ++;				    			
				    			//增加到标题
				    			biaotilist.add(mapKey);

					    		//parseBiaoti(book, xmlPath, content, tagName, zwbuf, picHttpUrl);'
				    			if(StringUtil.equalsIgnoreCase(isshow, "是")) {
				    				zwbuf.append(tempbtbuf);
				    			}					  
				    			
//				    			if(StringUtils.equals(book.getBookName(), "黨員讀本")) {
//				    				String temp_menu = RedisConstants.HUANGHE_TEMP_MAP.get("temp_menu");
//				    				if(!Strings.isNullOrEmpty(temp_menu) && !StringUtils.equals(temp_menu, "AAAAA")) {
//					    				//zwbuf.insert(0, RedisConstants.HUANGHE_TEMP_MAP.get("temp_menu"));
//					    				zwbuf.append(RedisConstants.HUANGHE_TEMP_MAP.get("temp_menu"));
//					    				RedisConstants.HUANGHE_TEMP_MAP.put("temp_menu", "AAAAA");
//					    				log.info(zwbuf.toString());
//					    			}
//				    			}
//					    		if(StringUtils.equals(biaoti, "第一章 共産黨") && int_level == 2) {
//					    			zwbuf.insert(0, "<div class=\"biaoti1\">黨員讀本</div>");
//					    		}
				    			
					    		mainMap.put(mapKey, zwbuf.toString());
				    		}else {
//				    			if(StringUtils.equals(book.getBookName(), "黨員讀本")) {
//					    			if(int_level == 1 && StringUtils.equals("黨員讀本", biaoti)) {
//					    				//log.info("===>黨員讀本  特殊处理");				    				
//					    				RedisConstants.HUANGHE_TEMP_MAP.put("temp_menu", tempbtbuf.toString());
//					    			}
//				    			}
				    			
//				    			if(!RedisConstants.HUANGHE_TEMP_MAP.isEmpty()) {
//				    				//log.info("===>黨員讀本之后  特殊处理");				    				
//				    				RedisConstants.HUANGHE_TEMP_MAP.put("temp_menu", tempbtbuf.toString());
//				    			}else {
					    			//提取标题 是否提取=false
					    			Iterator<Entry<String,String>> iterator = mainMap.entrySet().iterator();
					    		    Entry<String, String> tail = null;
					    		    while (iterator.hasNext()) {
					    		        tail = iterator.next();
					    		    }
					    		    if(StringUtil.equalsIgnoreCase(isshow, "是")) {
					    				zwbuf.append(tempbtbuf);
					    			}
					    		    if(tail != null) {
						    		    String key = tail.getKey();
						    		    String val = tail.getValue();
						    		    
						    			mainMap.put(key, zwbuf.toString());
					    		    }
				    			//}
				    		}
				    	}
				    	log.info("==>mainMap:{}",mainMap);
				    	if("table".equals(tagName)) {
				    		log.info("==>开始处理table标签......");
				    		
				    		String tableContent = parseTableTag(book, xmlPath, content, zwbuf,zhuhaoRefMap);				    		
				    		zwbuf.append(tableContent);
				    	}
				    	
				    
				    	if(tagName.contains("目录项")) {
				    		Element mlbt = content.element("目录标题");
				    		if(mlbt != null) {
					    		String biaoti = mlbt.getText();
					    		//log.info(mlbt.getText());
					    		if(!Strings.isNullOrEmpty(parentBT)) {
					    			mainMap.put(parentBT+"_"+parentLevel, zwbuf.toString());
					    			zwbuf.setLength(0);
					    		}
					    		
					    		parentBT = biaoti; 
					    		parentId = "";
					    		parentLevel = "1";					    		
					    		
					    		//zwbuf.append("<h2>").append(biaoti).append("<h2>");			
					    		zwbuf.append("<div class=\"mlbt1\">").append(biaoti).append("</div>");	
				    		}				    		
				    		Element bt = content.element("标题");
				    		if(bt != null) {
					    		String btlevel = bt.attributeValue("level");
					    		//String btString = bt.getText();
					    		//需要按照标题解析
					    		//可能会有多个正文
					    		List<Element> duanluoelements = bt.elements();

					    		for(Element zw_element : duanluoelements) {
					    			//正文中会有如下标签： 字体、原书页面、注释
					    			//log.info("==>"+zw_element.asXML());
					    			
					    			String duanTagName = zw_element.getName();
//					    			if(zw_element.getText().contains("府治洪武初自集慶路徙古")) {
//					    				log.info("==>test");
//					    			}
					    			if("专名".equals(duanTagName)) {
					    				Element _e = (Element)zw_element;

					    				//增加新标签
					    				_e.setName("span");
					    				Attribute deleteAttri = _e.attribute("type");
				    		    		if(deleteAttri != null) {
				    		    			_e.remove(deleteAttri);
				    		    		}
					    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
					    				_e.add(_newattri);		
					    				//log.info(zw_element.asXML());
					    			}		
					    			
					    			if("字图".equals(duanTagName)||"外字".equals(duanTagName)) {
					    				Element _e = (Element)zw_element;
					    				String picsrc = _e.attributeValue("src");
					    				//转换为http:// 形式
					    				String httpUrl = null;
										//log.info("FilenameUtils.getFullPath(xmlPath)==="+FilenameUtils.getFullPath(xmlPath));
					    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
					    				//log.info("src:"+srcFile.getAbsolutePath());
							            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
							            String uploadpath = HuangHeConfig.getUploadPath();
							            
							            
							            uploadpath = FilenameUtils.normalize(uploadpath);
							            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
										//log.info("==>拷贝图片：{}",destPath);
									    File destDir = new File(destPath);
									    if(!destDir.getParentFile().exists()) {
									    	destDir.getParentFile().mkdirs();
									    }									   
									    if(srcFile.exists()) {
									    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
									    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
									    }else {
									    	//排查是否路径有问题
									    	if(StringUtils.contains(picsrc, "items")) {									    		
									    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
									    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
									    		if(!srcFile.exists()) {
									    			throw new ServiceException(picsrc+"文件不存在");
									    		}
									    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
									    	}
									    }
									    
									    //组装http链接
									    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//									    String domain = picHttpUrl;
//									    if(Strings.isNullOrEmpty(domain)) {
//									    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
//									    }
									    String domain = "%domain";
									    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
							            //log.info(httpUrl);
					    				Element pica = DocumentHelper.createElement("img");
					    				pica.addAttribute("src", httpUrl);
					    				pica.addAttribute("class", "zitu");
					    				pica.addText(zw_element.getText());
					    				List elepar = zw_element.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zw_element), pica);		    			
				    				}		
					    				//log.info(tagname);
									if("字体".equals(duanTagName)){
					    				String _ftype = zw_element.attributeValue("type");
					    				//log.info(node.getText()+","+ftype);
					    				
					    				Element span = DocumentHelper.createElement("span");
					    				String hexstr = null;
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zw_element.getText();
						    				hexstr = BigwordUtil.hex2word(bigtext);
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zw_element.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zw_element.getText();
					    				}
					    				
					    				span.addText(hexstr);
					    				
					    				List elepar = zw_element.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zw_element), span);
									}
									
					    			
					    			if("原书页面".equals(duanTagName)) {
					    							    				
					    				Element _e = (Element)zw_element;
					    				String ysym = _e.getText();
					    				ysym = StringUtil.trimAll(ysym);
					    				zw_element.setName("a");		 
					    				//删除多余属性
							    		Attribute deleteAttri = zw_element.attribute("destsrc");
							    		if(deleteAttri != null) {
							    			zw_element.remove(deleteAttri);
							    		}
							    		
					    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
					    				zw_element.add(newattri);	 
					    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
					    				zw_element.add(idattri);	 
					    				//log.info(content.asXML());
					    				String hanshu = "openReader('"+ysym+"')";
					    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
					    				zw_element.add(clickatt);	
					    				//截取最后5位
					    				String ym = StringUtil.substring(ysym,ysym.length()-5);
					    				ym = StringUtil.trimAll(ym);
					    				ym = StringUtils.replace(ym, " ", "");
					    				int yamaInt = 0;
					    				try {
						    				yamaInt = Integer.parseInt(ym);			    				
					    				}catch(Exception e) {
					    					throw new ServiceException("原书页面"+ysym+"不符合规范");
					    				}
					    				zw_element.setText("P"+String.valueOf(yamaInt));
					    				//zwbuf.append(zw_element.asXML());	    			
					    			}
					    		   			
					    			if("注释".equals(duanTagName)) {
					    				//log.info("==>"+zw_element.asXML());
					    				String ftype = zw_element.attributeValue("type");
					    				String divtype = null;
					    				if("注".equals(ftype)) {
					    					divtype = "zhu";
					    				}else if("落款".equals(ftype)) {
					    					divtype = "luokuan";
					    				}else if("眉批".equals(ftype)) {
					    					divtype = "meipi";
					    				}else {
					    					divtype = "zhu";
					    				}
					    				
					    				//删除原有的标签
					    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
					    				zw_element.remove(delattri);
					    				
					    				//增加新标签
					    				zw_element.setName("span");
					    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
					    				zw_element.add(newattri);		
					    				
					    				//log.info(zw_element.asXML());
					    				/**
					    				 * 可能会有大字
					    				 */
					    				List<Element> bigwordelements = zw_element.elements();
					    				for(Element bigelement : bigwordelements) {
						    				String tagname = bigelement.getName();
						    				//log.info(tagname);
						    				if("字体".equals(tagname)){
							    				String _ftype = bigelement.attributeValue("type");
							    				//log.info(node.getText()+","+ftype);
							    				
							    				Element span = DocumentHelper.createElement("span");
							    				String hexstr = null;
							    				if(_ftype.contains("超大字2")) {
							    					span.addAttribute("class", "big02");
							    					//大字统一转&#x码
								    				String bigtext = bigelement.getText();
								    				//hexstr = BigwordUtil.str2Hex(bigtext);
								    				hexstr = BigwordUtil.hex2word(bigtext);
							    				}else if(_ftype.contains("超大字3")){
							    					span.addAttribute("class", "big15");
							    					//大字统一转&#x码
								    				String bigtext = bigelement.getText();
								    				//hexstr = BigwordUtil.str2Hex(bigtext);
								    				hexstr = BigwordUtil.hex2word(bigtext);
							    				}else if(_ftype.contains("超大字1")) {
							    					span.addAttribute("class", "big01");
							    					hexstr = zw_element.getText();
							    					hexstr = BigwordUtil.hex2word(hexstr);
							    				}else {
							    					span.addAttribute("class", _ftype);
							    					hexstr = bigelement.getText();
							    				}
							    				
							    				span.addText(hexstr);
							    				List elepar = bigelement.getParent().content();
							    				// 用content标签替换文本节点
							    				elepar.set(elepar.indexOf(bigelement), span);
						    				}
						    				//log.info(zw_element.asXML());
						    				if("原书页面".equals(tagname)) {
									    		String ysym = bigelement.getText();
									    		ysym = StringUtil.trimAll(ysym);
									    		//log.info("==>"+tagName +":"+ysym);
									    		//增加新标签
									    		bigelement.setName("a");		
									    		//删除多余属性
									    		Attribute deleteAttri = bigelement.attribute("destsrc");
									    		if(deleteAttri != null) {
									    			bigelement.remove(deleteAttri);
									    		}
									    		
							    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
							    				bigelement.add(zspageattri);	 
							    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
							    				bigelement.add(idattri);	 
							    				//log.info(content.asXML());
							    				String hanshu = "openReader('"+ysym+"')";
							    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
							    				bigelement.add(clickatt);	
							    				//截取最后5位
							    				String ym = StringUtil.substring(ysym,ysym.length()-5);
							    				ym = StringUtil.trimAll(ym);
							    				ym = StringUtils.replace(ym, " ", "");
							    				int yamaInt = 0;
							    				try {
								    				yamaInt = Integer.parseInt(ym);			    				
							    				}catch(Exception e) {
							    					throw new ServiceException("原书页面"+ysym+"不符合规范");
							    				}
							    				bigelement.setText("P"+String.valueOf(yamaInt));
							    				//zwbuf.append(bigelement.asXML());
									    	}
						    				//log.info(bigelement.asXML());
						    				if("字图".equals(tagname)||"外字".equals(tagname)) {
							    				Element _e = (Element)bigelement;
							    				String picsrc = _e.attributeValue("src");
							    				//转换为http:// 形式
							    				String httpUrl = null;
							    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
							    				//log.info("src:"+srcFile.getAbsolutePath());
									            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
									            String uploadpath = HuangHeConfig.getUploadPath();
									            
									            
									            uploadpath = FilenameUtils.normalize(uploadpath);
									            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
												//log.info("==>拷贝图片：{}",destPath);
											    File destDir = new File(destPath);
											    if(!destDir.getParentFile().exists()) {
											    	destDir.getParentFile().mkdirs();
											    }									   
											    if(srcFile.exists()) {
											    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
											    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
											    }else {
											    	//排查是否路径有问题
											    	if(StringUtils.contains(picsrc, "items")) {									    		
											    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
											    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
											    		if(!srcFile.exists()) {
											    			throw new ServiceException(picsrc+"文件不存在");
											    		}
											    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
											    	}
											    }
											    
											    //组装http链接
											    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//											    String domain = picHttpUrl;
//											    if(Strings.isNullOrEmpty(domain)) {
//											    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
//											    }
											    String domain = "%domain";
											    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
									            //log.info(httpUrl);
							    				Element pica = DocumentHelper.createElement("img");
							    				pica.addAttribute("src", httpUrl);
							    				pica.addAttribute("class", "zitu");
							    				pica.addText(bigelement.getText());
							    				List elepar = bigelement.getParent().content();
							    				// 用content标签替换文本节点
							    				elepar.set(elepar.indexOf(bigelement), pica);		    			
						    				}			
						    						
					    				}
					    			}	
					    		}
					    		List<Attribute> attlist = bt.attributes();
					    		//log.info("==>"+attlist.size());
					    		
					    		List<Attribute> alllist = Lists.newArrayList();
					    		alllist.addAll(attlist);
					    		for(int i = 0;i < alllist.size(); i++) {
					    			//log.info(alllist.get(i).getName()+"==>"+alllist.size());    			
					    			bt.remove(alllist.get(i));
					    		}
					    		String btString = bt.asXML();
					    		btString = StringUtil.replace(btString, "<标题>", "");
					    		btString = StringUtil.replace(btString, "</标题>", "");					    
					    		btString = StringUtil.replace(btString, "&amp;", "&");
					    		//log.info(btString);
					    			//zwbuf.append(xmlstr);
					    		zwbuf.append("<div class=\"mlbt"+btlevel+"\">").append(btString).append("</div>");			
				    		}
				    	}
				    	//System.out.print("==>"+tagName);
				    	// 获取插图
				    	if(xmlname.contains("封面")) {
				    		if(tagName.equals("插图")) {
				    			String picsrc = content.attributeValue("src");
				    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
					            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
					            String uploadpath = HuangHeConfig.getUploadPath();
					            String extension = FilenameUtils.getExtension(picsrc);
					            String fileName = DateUtil.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
					            String thumbPath =uploadpath+File.separator+fileName;
					            File destFile = new File(FilenameUtils.normalize(thumbPath));
					            if(!destFile.getParentFile().exists()) {
					            	destFile.getParentFile().mkdirs();
					            	log.info("==>创建文件夹成功 {}",destFile.getAbsolutePath());
					            }
					           
					            if(srcFile.exists()) {
							    	log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
							    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
							    }else {
							    	//排查是否路径有问题
							    	if(StringUtils.contains(picsrc, "items")) {									    		
							    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
							    		log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
							    		if(!srcFile.exists()) {
							    			throw new ServiceException(picsrc+"文件不存在");
							    		}
							    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
							    	}
							    }
					            String coverUrl = StringUtil.remove(thumbPath, uploadpath);
					            coverUrl = "/profile/upload/"+coverUrl;
					            coverUrl = FilenameUtils.normalize(coverUrl,true);
					            //book.setCoverUrl(coverUrl);
					            log.info("==>解析图书封面图成功{}",coverUrl);
				    		}
				    	}
				    	if(tagName.equals("插图")) {
			    			String picsrc = content.attributeValue("src");
			    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            String uploadpath = HuangHeConfig.getUploadPath();
				            String extension = FilenameUtils.getExtension(picsrc);
				            String fileName = DateUtil.datePath() + "/" + IdUtils.fastUUID() + "." + extension;
				            String thumbPath =uploadpath+File.separator+fileName;
				            File destFile = new File(FilenameUtils.normalize(thumbPath));
				            if(!destFile.getParentFile().exists()) {
				            	destFile.getParentFile().mkdirs();
				            	log.info("==>创建文件夹成功 {}",destFile.getAbsolutePath());
				            }
				           
				            if(srcFile.exists()) {
						    	log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝插图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
						    	}
						    }
//				            String coverUrl = StringUtil.remove(thumbPath, uploadpath);
//				            coverUrl = "/profile/upload/"+coverUrl;
//				            coverUrl = FilenameUtils.normalize(coverUrl,true);
				          //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(), true), FilenameUtils.normalize(uploadpath, true));
//						    String domain = picHttpUrl;
//						    if(Strings.isNullOrEmpty(domain)) {
//						    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
//						    }
						    String domain = "%domain";
						    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
//		    				Element pica = DocumentHelper.createElement("img");
//		    				pica.addAttribute("src", httpUrl);
//		    				pica.addText(content.getText());
//		    				List elepar = content.getParent().content();
//		    				// 用content标签替换文本节点
//		    				elepar.set(elepar.indexOf(content), pica);          
//				            log.info(content.asXML());
				            
				            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("</img>");				            
				            
			    		}
				    	if("原书页面".equals(tagName)) {
				    		String ysym = content.getText();
				    		log.info(ysym);
				    		ysym = StringUtil.trimAll(ysym);
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		content.setName("a");
				    	    //删除多余属性
				    		Attribute deleteAttri = content.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			content.remove(deleteAttri);
				    		}
				    		
		    				Attribute newattri = DocumentHelper.createAttribute(content, "class", "page");
		    				content.add(newattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(content, "id", ysym);
		    				content.add(idattri);	 
		    				//log.info(content.asXML());
		    				//增加调动函数
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(content, "onclick", hanshu);
		    				content.add(clickatt);			    				
		    				
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trimAll(ym);
		    				ym = StringUtils.replace(ym, " ", "");
		    				int yamaInt = 0;
		    				try {
			    				yamaInt = Integer.parseInt(ym);			    				
		    				}catch(Exception e) {
		    					throw new ServiceException("原书页面"+ysym+"不符合规范");
		    				}
		    				content.setText("P"+String.valueOf(yamaInt));
		    				zwbuf.append(content.asXML());
				    	}
				    	
				    	if("段落".equals(tagName)) {	    
				    		//log.info("进入段落解析"+xmlPath+"====="+content);
				    		parseDuanluo(book, xmlPath, content, tagName, zwbuf, zhuhaoRefMap);
				    	}			    	
				    }
				    
				    if(currentcount == totalcount) {
				    	if(Strings.isNullOrEmpty(parentBT)) {
				    		
				    	}else {
				    		//log.info(zwbuf.toString());
				    		mainMap.put(parentBT+"_"+parentLevel+"_"+parentId, zwbuf.toString());
				    	}
				    }				
				    //log.info("==>"+mainMap);
				    String lastPage = null;
				    List<Map.Entry<String, String>> newlist = new ArrayList<>(mainMap.size());
				    List<Map.Entry<String, String>> list = new ArrayList<>(mainMap.entrySet());
			        			 
			        for(int i = 0;i < list.size(); i++) {
			            Map.Entry<String, String> entry = list.get(i);
			            
			            String biaoti = entry.getKey();//重刻景㝎建康志序_1  标题_level
				    	String duanluo = entry.getValue();
//				    	log.info(biaoti);
//				    	log.info(duanluo);
				    	
				    	if(!StringUtil.startsWith(duanluo, "<a class=\"page\"")&&!Strings.isNullOrEmpty(lastPage)) {
				    		duanluo = lastPage + duanluo;			    		
				    	}
				    	lastPage = findLastPage(duanluo);
				    	entry.setValue(duanluo);
				    	newlist.add(entry);
			        }
			        for(int i = 0;i < newlist.size(); i++) {
			        	Map.Entry<String, String> entry = newlist.get(i);
			            
			            String biaoti = entry.getKey();//重刻景㝎建康志序_1  标题_level
				    	String duanluo = entry.getValue();
				    	log.info(biaoti);
				    	log.info(duanluo);
				    	
			    		lastPage = findLastPage(duanluo);
			    		//log.info("lastPage=>"+lastPage);
			    		if(!Strings.isNullOrEmpty(lastPage)) {
				    		if(duanluo.endsWith(lastPage)) {
				    			Map.Entry<String, String> nextentry = null;
				    			if(i < list.size()-1) {
				    				nextentry = newlist.get(i+1);
				    			}
				    			if(nextentry != null) {
					    			String nextduanluo = nextentry.getValue();
					    			if(nextduanluo.contains(lastPage)) {
					    				duanluo = StringUtil.removeEnd(duanluo, lastPage);
					    			}
				    			}
				    		}
				    		
			    		}	   
			    		String[] bt = StringUtil.split(biaoti, "_");
				    	String level = bt[1];
				    	String btstr = bt[0];
				    	int _level = Integer.parseInt(level);
				    	
				    	XmlBean bean = new XmlBean();
				    	bean.setBiaoti(btstr);
				    	bean.setLevel(_level);
				    	bean.setZhengwen(duanluo);
				    	xmlContentlist.add(bean);
			        }
			        
				    //Map转list
//				    for(Entry<String, String> entry : mainMap.entrySet()) {
//				    	String biaoti = entry.getKey();//重刻景㝎建康志序_1  标题_level
//				    	String duanluo = entry.getValue();
//				    	log.info(biaoti+"==》"+duanluo);
//				    	
//				    	if(!StringUtil.startsWith(duanluo, "<a class=\"page\"")&&!Strings.isNullOrEmpty(lastPage)) {
//				    		duanluo = lastPage + duanluo;
//				    		lastPage = findLastPage(duanluo);
//				    		if(duanluo.endsWith(lastPage)) {
//				    			duanluo = StringUtil.removeEnd(duanluo, lastPage);
//				    		}
//				    		
//				    	}
//				    	//log.info(biaoti+"==》"+duanluo);
//				    
//			    		lastPage = findLastPage(duanluo);
//			    		//log.info("lastPage=>"+lastPage);
//			    		if(!Strings.isNullOrEmpty(lastPage)) {
//				    		if(duanluo.endsWith(lastPage)) {
//				    			//duanluo = StringUtil.removeEnd(duanluo, lastPage);
//				    			if(StringUtils.contains(duanluo, "DJB00001-00117-L00117")) {
//				    				log.info("===test==");
//				    			}
//				    			log.info("==>最后一个页码，保持到最后..."+lastPage);
//				    		}
//			    		}	    
//				    	
//				    	String[] bt = StringUtil.split(biaoti, "_");
//				    	String level = bt[1];
//				    	String btstr = bt[0];
//				    	int _level = Integer.parseInt(level);
//				    	
//				    	XmlBean bean = new XmlBean();
//				    	bean.setBiaoti(btstr);
//				    	bean.setLevel(_level);
//				    	bean.setZhengwen(duanluo);
//				    	xmlContentlist.add(bean);
//				    }
				    mainMap.clear();
				}
			}
			
			log.info("==>解析处理完成，生成菜单数据......");
			//赋值图书
			book.setXmlMenuContentList(xmlContentlist);
		    //对章节进行处理
			long display = 1;
			
		    TProBookMenu bookmenu = new TProBookMenu();
		    bookmenu.setBookId(book.getId());
		    bookmenu.setId(IdUtil.simpleUUID());
		    bookmenu.setMenuName(book.getBookName());
		    bookmenu.setPid("0");	
		    bookmenu.setDisplay(display);
		    bookmenu.setLevel(0);
		    bookmenu.setFullMenuName(book.getBookName());
		    TProBookMenu pmenu = null;	    
		    //StringBuffer fullMenuBuff = new StringBuffer();
		    
		    for(int i = 0;i < xmlContentlist.size(); i++) {
		    	display ++;
		    	XmlBean bean =  xmlContentlist.get(i);
		    	String biaoti = bean.getBiaoti();//重刻景㝎建康志序_1  标题_level
		    	String duanluo = bean.getZhengwen();
		    	duanluo = StringEscapeUtils.unescapeXml(duanluo);
		    	int _level = bean.getLevel();
		    	
		    	if(_level == 1) {
		    		//fullMenuBuff.setLength(0);
		    		//全路径		    				    		
		    		TProBookMenu menu = new TProBookMenu();
		    		menu.setBookId(book.getId());
		    		menu.setId(IdUtil.simpleUUID());
		    		menu.setMenuName(biaoti);
		    		menu.setPid(bookmenu.getId());		    	
		    		menu.setZwContent(duanluo);
		    		menu.setDisplay(display);
		    		menu.setLevel(bean.getLevel());
		    		menu.setFullMenuName(biaoti);
		    		List<TProBookMenu> children = bookmenu.getChildren();
		    		if(children == null) {
		    			children = new ArrayList<TProBookMenu>();
		    		}
		    		children.add(menu);
		    		bookmenu.setChildren(children);
		    		
		    		pmenu = menu;
		    		
		    	}else {
		    		//log.info("_level="+_level+"  "+biaoti);
		    		
		    		TProBookMenu parent = getParentMenu(bookmenu, bean);
		    		TProBookMenu menu = new TProBookMenu();
		    		menu.setBookId(book.getId());
		    		menu.setId(IdUtil.simpleUUID());
		    		menu.setMenuName(biaoti);
		    		menu.setPid(parent.getId());	
		    		menu.setZwContent(duanluo);
		    		menu.setDisplay(display);
		    		menu.setLevel(bean.getLevel());
		    		menu.setFullMenuName(parent.getFullMenuName()+">"+menu.getMenuName());
		    		List<TProBookMenu> children = parent.getChildren();
		    		if(children == null) {
		    			children = new ArrayList<TProBookMenu>();
		    		}
		    		children.add(menu);
		    		parent.setChildren(children);
		    		
//		    		if(i< xmlContentlist.size() -1) {
//			    		if(xmlContentlist.get(i+1).getLevel() != _level) {//下级
//			    			pmenu = menu;		    			
//			    		}
//		    		}		    		
		    	}		    	
		    }
		   
		    return bookmenu;
		}catch(Exception e) {
			log.error("error,", e);
			throw e;
		}
	}
	
	public static String parseBiaoti(TProBooks book,String xmlPath,Element content,String tagName,StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		String uploadpath = HuangHeConfig.getUploadPath();
		if("标题".equals(tagName)) {	
			String level = content.attributeValue("level");
    		//String isget = content.attributeValue("是否提取");
    		//String biaoti = content.attributeValue("提取标题");
			String isshow = content.attributeValue("是否显示");
			//log.info(content.asXML());
    		//String id = content.attributeValue("id");
			zwbuf.append("<div class=\"biaoti"+level+"\">");
			
    		//可能会有多个正文
    		List<Element> duanluoelements = content.elements();

    		for(Element zw_element : duanluoelements) {
    			//正文中会有如下标签： 字体、原书页面、注释
    			//log.info("==>"+zw_element.asXML());
    			
    			String duanTagName = zw_element.getName();
//    			if(zw_element.getText().contains("府治洪武初自集慶路徙古")) {
//    				log.info("==>test");
//    			}
    			if("注号".equals(duanTagName)){
    				String id = zw_element.attributeValue("id");   				
    				String refid = zw_element.attributeValue("refid");
    				String hexstr = zw_element.getText();
    				
    				if(Strings.isNullOrEmpty(refid)) {   						    					
    						    			
    					String _refid = zhuhaoRefMap.get(id);
    					if(Strings.isNullOrEmpty(_refid)) {
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("span");
		    				
		    				span.addAttribute("class", "zhuhao");
		    				span.addAttribute("id", ""+id);
		    				span.addAttribute("name", ""+id);
		    					    				
		    				span.addText(hexstr);
		    				List elepar = zw_element.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(zw_element), span);
    					}else {
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("a");
		    				
		    				span.addAttribute("class", "zhuhao");
			    			span.addAttribute("id", ""+id);
			    			span.addAttribute("href", "#"+_refid);
		    				
		    				span.addText(hexstr);
		    				List elepar = zw_element.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(zw_element), span);    		
    					}   					
    					
    				}else {	    					
    					
    					zhuhaoRefMap.put(refid, id);
    					
						//说明是目标注
    					Element span = DocumentHelper.createElement("a");
	    				
	    				span.addAttribute("class", "zhuhao");
		    			span.addAttribute("id", ""+id);
		    			span.addAttribute("href", "#"+refid);
	    				
	    				span.addText(hexstr);
	    				List elepar = zw_element.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(zw_element), span);    					
    				}
				}
    			
    				//log.info(tagname);
				if("字体".equals(duanTagName)){
    				String _ftype = zw_element.attributeValue("type");
    				//log.info(node.getText()+","+ftype);
    				
    				Element span = DocumentHelper.createElement("span");
    				String hexstr = null;
    				if(Strings.isNullOrEmpty(_ftype)) {
    					span.addAttribute("class", "no");
    					hexstr = zw_element.getText();
    				}else {
	    				if(_ftype.contains("超大字2")) {
	    					span.addAttribute("class", "big02");
	    					//大字统一转&#x码
		    				String bigtext = zw_element.getText();
		    				//hexstr = BigwordUtil.str2Hex(bigtext);
		    				hexstr = BigwordUtil.hex2word(bigtext);
		    				//hexstr = BigwordUtil.hex2word(bigtext);
	    				}else if(_ftype.contains("超大字3")){
	    					span.addAttribute("class", "big15");
	    					//大字统一转&#x码
		    				String bigtext = zw_element.getText();
		    				//hexstr = BigwordUtil.str2Hex(bigtext);
		    				hexstr = BigwordUtil.hex2word(bigtext);
		    				//hexstr = BigwordUtil.hex2word(bigtext);
	    				}else if(_ftype.contains("超大字1")) {
	    					span.addAttribute("class", "big01");
	    					hexstr = zw_element.getText();
	    					hexstr = BigwordUtil.hex2word(hexstr);
	    				}else {
	    					span.addAttribute("class", _ftype);
	    					hexstr = zw_element.getText();
	    				}
    				}
    				
    				span.addText(hexstr);
    				
    				List elepar = zw_element.getParent().content();
    				// 用content标签替换文本节点
    				elepar.set(elepar.indexOf(zw_element), span);
				}
				
    			
    			if("原书页面".equals(duanTagName)) {
    							    				
    				Element _e = (Element)zw_element;
    				String ysym = _e.getText();
    				ysym = StringUtil.trimAll(ysym);
    				zw_element.setName("a");		 
    				//删除多余属性
		    		Attribute deleteAttri = zw_element.attribute("destsrc");
		    		if(deleteAttri != null) {
		    			zw_element.remove(deleteAttri);
		    		}
		    		
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
    				zw_element.add(newattri);	 
    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
    				zw_element.add(idattri);	 
    				//log.info(content.asXML());
    				String hanshu = "openReader('"+ysym+"')";
    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
    				zw_element.add(clickatt);	
    				//截取最后5位
    				String ym = StringUtil.substring(ysym,ysym.length()-5);
    				ym = StringUtil.trimAll(ym);
    				ym = StringUtils.replace(ym, " ", "");
    				int yamaInt = 0;
    				try {
	    				yamaInt = Integer.parseInt(ym);			    				
    				}catch(Exception e) {
    					throw new ServiceException("原书页面"+ysym+"不符合规范");
    				}
    				zw_element.setText("P"+String.valueOf(yamaInt));
    				//zwbuf.append(zw_element.asXML());	    			
    			}
    		   	
    			if("字图".equals(duanTagName)||"外字".equals(duanTagName)) {
    				Element _e = (Element)zw_element;
    				String picsrc = _e.attributeValue("src");
    				//转换为http:// 形式
    				String httpUrl = null;
    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
    				//log.info("src:"+srcFile.getAbsolutePath());
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            
		            uploadpath = FilenameUtils.normalize(uploadpath);
		            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
				    
				    File destDir = new File(destPath);
				    if(!destDir.getParentFile().exists()) {
				    	destDir.getParentFile().mkdirs();
				    }									   
				    if(srcFile.exists()) {
				    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
				    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
				    }else {
				    	//排查是否路径有问题
				    	if(StringUtils.contains(picsrc, "items")) {									    		
				    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
				    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
				    		if(!srcFile.exists()) {
				    			throw new ServiceException(picsrc+"文件不存在");
				    		}
				    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
				    	}
				    }
				    
				    //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//				    String domain = picHttpUrl;
////				    if(Strings.isNullOrEmpty(domain)) {
////				    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////				    }
				    String domain = "%domain";
				    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
		            //log.info(httpUrl);
    				Element pica = DocumentHelper.createElement("img");
    				pica.addAttribute("src", httpUrl);
    				pica.addAttribute("class", "zitu");
    				pica.addText(zw_element.getText());
    				List elepar = zw_element.getParent().content();
    				// 用content标签替换文本节点
    				elepar.set(elepar.indexOf(zw_element), pica);		    			
				}			
    			
    			if("注释".equals(duanTagName)) {
    				//log.info("==>"+zw_element.asXML());
    				String ftype = zw_element.attributeValue("type");
    				String divtype = null;
    				if("注".equals(ftype)) {
    					divtype = "zhu";
    				}else if("小注".equals(ftype)) {
    					divtype = "xiaozhu";
    				}else if("落款".equals(ftype)) {    			
    					divtype = "luokuan";
    				}else if("眉批".equals(ftype)) {
    					divtype = "meipi";
    				}else if("脚注-注".equals(ftype)) {
    					divtype = "jiaozhu-zhu";
    				}else if("脚注-校".equals(ftype)) {
    					divtype = "jiaozhu-jiao";
    				}else if("疏".equals(ftype)){
    					divtype = "shu";
    				}else {
    					divtype = "zhu";
    				}
    				//log.info("==>"+ftype);
    				//删除原有的标签
    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
    				zw_element.remove(delattri);
    				
//    				if("meipi".equalsIgnoreCase(divtype)) {
//    					//增加新标签
//	    				zw_element.setName("span");
//	    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
//	    				zw_element.add(newattri);	
//    				}else {
	    				//增加新标签
	    				zw_element.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
	    				zw_element.add(newattri);		
    				//}
    				//log.info(zw_element.asXML());
    				/**
    				 * 可能会有大字
    				 */
    				List<Element> bigwordelements = zw_element.elements();
    				for(Element bigelement : bigwordelements) {
	    				String tagname = bigelement.getName();
	    				//log.info(tagname);
	    				if("字体".equals(tagname)){
		    				String _ftype = bigelement.attributeValue("type");
		    				//log.info(node.getText()+","+ftype);
		    				
		    				Element span = DocumentHelper.createElement("span");
		    				String hexstr = bigelement.getText();
		    				if(Strings.isNullOrEmpty(_ftype)) {
		    					span.addAttribute("class", "no");
		    					//hexstr = bigelement.getText();
		    				}else {
			    				if(_ftype.contains("超大字2")) {
			    					span.addAttribute("class", "big02");
			    					//大字统一转&#x码
				    				//String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(hexstr);
				    				hexstr = BigwordUtil.hex2word(hexstr);
			    				}else if(_ftype.contains("超大字3")){
			    					span.addAttribute("class", "big15");
			    					//大字统一转&#x码
				    				//String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(hexstr);
				    				hexstr = BigwordUtil.hex2word(hexstr);
			    				}else if(_ftype.contains("超大字1")) {
			    					span.addAttribute("class", "big01");
			    					hexstr = BigwordUtil.hex2word(hexstr);
			    				}else {
			    					span.addAttribute("class", _ftype);
			    					//hexstr = bigelement.getText();
			    				}
		    				}
		    				
		    				span.addText(hexstr);
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), span);
	    				}
	    				//log.info(zw_element.asXML());
	    				if("原书页面".equals(tagname)) {
				    		String ysym = bigelement.getText();
				    		ysym = StringUtil.trimAll(ysym);
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		bigelement.setName("a");		
				    		//删除多余属性
				    		Attribute deleteAttri = bigelement.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			bigelement.remove(deleteAttri);
				    		}
				    		
		    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		    				bigelement.add(zspageattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		    				bigelement.add(idattri);	 
		    				//log.info(content.asXML());
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		    				bigelement.add(clickatt);	
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trimAll(ym);
		    				ym = StringUtils.replace(ym, " ", "");
		    				int yamaInt = 0;
		    				try {
			    				yamaInt = Integer.parseInt(ym);			    				
		    				}catch(Exception e) {
		    					throw new ServiceException("原书页面"+ysym+"不符合规范");
		    				}
		    				bigelement.setText("P"+String.valueOf(yamaInt));
		    				//zwbuf.append(bigelement.asXML());
				    	}
	    				//log.info(bigelement.asXML());
	    				if("字图".equals(tagname)||"外字".equals(tagname)) {
		    				Element _e = (Element)bigelement;
		    				String picsrc = _e.attributeValue("src");
		    				//转换为http:// 形式
		    				String httpUrl = null;
		    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		    				//log.info("src:"+srcFile.getAbsolutePath());
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            
				           
				            uploadpath = FilenameUtils.normalize(uploadpath);
				            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
						    
						    File destDir = new File(destPath);
						    if(!destDir.getParentFile().exists()) {
						    	destDir.getParentFile().mkdirs();
						    }									   
						    if(srcFile.exists()) {
						    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
						    	}
						    }
						    
						    //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//						    String domain = picHttpUrl;
////						    if(Strings.isNullOrEmpty(domain)) {
////						    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////						    }
						    String domain = "%domain";
						    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
		    				Element pica = DocumentHelper.createElement("img");
		    				pica.addAttribute("src", httpUrl);
		    				pica.addAttribute("class", "zitu");
		    				pica.addText(bigelement.getText());
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), pica);		    			
	    				}			
	    				if("书名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> bigword_elements = bigelement.elements();
		    				for(Element big_element : bigword_elements) {
			    				String tag_name = big_element.getName();
			    				//log.info(tagname);
			    				if("字体".equals(tag_name)){
				    				String _ftype = big_element.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = big_element.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = big_element.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = big_element.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = big_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = big_element.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = big_element.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(big_element), span);
			    				}
		    				}
		    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("专名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				//增加新标签
		    				_e.setName("span");
		    				Attribute deleteAttri = _e.attribute("type");
	    		    		if(deleteAttri != null) {
	    		    			_e.remove(deleteAttri);
	    		    		}
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("注号".equals(tagname)){
	        				String id = bigelement.attributeValue("id");	        				
	        				String refid = bigelement.attributeValue("refid");
	        				String hexstr = bigelement.getText();
	        				
	        				if(Strings.isNullOrEmpty(refid)) {   						    					
	        						    			
	        					String _refid = zhuhaoRefMap.get(id);
	        					if(Strings.isNullOrEmpty(_refid)) {
	        						//说明是目标注
	    	    					Element span = DocumentHelper.createElement("span");
	    		    				
	    		    				span.addAttribute("class", "zhuhao");
	    		    				span.addAttribute("id", ""+id);
	    		    				span.addAttribute("name", ""+id);
	    		    					    				
	    		    				span.addText(hexstr);
	    		    				List elepar = bigelement.getParent().content();
	    		    				// 用content标签替换文本节点
	    		    				elepar.set(elepar.indexOf(bigelement), span);
	        					}else {
	        						//说明是目标注
	    	    					Element span = DocumentHelper.createElement("a");
	    		    				
	    		    				span.addAttribute("class", "zhuhao");
	    			    			span.addAttribute("id", ""+id);
	    			    			span.addAttribute("href", "#"+_refid);
	    		    				
	    		    				span.addText(hexstr);
	    		    				List elepar = bigelement.getParent().content();
	    		    				// 用content标签替换文本节点
	    		    				elepar.set(elepar.indexOf(bigelement), span);    		
	        					}   					
	        					
	        				}else {	    					
	        					
	        					zhuhaoRefMap.put(refid, id);
	        					
	    						//说明是目标注
	        					Element span = DocumentHelper.createElement("a");
	    	    				
	    	    				span.addAttribute("class", "zhuhao");
	    		    			span.addAttribute("id", ""+id);
	    		    			span.addAttribute("href", "#"+refid);
	    	    				
	    	    				span.addText(hexstr);
	    	    				List elepar = bigelement.getParent().content();
	    	    				// 用content标签替换文本节点
	    	    				elepar.set(elepar.indexOf(bigelement), span);    					
	        				}
	    				}
    				}
    				
    				
    			}	
    			if("书名".equals(duanTagName)) {
    				Element _e = (Element)zw_element;

    				//增加新标签
    				_e.setName("span");
    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
    				_e.add(_newattri);		
    				//log.info(zw_element.asXML());
    			}
				if("专名".equals(duanTagName)) {
    				Element _e = (Element)zw_element;

    				//增加新标签
    				_e.setName("span");
    				Attribute deleteAttri = _e.attribute("type");
		    		if(deleteAttri != null) {
		    			_e.remove(deleteAttri);
		    		}
    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
    				_e.add(_newattri);		
    				//log.info(zw_element.asXML());
    			}			
//    			if(zw_element != null) {
//	    			String xmlstr = zw_element.asXML();
//	    			//log.info(xmlstr);
//	    			xmlstr = StringUtil.remove(xmlstr, "<正文>");
//	    			xmlstr = StringUtil.remove(xmlstr, "</正文>");
//	    					    			
//	    			zwbuf.append(xmlstr);
//    			}
    		}
    		
    		List<Attribute> attlist = content.attributes();
    		//log.info("==>"+attlist.size());
    		
    		List<Attribute> alllist = Lists.newArrayList();
    		alllist.addAll(attlist);
    		for(int i = 0;i < alllist.size(); i++) {
    			//log.info(alllist.get(i).getName()+"==>"+alllist.size());    			
    			content.remove(alllist.get(i));
    		}
    		String xmlstr = content.asXML();
    		xmlstr = StringUtil.replace(xmlstr, "<标题>", "");
    		xmlstr = StringUtil.replace(xmlstr, "</标题>", "");
    		//if(StringUtil.equalsIgnoreCase(isshow, "是")) {
    			zwbuf.append(xmlstr);
    		//}
    		zwbuf.append("</div>");
    		//log.info("====>"+zwbuf.toString());
    	}
		return zwbuf.toString();
	}
	
	
	public static TProBookMenu getParentMenu(TProBookMenu bookmenu , XmlBean bean) {
		TProBookMenu result = bookmenu;
		//从1级开始
		List<TProBookMenu> list = bookmenu.getChildren();
		for(TProBookMenu menu : list) {
			int level = menu.getLevel();
			int _level = bean.getLevel();
			//log.info("<==>level:"+menu.getMenuName()+"=="+level);
			//log.info("<==>_level:"+bean.getBiaoti()+"=="+_level);
			if(level < _level) {
				result = getParentMenu(menu , bean);
			}else {
				continue;
			}
		}
		//log.info("==>获取的上级："+result.getMenuName()+" ==>"+bean.getBiaoti());
		return result;
	}
	
	public static String parseTableTag(TProBooks book,String xmlPath,Element tableEle,
			StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		//log.info(tableEle.asXML());
		StringBuffer tablebuf = new StringBuffer();
		tablebuf.append("<table>");
				
		String row_xpath = tableEle.getUniquePath()+"/tgroup/tbody/row";
		List<Node> selectNodes_row = tableEle.selectNodes(row_xpath);
		//按行处理
		for (Node trnode : selectNodes_row) {		
			Element tr = (Element)trnode;
			//log.info(tr.asXML());
			//取出段落级别
			String tagName = "段落";
			tablebuf.append("<tr>");
			String td_xpath = tr.getUniquePath()+"/entry";
			List<Node> selectNodes_td = tableEle.selectNodes(td_xpath);
			for (Node tdnode : selectNodes_td) {				
				Element duanluo = (Element)tdnode;
				//log.info(duanluo.asXML());
				String rowspan = duanluo.attributeValue("rowspan");
				String colspan = duanluo.attributeValue("colspan");
				if(!Strings.isNullOrEmpty(rowspan)&&!Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td rowspan=\""+rowspan+"\" colspan=\""+colspan+"\">");
				}
				if(!Strings.isNullOrEmpty(rowspan)&&Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td rowspan=\""+rowspan+">\"");
				}
				if(Strings.isNullOrEmpty(rowspan)&&!Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td colspan=\""+colspan+"\">");
				}
				if(Strings.isNullOrEmpty(rowspan)&&Strings.isNullOrEmpty(colspan)) {
					tablebuf.append("<td>");
				}
				//可能有多个段落
				List<Element> duanluos = duanluo.elements();
				for(Element _duanluo : duanluos) {
					String tagname = _duanluo.getName();
					if("段落".equals(tagname)) {
						parseDuanluo(book, xmlPath, _duanluo, tagName, tablebuf,zhuhaoRefMap);
					}else if("原书页面".equals(tagname)) {
						//log.info("==>");
						Element _e = (Element)_duanluo;
	    				String ysym = _e.getText();
	    				ysym = StringUtil.trimAll(ysym);
	    				_duanluo.setName("a");		    	
	    				 //删除多余属性
			    		Attribute deleteAttri = _duanluo.attribute("destsrc");
			    		if(deleteAttri != null) {
			    			_duanluo.remove(deleteAttri);
			    		}
	    				Attribute newattri = DocumentHelper.createAttribute(_duanluo, "class", "page");
	    				_duanluo.add(newattri);	 
	    				Attribute idattri = DocumentHelper.createAttribute(_duanluo, "id", ysym);
	    				_duanluo.add(idattri);	 
	    				//log.info(content.asXML());
	    				//增加调动函数
	    				String hanshu = "openReader('"+ysym+"')";
	    				Attribute clickatt = DocumentHelper.createAttribute(_duanluo, "onclick", hanshu);
	    				_duanluo.add(clickatt);	
	    				//log.info("==>原书页面{}，=={}",ysym,tr.asXML());
	    				
	    				//截取最后5位
	    				String ym = StringUtil.substring(ysym,ysym.length()-5);
	    				ym = StringUtil.trimAll(ym);
	    				ym = StringUtils.replace(ym, " ", "");
	    				int yamaInt = 0;
	    				try {
		    				yamaInt = Integer.parseInt(ym);			    				
	    				}catch(Exception e) {
	    					throw new ServiceException("原书页面"+ysym+"不符合规范");
	    				}
	    				_duanluo.setText("P"+String.valueOf(yamaInt));
	    				
	    				tablebuf.append(_duanluo.asXML());
					}
				}
				tablebuf.append("</td>");
				//log.info(tablebuf.toString());
			}	
						
			tablebuf.append("</tr>");
		}
		
		tablebuf.append("</table>");
		return tablebuf.toString();
	}
	
	
	/**
	 * 解析段落
	 * @param book
	 * @param xmlPath
	 * @param content
	 * @param tagName
	 * @param zwbuf
	 * @return
	 */
	public static String parseDuanluo(TProBooks book,String xmlPath,Element content,String tagName,StringBuffer zwbuf,Map<String,String> zhuhaoRefMap) {
		String uploadpath = HuangHeConfig.getUploadPath();
		if("段落".equals(tagName)) {	    		
    		
			zwbuf.append("<p>");
    		//可能会有多个正文
    		List<Element> duanluoelements = content.elements();
    		for(Element zw_element : duanluoelements) {
    			//正文中会有如下标签： 字体、原书页面、注释
    			//log.info("==>"+zw_element.asXML());
//    			if(zw_element.asXML().contains("成都處士廖吉人先生，凡事")) {
//    				log.info("=====");
//    			}
    			String duanTagName = zw_element.getName();
    			Attribute zwattri = zw_element.attribute("type");
    			String zwtype  = null;
    			if(zwattri != null) {
    				zwtype = zwattri.getValue();
    			}
    			String tempDiv = "";
    			//log.info("==>正文type={}",zwtype);
    			if(!Strings.isNullOrEmpty(zwtype)) {
    				if("正文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhengwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhengwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhengwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhengwen-jiao\">";
	    				}else {
//	    					if(StringUtil.in)
//	    					xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    					//tempDiv = "<span class=\"zhengwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhengwen\">";
	    				}
    				}
    				
    				if("注文".equals(duanTagName)) {
	    				if(StringUtil.equalsIgnoreCase(zwtype, "引文")){    					
	    					tempDiv = "<span class=\"zhuwen-yinwen\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "注")){
	    					tempDiv = "<span class=\"zhuwen-zhu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "疏")) {
	    					tempDiv = "<span class=\"zhuwen-shu\">";
	    				}else if(StringUtil.equalsIgnoreCase(zwtype, "校")) {
	    					tempDiv = "<span class=\"zhuwen-jiao\">";
	    				}else {
	    					//tempDiv = "<span class=\"zhuwen-"+zwtype+"\">";
	    					tempDiv = "<span class=\"zhuwen\">";
	    				}
    				}
    				
    			}
    			zwbuf.append(tempDiv);
    			
    			if("注号".equals(duanTagName)){
    				String id = zw_element.attributeValue("id");   				
    				String refid = zw_element.attributeValue("refid");
    				String hexstr = zw_element.getText();      
    				if(Strings.isNullOrEmpty(refid)) {
    					
    					String _refid = zhuhaoRefMap.get(id);
    					if(Strings.isNullOrEmpty(_refid)) {
    						//说明是目标注
        					Element span = DocumentHelper.createElement("span");	    				
    	    				span.addAttribute("class", "zhuhao");   	    				
    	    				 				
    	    				span.addText(hexstr);

    	    				zw_element = null;
    	    				zwbuf.append(span.asXML());
    					}else {
    						Element atag = DocumentHelper.createElement("a");
        					atag.addAttribute("class", "zhuhao");
        					atag.addAttribute("id", id);
        					atag.addAttribute("href", "#"+_refid);
    	    				
    	    				atag.addText(hexstr);
    	    				
    	    				zw_element = null;
    	    				zwbuf.append(atag.asXML());
    					}
    					
    				}else {
    					
    					zhuhaoRefMap.put(refid, id);
    					
    					Element atag = DocumentHelper.createElement("a");
    					atag.addAttribute("class", "zhuhao");
    					atag.addAttribute("id", id);
    					atag.addAttribute("href", "#"+refid);
	    				
	    				atag.addText(hexstr);
	    				
	    				zw_element = null;
	    				zwbuf.append(atag.asXML());
    				}
				}
    			if(zw_element == null) {
    				continue;
    			}
    			if("正文".equals(duanTagName) || "注文".equals(duanTagName)) {
	    			String xpath = zw_element.getUniquePath()+"/字体";
	    			//String xpath2 = zw_element.getUniquePath()+"/b/字体";
	    			List<Node> selectNodes = zw_element.selectNodes(xpath);
//	    			if(selectNodes == null || selectNodes.size() ==0) {
//	    				selectNodes = zw_element.selectNodes(xpath2);
//	    			}
	    			for (Node node : selectNodes) {
	    				Element _e = (Element)node;
	    				String bigtext = _e.getText();
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				String zhuanming_xpath = _e.getUniquePath()+"/专名";
		    			List<Node> selectNodes_zhuanming = _e.selectNodes(zhuanming_xpath);
		    			for (Node _node : selectNodes_zhuanming) {		
		    				Element _e1 = (Element)_node;   				
		    			
		    				String tagname = _e1.getName();
		    					
		    				Element span = DocumentHelper.createElement("span");
		    				span.addAttribute("class", "zhuanming");
		    				String hexstr = _e1.getText();;
		    				
		    				span.addText(hexstr);
//		    				List elepar = _e1.getParent().content();
//		    				// 用content标签替换文本节点
//		    				elepar.set(elepar.indexOf(_e1), span);		    				
		    				bigtext = span.asXML();
		    			}
	    				Element span = DocumentHelper.createElement("span");
	    				
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}		
	    				}
	    				
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}
	    			String xpath2 = zw_element.getUniquePath()+"/b/字体";	    			
	    			selectNodes = zw_element.selectNodes(xpath2);
	    			
	    			for (Node node : selectNodes) {
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				//log.info(node.getText()+","+ftype);
	    				
	    				Element span = DocumentHelper.createElement("span");
	    				String bigtext = node.getText();
	    				String hexstr = null;
	    				if(Strings.isNullOrEmpty(ftype)) {
	    					span.addAttribute("class", "no");
	    					hexstr = bigtext;
	    				}else {
		    				if(ftype.contains("超大字2")) {
		    					span.addAttribute("class", "big02");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字3")){
		    					span.addAttribute("class", "big15");
		    					//hexstr = BigwordUtil.str2Hex(bigtext);
		    					hexstr = BigwordUtil.hex2word(bigtext);
		    				}else if(ftype.contains("超大字1")) {
		    					span.addAttribute("class", "big01");
		    					hexstr = zw_element.getText();
		    					hexstr = BigwordUtil.hex2word(hexstr);
		    				}else {
		    					span.addAttribute("class", ftype);
		    					hexstr = bigtext;
		    				}				    				
	    				}
	    				span.addText(hexstr);
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), span);
	    			}    			
	    			
	    			
	    			String ysym_xpath = zw_element.getUniquePath()+"/原书页面";
	    			List<Node> selectNodes_ysym = zw_element.selectNodes(ysym_xpath);
	    			for (Node node : selectNodes_ysym) {
	    				Element _e = (Element)node;
	    				String yema = node.getText();
	    				yema = StringUtil.trimAll(yema);
	    				//截取最后5位
	    				String ym = StringUtil.substring(yema,yema.length()-5);
	    				ym = StringUtil.trimAll(ym);
	    				ym = StringUtils.replace(ym, " ", "");
	    				int yamaInt = 0;
	    				try {
		    				yamaInt = Integer.parseInt(ym);			    				
	    				}catch(Exception e) {
	    					throw new ServiceException("原书页面"+yema+"不符合规范");
	    				}
	    				
	    				Element pica = DocumentHelper.createElement("a");					    				
	    				pica.addAttribute("class", "page");
	    				pica.addAttribute("id", _e.getText());
	    				
	    				//增加调动函数
	    				String hanshu = "openReader('"+yema+"')";	    				
	    				pica.addAttribute("onclick", hanshu);	
	    				
	    				
	    				pica.addText("P"+String.valueOf(yamaInt));
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    					 
	    			String zhuhao_xpath = zw_element.getUniquePath()+"/注号";
	    			List<Node> selectNodes_zhuhao = zw_element.selectNodes(zhuhao_xpath);
	    			for (Node node : selectNodes_zhuhao) {
	    				Element _e = (Element)node;
	    				String hexstr = node.getText();
	    				
	    				String id = _e.attributeValue("id");
	    				String refid = _e.attributeValue("refid");
	    				if(Strings.isNullOrEmpty(refid)) {   						    					
	    						    			
	    					String _refid = zhuhaoRefMap.get(id);
	    					if(Strings.isNullOrEmpty(_refid)) {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("span");
			    				
			    				span.addAttribute("class", "zhuhao");
			    				span.addAttribute("id", ""+id);
			    				span.addAttribute("name", ""+id);
			    					    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);
	    					}else {
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+_refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = node.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(node), span);    		
	    					}   					
	    					
	    				}else {	    					
	    					
	    					zhuhaoRefMap.put(refid, id);
	    					
    						//说明是目标注
	    					Element span = DocumentHelper.createElement("a");
		    				
		    				span.addAttribute("class", "zhuhao");
			    			span.addAttribute("id", ""+id);
			    			span.addAttribute("href", "#"+refid);
		    				
		    				span.addText(hexstr);
		    				List elepar = node.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(node), span);    					
	    				}
	    			}
	    			
	    			String zhu_xpath = zw_element.getUniquePath()+"/注释";
	    			List<Node> selectNodes_zhu = zw_element.selectNodes(zhu_xpath);
	    			for (Node node : selectNodes_zhu) {		
	    				Element _e = (Element)node;
	    				String ftype = _e.attributeValue("type");
	    				String divtype = null;
	    				if("注".equals(ftype)) {
	    					divtype = "zhu";
	    				}else if("小注".equals(ftype)) {
	    					divtype = "xiaozhu";
	    				}else if("落款".equals(ftype)) {
	    					divtype = "luokuan";
	    				}else if("眉批".equals(ftype)) {
	    					divtype = "meipi";
	    				}else if("脚注-注".equals(ftype)) {
	    					divtype = "jiaozhu-zhu";
	    				}else if("脚注-校".equals(ftype)) {
	    					divtype = "jiaozhu-jiao";
	    				}else if("疏".equals(ftype)){
	    					divtype = "shu";
	    				}else {
	    					divtype = "zhu";
	    				}
	    				Element pica = DocumentHelper.createElement("span");
	    				pica.addAttribute("class", divtype);
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			String zhuanming_xpath = zw_element.getUniquePath()+"/专名";
	    			List<Node> selectNodes_zhuanming = zw_element.selectNodes(zhuanming_xpath);
	    			for (Node node : selectNodes_zhuanming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				//log.info(tagname);
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element _epage = (Element)bigelement;
		        				String ysym = _epage.getText();
		        				ysym = StringUtil.trimAll(ysym);
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trimAll(ym);
		        				ym = StringUtils.replace(ym, " ", "");
		        				int yamaInt = 0;
			    				try {
				    				yamaInt = Integer.parseInt(ym);			    				
			    				}catch(Exception e) {
			    					throw new ServiceException("原书页面"+ysym+"不符合规范");
			    				}
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zitu_e = (Element)bigelement;
			    				String picsrc = zitu_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
								log.info("FilenameUtils.getFullPath(xmlPath)==="+FilenameUtils.getFullPath(xmlPath));
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
			    				//log.info("src:"+srcFile.getAbsolutePath());
					            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
					            //String uploadpath = baseParms.getUploadPath();
					            
					          
					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								//log.info("==>拷贝图片：{}",destPath);

							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }

							    if(srcFile.exists()) {
							    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }else {
							    	//排查是否路径有问题
							    	if(StringUtils.contains(picsrc, "items")) {									    		
							    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
							    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    		if(!srcFile.exists()) {
							    			throw new ServiceException(picsrc+"文件不存在");
							    		}
							    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
							    	}
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//							    String domain = picHttpUrl;
////							    if(Strings.isNullOrEmpty(domain)) {
////							    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////							    }
							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}		
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");	    				
	    				Attribute deleteAttri = _e.attribute("type");
    		    		if(deleteAttri != null) {
    		    			_e.remove(deleteAttri);
    		    		}
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String shuming_xpath = zw_element.getUniquePath()+"/书名";
	    			List<Node> selectNodes_shuming = zw_element.selectNodes(shuming_xpath);
	    			for (Node node : selectNodes_shuming) {		
	    				Element _e = (Element)node;   				
	    				//log.info(_e.asXML());
	    				//List<Element> fontEle = _e.elements();
	    				List<Element> bigwordelements = _e.elements();
	    				for(Element bigelement : bigwordelements) {
		    				String tagname = bigelement.getName();
		    				if("原书页面".equals(tagname)) {
			    				
		        				Element b_e = (Element)bigelement;
		        				String ysym = b_e.getText();
		        				ysym = StringUtil.trimAll(ysym);
		        				bigelement.setName("a");		 
		        				//删除多余属性
		    		    		Attribute deleteAttri = bigelement.attribute("destsrc");
		    		    		if(deleteAttri != null) {
		    		    			bigelement.remove(deleteAttri);
		    		    		}
		    		    		
		        				Attribute newattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		        				bigelement.add(newattri);	 
		        				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		        				bigelement.add(idattri);	 
		        				//log.info(content.asXML());
		        				String hanshu = "openReader('"+ysym+"')";
		        				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		        				bigelement.add(clickatt);	
		        				//截取最后5位
		        				String ym = StringUtil.substring(ysym,ysym.length()-5);
		        				ym = StringUtil.trimAll(ym);
		        				ym = StringUtils.replace(ym, " ", "");
		        				int yamaInt = 0;
			    				try {
				    				yamaInt = Integer.parseInt(ym);			    				
			    				}catch(Exception e) {
			    					throw new ServiceException("原书页面"+ysym+"不符合规范");
			    				}
		        				bigelement.setText("P"+String.valueOf(yamaInt));
		        				//zwbuf.append(zw_element.asXML());	    			
		        			}
		    				if("注号".equals(tagname)){
			    				String id = bigelement.attributeValue("id");
			    				String refid = bigelement.attributeValue("refid");
			    				String hexstr = bigelement.getText();
			    				
			    				if(Strings.isNullOrEmpty(refid)) {   						    					
			    						    			
			    					String _refid = zhuhaoRefMap.get(id);
			    					if(Strings.isNullOrEmpty(_refid)) {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("span");
					    				
					    				span.addAttribute("class", "zhuhao");
					    				span.addAttribute("id", ""+id);
					    				span.addAttribute("name", ""+id);
					    					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);
			    					}else {
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+_refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = bigelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(bigelement), span);    		
			    					}   					
			    					
			    				}else {	    					
			    					
			    					zhuhaoRefMap.put(refid, id);
			    					
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    					
			    				}
		    				}
		    				if("字体".equals(tagname)){
			    				String _ftype = bigelement.attributeValue("type");
			    				//log.info(node.getText()+","+ftype);
			    				
			    				Element span = DocumentHelper.createElement("span");
			    				String hexstr = null;
			    				if(Strings.isNullOrEmpty(_ftype)) {
			    					span.addAttribute("class", "no");
			    					hexstr = bigelement.getText();
			    				}else {
				    				if(_ftype.contains("超大字2")) {
				    					span.addAttribute("class", "big02");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字3")){
				    					span.addAttribute("class", "big15");
				    					//大字统一转&#x码
					    				String bigtext = bigelement.getText();
					    				//hexstr = BigwordUtil.str2Hex(bigtext);
					    				hexstr = BigwordUtil.hex2word(bigtext);
				    				}else if(_ftype.contains("超大字1")) {
				    					span.addAttribute("class", "big01");
				    					hexstr = zw_element.getText();
				    					hexstr = BigwordUtil.hex2word(hexstr);
				    				}else {
				    					span.addAttribute("class", _ftype);
				    					hexstr = bigelement.getText();
				    				}
			    				}
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);
		    				}
		    				
		    				if("字图".equals(tagname)||"外字".equals(tagname)) {
			    				Element zm_e = (Element)bigelement;
			    				String picsrc = zm_e.attributeValue("src");
			    				//转换为http:// 形式
			    				String httpUrl = null;
			    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
			    				//log.info("src:"+srcFile.getAbsolutePath());
					            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
					            //String uploadpath = baseParms.getUploadPath();
					            
					            
					            uploadpath = FilenameUtils.normalize(uploadpath);
					            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
							    
							    File destDir = new File(destPath);
							    if(!destDir.getParentFile().exists()) {
							    	destDir.getParentFile().mkdirs();
							    }									   
							    if(srcFile.exists()) {
							    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
							    }else {
							    	//排查是否路径有问题
							    	if(StringUtils.contains(picsrc, "items")) {									    		
							    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
							    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
							    		if(!srcFile.exists()) {
							    			throw new ServiceException(picsrc+"文件不存在");
							    		}
							    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
							    	}
							    }
							    
							    //组装http链接
							    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//							    String domain = picHttpUrl;
////							    if(Strings.isNullOrEmpty(domain)) {
////							    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////							    }
							    String domain = "%domain";
							    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
					            //log.info(httpUrl);
			    				Element pica = DocumentHelper.createElement("img");
			    				pica.addAttribute("src", httpUrl);
			    				pica.addAttribute("class", "zitu");
			    				pica.addText(bigelement.getText());
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), pica);		    			
		    				}	
		    				
		    					
	    				}
	    				//log.info(zw_element.asXML());   				
	    				//增加新标签
	    				_e.setName("span");
	    				Attribute newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
	    				_e.add(newattri);		
	    				//log.info(zw_element.asXML());
	    			}
	    			
	    			String zitu_xpath = zw_element.getUniquePath()+"/字图";
	    			List<Node> selectNodes_zitu = zw_element.selectNodes(zitu_xpath);
	    			for (Node node : selectNodes_zitu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			            
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//					    String domain = picHttpUrl;
////					    if(Strings.isNullOrEmpty(domain)) {
////					    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////					    }
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			//外字
	    			String waizi_xpath = zw_element.getUniquePath()+"/外字";
	    			List<Node> selectNodes_waizi = zw_element.selectNodes(waizi_xpath);
	    			for (Node node : selectNodes_waizi) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
						//log.info("获取资源的目录部分路径：{}===="+FilenameUtils.getFullPath(xmlPath));
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
			           
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    File parentdir = destDir.getParentFile();
					    log.info("==>文件夹成功 {}",parentdir.getAbsolutePath());
					    log.info("==>exist={}",parentdir.exists());
					    if(!parentdir.exists()) {
					    	parentdir.mkdirs();					    	
					    	log.info("==>创建文件夹成功 {}",parentdir.getAbsolutePath());
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//					    String domain = picHttpUrl;
////					    if(Strings.isNullOrEmpty(domain)) {
////					    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////					    }
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "zitu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    			
	    			
	    			String chatu_xpath = zw_element.getUniquePath()+"/插图";
	    			List<Node> selectNodes_chatu = zw_element.selectNodes(chatu_xpath);
	    			for (Node node : selectNodes_chatu) {		
	    				Element _e = (Element)node;
	    				String picsrc = _e.attributeValue("src");
	    				//转换为http:// 形式
	    				String httpUrl = null;
	    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    				//log.info("src:"+srcFile.getAbsolutePath());
			            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
			            //String uploadpath = baseParms.getUploadPath();
			            
//			            if(Strings.isNullOrEmpty(uploadpath)||uploadpath.contains("null")) {
//			            	uploadpath = "D:/huanghe/uploadPath/upload";
//			            }
			            uploadpath = FilenameUtils.normalize(uploadpath);
			            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
					    
					    File destDir = new File(destPath);
					    if(!destDir.getParentFile().exists()) {
					    	destDir.getParentFile().mkdirs();
					    }									   
					    if(srcFile.exists()) {
					    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
					    }else {
					    	//排查是否路径有问题
					    	if(StringUtils.contains(picsrc, "items")) {									    		
					    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
					    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
					    		if(!srcFile.exists()) {
					    			throw new ServiceException(picsrc+"文件不存在");
					    		}
					    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
					    	}
					    }
					    
					    //组装http链接
					    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//					    String domain = picHttpUrl;
////					    if(Strings.isNullOrEmpty(domain)) {
////					    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////					    }
					    String domain = "%domain";
					    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
			            //log.info(httpUrl);
	    				Element pica = DocumentHelper.createElement("img");
	    				pica.addAttribute("src", httpUrl);
	    				pica.addAttribute("class", "chatu");
	    				pica.addText(node.getText());
	    				List elepar = node.getParent().content();
	    				// 用content标签替换文本节点
	    				elepar.set(elepar.indexOf(node), pica);
	    			}
	    				    			
    			}
    			if("原书页面".equals(duanTagName)) {
    							    				
    				Element _e = (Element)zw_element;
    				String ysym = _e.getText();
    				log.info(ysym);
    				ysym = StringUtil.trimAll(ysym);
    				zw_element.setName("a");		 
    				//删除多余属性
		    		Attribute deleteAttri = zw_element.attribute("destsrc");
		    		if(deleteAttri != null) {
		    			zw_element.remove(deleteAttri);
		    		}
		    		
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", "page");
    				zw_element.add(newattri);	 
    				Attribute idattri = DocumentHelper.createAttribute(zw_element, "id", ysym);
    				zw_element.add(idattri);	 
    				//log.info(content.asXML());
    				String hanshu = "openReader('"+ysym+"')";
    				Attribute clickatt = DocumentHelper.createAttribute(zw_element, "onclick", hanshu);
    				zw_element.add(clickatt);	
    				//截取最后5位
    				String ym = StringUtil.substring(ysym,ysym.length()-5);
    				ym = StringUtil.trimAll(ym);
    				ym = StringUtils.replace(ym, " ", "");
    				int yamaInt = 0;
    				try {
	    				yamaInt = Integer.parseInt(ym);			    				
    				}catch(Exception e) {
    					throw new ServiceException("原书页面"+ysym+"不符合规范");
    				}
    				zw_element.setText("P"+String.valueOf(yamaInt));
    				//zwbuf.append(zw_element.asXML());	    			
    			}
    			//插图
    			if("插图".equals(duanTagName)) {
    				String picsrc = zw_element.attributeValue("src");
	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
		            //String uploadpath = baseParms.getUploadPath();
		            String extension = FilenameUtils.getExtension(picsrc);
		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
		            String thumbPath =uploadpath+File.separator+fileName;
		            File destFile = new File(FilenameUtils.normalize(thumbPath));
		            if(!destFile.getParentFile().exists()) {
		            	destFile.getParentFile().mkdirs();
		            }
		           
		            if(srcFile.exists()) {
				    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
				    }else {
				    	//排查是否路径有问题
				    	if(StringUtils.contains(picsrc, "items")) {									    		
				    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
				    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
				    		if(!srcFile.exists()) {
				    			throw new ServiceException(picsrc+"文件不存在");
				    		}
				    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
				    	}
				    }

		          //组装http链接
				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));
//				    String domain = picHttpUrl;
////				    if(Strings.isNullOrEmpty(domain)) {
////				    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////				    }
				    String domain = "%domain";
				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
		            //log.info(httpUrl);
//    				Element pica = DocumentHelper.createElement("img");
//    				pica.addAttribute("src", httpUrl);
//    				pica.addText(content.getText());
//    				List elepar = content.getParent().content();
//    				// 用content标签替换文本节点
//    				elepar.set(elepar.indexOf(content), pica);          
//		            log.info(content.asXML());
		            //删除该标签
		            //content.remove(zw_element);
		            zw_element = null;
		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("</img>");
		            
    			}
    			if(zw_element == null) {
    				continue;
    			}
    			if("注释".equals(duanTagName)) {
    				//log.info("==>"+zw_element.asXML());
    				String ftype = zw_element.attributeValue("type");
    				String divtype = null;
    				if("注".equals(ftype)) {
    					divtype = "zhu";
    				}else if("小注".equals(ftype)) {
    					divtype = "xiaozhu";
    				}else if("落款".equals(ftype)) {
    					divtype = "luokuan";
    				}else if("眉批".equals(ftype)) {
    					divtype = "meipi";
    				}else if("脚注-注".equals(ftype)) {
    					divtype = "jiaozhu-zhu";
    				}else if("脚注-校".equals(ftype)) {
    					divtype = "jiaozhu-jiao";
    				}else if("疏".equals(ftype)){
    					divtype = "shu";
    				}else {
    					divtype = "zhu";
    				}
    				
    				//删除原有的标签
    				Attribute delattri = DocumentHelper.createAttribute(zw_element, "type", divtype);		    				
    				zw_element.remove(delattri);
    				
    				//增加新标签
    				zw_element.setName("span");
    				Attribute newattri = DocumentHelper.createAttribute(zw_element, "class", divtype);
    				zw_element.add(newattri);		
    				
    				//log.info(zw_element.asXML());
    				/**
    				 * 可能会有大字
    				 */
    				List<Element> bigwordelements = zw_element.elements();
    				for(Element bigelement : bigwordelements) {
	    				String tagname = bigelement.getName();
	    				//log.info(tagname);
	    				if("注号".equals(tagname)){
		    				String id = bigelement.attributeValue("id");
		    				String refid = bigelement.attributeValue("refid");
		    				String hexstr = bigelement.getText();
		    				
		    				if(Strings.isNullOrEmpty(refid)) {   						    					
		    						    			
		    					String _refid = zhuhaoRefMap.get(id);
		    					if(Strings.isNullOrEmpty(_refid)) {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("span");
				    				
				    				span.addAttribute("class", "zhuhao");
				    				span.addAttribute("id", ""+id);
				    				span.addAttribute("name", ""+id);
				    					    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);
		    					}else {
		    						//说明是目标注
			    					Element span = DocumentHelper.createElement("a");
				    				
				    				span.addAttribute("class", "zhuhao");
					    			span.addAttribute("id", ""+id);
					    			span.addAttribute("href", "#"+_refid);
				    				
				    				span.addText(hexstr);
				    				List elepar = bigelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(bigelement), span);    		
		    					}   					
		    					
		    				}else {	    					
		    					
		    					zhuhaoRefMap.put(refid, id);
		    					
	    						//说明是目标注
		    					Element span = DocumentHelper.createElement("a");
			    				
			    				span.addAttribute("class", "zhuhao");
				    			span.addAttribute("id", ""+id);
				    			span.addAttribute("href", "#"+refid);
			    				
			    				span.addText(hexstr);
			    				List elepar = bigelement.getParent().content();
			    				// 用content标签替换文本节点
			    				elepar.set(elepar.indexOf(bigelement), span);    					
		    				}
		    				
	    				}
	    				
	    				if("字体".equals(tagname)){
		    				String _ftype = bigelement.attributeValue("type");
		    				//log.info(node.getText()+","+ftype);
		    				
		    				Element span = DocumentHelper.createElement("span");
		    				String hexstr = null;
		    				if(Strings.isNullOrEmpty(_ftype)) {
		    					span.addAttribute("class", "no");
		    					hexstr = bigelement.getText();
		    				}else {
			    				if(_ftype.contains("超大字2")) {
			    					span.addAttribute("class", "big02");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字3")){
			    					span.addAttribute("class", "big15");
			    					//大字统一转&#x码
				    				String bigtext = bigelement.getText();
				    				//hexstr = BigwordUtil.str2Hex(bigtext);
				    				hexstr = BigwordUtil.hex2word(bigtext);
			    				}else if(_ftype.contains("超大字1")) {
			    					span.addAttribute("class", "big01");
			    					hexstr = zw_element.getText();
			    					hexstr = BigwordUtil.hex2word(hexstr);
			    				}else {
			    					span.addAttribute("class", _ftype);
			    					hexstr = bigelement.getText();
			    				}
		    				}
		    				
		    				span.addText(hexstr);
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), span);
	    				}
	    				//log.info(zw_element.asXML());
	    				if("原书页面".equals(tagname)) {
				    		String ysym = bigelement.getText();
				    		ysym = StringUtil.trimAll(ysym);
				    		//log.info("==>"+tagName +":"+ysym);
				    		//增加新标签
				    		bigelement.setName("a");		
				    		//删除多余属性
				    		Attribute deleteAttri = bigelement.attribute("destsrc");
				    		if(deleteAttri != null) {
				    			bigelement.remove(deleteAttri);
				    		}
				    		
		    				Attribute zspageattri = DocumentHelper.createAttribute(bigelement, "class", "page");
		    				bigelement.add(zspageattri);	 
		    				Attribute idattri = DocumentHelper.createAttribute(bigelement, "id", ysym);
		    				bigelement.add(idattri);	 
		    				//log.info(content.asXML());
		    				String hanshu = "openReader('"+ysym+"')";
		    				Attribute clickatt = DocumentHelper.createAttribute(bigelement, "onclick", hanshu);
		    				bigelement.add(clickatt);	
		    				//截取最后5位
		    				String ym = StringUtil.substring(ysym,ysym.length()-5);
		    				ym = StringUtil.trimAll(ym);
		    				ym = StringUtils.replace(ym, " ", "");
		    				int yamaInt = 0;
		    				try {
			    				yamaInt = Integer.parseInt(ym);			    				
		    				}catch(Exception e) {
		    					throw new ServiceException("原书页面"+ysym+"不符合规范");
		    				}
		    				bigelement.setText("P"+String.valueOf(yamaInt));
		    				//zwbuf.append(bigelement.asXML());
				    	}
	    				//插图
	        			if("插图".equals(tagname)) {
	        				String picsrc = bigelement.attributeValue("src");
	    	    			File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
	    		            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
	    		            //String uploadpath = baseParms.getUploadPath();
	    		            String extension = FilenameUtils.getExtension(picsrc);
	    		            String fileName = DateUtil.getDate() + File.separator + IdUtil.fastUUID() + "." + extension;
	    		            String thumbPath =uploadpath+File.separator+fileName;
	    		            File destFile = new File(FilenameUtils.normalize(thumbPath));
	    		            if(!destFile.getParentFile().exists()) {
	    		            	destFile.getParentFile().mkdirs();
	    		            }
	    		           
	    		            if(srcFile.exists()) {
						    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destFile.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destFile.getAbsolutePath(), true);		
						    	}
						    }

	    		          //组装http链接
	    				    String relapath = StringUtil.remove(FilenameUtils.normalize(destFile.getAbsolutePath(),true), FilenameUtils.normalize(uploadpath,true));
//	    				    String domain = picHttpUrl;
////	    				    if(Strings.isNullOrEmpty(domain)) {
////	    				    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////	    				    }
	    				    String domain = "%domain";
	    				    String httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
	    		            //log.info(httpUrl);
	        				Element pica = DocumentHelper.createElement("img");
	        				pica.addAttribute("src", httpUrl);
	        				pica.addText(bigelement.getText());
	        				List elepar = bigelement.getParent().content();
	        				// 用content标签替换文本节点
	        				elepar.set(elepar.indexOf(bigelement), pica);          
//	    		            log.info(content.asXML());
	    		            //删除该标签
	    		            //content.remove(zw_element);
//	    				    bigelement = null;
//	    		            zwbuf.append("<img src='"+httpUrl+"'  class=\"chatu\">").append("");
	    		            
	        			}
	        			
	    				//log.info(bigelement.asXML());
	    				if("字图".equals(tagname)||"外字".equals(tagname)) {
		    				Element _e = (Element)bigelement;
		    				String picsrc = _e.attributeValue("src");
		    				//转换为http:// 形式
		    				String httpUrl = null;
		    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
		    				//log.info("src:"+srcFile.getAbsolutePath());
				            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
				            //String uploadpath = baseParms.getUploadPath();
				            
				            
				            uploadpath = FilenameUtils.normalize(uploadpath);
				            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
						    
						    File destDir = new File(destPath);
						    if(!destDir.getParentFile().exists()) {
						    	destDir.getParentFile().mkdirs();
						    }									   
						    if(srcFile.exists()) {
						    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
						    }else {
						    	//排查是否路径有问题
						    	if(StringUtils.contains(picsrc, "items")) {									    		
						    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
						    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
						    		if(!srcFile.exists()) {
						    			throw new ServiceException(picsrc+"文件不存在");
						    		}
						    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
						    	}
						    }
						    
						    //组装http链接
						    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//						    String domain = picHttpUrl;
////						    if(Strings.isNullOrEmpty(domain)) {
////						    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////						    }
						    String domain = "%domain";
						    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
				            //log.info(httpUrl);
		    				Element pica = DocumentHelper.createElement("img");
		    				pica.addAttribute("src", httpUrl);
		    				pica.addAttribute("class", "zitu");
		    				pica.addText(bigelement.getText());
		    				List elepar = bigelement.getParent().content();
		    				// 用content标签替换文本节点
		    				elepar.set(elepar.indexOf(bigelement), pica);		    			
	    				}	
	    				
	    				if("书名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");			    				
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trimAll(ysym);
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trimAll(ym);
				    				ym = StringUtils.replace(ym, " ", "");
				    				int yamaInt = 0;
				    				try {
					    				yamaInt = Integer.parseInt(ym);			    				
				    				}catch(Exception e) {
				    					throw new ServiceException("原书页面"+ysym+"不符合规范");
				    				}
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						            
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }else {
								    	//排查是否路径有问题
								    	if(StringUtils.contains(picsrc, "items")) {									    		
								    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
								    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    		if(!srcFile.exists()) {
								    			throw new ServiceException(picsrc+"文件不存在");
								    		}
								    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
								    	}
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//								    String domain = picHttpUrl;
////								    if(Strings.isNullOrEmpty(domain)) {
////								    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////								    }
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
		    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "shuming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}
	    				if("专名".equals(tagname)) {
		    				Element _e = (Element)bigelement;

		    				List<Element> zmelements = bigelement.elements();
		    				for(Element zmelement : zmelements) {
			    				String zmtag = zmelement.getName();
			    				//log.info(zmtag);
			    				if("注号".equals(zmtag)){
				    				String id = zmelement.attributeValue("id");
				    				String refid = zmelement.attributeValue("refid");
				    				String hexstr = zmelement.getText();
				    				
				    				if(Strings.isNullOrEmpty(refid)) {   						    					
				    						    			
				    					String _refid = zhuhaoRefMap.get(id);
				    					if(Strings.isNullOrEmpty(_refid)) {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("span");
						    				
						    				span.addAttribute("class", "zhuhao");
						    				span.addAttribute("id", ""+id);
						    				span.addAttribute("name", ""+id);
						    					    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);
				    					}else {
				    						//说明是目标注
					    					Element span = DocumentHelper.createElement("a");
						    				
						    				span.addAttribute("class", "zhuhao");
							    			span.addAttribute("id", ""+id);
							    			span.addAttribute("href", "#"+_refid);
						    				
						    				span.addText(hexstr);
						    				List elepar = zmelement.getParent().content();
						    				// 用content标签替换文本节点
						    				elepar.set(elepar.indexOf(zmelement), span);    		
				    					}   					
				    					
				    				}else {	    					
				    					
				    					zhuhaoRefMap.put(refid, id);
				    					
			    						//说明是目标注
				    					Element span = DocumentHelper.createElement("a");
					    				
					    				span.addAttribute("class", "zhuhao");
						    			span.addAttribute("id", ""+id);
						    			span.addAttribute("href", "#"+refid);
					    				
					    				span.addText(hexstr);
					    				List elepar = zmelement.getParent().content();
					    				// 用content标签替换文本节点
					    				elepar.set(elepar.indexOf(zmelement), span);    					
				    				}
			    				}
			    				
			    				if("字体".equals(zmtag)){
				    				String _ftype = zmelement.attributeValue("type");
				    				//log.info(node.getText()+","+ftype);
				    				
				    				Element span = DocumentHelper.createElement("span");
				    				String hexstr = null;
				    				if(Strings.isNullOrEmpty(_ftype)) {
				    					span.addAttribute("class", "no");
				    					hexstr = zmelement.getText();
				    				}else {
					    				if(_ftype.contains("超大字2")) {
					    					span.addAttribute("class", "big02");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字3")){
					    					span.addAttribute("class", "big15");
					    					//大字统一转&#x码
						    				String bigtext = zmelement.getText();
						    				//hexstr = BigwordUtil.str2Hex(bigtext);
						    				hexstr = BigwordUtil.hex2word(bigtext);
					    				}else if(_ftype.contains("超大字1")) {
					    					span.addAttribute("class", "big01");
					    					hexstr = zw_element.getText();
					    					hexstr = BigwordUtil.hex2word(hexstr);
					    				}else {
					    					span.addAttribute("class", _ftype);
					    					hexstr = zmelement.getText();
					    				}
				    				}
				    				
				    				span.addText(hexstr);
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), span);
			    				}
			    				//log.info(zw_element.asXML());
			    				if("原书页面".equals(zmtag)) {
						    		String ysym = zmelement.getText();
						    		ysym = StringUtil.trimAll(ysym);
						    		//log.info("==>"+tagName +":"+ysym);
						    		//增加新标签
						    		zmelement.setName("a");		
						    		//删除多余属性
						    		Attribute deleteAttri = zmelement.attribute("destsrc");
						    		if(deleteAttri != null) {
						    			zmelement.remove(deleteAttri);
						    		}
						    		
				    				Attribute zspageattri = DocumentHelper.createAttribute(zmelement, "class", "page");
				    				zmelement.add(zspageattri);	 
				    				Attribute idattri = DocumentHelper.createAttribute(zmelement, "id", ysym);
				    				zmelement.add(idattri);	 
				    				//log.info(content.asXML());
				    				String hanshu = "openReader('"+ysym+"')";
				    				Attribute clickatt = DocumentHelper.createAttribute(zmelement, "onclick", hanshu);
				    				zmelement.add(clickatt);	
				    				//截取最后5位
				    				String ym = StringUtil.substring(ysym,ysym.length()-5);
				    				ym = StringUtil.trimAll(ym);
				    				ym = StringUtils.replace(ym, " ", "");
				    				int yamaInt = 0;
				    				try {
					    				yamaInt = Integer.parseInt(ym);			    				
				    				}catch(Exception e) {
				    					throw new ServiceException("原书页面"+ysym+"不符合规范");
				    				}
				    				zmelement.setText("P"+String.valueOf(yamaInt));
				    				//zwbuf.append(zmelement.asXML());
						    	}
			    				//log.info(zmelement.asXML());
			    				if("字图".equals(zmtag)||"外字".equals(zmtag)) {
				    				Element zm_e = (Element)zmelement;
				    				String picsrc = zm_e.attributeValue("src");
				    				//转换为http:// 形式
				    				String httpUrl = null;
				    				File srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+picsrc));
				    				//log.info("src:"+srcFile.getAbsolutePath());
						            ///profile/upload/2020/10/29/9fddaead-3d84-416c-a30b-91dd339ee3ee.jpg
						            //String uploadpath = baseParms.getUploadPath();
						            
						           
						            uploadpath = FilenameUtils.normalize(uploadpath);
						            String destPath = FilenameUtils.normalize(uploadpath+File.separator+book.getId()+File.separator+"zt"+File.separator+FilenameUtils.getName(picsrc));
								    
								    File destDir = new File(destPath);
								    if(!destDir.getParentFile().exists()) {
								    	destDir.getParentFile().mkdirs();
								    }									   
								    if(srcFile.exists()) {
								    	log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    	FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);				
								    }else {
								    	//排查是否路径有问题
								    	if(StringUtils.contains(picsrc, "items")) {									    		
								    		srcFile = new File(FilenameUtils.normalize(FilenameUtils.getFullPath(xmlPath)+File.separator+StringUtils.remove(picsrc, "items/")));
								    		log.info("==>拷贝字图：{}  {}",srcFile.getAbsolutePath(), destDir.getAbsolutePath());
								    		if(!srcFile.exists()) {
								    			throw new ServiceException(picsrc+"文件不存在");
								    		}
								    		FileUtil.copy(srcFile.getAbsolutePath(), destPath, true);		
								    	}
								    }
								    
								    //组装http链接
								    String relapath = StringUtil.remove(FilenameUtils.normalize(destPath,true), FilenameUtils.normalize(uploadpath,true));
//								    String domain = picHttpUrl;
////								    if(Strings.isNullOrEmpty(domain)) {
////								    	domain = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
////								    }
								    String domain = "%domain";
								    httpUrl = domain + FilenameUtils.normalize(relapath,true);						           
						            //log.info(httpUrl);
				    				Element pica = DocumentHelper.createElement("img");
				    				pica.addAttribute("src", httpUrl);
				    				pica.addAttribute("class", "zitu");
				    				pica.addText(zmelement.getText());
				    				List elepar = zmelement.getParent().content();
				    				// 用content标签替换文本节点
				    				elepar.set(elepar.indexOf(zmelement), pica);		    			
			    				}	
		    				}
			    				
		    				//增加新标签
		    				_e.setName("span");
		    				Attribute deleteAttri = _e.attribute("type");
	    		    		if(deleteAttri != null) {
	    		    			_e.remove(deleteAttri);
	    		    		}
		    				Attribute _newattri = DocumentHelper.createAttribute(_e, "class", "zhuanming");
		    				_e.add(_newattri);		
		    				//log.info(zw_element.asXML());
		    			}	
    				}
    			}				    					    			
    			if(zw_element != null) {
	    			String xmlstr = zw_element.asXML();
	    			xmlstr = StringUtil.replace(xmlstr, "&lt;", "<");
	    			xmlstr = StringUtil.replace(xmlstr, "&gt;", ">");
	    			
	    			//log.info(xmlstr);
	    			xmlstr = StringUtil.remove(xmlstr, "<正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"\">");	    			
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"引文\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"注\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"校\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<注文 type=\"疏\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<正文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.removePattern(xmlstr, "<注文 type=\"[\u4e00-\u9fa5\\-]{2,6}\">");
	    			xmlstr = StringUtil.remove(xmlstr, "</注文>");
	    			xmlstr = StringUtil.remove(xmlstr, "</正文>");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文 type=\"2\">");
	    			xmlstr = StringUtil.remove(xmlstr, "<正文/>");   			
	    			zwbuf.append(xmlstr);
    			}
    			if(!Strings.isNullOrEmpty(tempDiv)) {
	    			if(tempDiv.indexOf("<span class=\"zhengwen-") != -1||tempDiv.indexOf("<span class=\"zhuwen-") != -1) {
	    				//xmlstr = xmlstr +"</div>";
	    				zwbuf.append("</span>");
	    			}
    			}
    			
    		}
    		zwbuf.append("</p>");
    		
    	}
		//log.info("==>解析结果：{}",zwbuf.toString());
		return zwbuf.toString();
	}
	
	
	public static String findLastPage(String str) {
		String result = null;
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		    	   
	    while(m.find()){	      
	       String pagestr = m.group();       
	       result = pagestr;
	    }	
		
		return result;
	}
	
	public static String findLastPageNoTag(String str) {
		String result = null;
		String patstr = "(P[0-9]*)";
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		    	   
	    while(m.find()){	      
	       String pagestr = m.group();       
	       result = pagestr;
	    }	
		
		return result;
	}
	
	
	public static String  removePage(String content) {
		//String patstr = "(P[0-9]*)";
		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
		
		Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(content);		    
	    while(m.find()){	 
	    	content = m.replaceAll("");	    	
	    }
	    return content;
	}
	
	public static String removeBigwordStr(String str,String classname) {

		String pattern= "(<span class=\""+classname+"\">[\u4e00-\u9fff]{1,2}</span>)";
		Pattern r = Pattern.compile(pattern); 
		
		int laststart = 0;
		int lastend = 0;
		Matcher m = r.matcher(str);
		while (m.find()) { 
			String pattStr = m.group();
			//System.out.println(pattStr);
			String _pattStr = StringUtil.replace(pattStr, "<span class=\""+classname+"\">", "");
			String bigword = StringUtil.replace(_pattStr, "</span>", "");
			
			str = StringUtil.replace(str, pattStr, bigword);
		}
		
		return str.toString();
	}
	
	
	
	
	/**
	 * 格式化html
	 * @param xmlcontent
	 * @return
	 */
	public static String formatHtml(String xmlcontent) {
		if(Strings.isNullOrEmpty(xmlcontent)) {
			return xmlcontent;
		}
//		xmlcontent = StringUtil.replace(xmlcontent, "<div class=\"mlbt1\">", "<p>");
//		xmlcontent = StringUtil.replace(xmlcontent, "<div class=\"mlbt2\">", "<p>");
//		xmlcontent = StringUtil.replaceAll(xmlcontent, "<div class=\"mlbt[\\d]+\">", "<p>");
		
		Pattern p1 = Pattern.compile("<div class=\"mlbt[1-9]+\">");
		Matcher m1 = p1.matcher(xmlcontent);		
		// 先找出不配对得标签，记录位置下表
		while (m1.find()) {
			String tag = m1.group();
			log.info(tag);
			if(StringUtils.contains(tag, "mlbt1")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti1\">");
			}else if(StringUtils.contains(tag, "mlbt2")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti2\">");
			}else if(StringUtils.contains(tag, "mlbt3")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti3\">");
			}else if(StringUtils.contains(tag, "mlbt4")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti4\">");
			}else if(StringUtils.contains(tag, "mlbt5")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti5\">");
			}else if(StringUtils.contains(tag, "mlbt6")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti6\">");
			}
		}
		
		xmlcontent = StringUtil.replace(xmlcontent, "</div>", "</p>");
		
		//xmlcontent = StringUtil.replaceAll(xmlcontent, "<div class=\"biaoti[\\d]+\">", "<p>");
		//正则匹配标题
		Pattern p = Pattern.compile("<div class=\"biaoti[1-9]+\">");
		Matcher m = p.matcher(xmlcontent);		
		// 先找出不配对得标签，记录位置下表
		while (m.find()) {
			String tag = m.group();
			log.info(tag);
			if(StringUtils.contains(tag, "biaoti1")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti1\">");
			}else if(StringUtils.contains(tag, "biaoti2")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti2\">");
			}else if(StringUtils.contains(tag, "biaoti3")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti3\">");
			}else if(StringUtils.contains(tag, "biaoti4")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti4\">");
			}else if(StringUtils.contains(tag, "biaoti5")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti5\">");
			}else if(StringUtils.contains(tag, "biaoti6")) {
				xmlcontent = xmlcontent.replace(tag, "<p class=\"biaoti6\">");
			}
		}
			
		
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号\">", "<span class=\"zhuozhonghao\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号1\">", "<span class=\"zhuozhonghao1\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号2\">", "<span class=\"zhuozhonghao2\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号3\">", "<span class=\"zhuozhonghao3\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号4\">", "<span class=\"zhuozhonghao4\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"着重号5\">", "<span class=\"zhuozhonghao5\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"超大字1\">", "<span class=\"big01\">");
		//temp_result = StringUtil.replace(temp_result, "<span class=\"楷体\">", "<span class=\"kaiti\">");
		xmlcontent = StringUtil.replace(xmlcontent, "<span class=\"楷体\">", "");
		//去掉表格
		xmlcontent = StringUtil.replace(xmlcontent, "<table>","");
    	xmlcontent = StringUtil.replace(xmlcontent, "</table>","");
    	xmlcontent = StringUtil.replace(xmlcontent, "<tr>","");	        	
    	xmlcontent = StringUtil.replace(xmlcontent, "</tr>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td rowspan=[0-9]{1,2} colspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td rowspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replaceAll(xmlcontent, "<td colspan=[0-9]{1,2}>","");
		xmlcontent = StringUtil.replace(xmlcontent, "</td>","");
		xmlcontent = StringUtil.replace(xmlcontent, "<td>","");

		//log.info("==>"+xmlcontent);	
		return xmlcontent;
	}
	/**
	 * 
	 * @param pageTaglist
	 * @param i
	 * @param xmlcontent
	 * @param pageContent
	 * @return
	 */
//	public static String getPageHtmlContent(List<String> pageTaglist, int i, String xmlcontent,String pageContent) {
//		String pageStr = pageTaglist.get(i);
//		String direct = "both";
//    	if(i == 0) {
//    		direct = "after";
//    		if(pageTaglist.size() > 1) {
//    			pageStr = pageTaglist.get(i+1);
//    		}else {
//    			direct = "no";
//    		}
//    	}
//    	if(i == pageTaglist.size() -1) {
//    		direct = "before";
//    	}
//    	String pagehtmlstr = completeHtmlTag(xmlcontent , pageStr, direct, pageContent);
//    	//log.info("==>"+pagehtmlstr);	 
//    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p></p>","</p>");
//    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p><p>","<p>");
//    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<span class=\"zhu\"/></span>","");
//    	if(StringUtil.endsWith(pagehtmlstr, "</p><p>")) {
//    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<p>");
//    	}
//    	if(StringUtil.startsWith(pagehtmlstr, "</p><p>")) {
//    		pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p><p>","<p>");
//    	}
//    	pagehtmlstr = StringUtil.replaceAll(pagehtmlstr, "(<p>){2,10}","<p>");
//    	
//    	return pagehtmlstr;
//	}
	public static String getPageHtmlContent(List<String> pageTaglist, int i, String xmlcontent,String pageContent) {
		String pageStr = pageTaglist.get(i);
		String direct = "both";
    	if(i == 0) {
    		direct = "after";
    		if(pageTaglist.size() > 1) {
    			pageStr = pageTaglist.get(i+1);
    		}else {
    			direct = "no";
    		}
    	}
    	if(i == pageTaglist.size() -1) {
    		direct = "before";
    	}
    	String pagehtmlstr = completeHtmlTag(xmlcontent , pageStr, direct, pageContent);
    	//log.info("==>"+pagehtmlstr);	 
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p></p>","</p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p><p>","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p class=\"bt\"><p class=\"bt\">","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<span class=\"zhu\"/></span>","");
    	if(StringUtil.endsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p><p class=\"bt\">","<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p>","");
    	}
    	if(StringUtil.endsWith(pagehtmlstr, "<tr>")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<tr>");
    	}
    	if(!StringUtil.endsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = pagehtmlstr + "</p>";
    	}
    	pagehtmlstr = StringUtil.replaceAll(pagehtmlstr, "(<p class=\"bt\">){2,10}","<p class=\"bt\">");
    	if(StringUtils.equalsAny(pagehtmlstr, "<p>","</p>")) {
    		pagehtmlstr = "<p></p>";
    	}
    	
    	return pagehtmlstr;
	}
	
	public static String lastFormat(String pagehtmlstr) {
		
    	//log.info("==>"+pagehtmlstr);	 
		int lastlen = 0;
		while(true) {
			pagehtmlstr = StringUtil.replace(pagehtmlstr, "</p></p>","</p>");
			int len = pagehtmlstr.length();
			if(lastlen == len) {
				break;
			}
			lastlen = len;
		}
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p><p>","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<p class=\"bt\"><p class=\"bt\">","<p>");
    	pagehtmlstr = StringUtil.replace(pagehtmlstr, "<span class=\"zhu\"/></span>","");
    	if(StringUtil.endsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p><p class=\"bt\">")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p><p class=\"bt\">","<p class=\"bt\">");
    	}
    	if(StringUtil.startsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = StringUtil.replaceOnce(pagehtmlstr, "</p>","");
    	}
    	if(StringUtil.endsWith(pagehtmlstr, "<tr>")) {
    		pagehtmlstr = StringUtil.removeEnd(pagehtmlstr, "<tr>");
    	}
    	if(!StringUtil.endsWith(pagehtmlstr, "</p>")) {
    		pagehtmlstr = pagehtmlstr + "</p>";
    	}
    	pagehtmlstr = StringUtil.replaceAll(pagehtmlstr, "(<p class=\"bt\">){2,10}","<p class=\"bt\">");
    	if(StringUtils.equalsAny(pagehtmlstr, "<p>","</p>")) {
    		pagehtmlstr = "<p></p>";
    	}
    	
    	return pagehtmlstr;
	}
	/**
	 * 
	 * @param menuHtmlStr 本章节的全部html内容
	 * @param pageStr  页码
	 * @param direct
	 * @param pageContent
	 * @return
	 * @throws Exception
	 */
	private static String completeHtmlTag(String menuHtmlStr,String pageStr, String direct, String pageContent) {
		String result = "";
		
		if(StringUtil.equals(direct, "no")) {
			return pageContent;
		}
		if(StringUtil.equals(direct, "before")) {
	
			String _str = StringUtil.substringBefore(menuHtmlStr, pageStr);
			_str = removePage(_str);
			//log.info("_str="+_str);			
			
			List<String> beforeclosetags = lastUncloseTags(_str);
			//log.info(StringUtil.join(beforeclosetags, "|||||"));
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = pageContent;
			}else {
				result = StringUtil.join(beforeclosetags,"") + pageContent;
				if(StringUtils.startsWith(result.toString(), "<p class=\"bt\"")) {
					result = StringUtils.replace(result, "<p class=\"bt\"", "<p");
				}
			}
			
		}else if(StringUtil.equals(direct, "after")) {			

			List<String> afterclosetags = lastUncloseTags(pageContent);
			StringBuilder buff = new StringBuilder();
			Collections.reverse(afterclosetags);
			for(String tag : afterclosetags) {
				if(StringUtil.startsWith(tag, "<p")) {
					buff.append("</p>");
				}
				if(StringUtil.startsWith(tag, "<tr")) {
					buff.append("</tr>");
				}
				if(StringUtil.startsWith(tag, "</tr")) {
					buff.append("<tr>");
				}
				if(StringUtil.startsWith(tag, "<td")) {
					buff.append("</td>");
				}
				if(StringUtil.startsWith(tag, "<span")) {
					buff.append("</span>");
				}
			}
			result =  pageContent + buff.toString();
			
		}else {
//			if(StringUtil.contains(pageStr, "L00007")) {
//				log.info("debug");
//			}
			//中间部分处理
			String _str = StringUtil.substringBefore(menuHtmlStr, pageStr);
			_str = removePage(_str);
			//log.info("_str="+_str);		
			List<String> beforeclosetags = lastUncloseTags(_str);
			//log.info(StringUtil.join(beforeclosetags, "|||||"));
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = "";
			}else {
				result = StringUtil.join(beforeclosetags,"");
				if(StringUtils.startsWith(result.toString(), "<p class=\"bt\"")) {
					result = StringUtils.replace(result, "<p class=\"bt\"", "<p");
				}
			}
			//本身
			List<String> closetags = lastUncloseTags(pageContent);
			//log.info(StringUtil.join(closetags, "|||||"));
			StringBuilder buff = new StringBuilder();
			//Collections.sort(closetags, Collections.reverseOrder());
			Collections.reverse(closetags);
			for(String tag : closetags) {
				if(StringUtil.startsWith(tag, "<span")) {
					buff.append("</span>");
				}
				if(StringUtil.startsWith(tag, "<p")) {
					buff.append("</p>");
				}				
			}
			String lostTags = StringUtil.join(closetags, "");
			
			result =  result + pageContent + buff.toString();
			//再次校验
			List<String> checktags = lastUncloseTags(result);
			if(checktags.size() == 0) {
				return result;
			}
			
			//后面
			String _str1 = StringUtil.substringAfter(menuHtmlStr, pageStr);
			_str1 = removePage(_str1);
			//log.info("_str1="+_str1);		
			List<String> afterclosetags = lastUncloseTags(_str1);
			//log.info(StringUtil.join(afterclosetags, "|||||"));
			//中间和最后比较 是否一致
			String lost2Tags = StringUtil.join(afterclosetags, "");//lost2Tags = </span></p>  
			for(String tags1 : closetags) {
				afterclosetags.remove(tags1);
			}
			
			if(beforeclosetags == null || beforeclosetags.size() == 0) {
				result = result;
			}else {
				result = result + StringUtil.join(afterclosetags,"");	
			}
		}
		
		return result;
	}
	
	/**
	 * 
	 * @param htmlStr
	 * @return
	 */
	private static List<String> lastUncloseTags(String htmlStr){
		List<String> list = Lists.newCopyOnWriteArrayList();
		Stack<String> statck = new Stack<String>();
		
		//Pattern p = Pattern.compile("<\\/[a-zA-Z]*>");
		//Pattern p = Pattern.compile("<p>|<span class=\"[a-zA-Z0-9]*\"[^<>]*>|</span>|</p>");
		Pattern p = Pattern.compile("<p>|<tr>|<td>|</tr>|</td>|<p class=\"bt\">|<span class=\"[a-zA-Z0-9]*\"[^<>]*>|<span>|</span>|</p>");
		Matcher m = p.matcher(htmlStr);		
		// 先找出不配对得标签，记录位置下表
		while (m.find()) {
			String tag = m.group();
			//System.out.println(tag);
			if(StringUtil.startsWith(tag, "<")&&!StringUtil.startsWith(tag, "</")) {
				statck.add(tag);
			}
			if(StringUtil.startsWith(tag, "</")) {	
				if(!statck.empty()) {
					String tags = statck.pop();
					boolean isdelete = list.remove(tags);
					if(!isdelete) {
						list.add(tag);
					}
				}else {
					list.add(tag);
				}
			}else {
				list.add(tag);
			}
		}
		return list;
	}

	public static Element element(Element e, String...ename) {
		for (String name : ename) {
			Element element = e.element(name);
			if(element != null) {
				return element;
			}
		}
		return null;
	}
	
	public static String formatP(String enccontent) {
		if(Strings.isNullOrEmpty(enccontent)) {
			return enccontent;
		}
		if(StringUtils.startsWith(enccontent, "<p></p>")) {
      	  enccontent = StringUtil.replaceOnce(enccontent, "<p></p>", "");
        }
		if(StringUtils.startsWith(enccontent, "</p></p>")) {
	      	  enccontent = StringUtil.replace(enccontent, "</p></p>", "</p>");
	        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti1\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti1\"><p>", "<p class=\"biaoti1\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti2\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti2\"><p>", "<p class=\"biaoti2\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti3\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti3\"><p>", "<p class=\"biaoti3\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti4\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti4\"><p>", "<p class=\"biaoti4\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti5\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti5\"><p>", "<p class=\"biaoti5\">");
        }
        if(StringUtils.contains(enccontent, "<p class=\"biaoti6\"><p>")) {
      	  enccontent = StringUtil.replace(enccontent, "<p class=\"biaoti6\"><p>", "<p class=\"biaoti6\">");
        }
        
        return enccontent;
	}
	
	
	public static void main(String[] args) throws Exception {
		
		
		String xmlpath1 = "C:\\Users\\<USER>\\Desktop\\v1\\items\\正文.xml";
		
		TProBooks book = new TProBooks();
		List<String> filelist = Lists.newArrayList();
		filelist.add("test");
		filelist.add("正文");
		book.setXmlFileList(filelist);
		book.setId("123328493483948");
		Map<String,String> fileMap = Maps.newHashMap();
		//fileMap.put("test", xmlpath);
		fileMap.put("正文", xmlpath1);
		book.setBookName("黨員讀本");
		String rootPATH = null;
		rootPATH = "C:\\Users\\<USER>\\Desktop\\v1\\items";
		TProBookMenu menu = parseBookMenuContentXml(book,fileMap,rootPATH,"");
//		
//		
////		String xmlPath = "C:\\Users\\<USER>\\Desktop\\正文.xml";
////		
////		String xmlStr = TxtUtil.readTxtFile(xmlPath);
////		xmlStr = StringUtil.remove(xmlStr, " xmlns=\"http://shangyuan/shuju_yuliao\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"");
////		System.out.println(xmlStr);
//		
////		String pageNostr = "P0";
////		int a = Integer.parseInt(StringUtil.remove(pageNostr, "P"));
////		System.out.println(a);
//		
//		String xmlcontent = "<a class=\"page\" id=\"DJB00016-00091-L00000\" onclick=\"openReader('DJB00016-00091-L00000')\">P0</a>";
//		String patstr = "(<a class=\"page\" id=\"[0-9A-Za-z-]{1,}\" onclick=\"openReader\\('[0-9A-Za-z-]{1,}'\\)\">P[0-9]*</a>)";
//		Pattern p = Pattern.compile( patstr );
//	    Matcher m = p.matcher(xmlcontent);		    
//	    List<String> pageTaglist = Lists.newArrayList();
//	    while(m.find()){	      
//	       String pagestr = m.group();
//	       log.info("==>页码："+pagestr);	       
//	       
//	        org.jsoup.nodes.Document document = Jsoup.parse(pagestr);
//	    	//log.info(document.html());
//	    	String picId = null;
//	    	String pageNostr = null;
//	    	org.jsoup.select.Elements ides = document.getElementsByClass("page");
//			if(ides != null) {
//				org.jsoup.nodes.Element _ele = ides.first();
//				pageNostr = _ele.text();
//				picId = _ele.id();
//				
//				 log.info("==>页码："+pageNostr);	
//				 System.out.println(Integer.parseInt(StringUtil.remove(pageNostr, "P")));
//				 
//				 TBookMenuImageMongo pageMongo = new TBookMenuImageMongo();
//				 pageMongo.setPageNo(Integer.parseInt(StringUtil.remove(pageNostr, "P")));	
//				 log.info("==>"+pageMongo.getPageNo());
//			}			  
//	    }
		
//		String xmlcontent = FileUtils.readFileToString(new File("d://wenzhou-test.txt"), Charsets.UTF_8);
//		TProBookMenu menus = new TProBookMenu();
//		menus.setZwContent(xmlcontent);
//		List<TBookPageContentMongo> pagelist = XmlToolUtils.parseBookPageXml(menus);
		
//		String sss = "</p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p><p class=\"bt\">固自可扫荡邪媚使中正之道大明皆主振起奋发此即思狂狷之意</p><p class=\"bt\">何如斯可为狂矣是问甚样人方谓之狂何以谓之狂是问甚样行事叫做狂二处有分别</p><p class=\"bt\">狂者又不可得陈大始说琴张曾晰牧皮虽狂者一流然终不知所裁不足以与大道之传故曰不可得不必为上文回护〇此不可得是难得传道非难见其人</p><p class=\"bt\">以假乱真而足以坏真谓之曰贼如榖中秕子银中之铅相似〇乡原是狂狷反对</p>";
//		log.info(XmlToolUtils.lastFormat(sss));
//		Map<String,String> fileMap = Maps.newLinkedHashMap();
//		fileMap.put("main.xpro", "C:\\Users\\<USER>\\Desktop\\版式还原\\HSB00113 怎樣開展今年的大生産\\main - 副本.xpro");
//		TProBooks bookXpro = XmlToolUtils.parseMainXpro4Meta(fileMap);
//		log.info("{}",bookXpro.getXmlFileList());
	}


}
