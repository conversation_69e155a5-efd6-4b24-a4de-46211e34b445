package cn.guliandigital.storage.storage.domain;

import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据入库配置对象 t_task_settle
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
@Data
public class TTaskSettle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 解析文件所在路径 */
    @Excel(name = "解析文件所在路径")
    private String dataPath;

    private String  readMode;

    //入库进度    0-入库中 1-入库完成 2-入库失败
    private String inStatus;
    
    /** 解析状态 0-准备 1-解析中  2-解析成功 3-解析失败 */
    @Excel(name = "解析状态 0-准备 1-解析中  2-解析成功 3-解析失败")
    private String dataStatus;
    
    private String dataStatusNot;

    /** 解析时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") //入参
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss") //出参
    private Date parseStartTime;

    /** 解析结束时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") //入参
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss") //出参
    private Date parseEndTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") //入参
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss") //出参
    private Date createTime;
    /** 描述 */
    @Excel(name = "备注")
    private String dataRemark;

    /** $column.columnComment */
    @Excel(name = "创建人")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
    @Excel(name = "创建人")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    /** 解析方式 S-推送 M-手动 */
    @Excel(name = "解析方式 S-推送 M-手动")
    private String parseMethod;

    /** 唯一标识符 */
    @Excel(name = "唯一标识符")
    private String uniqueId;

    private String source;

    private String bookName;

    private String dbName;
    private String resourceClasses;
    
    private String respMsg;

    private Integer pdfCount;//pdf资源量

    private Integer fullCount;//全文资源量

    private String dataJson;//描述

    private AtomicInteger errorCount;

    private AtomicInteger allCount;
}
