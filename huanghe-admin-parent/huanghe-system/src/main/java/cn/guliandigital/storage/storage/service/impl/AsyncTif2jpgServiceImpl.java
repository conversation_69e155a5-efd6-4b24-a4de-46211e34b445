package cn.guliandigital.storage.storage.service.impl;

import java.io.File;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.file.Tif2JpgPythonUtils;
import cn.guliandigital.product.book.service.IPicService;
import cn.guliandigital.storage.storage.service.IAsyncTif2jpgService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/10/28 21:40
 * 
 */
@Slf4j
//@Transactional
@Service("asyncTif2jpgService")
public class AsyncTif2jpgServiceImpl implements IAsyncTif2jpgService {

	
	
	@Autowired
    private RedisCache redisCache;
	
	@Autowired
	private IPicService picService;
	
	
	
	//@Async(value="picencodeExecutor")
	@Override
	public void tif2jpg(String imagePath) {
		///data/huanghe/uploadPath/upload/2a17e46e67e440129f9a27dd4a996791/ystx/
		log.info("==>tif2jpg......{}", imagePath);
		//String localPath = HuangHeConfig.getUploadPath();
				
		File dir = new File(imagePath);
		if(dir.isDirectory()) {			
			File[] files = dir.listFiles();
			int pictotal = files.length;
			int picindex = 0;
			for(File file : files) {
				try {
					picindex++;
					String basename = FilenameUtils.getBaseName(file.getName());
					if(StringUtil.equalsAnyIgnoreCase(basename, "thumbs")) {
						continue;
					}				
					log.info("==>转换进度：{}->{}  file={}", picindex, pictotal,file.getAbsolutePath());
					//String picPath = file.getAbsolutePath();
					if (StringUtil.endsWithAnyIgnoreCase(file.getName(), ".tif", ".tiff", ".png")) {
						String jpgname = FilenameUtils.getBaseName(file.getName());
						String _outPath = FilenameUtils.normalize(imagePath + File.separator + jpgname + "_tif2jpg.jpg", true);
						File jpgf = new File(_outPath);
						if (!jpgf.exists() || jpgf.length() == 0) {								
							// 不存在，需要转换
							try {
								//ImgConvert.tiffTurnJpg(file.getPath(), _outPath);
//								if(jpgf.length() > 10 * 1024 * 1024) {
//			                		TifCompressPythonUtils.tifCompressHandler(jpgf.getAbsolutePath(), _outPath, 0, 0);
//			                	}else {
			                		Tif2JpgPythonUtils.tif2jpgHandler(file.getAbsolutePath(), _outPath, false);
			                	//}
							} catch (Exception e) {
								log.error("error,",e);
							}	
							log.info("==>图片转换成功：{}", _outPath);
						} else {
							log.info("==>{}图片已存在，不需要转换...", jpgf.getName());
						}		
						//picPath = jpgf.getAbsolutePath();
					}else {
						log.info("==>不是tif图片，不需要转换");
					}					
					
				
				}catch(Exception e) {
			    	log.error("error,{}",e, file.getAbsolutePath());
			    }
			}
		}
		
	}
	
}
