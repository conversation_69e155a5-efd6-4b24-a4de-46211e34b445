package cn.guliandigital.storage.log.service.impl;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.storage.log.domain.TTaskLog;
import cn.guliandigital.storage.log.mapper.TTaskLogMapper;
import cn.guliandigital.storage.log.service.ITTaskLogService;

/**
 * 数据解析日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
@Service
public class TTaskLogServiceImpl implements ITTaskLogService
{
    @Autowired
    private TTaskLogMapper tTaskLogMapper;

    /**
     * 查询数据解析日志
     * 
     * @param id 数据解析日志ID
     * @return 数据解析日志
     */
    @Override
    public TTaskLog selectTTaskLogById(String id)
    {
        return tTaskLogMapper.selectTTaskLogById(id);
    }

    /**
     * 查询数据解析日志列表
     * 
     * @param tTaskLog 数据解析日志
     * @return 数据解析日志
     */
    @Override
    public List<TTaskLog> selectTTaskLogList(TTaskLog tTaskLog)
    {
        return tTaskLogMapper.selectTTaskLogList(tTaskLog);
    }

    /**
     * 新增数据解析日志
     * 
     * @param tTaskLog 数据解析日志
     * @return 结果
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    @Override
    public int insertTTaskLog(TTaskLog tTaskLog)
    {
        tTaskLog.setCreateTime(DateUtil.getCuurentDate());
        return tTaskLogMapper.insertTTaskLog(tTaskLog);
    }

    /**
     * 修改数据解析日志
     * 
     * @param tTaskLog 数据解析日志
     * @return 结果
     */
    @Transactional(propagation=Propagation.REQUIRES_NEW)
    @Override
    public int updateTTaskLog(TTaskLog tTaskLog)
    {
        tTaskLog.setUpdateTime(DateUtil.getCuurentDate());
        return tTaskLogMapper.updateTTaskLog(tTaskLog);
    }

    /**
     * 批量删除数据解析日志
     * 
     * @param ids 需要删除的数据解析日志ID
     * @return 结果
     */
    @Override
    public int deleteTTaskLogByIds(String[] ids)
    {
        return tTaskLogMapper.deleteTTaskLogByIds(ids);
    }

    /**
     * 删除数据解析日志信息
     * 
     * @param id 数据解析日志ID
     * @return 结果
     */
    @Override
    public int deleteTTaskLogById(String id)
    {
        return tTaskLogMapper.deleteTTaskLogById(id);
    }
}
