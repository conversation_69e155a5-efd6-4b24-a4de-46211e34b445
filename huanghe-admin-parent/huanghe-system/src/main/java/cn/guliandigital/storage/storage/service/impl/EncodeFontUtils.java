package cn.guliandigital.storage.storage.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import cn.guliandigital.common.config.HuangHeConfig;
import com.alibaba.excel.util.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import cn.guliandigital.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
public class EncodeFontUtils {


	public Map<String,Object> encodeFonts(String content,String bookId,String menuId){
		Map<String,Object> result = Maps.newHashMap();

		String base = FilenameUtils.normalize(HuangHeConfig.getUploadPath() + File.separator + "fonts");

		List<String> Font_files = new ArrayList<String>();
		Font_files.add("FZSONG_ZhongHuaSongPlane00.TTF");
		String outPath = FilenameUtils.normalize(base+File.separator+"menu"+File.separator+bookId);
		File out = new File(outPath);
		if(!out.exists()) {
			out.mkdirs();
		}
		if(Strings.isNullOrEmpty(content)) {
			return result;
		}
		// 是否字体加密  true-加密 false-不加密
		boolean entry = true ;
		Map<String, Object> map = FontUtils.cutFonts(base, Font_files, content, outPath, menuId, entry);
		//生成结果
		String encContent = map.get("content").toString();

		result.put("contentEnc", encContent);
		//获取字体文件链接
		String woffpaths = map.get("destWOFFFilePaths").toString();
		List<String> wofflist = StringUtil.str2List(woffpaths, ";", true, true);
		//格式化连接
		List<String> wpaths = Lists.newArrayList();
		for(String woff : wofflist) {
			String fontpath =FilenameUtils.normalize(woff,true)
					.replace(FilenameUtils.normalize(HuangHeConfig.getProfile(),true),"");

			log.info("==>字体url={}",fontpath);
			wpaths.add(fontpath);
		}
		result.put("fontPaths", wpaths);
		Map<String,String> mappingMap = Maps.newHashMap();
		LinkedHashMap code_map = (LinkedHashMap)map.get("codeMap");
		Iterator iter = code_map.entrySet().iterator();
		while (iter.hasNext()) {
			Map.Entry entry_ = (Map.Entry) iter.next();
			Object key = entry_.getKey();
			Object val = entry_.getValue();
			String hex_old = FontUtils.toHex(Integer.parseInt(key.toString()));
			String hex_enc = FontUtils.toHex(Integer.parseInt(val.toString()));
			String old_content = FontUtils.hex2Str(hex_old);
			String enc_content = FontUtils.hex2Str(hex_enc);
			mappingMap.put(enc_content, old_content);
		}
		result.put("mappingMap", mappingMap);
		deleteFontFile(map);
		return result;
	}
	//删除无用的文件
	public static void deleteFontFile(Map<String, Object> map) {

		//log.info("==>开始删除无用的字体文件.....");
		String ttfpaths = map.get("destTTFFilePaths").toString();
		List<String> ttflist = StringUtil.str2List(ttfpaths, ";", true, true);
		for(String ttf : ttflist) {
			File ttffile = new File(ttf);
			if(ttffile.exists()) {
				FileUtils.delete(ttffile);
			}
		}
		String eotpaths = map.get("destEOTFilePaths").toString();
		List<String> eotlist = StringUtil.str2List(eotpaths, ";", true, true);
		for(String eot : eotlist) {
			File eotfile = new File(eot);
			if(eotfile.exists()) {
				FileUtils.delete(eotfile);
			}
		}
	}
}
