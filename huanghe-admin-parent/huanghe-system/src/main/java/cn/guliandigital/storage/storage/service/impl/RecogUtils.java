package cn.guliandigital.storage.storage.service.impl;

import java.awt.Graphics;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.imageio.ImageIO;

import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;

import cn.guliandigital.common.utils.StringUtil;
import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RecogUtils {
	
	public static StringBuilder convertFullText(String json) throws IOException {
		JSONObject contentObject = JSON.parseObject(json);
		StringBuilder stringBuilder = new StringBuilder("");
		if(contentObject.isEmpty()) {
			return stringBuilder;
		}
		convertFullText(contentObject, stringBuilder);
		return stringBuilder;
	}
	
	private static void convertFullText(JSONObject contentObject, StringBuilder stringBuilder) {
		//JSONObject contentObject = JSON.parseObject(content);
		getDefaultWords(contentObject, "content", stringBuilder);
		getDefaultWords(contentObject, "multiColumn", stringBuilder);
		getDefaultWords(contentObject, "heavenContent", stringBuilder);
		getTableWords(contentObject, stringBuilder);
	}
	
	
	public static void getDefaultWords(JSONObject parseObject, String key, StringBuilder stringBuilder) {
		if (parseObject.containsKey(key)) {
			JSONArray jsonArray = parseObject.getJSONArray(key);
			if(ObjectUtils.isEmpty(jsonArray)) {
				return;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject jsonObject = jsonArray.getJSONObject(i);
				JSONArray wordsArray = jsonObject.getJSONArray("words");
				int k = 0;
				for (int j = 0; j < wordsArray.size(); j++) {
					JSONObject wordObject = wordsArray.getJSONObject(j);
					String word = wordObject.getString("word");
					stringBuilder.append(word);
				}
			}
		}

	}
	
	public static void getTableWords(JSONObject parseObject, StringBuilder stringBuilder) {
		if (parseObject.containsKey("tableContent")) {
			JSONArray jsonArray = parseObject.getJSONArray("tableContent");
			if(ObjectUtils.isEmpty(jsonArray)) {
				return;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject jsonObject = jsonArray.getJSONObject(i);
				JSONArray blocks = jsonObject.getJSONArray("blocks");
				for (int j = 0; j < blocks.size(); j++) {
					JSONObject block = blocks.getJSONObject(j);
					getDefaultWords(block, "charlines", stringBuilder);
				}
			}
		}
		
	}
	
	public static Map<String, Object> convertJSONs(String json, StringBuilder fullText) throws IOException {
		JSONObject parseObject = JSON.parseObject(json);
		JSONObject images = parseObject.getJSONObject("Images");
		return convert(images, fullText);
	}

	/**
	 * 
	 * @param images    ocr接口响应数据
	 * @param imagePath 图片文件路径, 当isInlay = true 时才会使用
	 * @param isInlay   是否嵌入图片(折校)
	 * @return
	 * @throws IOException
	 */
	public static Map<String, Object> convert(JSONObject images, StringBuilder fullText) throws IOException {
		Map<String, Object> result = new HashMap<String, Object>();
		Integer width = images.getInteger("width");
		Integer height = images.getInteger("height");
		JSONObject referenceSignInstances = images.getJSONObject("referenceSignInstances");
		JSONObject top = referenceSignInstances.getJSONObject("天头");
		JSONObject layoutAnalysis = images.getJSONObject("layoutAnalysis");
		long totalWords = 0;
		if (layoutAnalysis.containsKey("天头")) {
			result.put("heavenPoints", layoutAnalysis.get("天头"));
			if (top.getJSONArray("original_position") instanceof JSONArray) {
				JSONArray heavenArray = top.getJSONArray("original_position");
				for (int i = 0; i < heavenArray.size(); i++) {
					JSONObject jsonObject = heavenArray.getJSONObject(i);
					String text = jsonObject.getString("text");
					fullText.append(text);
					totalWords += text.length();
				}
				JSONArray processHeaven = processBody(heavenArray, 0);
				result.put("heavenContent", processHeaven);
			}
		} else {
			result.put("heavenContent", null);
		}

		Object body = referenceSignInstances.get("正文");
		if (body instanceof JSONArray) {
			// 多列
			JSONArray bodyArray = (JSONArray) body;
			List<JSONObject> list = new ArrayList<JSONObject>();
			for (int i = 0; i < bodyArray.size(); i++) {
				JSONObject originalObj = bodyArray.getJSONObject(i);
				JSONArray originalPosition = originalObj.getJSONArray("original_position");
				if (originalPosition != null && originalPosition.size() > 0) {
					for (int j = 0; j < originalPosition.size(); j++) {
						JSONObject jsonObject = originalPosition.getJSONObject(j);
						String text = jsonObject.getString("text");
						fullText.append(text);
						totalWords += text.length();
					}
					JSONArray processBody = processBody(originalPosition, 0);
					list.add(processImageMultiColumn(processBody));
				}
			}
			result.put("multiColumn", list);
		} else {
			// 整页
			JSONObject jsonObject = (JSONObject) body;
			JSONArray originalPosition = (JSONArray) jsonObject.get("original_position");
			if (originalPosition != null && originalPosition.size() > 0) {
				for (int i = 0; i < originalPosition.size(); i++) {
					// 当前索引值
					JSONObject currJSONObject = originalPosition.getJSONObject(i);
					String text = currJSONObject.getString("text");
					fullText.append(text);
					totalWords += text.length();
				}
				JSONArray processBody = processBody(originalPosition, 1);
				result.put("content", processBody);
			} else {
				result.put("content", null);
			}
		}
		Object table = referenceSignInstances.get("表格");
		if (table instanceof JSONArray) {
			JSONArray tableArray = (JSONArray) table;
			for (int i = 0; i < tableArray.size(); i++) {
				JSONObject jsonObject = tableArray.getJSONObject(i);
				JSONArray blocksJsonArray = jsonObject.getJSONArray("blocks");
				for (int k = 0; k < blocksJsonArray.size(); k++) {
					JSONObject blockJsonObject = blocksJsonArray.getJSONObject(k);
					JSONArray originalPositionJSONArray = blockJsonObject.getJSONArray("original_position");
					for (int k1 = 0; k1 < originalPositionJSONArray.size(); k1++) {
						JSONObject originalPositionJsonObject = originalPositionJSONArray.getJSONObject(k1);
						totalWords += originalPositionJsonObject.getString("text").length();
					}
				}
			}
			JSONArray processTable = processTable(tableArray);
			result.put("tableContent", processTable);
		} else {
			// 无
			result.put("tableContent", null);
		}
		result.put("totalWords", totalWords);
		result.put("width", width);
		result.put("height", height);
		return result;
	}

	public static Map<String, String> convert2Map(String resultContent) throws IOException {
		if(StringUtil.isBlank(resultContent)) {
			throw new RuntimeException();
		}
		JSONObject jsonObject = JSON.parseObject(resultContent, Feature.OrderedField);
		Map<String, String> result = new HashMap<String, String>();
		bk0: if (jsonObject.containsKey("heavenContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("heavenContent");
			if (jsonArray == null || jsonArray.isEmpty()) {
				break bk0;
			}
			result.put("heavenContent", jsonArray.toJSONString());
		}
		
		bk1: if (jsonObject.containsKey("multiColumn")) {
			JSONArray jsonArray = jsonObject.getJSONArray("multiColumn");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk1;
			}
			result.put("multiColumn", jsonArray.toJSONString());
		}
		
		bk2: if (jsonObject.containsKey("content")) {
			JSONArray jsonArray = jsonObject.getJSONArray("content");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk2;
			}
			result.put("content", jsonArray.toJSONString());
		}
		
		bk3: if (jsonObject.containsKey("tableContent")) {
			JSONArray jsonArray = jsonObject.getJSONArray("tableContent");
			if (jsonArray == null|| jsonArray.isEmpty()) {
				break bk3;
			}
			result.put("tableContent", jsonArray.toJSONString());
		}
		return result;
	}
	
	public static void removeImg(JSONArray jsonArray) {
		if(jsonArray == null) {
			return;
		}
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject item = (JSONObject) jsonArray.get(i);
			item.remove("img");
		}
	}
	
	public static void removeImgFurther(JSONArray jsonArray, String property) {
		if(jsonArray == null) {
			return;
		}
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject item = (JSONObject) jsonArray.get(i);
			JSONArray blocksArray = item.getJSONArray(property);
			for (int j = 0; j < blocksArray.size(); j++) {
				JSONObject further = blocksArray.getJSONObject(j);
				further.remove("img");
			}
		}
	}

	/**
	 * 多栏数据结果处理
	 * 
	 * @param json
	 * @param picPath
	 * @return
	 * @throws IOException
	 */
	private static JSONObject processImageMultiColumn(JSONArray jsonArray) throws IOException {
		JSONObject resultJSONObject = new JSONObject();
		JSONObject obj = null;
		Integer height = 0;
		Integer width = 0;
		for (int i = 0; i < jsonArray.size(); i++) {
			obj = (JSONObject) jsonArray.get(i);
			width = obj.getInteger("width");
			if (height < obj.getInteger("height")) {
				height = obj.getInteger("height");
			}
		}
		resultJSONObject.put("width", width);
		resultJSONObject.put("height", height);
		resultJSONObject.put("original_height", height);
		resultJSONObject.put("content", jsonArray);
		return resultJSONObject;
	}

	/**
	 * 多栏数据结果处理
	 * 
	 * @param json
	 * @param picPath
	 * @return
	 * @throws IOException
	 */
	private static JSONObject processImageInlayMultiColumn(JSONArray jsonArray, Path imagePath) throws IOException {
		JSONObject resultJSONObject = new JSONObject();
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		Integer height = 0;
		Integer width = 0;
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = (JSONObject) jsonArray.get(i);
			JSONObject points = obj.getJSONObject("points");
			int x = points.getIntValue("left");
			int y = points.getIntValue("top");
			width = obj.getInteger("width");
			height = obj.getInteger("orgin_height");
			if (width + x > w) {
				width = w - x;
			}
			if (height + y > h) {
				height = h - y;
			}
			String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
			obj.put("img", imgStr);
		}
		resultJSONObject.put("width", width);
		resultJSONObject.put("height", height);
		resultJSONObject.put("original_height", height);
		resultJSONObject.put("content", jsonArray);
		return resultJSONObject;
	}

	private static JSONArray processImageInlayTable(JSONArray jsonArray, Path imagePath) throws IOException {
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = jsonArray.getJSONObject(i);
			JSONArray blocksArray = obj.getJSONArray("blocks");
			for (int j = 0; j < blocksArray.size(); j++) {
				JSONObject blocksObj = blocksArray.getJSONObject(j);
				JSONObject points = blocksObj.getJSONObject("points");
				String imgStr = subimage2Base64(bi, true, "1", i, 1, points.getIntValue("left"),
						points.getIntValue("top"), blocksObj.getIntValue("width"),
						blocksObj.getInteger("orgin_height"));
				blocksObj.put("img", imgStr);
			}
		}
		return jsonArray;
	}

	public static JSONArray processImageInlay(JSONArray jsonArray, Path imagePath) throws IOException {
		BufferedImage bi = ImageIO.read(imagePath.toFile());
		int w = bi.getWidth();
		int h = bi.getHeight();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = (JSONObject) jsonArray.get(i);
			JSONObject points = obj.getJSONObject("points");
			int x = points.getIntValue("left");
			int y = points.getIntValue("top");
			int width = obj.getIntValue("orgin_width");
			int height = obj.getInteger("orgin_height");
			if (width + x > w) {
				width = w - x;
			}
			if (height + y > h) {
				height = h - y;
			}
			String imgStr = subimage2Base64(bi, true, "1", i, 1, x, y, width, height);
			obj.put("img", imgStr);
		}
		return jsonArray;
	}

	public static JSONArray processBody(String input, int baseLeftMargin) {
		JSONArray inputArray = JSON.parseArray(input);
		return processBody(inputArray, baseLeftMargin);
	}

	public static JSONArray processBody(JSONArray inputArray, int baseLeftMargin) {
		JSONArray resultArray = new JSONArray();
		Integer width = 0;
		//
		Integer prevRight = null;
		int offset = 0;
		// 倒序处理 从左至右
		for (int i = inputArray.size() - 1; i >= 0; i--) {
			JSONObject column = inputArray.getJSONObject(i);
			/* System.out.println(column); */
			JSONArray charlinePointsJsonArray = column.getJSONArray("points");
			JSONObject columnMap = new JSONObject();
			//
			int lineLeft = charlinePointsJsonArray.getJSONArray(0).getInteger(0);
			if (prevRight != null && lineLeft < prevRight) {
				int diff = lineLeft - prevRight;
				if (diff < baseLeftMargin) {
					offset += baseLeftMargin - diff;
				}
			}
			int lineRight = charlinePointsJsonArray.getJSONArray(1).getInteger(0);
			prevRight = lineRight;
			//
			JSONObject columnPointsMap = new JSONObject();
			columnPointsMap.put("top", charlinePointsJsonArray.getJSONArray(0).getInteger(1));
			columnPointsMap.put("leftOffset", charlinePointsJsonArray.getJSONArray(0).getInteger(0) + offset);
			columnPointsMap.put("left", charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnPointsMap.put("right", charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnPointsMap.put("bottom", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
//			columnPointsMap.put("right", charlinePointsJsonArray.getJSONArray(1).getInteger(0) - charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnMap.put("points", columnPointsMap);
			width = charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0);
			columnMap.put("width", width);
			columnMap.put("orgin_width", charlinePointsJsonArray.getJSONArray(1).getInteger(0)
					- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
			columnMap.put("height", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
			columnMap.put("orgin_height", charlinePointsJsonArray.getJSONArray(2).getInteger(1)
					- charlinePointsJsonArray.getJSONArray(1).getInteger(1));
			columnMap.put("confidence", column.getBigDecimal("confidence"));
			columnMap.put("isTypePage", column.getBoolean("is_type_page"));

			List<JSONObject> wordOutputs = new ArrayList<JSONObject>();
			JSONArray charlinesJsonArray = column.getJSONArray("charlines");
			for (int c = 0; c < charlinesJsonArray.size(); c++) {
				JSONObject chunk = charlinesJsonArray.getJSONObject(c);
				String fontSize = chunk.getString("font_size");
				JSONArray chunkPointsJsonArray = chunk.getJSONArray("points");
				if (StringUtil.equalsAny(fontSize, "large", "media")) {
					JSONArray wordsJSONArray = chunk.getJSONArray("texts");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if (StringUtil.isBlank(wordText)) {
							continue;
						}
						JSONObject wordMap = new JSONObject();
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						JSONObject wordPointsMap = new JSONObject();
						wordPointsMap.put("top", wordPointsJsonArray.getJSONArray(0).getInteger(1)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(1));
						wordPointsMap.put("left", wordPointsJsonArray.getJSONArray(0).getInteger(0)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("points", wordPointsMap);
						wordMap.put("color", chunk.getString("color"));
						wordMap.put("type", chunk.getString("font_size"));
//						JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
//						JSONObject resultTopk = getTopk(topkJsonObject);
//						wordMap.put("confidence", resultTopk.get(wordText));
//						wordMap.put("topk", resultTopk);// 从大到小排序
						wordMap.put("word", wordText);
						wordMap.put("new_word", false);
						wordMap.put("original_word", wordText);
						Entry<Integer, String> font = font(
								wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0),
								wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						// wordMap.put("height", font.getKey());
						if (font.getKey() > width) {
							columnMap.put("width", font.getKey());
						}

						// wordMap.put("width", font.getKey());
						wordMap.put("fontSize", font.getKey());
						wordMap.put("scale", font.getValue());
						wordOutputs.add(wordMap);
					}
				} else if (StringUtil.equals(fontSize, "small")) {
					JSONObject jsonObject = chunk.getJSONObject("texts");
					String fontSizeType = jsonObject.getString("font_size_type");
					String type = "small";
					if (StringUtil.isNotBlank(fontSizeType)) {
						type = fontSizeType + type.substring(0, 1).toUpperCase() + type.substring(1);
					}
					JSONArray wordsJSONArray = jsonObject.getJSONArray("small_print");
					for (int j = 0; j < wordsJSONArray.size(); j++) {
						JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
						String wordText = wordJSONObject.getString("word");
						if (StringUtil.isBlank(wordText)) {
							continue;
						}
						JSONObject wordMap = new JSONObject();
						JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
						JSONObject wordPointsMap = new JSONObject();
						wordPointsMap.put("top", wordPointsJsonArray.getJSONArray(0).getInteger(1)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(1));
						wordPointsMap.put("left", wordPointsJsonArray.getJSONArray(0).getInteger(0)
								- charlinePointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						wordMap.put("points", wordPointsMap);
						wordMap.put("color", chunk.getString("color"));
						wordMap.put("type", type);
//						JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
//						JSONObject resultTopk = getTopk(topkJsonObject);
//						wordMap.put("confidence", resultTopk.get(wordText));
//						wordMap.put("topk", resultTopk);// 从大到小排序
						wordMap.put("word", wordText);
						wordMap.put("new_word", false);
						wordMap.put("original_word", wordText);

						Entry<Integer, String> font = font(
								wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0),
								wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						// wordMap.put("height", font.getKey());
						// wordMap.put("width", font.getKey());
						wordMap.put("height", wordPointsJsonArray.getJSONArray(1).getInteger(0)
								- wordPointsJsonArray.getJSONArray(0).getInteger(0));
						wordMap.put("width", wordPointsJsonArray.getJSONArray(3).getInteger(1)
								- wordPointsJsonArray.getJSONArray(0).getInteger(1));
						if (font.getKey() > width) {
							columnMap.put("width", font.getKey());
						}
						wordMap.put("fontSize", font.getKey());
						wordMap.put("scale", font.getValue());
						wordOutputs.add(wordMap);
					}
				}
			}
			columnMap.put("words", wordOutputs);
			resultArray.add(columnMap);
		}
		// 将倒叙结果归正 从右至左
		Collections.reverse(resultArray);
		return resultArray;
	}

	public static JSONArray processTable(JSONArray inputArray) {
		JSONArray list = new JSONArray();
		List<Map<String, Object>> wordOutputs = null;
		List<Map<String, Object>> charlinesList = null;
		// 倒序处理 从左至右
		for (int i = 0; i < inputArray.size(); i++) {
			Map<String, Object> columnMap = new HashMap<String, Object>();
			JSONObject columnJSONObject = inputArray.getJSONObject(i);
			BigDecimal columnYmin = columnJSONObject.getBigDecimal("ymin");
			BigDecimal columnXmin = columnJSONObject.getBigDecimal("xmin");
			BigDecimal columnYmax = columnJSONObject.getBigDecimal("ymax");
			BigDecimal columnXmax = columnJSONObject.getBigDecimal("xmax");
			Map<String, Object> columnPointsMap = new HashMap<String, Object>();
			columnPointsMap.put("top", columnYmin);
			columnPointsMap.put("left", columnXmin);
			columnPointsMap.put("right", columnXmax.subtract(columnXmin));
			columnPointsMap.put("bottom", columnYmax.subtract(columnYmin));
			columnMap.put("points", columnPointsMap);
			columnMap.put("width", columnXmax.subtract(columnXmin));
			columnMap.put("orgin_width", columnXmax.subtract(columnXmin));
			columnMap.put("height", columnYmax.subtract(columnYmin));
			columnMap.put("orgin_height", columnYmax.subtract(columnYmin));
			List<Map<String, Object>> columnList = new ArrayList<Map<String, Object>>();
			columnMap.put("blocks", columnList);
			JSONArray blocksJsonArray = columnJSONObject.getJSONArray("blocks");
			for (int b = blocksJsonArray.size() - 1; b >= 0 ; b--) {
				JSONObject blockJSONObject = blocksJsonArray.getJSONObject(b);
				BigDecimal blockYmin = blockJSONObject.getBigDecimal("ymin");
				BigDecimal blockXmin = blockJSONObject.getBigDecimal("xmin");
				BigDecimal blockYmax = blockJSONObject.getBigDecimal("ymax");
				BigDecimal blockXmax = blockJSONObject.getBigDecimal("xmax");
				Map<String, Object> blockMap = new HashMap<String, Object>();
				Map<String, Object> blockPointsMap = new HashMap<String, Object>();
				blockPointsMap.put("top", blockYmin);
				blockPointsMap.put("left", blockXmin);
				blockPointsMap.put("right", blockXmax.subtract(blockXmin));
				blockPointsMap.put("bottom", blockYmax.subtract(blockYmin));
				blockMap.put("points", blockPointsMap);
				blockMap.put("width", blockXmax.subtract(blockXmin));
				blockMap.put("height", blockYmax.subtract(blockYmin));
				blockMap.put("orgin_height", blockYmax.subtract(blockYmin));
				blockMap.put("confidence", blockJSONObject.getBigDecimal("confidence"));
				blockMap.put("isTypePage", blockJSONObject.getBoolean("is_type_page"));
				blockMap.put("type", blockJSONObject.get("type"));// rowspan
				if (blockJSONObject.getInteger("rowspan") > 0) {
					blockMap.put("rowspan", blockJSONObject.getInteger("rowspan"));
				} else {
					blockMap.put("rowspan", null);
				}
				blockMap.put("area_number", blockJSONObject.get("area_number"));
				JSONArray orginsJsonArray = blockJSONObject.getJSONArray("original_position");
				charlinesList = new ArrayList<Map<String, Object>>();
				for (int o = 0; o < orginsJsonArray.size(); o++) {
					JSONObject orgin = orginsJsonArray.getJSONObject(o);
					JSONArray charlinesJsonArray = orgin.getJSONArray("charlines");
					Map<String, Object> charlinesMap = new HashMap<String, Object>();

					wordOutputs = new ArrayList<Map<String, Object>>();
					charlinesMap.put("words", wordOutputs);

					for (int c = 0; c < charlinesJsonArray.size(); c++) {
						JSONObject chunk = charlinesJsonArray.getJSONObject(c);
						String fontSize = chunk.getString("font_size");
						JSONArray chunkPointsJsonArray = chunk.getJSONArray("points");
						charlinesMap.put("width", chunk.get("width"));
						charlinesMap.put("height", chunk.get("height"));
						charlinesMap.put("top", chunk.get("top"));
						charlinesMap.put("left", chunk.get("left"));
						if (StringUtil.equalsAny(fontSize, "large", "media")) {
							JSONArray wordsJSONArray = chunk.getJSONArray("texts");
							for (int j = 0; j < wordsJSONArray.size(); j++) {
								JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
								String wordText = wordJSONObject.getString("word");
								if (StringUtil.isBlank(wordText)) {
									continue;
								}
								Map<String, Object> wordMap = new HashMap<String, Object>();
								JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
								Map<String, Object> wordPointsMap = new HashMap<String, Object>();
								wordPointsMap.put("top",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(1).subtract(blockYmin));
								wordPointsMap.put("left",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(0).subtract(blockXmin));
								wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("points", wordPointsMap);
								wordMap.put("color", chunk.getString("color"));
								wordMap.put("type", chunk.getString("font_size"));
//								JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
//								JSONObject resultTopk = getTopk(topkJsonObject);
//								wordMap.put("confidence", resultTopk.get(wordText));
//								wordMap.put("topk", resultTopk);// 从大到小排序
								wordMap.put("word", wordText);
								wordMap.put("new_word", false);
								wordMap.put("original_word", wordText);
								Entry<Integer, String> font = font(
										wordPointsJsonArray.getJSONArray(1).getInteger(0)
												- wordPointsJsonArray.getJSONArray(0).getInteger(0),
										wordPointsJsonArray.getJSONArray(3).getInteger(1)
												- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordMap.put("fontSize", font.getKey());
								wordMap.put("scale", font.getValue());
								wordOutputs.add(wordMap);
							}
						} else if (StringUtil.equals(fontSize, "small")) {
							JSONObject jsonObject = chunk.getJSONObject("texts");
							String fontSizeType = jsonObject.getString("font_size_type");
							String type = "small";
							if (StringUtil.isNotBlank(fontSizeType)) {
								type = fontSizeType + type.substring(0, 1).toUpperCase() + type.substring(1);
							}
							JSONArray wordsJSONArray = jsonObject.getJSONArray("small_print");
							for (int j = 0; j < wordsJSONArray.size(); j++) {
								JSONObject wordJSONObject = wordsJSONArray.getJSONObject(j);
								String wordText = wordJSONObject.getString("word");
								if (StringUtil.isBlank(wordText)) {
									continue;
								}
								Map<String, Object> wordMap = new HashMap<String, Object>();
								JSONArray wordPointsJsonArray = wordJSONObject.getJSONArray("char_bbox");
								Map<String, Object> wordPointsMap = new HashMap<String, Object>();
								wordPointsMap.put("top",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(1).subtract(blockYmin));
								wordPointsMap.put("left",
										wordPointsJsonArray.getJSONArray(0).getBigDecimal(0).subtract(blockXmin));
								wordPointsMap.put("right", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordPointsMap.put("bottom", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("points", wordPointsMap);
								wordMap.put("color", chunk.getString("color"));
								wordMap.put("type", type);
//								JSONObject topkJsonObject = wordJSONObject.getJSONObject("topk");
//								JSONObject resultTopk = getTopk(topkJsonObject);
//								wordMap.put("confidence", resultTopk.get(wordText));
//								wordMap.put("topk", resultTopk);// 从大到小排序
								wordMap.put("word", wordText);
								wordMap.put("new_word", false);
								wordMap.put("original_word", wordText);
								Entry<Integer, String> font = font(
										wordPointsJsonArray.getJSONArray(1).getInteger(0)
												- wordPointsJsonArray.getJSONArray(0).getInteger(0),
										wordPointsJsonArray.getJSONArray(3).getInteger(1)
												- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("height", wordPointsJsonArray.getJSONArray(3).getInteger(1)
										- wordPointsJsonArray.getJSONArray(0).getInteger(1));
								wordMap.put("width", wordPointsJsonArray.getJSONArray(1).getInteger(0)
										- wordPointsJsonArray.getJSONArray(0).getInteger(0));
								wordMap.put("fontSize", font.getKey());
								wordMap.put("scale", font.getValue());
								wordOutputs.add(wordMap);
							}
						}
					}
					charlinesList.add(charlinesMap);

				}
				blockMap.put("charlines", charlinesList);
				columnList.add(blockMap);
			}
			list.add(columnMap);
		}

		return list;
	}

	private static Entry<Integer, String> font(int w, int h) {
		Integer key = w;
		String value = "(1, 1)";
		if (w < h) {
			key = h;
			double floor = Math.floor((Double.valueOf(w) / Double.valueOf(h)) * 10) / 10;
			if (floor < 0.8) {
				floor = 0.8;
			}
			value = "(" + floor + ", 1)";
		} else if (w > h) {
			key = w;
			double floor = Math.floor((Double.valueOf(h) / Double.valueOf(w)) * 10) / 10;
			value = "(1, " + floor + ")";
		}
		return new AbstractMap.SimpleEntry<>(key, value);
	}

	final static BigDecimal HUNDRED = new BigDecimal(100);

	public static JSONObject getTopk(JSONObject topkJsonObject) {
		Map<String, BigDecimal> topk = new LinkedHashMap<String, BigDecimal>();
		for (Entry<String, Object> entry : topkJsonObject.entrySet()) {
			topk.put(entry.getKey(), (BigDecimal) entry.getValue());
		}
		List<Entry<String, BigDecimal>> topkList = new ArrayList<>(topk.entrySet());
		topkList.sort(Entry.comparingByValue());
		Collections.reverse(topkList);
		JSONObject resultTopk = new JSONObject(true);
		for (Entry<String, BigDecimal> entry : topkList) {
			resultTopk.put(entry.getKey(), entry.getValue().multiply(HUNDRED));
		}
		return resultTopk;
	}

	public static String subimage2Base64(BufferedImage bi, boolean cutType, String imgKey, int each, Integer cutNum,
			int x, int y, final int imgWidth, final Integer imgHeight) throws IOException {
		ByteArrayOutputStream byOut = new ByteArrayOutputStream();
		try {
			if (x < 0) {
				x = 0;
			}
			if (y < 0) {
				y = 0;
			}
			Integer destHeight = imgHeight;
			Integer destWidth = imgWidth;

			if (cutType) {
				// 纵向切图
				destWidth = destWidth / cutNum;
			} else {
				// 横向切图
				destHeight = destHeight / cutNum;
			}
			// 用来处理最后一次的图片精度问题
			Integer w = destWidth;
			Integer h = destHeight;
			// 用来处理最后一次的图片精度问题
			for (Integer i = 0; i < cutNum; i++) {
				Image image;
				if (cutType) {
					// 纵向切割。
					if (cutNum - 1 == i) {
						w = destWidth;
						destWidth = imgWidth - i * destWidth;
					}
					image = bi.getSubimage(x + w * i, y, destWidth, destHeight);
				} else {
					if (cutNum - 1 == i) {
						h = destHeight;
						destHeight = imgHeight - i * destHeight;
					}
					image = bi.getSubimage(x, y + h * i, destWidth, destHeight);
				}
//              System.out.println("destWidth = " + destWidth + "，destHeight = " + destHeight);
				BufferedImage tag = new BufferedImage(destWidth, destHeight, BufferedImage.TYPE_INT_RGB);
				Graphics g = tag.getGraphics();
				g.drawImage(image, 0, 0, destWidth, destHeight, null);
				g.dispose();
				ImageIO.write(tag, "JPEG", byOut);
				// 将byte数组保存成本地文件或者上传到图床均可
				byte[] imgBytes = byOut.toByteArray();
				return Base64.encode(imgBytes);
			}

			return null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			byOut.close();
		}

	}
	
	public static boolean verifyMap(Map<String, String> map) {
		boolean flag = false;
		String headContent = map.get("heavenContent");
		if(headContent != null) {
			flag = true;
		}
		String multiContent = map.get("multiColumn");
		if(multiContent != null) {
			flag = true;
		}
		String bodyContent = map.get("content");
		if(bodyContent != null) {
			flag = true;
		}
		String tableContent = map.get("tableContent");
		if(tableContent != null) {
			flag = true;
		}
		return flag;
	}
}
