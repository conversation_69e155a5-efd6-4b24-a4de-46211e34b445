package cn.guliandigital.storage.storage.service.impl;

import java.io.File;
import java.io.FilenameFilter;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 解析xml工具类
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class XmlToolStyleRestoreV3Utils {

	/**
	 * 根据目录获取所有相关文件
	 * 
	 * @param rootPath
	 * @param dirPath
	 * @param fileMap
	 */
	public static void getAllFileByStyleRestore(String rootPath, String dirPath, Map<String, String> fileMap) {

		File dir = new File(dirPath);
		if (dir.isDirectory()) {
			File[] files = dir.listFiles(new FilenameFilter() {
				@Override
				public boolean accept(File dir, String name) {
					String fileName = name.toLowerCase();
					File path = new File(dir + File.separator + name);
					// log.info(path.getAbsolutePath());
					if (path.isDirectory()) {
						return true;
					}

					if (StringUtil.endsWithAnyIgnoreCase(fileName, ".jpg", ".png", ".tif", ".tiff", ".json", ".xml",
							".xpro")) {
						log.info("==>匹配到文件：{}", fileName);
						return true;
					}
					return false;
				}
			});
			for (File ff : files) {
				if (ff.isDirectory()) {
					getAllFileByStyleRestore(rootPath, ff.getAbsolutePath(), fileMap);
				} else {
					String key = StringUtil.replace(ff.getAbsolutePath(), rootPath, "");
					key = FilenameUtils.normalize(key, true);
					key = StringUtil.removeStart(key, "/");
					fileMap.put(key, ff.getAbsolutePath());
				}
			}
		}
	}

	/**
	 * 解析工程文件
	 * 
	 * @param fileMap
	 * @throws Exception
	 */
	public static TProBooks parseMainXpro4Meta(Map<String, String> fileMap) throws Exception {
		TProBooks bean = new TProBooks();
		try {
			String xproPath = fileMap.get("main.xpro");
			File xmlfile = new File(xproPath);
			if (!xmlfile.exists()) {
				throw new Exception("main.xpro工程文件不存在");
			}
			SAXReader reader = new SAXReader();
			Document document = reader.read(xmlfile);
			Element root = document.getRootElement();// 获取根元素
			List<Element> childElements = root.elements();//

			Element bookE = root.element("书籍");
			if (bookE == null) {
				throw new Exception("<书籍>不存在");
			}
			Element bookInfoE = bookE.element("书籍信息");
			if (bookInfoE == null) {
				throw new Exception("<书籍信息>不存在");
			}
			Element bookNameE = element(bookInfoE, "题名", "題名");
			if (bookNameE == null) {
				throw new Exception("<题名>不存在");
			}
			String bookName = bookNameE.getText();
			Element coverPathE = bookInfoE.element("缩微图");
			if (coverPathE == null) {
				throw new Exception("<缩微图>不存在");
			}
			String coverPath = coverPathE.getText();
			// <其他信息>
			Element otherinfo = bookInfoE.element("其他信息");
			if (otherinfo != null) {
				String resourceType = otherinfo.element("资源类型") != null ? otherinfo.element("资源类型").getText() : "";
				String siClassification = otherinfo.element("四部分类法") != null ? otherinfo.element("四部分类法").getText()
						: "";
				String ztfenlei = otherinfo.element("中图分类法") != null ? otherinfo.element("中图分类法").getText() : "";
				// String descriptionNo = otherinfo.element("著录号").getText();
				String uniqueId = otherinfo.element("唯一标识符") != null ? otherinfo.element("唯一标识符").getText() : "";
				// String isbn = otherinfo.element("标准编号").getText();
				// String language = otherinfo.element("语种").getText();
				String congbian = otherinfo.element("丛编")!=null ? otherinfo.element("丛编").getText():"";
				// String dbName = otherinfo.element("丛编") != null ? otherinfo.element("丛编").getText() : "";
				String summary = otherinfo.element("提要") != null ? otherinfo.element("提要").getText() : "";
				String banci = otherinfo.element("版次") != null ? otherinfo.element("版次").getText() : "";
				// String yinci = otherinfo.element("印次").getText();
				// String kaiben = otherinfo.element("开本信息").getText();
				// String zhuangding = otherinfo.element("装订形式").getText();
				String banben = otherinfo.element("版本").getText();
				String price = otherinfo.element("定价") != null ? otherinfo.element("定价").getText() : "";
				// 其他分类 -方志編/縣志
				String otherclass = otherinfo.element("其他分类") != null ? otherinfo.element("其他分类").getText() : "";
				// 转简体

				if (!Strings.isNullOrEmpty(otherclass)) {
					String otherclassS = ZhConverterUtil.convertToSimple(otherclass);
					log.info("==>转简体：{} TO {}", otherclass, otherclassS);
					otherclass = otherclassS;
					List<String> oclist = StringUtil.str2List(otherclass, "/", true, true);
					// TODO 丛编需要查询数据库
					bean.setDbId("");
					String _dbName = oclist.get(0);
					if (StringUtil.contains(_dbName, "类")) {
						_dbName = StringUtil.replace(_dbName, "类", "编");
					}
					bean.setDbName(_dbName);

					if (otherclass.contains("方志")) {
						otherclass = "甲编." + otherclass;
					} else if (otherclass.contains("史料")) {
						otherclass = "乙编." + otherclass;
					} else if (otherclass.contains("档案")) {
						otherclass = "丙编." + otherclass;
					} else if (otherclass.contains("文献 ")) {
						otherclass = "丁编." + otherclass;
					}

					otherclass = StringUtil.replace(otherclass, "編", "类");
				}
				bean.setConglomeration(congbian);

				bean.setSiClassification(siClassification);
				//  查询四部分类ID
				bean.setSiClassificationId("-");

				bean.setResourceType(resourceType);

				bean.setResourceClasses(otherclass);

				// bean.setPublishYear(publishYear);
				// bean.setClassification(classification);//其他分类
				bean.setBookDesc(summary);
				bean.setRevision(banben);

				// 价格 1800.00元 1000.00元（合刊）
				// price = StringUtil.remove(price, "元");
				bean.setPrice(price);
				bean.setClassiMethodCode(ztfenlei);
				bean.setUniqueId(uniqueId);
			}
			Element zereninfo = bookInfoE.element("责任信息");
			if (zereninfo != null) {
				String mainResponsibility = zereninfo.element("主要责任者") != null ? zereninfo.element("主要责任者").getText()
						: "";
				bean.setMainResponsibility(mainResponsibility);
			}

			Element publishinfo = bookInfoE.element("出版信息");
			if (publishinfo != null) {
				String publishDate = publishinfo.element("出版日期") != null ? publishinfo.element("出版日期").getText() : "";
				String publisher = publishinfo.element("出版者") != null ? publishinfo.element("出版者").getText() : "";
				String publishland = publishinfo.element("出版地") != null ? publishinfo.element("出版地").getText() : "";
				// String printDate = publishinfo.element("印刷日期").getText();
				bean.setPublishDate(publishDate);
				bean.setPublisher(publisher);
				bean.setPublishland(publishland);
			}
			// log.info("==>resourceType:"+resourceType+",descriptionNo="+descriptionNo);
			bean.setId(IdUtil.simpleUUID());

			bean.setBookName(bookName);

			bean.setProStatus(0);

			bean.setDelFlag(0);

			bean.setDisplay(1);

			List<String> xmllist = Lists.newArrayList();
			Element files = bookE.element("文件目录");
			List<Element> fs = files.elements();
			for (Element fe : fs) {
				String xmlpath = fe.element("文件").attributeValue("路径");
				// log.info(xmlpath);
				xmllist.add(FilenameUtils.normalize(xmlpath, true));
			}
			bean.setXmlFileList(xmllist);

		} catch (Exception e) {
			log.error("error,", e);
			throw e;
		}

		return bean;
	}

	
	public static Element element(Element e, String... ename) {
		for (String name : ename) {
			Element element = e.element(name);
			if (element != null) {
				return element;
			}
		}
		return null;
	}
}
