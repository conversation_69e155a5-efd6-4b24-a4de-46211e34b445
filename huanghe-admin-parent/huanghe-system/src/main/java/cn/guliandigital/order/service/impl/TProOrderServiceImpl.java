package cn.guliandigital.order.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.common.enums.OrderStatus;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.order.domain.TProOrder;
import cn.guliandigital.order.mapper.TProOrderMapper;
import cn.guliandigital.order.service.ITProOrderService;

/**
 * 数据库订单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-09-17
 */
@Service
public class TProOrderServiceImpl implements ITProOrderService {
    @Autowired
    private TProOrderMapper tProOrderMapper;

    /**
     * 查询数据库订单管理
     *
     * @param id 数据库订单管理ID
     * @return 数据库订单管理
     */
    @Override
    public TProOrder selectTProOrderById(String id) {

        return tProOrderMapper.selectTProOrderById(id);
    }

    /**
     * 查询数据库订单管理列表
     *
     * @param tProOrder 数据库订单管理
     * @return 数据库订单管理
     */
    @Override
    public List<TProOrder> selectTProOrderList(TProOrder tProOrder) {
        if (StringUtil.isNotEmpty(tProOrder.getSearchTime())){
            tProOrder.setStartTime(tProOrder.getSearchTime()[0]);
            tProOrder.setEndTime(tProOrder.getSearchTime()[1]);
        }
        List<TProOrder> orderList = tProOrderMapper.selectTProOrderList(tProOrder);
        return orderList;
    }

    /**
     * 新增数据库订单管理
     *
     * @param tProOrder 数据库订单管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTProOrder(TProOrder tProOrder) {

     /*   String nowtime = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String nowtime1 = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String orderCode = nowtime + 1;
        TProOrder tProOrder1 = tProOrderMapper.selectMax(nowtime1);
        if (null != tProOrder1) {
            String dateTimeInfo = tProOrder1.getOrderCode().substring(0, 8);
            if (nowtime.equals(dateTimeInfo)) {
                String code = tProOrder1.getOrderCode().substring(8);
                orderCode = nowtime + String.format("%3d", Integer.parseInt(code)+1).trim();
            }
        }*/

        tProOrder.setOrgId(tProOrder.getOrgId());
//        tProOrder.setOrderStatus(0);
        tProOrder.setId(IdUtils.simpleUUID());

        tProOrder.setOrderCode(generateUniqueKey());
        tProOrder.setCreateTime(DateUtil.getCuurentDate());
        tProOrder.setCreatebyName(SecurityUtils.getUsername());
        tProOrder.setDelFlag(0);

        return tProOrderMapper.insertTProOrder(tProOrder);


    }
    public static synchronized String generateUniqueKey(){
        Random random = new Random();
        // 随机数的量 自由定制，这是9位随机数
        Integer r = random.nextInt(9000) + 1000;

        // 返回  13位时间
//        Long timeMillis = System.currentTimeMillis();

        // 返回  17位时间
//        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
//        String timeStr = sdf.format(new Date());
        DateFormat sdf = new SimpleDateFormat("yyMMddHH");
        String timeStr = sdf.format(new Date());

        // 13位毫秒+9位随机数
        ///return  timeMillis + String.valueOf(r);
        // 8位时间+3位随机数
        return  timeStr + r;
    }

    /**
     * 修改数据库订单管理
     *
     * @param tProOrder 数据库订单管理
     * @return 结果
     */
    @Override
    public int updateTProOrder(TProOrder tProOrder) {

//        Date date = DateUtils.dateTime("yyy-MM-dd HH:mm:ss", tProOrder.getAuthEndTime());
//        SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        String end = format.format(tProOrder.getAuthEndTime());
//        String start = format.format(tProOrder.getAuthStartTime());
//        tProOrder.setAuthEndTime(end);
//        tProOrder.setAuthStartTime(start);

        tProOrder.setUpdatebyName(SecurityUtils.getUsername());
        return tProOrderMapper.updateTProOrder(tProOrder);
    }

    /**
     * 批量删除数据库订单管理
     *
     * @param ids 需要删除的数据库订单管理ID
     * @return 结果
     */
    @Override
    public int deleteTProOrderByIds(String[] ids) {
        for (String id : ids) {
            TProOrder tProOrder = tProOrderMapper.selectTProOrderById(id);
            if(tProOrder.getOrderStatus().equals(OrderStatus.DONE.getCode())){
                throw new ServiceException("该机构正在授权中，无法删除！");
            }

        }
        return tProOrderMapper.deleteTProOrderByIds(ids);
    }

    /**
     * 删除数据库订单管理信息
     *
     * @param id 数据库订单管理ID
     * @return 结果
     */
    @Override
    public int deleteTProOrderById(String id) {
        return tProOrderMapper.deleteTProOrderById(id);
    }

    @Override
    public List<TProOrder> selectALl() {
        return tProOrderMapper.selectALl();
    }

    @Override
    public int updateStaus(String id) {
        return tProOrderMapper.updateStaus(id);
    }

    @Override
    public int updateStaus1(String id) {
        return tProOrderMapper.updateStaus1(id);
    }


}
