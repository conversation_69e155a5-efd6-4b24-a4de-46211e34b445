package cn.guliandigital.order.service;

import cn.guliandigital.order.domain.TProOrder;

import java.util.List;


/**
 * 数据库订单管理Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-17
 */
public interface ITProOrderService 
{
    /**
     * 查询数据库订单管理
     * 
     * @param id 数据库订单管理ID
     * @return 数据库订单管理
     */
        TProOrder selectTProOrderById(String id);

    /**
     * 查询数据库订单管理列表
     * 
     * @param tProOrder 数据库订单管理
     * @return 数据库订单管理集合
     */
    List<TProOrder> selectTProOrderList(TProOrder tProOrder);

    /**
     * 新增数据库订单管理
     * 
     * @param tProOrder 数据库订单管理
     * @return 结果
     */
    int insertTProOrder(TProOrder tProOrder);


    /**
     * 修改数据库订单管理
     * 
     * @param tProOrder 数据库订单管理
     * @return 结果
     */
    int updateTProOrder(TProOrder tProOrder);

    /**
     * 批量删除数据库订单管理
     * 
     * @param ids 需要删除的数据库订单管理ID
     * @return 结果
     */
    int deleteTProOrderByIds(String[] ids);

    /**
     * 删除数据库订单管理信息
     * 
     * @param id 数据库订单管理ID
     * @return 结果
     */
    int deleteTProOrderById(String id);


    List<TProOrder >selectALl();



    int  updateStaus(String id);


    int updateStaus1(String id);
}
