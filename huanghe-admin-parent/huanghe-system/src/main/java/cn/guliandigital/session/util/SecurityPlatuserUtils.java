package cn.guliandigital.session.util;

import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.base.Strings;
import cn.guliandigital.common.constant.HttpStatus;
import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.plat.service.ITPlatUserService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 安全服务工具类  前台用户登录辅助类
 * 
 * <AUTHOR>
 */

@Slf4j
@Component
public class SecurityPlatuserUtils
{

	@Autowired
    private RedisCache redisCache;
	
	@Autowired
    private ITPlatUserService tPlatUserService;

    /**
     * 获取用户信息
     **/
    public TPlatUser getUser(HttpServletRequest request){
    	TPlatUser user = null;
        try{
        	//从header中取出userToken
        	String token = request.getHeader("userToken");
            if(StrUtil.isBlank(token)){
            	token = request.getParameter("userToken");
            }
            if(Strings.isNullOrEmpty(token)) {
            	log.error("token is null ",token);
            	throw new CustomException("获取用户信息异常", HttpStatus.FORBIDDEN);
            }
             
        	Object userObj = redisCache.getCacheObject(RedisConstants.LOGIN_USER_TOKEN + token);
        	if(Objects.isNull(userObj)) {
        		log.error("userObj is null ",token);
        		throw new CustomException("获取用户信息异常", HttpStatus.FORBIDDEN);
        	}
        	
        	user = (TPlatUser)userObj;
        	
        	user = tPlatUserService.selectTPlatUserById(user.getId());
        	
        	//log.info("==>查询的用户数据是：{}",user.toString());
        	
        	
        }catch (CustomException e) {        	
            throw e;
        }catch (Exception e) {        	
            throw new CustomException("获取用户信息异常", HttpStatus.FORBIDDEN);
        }
        
        return user;
    }

    
}
