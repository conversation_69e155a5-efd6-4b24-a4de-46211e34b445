package cn.guliandigital.nameAssociation.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.guliandigital.nameAssociation.mapper.TProNameAssociationMapper;
import cn.guliandigital.nameAssociation.domain.TProNameAssociation;
import cn.guliandigital.nameAssociation.service.ITProNameAssociationService;

/**
 * 任务关联管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-12-06
 */
@Service
public class TProNameAssociationServiceImpl implements ITProNameAssociationService 
{
    @Autowired
    private TProNameAssociationMapper tProNameAssociationMapper;

    /**
     * 查询任务关联管理
     * 
     * @param id 任务关联管理ID
     * @return 任务关联管理
     */
    @Override
    public TProNameAssociation selectTProNameAssociationById(String id)
    {
        return tProNameAssociationMapper.selectTProNameAssociationById(id);
    }

    /**
     * 查询任务关联管理列表
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 任务关联管理
     */
    @Override
    public List<TProNameAssociation> selectTProNameAssociationList(TProNameAssociation tProNameAssociation)
    {
        return tProNameAssociationMapper.selectTProNameAssociationList(tProNameAssociation);
    }

    /**
     * 新增任务关联管理
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 结果
     */
    @Override
    public int insertTProNameAssociation(TProNameAssociation tProNameAssociation)
    {
        return tProNameAssociationMapper.insertTProNameAssociation(tProNameAssociation);
    }

    /**
     * 修改任务关联管理
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 结果
     */
    @Override
    public int updateTProNameAssociation(TProNameAssociation tProNameAssociation)
    {
        return tProNameAssociationMapper.updateTProNameAssociation(tProNameAssociation);
    }

    /**
     * 批量删除任务关联管理
     * 
     * @param ids 需要删除的任务关联管理ID
     * @return 结果
     */
    @Override
    public int deleteTProNameAssociationByIds(String[] ids)
    {
        return tProNameAssociationMapper.deleteTProNameAssociationByIds(ids);
    }

    /**
     * 删除任务关联管理信息
     * 
     * @param id 任务关联管理ID
     * @return 结果
     */
    @Override
    public int deleteTProNameAssociationById(String id)
    {
        return tProNameAssociationMapper.deleteTProNameAssociationById(id);
    }

	@Override
	public List<TProNameAssociation> selectGroupList(TProNameAssociation tProNameAssociation) {
		return tProNameAssociationMapper.selectGroupList(tProNameAssociation);
	}

	@Override
	public List<TProNameAssociation> selectNamesList(List<Integer> ids) {		
		return tProNameAssociationMapper.selectNamesList(ids);
	}
}
