package cn.guliandigital.nameAssociation.domain;

import java.io.Serializable;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 任务关联管理对象 t_pro_name_association
 * 
 * <AUTHOR>
 * @date 2020-12-06
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TProNameAssociation implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 人名 */
    @Excel(name = "人名")
    private String pname;

    /** 关联组ID */
    @Excel(name = "关联组ID")
    private Integer groupId;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setPname(String pname) 
    {
        this.pname = pname;
    }

    public String getPname() 
    {
        return pname;
    }
    public void setGroupId(Integer groupId) 
    {
        this.groupId = groupId;
    }

    public Integer getGroupId() 
    {
        return groupId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pname", getPname())
            .append("groupId", getGroupId())
            .toString();
    }
}
