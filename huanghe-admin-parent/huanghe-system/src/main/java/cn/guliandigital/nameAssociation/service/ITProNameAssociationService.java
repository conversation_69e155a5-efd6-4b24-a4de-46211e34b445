package cn.guliandigital.nameAssociation.service;

import java.util.List;
import cn.guliandigital.nameAssociation.domain.TProNameAssociation;

/**
 * 任务关联管理Service接口
 * 
 * <AUTHOR>
 * @date 2020-12-06
 */
public interface ITProNameAssociationService 
{
    /**
     * 查询任务关联管理
     * 
     * @param id 任务关联管理ID
     * @return 任务关联管理
     */
    TProNameAssociation selectTProNameAssociationById(String id);

    /**
     * 查询任务关联管理列表
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 任务关联管理集合
     */
    List<TProNameAssociation> selectTProNameAssociationList(TProNameAssociation tProNameAssociation);

    /**
     * 新增任务关联管理
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 结果
     */
    int insertTProNameAssociation(TProNameAssociation tProNameAssociation);

    /**
     * 修改任务关联管理
     * 
     * @param tProNameAssociation 任务关联管理
     * @return 结果
     */
    int updateTProNameAssociation(TProNameAssociation tProNameAssociation);

    /**
     * 批量删除任务关联管理
     * 
     * @param ids 需要删除的任务关联管理ID
     * @return 结果
     */
    int deleteTProNameAssociationByIds(String[] ids);

    /**
     * 删除任务关联管理信息
     * 
     * @param id 任务关联管理ID
     * @return 结果
     */
    int deleteTProNameAssociationById(String id);
    
    List<TProNameAssociation> selectGroupList(TProNameAssociation tProNameAssociation);

    List<TProNameAssociation> selectNamesList(List<Integer> ids);
}
