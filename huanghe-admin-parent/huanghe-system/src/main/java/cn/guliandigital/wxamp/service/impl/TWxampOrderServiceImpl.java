package cn.guliandigital.wxamp.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.guliandigital.common.enums.WxampOrderValidStatus;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.utils.service.IRedisService;
import cn.guliandigital.wxamp.domain.TWxampOrder;
import cn.guliandigital.wxamp.domain.TWxampOrderItem;
import cn.guliandigital.wxamp.domain.TWxampRight;
import cn.guliandigital.wxamp.domain.vo.WxampCartItemVo;
import cn.guliandigital.wxamp.mapper.TWxampOrderMapper;
import cn.guliandigital.wxamp.service.ITWxampOrderItemService;
import cn.guliandigital.wxamp.service.ITWxampOrderService;
import cn.guliandigital.wxamp.service.ITWxampRightService;
import cn.guliandigital.wxamp.service.IWxampService;

/**
 * 订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
@Service
public class TWxampOrderServiceImpl implements ITWxampOrderService 
{
    @Autowired
    private TWxampOrderMapper tWxampOrderMapper;
    
    @Autowired
	private IRedisService redisService;
    
    @Autowired
	private ITWxampOrderItemService wxampOrderItemService;
    
    @Autowired
	private ITWxampRightService wxampRightService;
    
    @Autowired
	private IWxampService wxAmpService;

    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public TWxampOrder selectTWxampOrderById(String id)
    {
        return tWxampOrderMapper.selectTWxampOrderById(id);
    }

    /**
     * 查询订单列表
     * 
     * @param tWxampOrder 订单
     * @return 订单
     */
    @Override
    public List<TWxampOrder> selectTWxampOrderList(TWxampOrder tWxampOrder)
    {
        return tWxampOrderMapper.selectTWxampOrderList(tWxampOrder);
    }
    
    /**
     * 新增订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    @Override
    public int insertTWxampOrder(TWxampOrder tWxampOrder)
    {
        tWxampOrder.setCreateTime(DateUtil.getCuurentDate());
        return tWxampOrderMapper.insertTWxampOrder(tWxampOrder);
    }

    /**
     * 修改订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    @Override
    public int updateTWxampOrder(TWxampOrder tWxampOrder)
    {
        tWxampOrder.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampOrderMapper.updateTWxampOrder(tWxampOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteTWxampOrderByIds(String[] ids)
    {
        return tWxampOrderMapper.deleteTWxampOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteTWxampOrderById(String id)
    {
        return tWxampOrderMapper.deleteTWxampOrderById(id);
    }

	@Override
	public TWxampOrder selectTWxampOrderByOrderNo(String tradeNo) {
		return tWxampOrderMapper.selectTWxampOrderByOrderNo(tradeNo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TWxampOrder createOrder(TPlatUser user, String wxOpenId, Collection<WxampCartItemVo> items) {
		// 保存订单信息
		String orderId = IdUtils.fastSimpleUUID();
		String orderNo = redisService.getWxAmpTransactionsNumber();
		String userId = user.getId();
		List<TWxampOrderItem> list = new ArrayList<TWxampOrderItem>();
		long totalAmount = 0L;
		StringBuffer description = new StringBuffer();
		for (WxampCartItemVo wxampCartItemsVo : items) {
			Long quantity = wxampCartItemsVo.getQuantity();
			if (quantity != null && quantity > 0) {
				totalAmount += quantity * wxampCartItemsVo.getPrice();
				description.append(wxampCartItemsVo.getName()).append("、");
			}
			TWxampOrderItem wxampOrderItem = new TWxampOrderItem();
			wxampOrderItem.setId(IdUtils.fastSimpleUUID());
			wxampOrderItem.setOrderId(orderId);
			wxampOrderItem.setDatabaseId(wxampCartItemsVo.getId());
			wxampOrderItem.setUserId(userId);
			wxampOrderItem.setPrice(new BigDecimal(wxampCartItemsVo.getPrice()));
			wxampOrderItem.setQuantity(quantity);
			list.add(wxampOrderItem);
		}
		String removeEnd = StringUtils.removeEnd(description.toString(), "、");
		String _description = "会员订购-" + removeEnd.toString();
		_description = "会员订购-" + items.size()+"个编数据库";

		TWxampOrder tWxampOrder = new TWxampOrder();
		tWxampOrder.setId(orderId);
		tWxampOrder.setUserId(userId);
		tWxampOrder.setOpenId(wxOpenId);
		tWxampOrder.setOrderNo(orderNo);
		tWxampOrder.setOrderAmount(new BigDecimal(totalAmount));
		tWxampOrder.setOrderDescription(_description);
		tWxampOrder.setOrderValid(WxampOrderValidStatus.OFF.getCode());
		tWxampOrder.setPayResult("未支付");
		tWxampOrder.setPayType("微信小程序");
		tWxampOrder.setDelFlag(0);
		tWxampOrder.setShippingStatus(0);
		insertTWxampOrder(tWxampOrder);
		for (TWxampOrderItem tWxampOrderItem : list) {
			wxampOrderItemService.insertTWxampOrderItem(tWxampOrderItem);
		}
		return tWxampOrder;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void notifyOrder(TWxampOrder order, String transactionId) {
		TWxampOrderItem selectWxampOrderItem = new TWxampOrderItem();
		selectWxampOrderItem.setOrderId(order.getId());
		List<TWxampOrderItem> selectTWxampOrderItemList = wxampOrderItemService.selectTWxampOrderItemList(selectWxampOrderItem);
		for (TWxampOrderItem tWxampOrderItem : selectTWxampOrderItemList) {
			// 更新用户权益
			TWxampRight selectWxampRight = new TWxampRight();
			selectWxampRight.setUserId(tWxampOrderItem.getUserId());
			selectWxampRight.setDatabaseId(tWxampOrderItem.getDatabaseId());
			selectWxampRight.setStatus(1); // 1-限时权益
			TWxampRight selectTWxampRight = wxampRightService.selectTWxampRight(selectWxampRight);
			if(selectTWxampRight == null) {
				// 用户无权益
				TWxampRight insert = new TWxampRight();
				insert.setId(IdUtils.fastSimpleUUID());
				insert.setUserId(tWxampOrderItem.getUserId());
				insert.setDatabaseId(tWxampOrderItem.getDatabaseId());
				LocalDateTime now = LocalDateTime.now();
				Date startTime = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
				insert.setStartTime(startTime);
				LocalDateTime plus = now.plusYears(tWxampOrderItem.getQuantity());
				Date expireTime = Date.from(plus.atZone(ZoneId.systemDefault()).toInstant());
				insert.setExpireTime(expireTime);
				insert.setPrice(tWxampOrderItem.getPrice());
				insert.setQuantity(tWxampOrderItem.getQuantity());
				insert.setStatus(1);
				wxampRightService.insertTWxampRight(insert);
				// 更新订单详情
				TWxampOrderItem updateWxampOrderItem = new TWxampOrderItem();
				updateWxampOrderItem.setId(tWxampOrderItem.getId());
				updateWxampOrderItem.setValidStartTime(startTime);
				updateWxampOrderItem.setValidExpireTime(expireTime);
				wxampOrderItemService.updateTWxampOrderItem(updateWxampOrderItem);
			} else {
				// 用户拥有历史权益
				TWxampRight update = new TWxampRight();
				update.setId(selectTWxampRight.getId());
				Date lastExpireTime = selectTWxampRight.getExpireTime();
				LocalDateTime now = LocalDateTime.now();
				LocalDateTime before = LocalDateTime.ofInstant(lastExpireTime.toInstant(),ZoneId.systemDefault());
				if(before.isBefore(now)) {
					// 历史权益过期时间大于当前时间，以当前时间作为开始时间
					before = now;
					lastExpireTime = Date.from(before.atZone(ZoneId.systemDefault()).toInstant());
					update.setStartTime(lastExpireTime);
				}
				// 累计时间
				LocalDateTime plus = before.plusYears(tWxampOrderItem.getQuantity());
				Date plusExpireTime = Date.from(plus.atZone(ZoneId.systemDefault()).toInstant());
				update.setExpireTime(plusExpireTime);
				BigDecimal price = selectTWxampRight.getPrice();
				update.setPrice(price.add(tWxampOrderItem.getPrice()));
				Long quantity = selectTWxampRight.getQuantity();
				update.setQuantity(tWxampOrderItem.getQuantity() + quantity);
				wxampRightService.updateTWxampRight(update);
				// 更新订单详情生效时间
				TWxampOrderItem updateWxampOrderItem = new TWxampOrderItem();
				updateWxampOrderItem.setId(tWxampOrderItem.getId());
				updateWxampOrderItem.setValidStartTime(lastExpireTime);
				updateWxampOrderItem.setValidExpireTime(plusExpireTime);
				wxampOrderItemService.updateTWxampOrderItem(updateWxampOrderItem);
			}
		}
		// 更新订单生效状态
		TWxampOrder updateWxampOrder = new TWxampOrder();
		updateWxampOrder.setId(order.getId());
		updateWxampOrder.setTransactionId(transactionId);
		updateWxampOrder.setPayResult("支付成功");
		updateWxampOrder.setOrderValid(WxampOrderValidStatus.ON.getCode());
		updateTWxampOrder(updateWxampOrder);
	}
}
