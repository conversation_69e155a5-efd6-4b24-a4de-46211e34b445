package cn.guliandigital.wxamp.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.guliandigital.common.constant.WxampConstants;
import cn.guliandigital.common.enums.WxampOrderValidStatus;
import cn.guliandigital.common.utils.file.HttpUtils;
import cn.guliandigital.common.wechat.WeChatAmpApiProperties;
import cn.guliandigital.common.wechat.WeChatAmpProperties;
import cn.guliandigital.common.wechat.WeChatPayProperties;
import cn.guliandigital.wxamp.domain.TWxampOrder;
import cn.guliandigital.wxamp.domain.vo.WxampLoginVo;
import cn.guliandigital.wxamp.service.ITWxampOrderService;
import cn.guliandigital.wxamp.service.IWxampService;
import cn.hutool.json.JSONArray;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WxampServiceImpl implements IWxampService {

	@Resource
	private WeChatAmpProperties weChatAmpProperties;

	@Resource
	private WeChatAmpApiProperties weChatAmpApiProperties;
	
	@Autowired
    public RedisTemplate redisTemplate;
	
	@Resource
	private WeChatPayProperties weChatPayProperties;
	
	@Autowired
    private ITWxampOrderService itWxampOrderService;

	@Override
	public JSONObject wxAmpLogin(WxampLoginVo loginVo) {
		String url = weChatAmpApiProperties.getCode2Session() + "?appid=" + weChatAmpProperties.getAppId() + "&secret="
				+ weChatAmpProperties.getAppSecret() + "&grant_type=authorization_code&js_code=" + loginVo.getCode();
		log.info("url {}", url);
		Entry<Integer, String> entry = HttpUtils.get1(url);
		log.info("{}", JSON.toJSONString(entry));
		HttpStatus valueOf = HttpStatus.valueOf(entry.getKey());
		if(HttpStatus.OK == valueOf) {
			return JSON.parseObject(entry.getValue());
		} else {
			return null;
		}
	}
	
	@Override
	public String accessToken() {
		String url = weChatAmpApiProperties.getAccessToken() + "?grant_type=client_credential&appid="
				+ weChatAmpProperties.getAppId() + "&secret=" + weChatAmpProperties.getAppSecret();
		log.info("url {}", url);
		Entry<Integer, String> entry = HttpUtils.get1(url);
		log.info("{} -> {}", url, JSON.toJSONString(entry));
		HttpStatus valueOf = HttpStatus.valueOf(entry.getKey());
		if (HttpStatus.OK == valueOf) {
			JSONObject parseObject = JSON.parseObject(entry.getValue());
			String accessToken = parseObject.getString("access_token");
			int expiresIn = parseObject.getIntValue("expires_in");
			redisTemplate.opsForValue().set(WxampConstants.REDIS_KEY_ACCESS_TOKEN, accessToken, expiresIn,
					TimeUnit.SECONDS);
			return accessToken;
		} else {
			return null;
		}
	}
	
	
	
	@Override
	public void uploadShippingInfo(TWxampOrder tWxampOrder) {
		String accessToken = (String) redisTemplate.opsForValue().get(WxampConstants.REDIS_KEY_ACCESS_TOKEN);
		String url = weChatAmpApiProperties.getUploadShippingInfo() + accessToken;
		log.info("url {}", url);
		JSONObject request = new JSONObject();
		JSONObject order_key = new JSONObject();
		order_key.put("order_number_type", 1);
//		order_key.put("transaction_id", tWxampOrder.getTransactionId());
		order_key.put("mchid", weChatPayProperties.getMerchantId());
		order_key.put("out_trade_no", tWxampOrder.getOrderNo());
		request.put("order_key", order_key);
		request.put("logistics_type", 3); //  3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 
		request.put("delivery_mode", "UNIFIED_DELIVERY"); // 统一发货
		JSONArray shipping_list = new JSONArray();
		JSONObject shipping_item = new JSONObject();
		shipping_item.put("item_desc", tWxampOrder.getOrderDescription());
		shipping_list.add(shipping_item);
		request.put("shipping_list", shipping_list);
		String upload_time = LocalDateTime.now().atOffset(ZoneOffset.ofHours(8)).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
		request.put("upload_time", upload_time);
		JSONObject payer = new JSONObject();
		payer.put("openid", tWxampOrder.getOpenId());
		request.put("payer", payer);
		log.info("{} -> {}", url, request.toJSONString());
		Entry<Integer, String> entry = HttpUtils.post1(url, request.toJSONString(), ContentType.APPLICATION_JSON);
		String jsonString = JSON.toJSONString(entry);
		log.info("{} <- {}", url, JSON.toJSONString(entry));
		HttpStatus valueOf = HttpStatus.valueOf(entry.getKey());
		if (HttpStatus.OK == valueOf) {
			log.info("发货成功 {}", jsonString);
			// 更新发货状态
			TWxampOrder updateWxampOrder = new TWxampOrder();
			updateWxampOrder.setId(tWxampOrder.getId());
			updateWxampOrder.setShippingStatus(1);// 1-已发货
			itWxampOrderService.updateTWxampOrder(updateWxampOrder);
		} else {
			log.info("发货失败 {}", jsonString);
		}
	}

}
