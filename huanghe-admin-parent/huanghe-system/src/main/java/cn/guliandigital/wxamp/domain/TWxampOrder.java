package cn.guliandigital.wxamp.domain;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 订单对象 t_wxamp_order
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public class TWxampOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;
    
    /** 微信openId */
    private String openId;
    
    private String transactionId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    /** 描述 */
    @Excel(name = "描述")
    private String orderDescription;

    /** 订单生效状态 */
    @Excel(name = "订单生效状态")
    private String orderValid;

    /** 订单支付结果 */
    @Excel(name = "订单支付结果")
    private String payResult;

    /** 支付类型，微信支付/系统开通 */
    @Excel(name = "支付类型，微信支付/系统开通")
    private String payType;
    
    // 物流状态
    private Integer shippingStatus;// 0-未发货, 1-已发货

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** 更新人ID */
    @Excel(name = "更新人ID")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setOrderAmount(BigDecimal orderAmount) 
    {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getOrderAmount() 
    {
        return orderAmount;
    }
    public void setOrderDescription(String orderDescription) 
    {
        this.orderDescription = orderDescription;
    }

    public String getOrderDescription() 
    {
        return orderDescription;
    }
    public void setOrderValid(String orderValid) 
    {
        this.orderValid = orderValid;
    }

    public String getOrderValid() 
    {
        return orderValid;
    }
    public void setPayResult(String payResult) 
    {
        this.payResult = payResult;
    }

    public String getPayResult() 
    {
        return payResult;
    }
    public void setPayType(String payType) 
    {
        this.payType = payType;
    }

    public String getPayType() 
    {
        return payType;
    }
    
    public Integer getShippingStatus() {
		return shippingStatus;
	}

	public void setShippingStatus(Integer shippingStatus) {
		this.shippingStatus = shippingStatus;
	}

	public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("orderNo", getOrderNo())
            .append("orderAmount", getOrderAmount())
            .append("orderDescription", getOrderDescription())
            .append("orderValid", getOrderValid())
            .append("payResult", getPayResult())
            .append("payType", getPayType())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }}
