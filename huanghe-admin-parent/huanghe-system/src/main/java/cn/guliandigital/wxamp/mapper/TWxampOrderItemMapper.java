package cn.guliandigital.wxamp.mapper;

import java.util.List;

import cn.guliandigital.order.domain.WxampOrderVo;
import cn.guliandigital.wxamp.domain.TWxampOrderItem;

/**
 * 订单详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public interface TWxampOrderItemMapper 
{
    /**
     * 查询订单详情
     * 
     * @param id 订单详情主键
     * @return 订单详情
     */
    public TWxampOrderItem selectTWxampOrderItemById(String id);

    /**
     * 查询订单详情列表
     * 
     * @param tWxampOrderItem 订单详情
     * @return 订单详情集合
     */
    public List<TWxampOrderItem> selectTWxampOrderItemList(TWxampOrderItem tWxampOrderItem);

    /**
     * 新增订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    public int insertTWxampOrderItem(TWxampOrderItem tWxampOrderItem);

    /**
     * 修改订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    public int updateTWxampOrderItem(TWxampOrderItem tWxampOrderItem);

    /**
     * 删除订单详情
     * 
     * @param id 订单详情主键
     * @return 结果
     */
    public int deleteTWxampOrderItemById(String id);

    /**
     * 批量删除订单详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTWxampOrderItemByIds(String[] ids);

    WxampOrderVo selectTWxampOrderItemBy(String id);

    List<WxampOrderVo> selectTWxampOrderItemVO(WxampOrderVo wxampOrderVo);
}
