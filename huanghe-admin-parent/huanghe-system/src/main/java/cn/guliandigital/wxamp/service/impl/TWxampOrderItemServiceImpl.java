package cn.guliandigital.wxamp.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.order.domain.WxampOrderVo;
import cn.guliandigital.wxamp.domain.TWxampOrder;
import cn.guliandigital.wxamp.domain.TWxampOrderItem;
import cn.guliandigital.wxamp.mapper.TWxampOrderItemMapper;
import cn.guliandigital.wxamp.mapper.TWxampOrderMapper;
import cn.guliandigital.wxamp.service.ITWxampOrderItemService;
import lombok.extern.log4j.Log4j;

/**
 * 订单详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
@Log4j
@Service
public class TWxampOrderItemServiceImpl implements ITWxampOrderItemService 
{
    @Autowired
    private TWxampOrderItemMapper tWxampOrderItemMapper;
    @Autowired
    private TWxampOrderMapper tWxampOrderMapper;

    /**
     * 查询订单详情
     * 
     * @param id 订单详情主键
     * @return 订单详情
     */
    @Override
    public TWxampOrderItem selectTWxampOrderItemById(String id)
    {
        return tWxampOrderItemMapper.selectTWxampOrderItemById(id);
    }

    /**
     * 查询订单详情列表
     * 
     * @param tWxampOrderItem 订单详情
     * @return 订单详情
     */
    @Override
    public List<TWxampOrderItem> selectTWxampOrderItemList(TWxampOrderItem tWxampOrderItem)
    {

        return tWxampOrderItemMapper.selectTWxampOrderItemList(tWxampOrderItem);
    }

    /**
     * 新增订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    @Override
    public int insertTWxampOrderItem(TWxampOrderItem tWxampOrderItem)
    {
        tWxampOrderItem.setCreateTime(DateUtil.getCuurentDate());
        return tWxampOrderItemMapper.insertTWxampOrderItem(tWxampOrderItem);
    }

    /**
     * 修改订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    @Override
    public int updateTWxampOrderItem(TWxampOrderItem tWxampOrderItem)
    {
        if(StringUtil.isNotEmpty(tWxampOrderItem.getOrderId())){
            TWxampOrder tWxampOrder = new TWxampOrder();
            tWxampOrder.setId(tWxampOrderItem.getOrderId());
            tWxampOrder.setOrderValid(tWxampOrderItem.getOrderValid());
            tWxampOrderMapper.updateTWxampOrder(tWxampOrder);
        }
        tWxampOrderItem.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampOrderItemMapper.updateTWxampOrderItem(tWxampOrderItem);
    }

    @Override
    public int updateTWxampOrderItemDelFlag(TWxampOrderItem tWxampOrderItem)
    {
        tWxampOrderItem.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampOrderItemMapper.updateTWxampOrderItem(tWxampOrderItem);
    }




    /**
     * 批量删除订单详情
     * 
     * @param ids 需要删除的订单详情主键
     * @return 结果
     */
    @Override
    public int deleteTWxampOrderItemByIds(String[] ids)
    {
        return tWxampOrderItemMapper.deleteTWxampOrderItemByIds(ids);
    }

    /**
     * 删除订单详情信息
     * 
     * @param id 订单详情主键
     * @return 结果
     */
    @Override
    public int deleteTWxampOrderItemById(String id)
    {
        return tWxampOrderItemMapper.deleteTWxampOrderItemById(id);
    }

    @Override
    public WxampOrderVo selectTWxampOrderItemBy(String id) {
        return tWxampOrderItemMapper.selectTWxampOrderItemBy(id);
    }

    @Override
    public List<WxampOrderVo> selectTWxampOrderItemVO(WxampOrderVo wxampOrderVo) {
        if (StringUtil.isNotEmpty(wxampOrderVo.getSearchTime())){
            try {
                String[] searchTime = wxampOrderVo.getSearchTime();
                Date start = DateUtil.parseDate(searchTime[0]);
                Date end = DateUtil.parseDate(searchTime[1]);
                wxampOrderVo.setValidStartTime(start);
                wxampOrderVo.setValidExpireTime(end);
            } catch (Exception e) {
                log.info("时间转换错误"+e);
            }

        }
        return tWxampOrderItemMapper.selectTWxampOrderItemVO(wxampOrderVo);
    }
}
