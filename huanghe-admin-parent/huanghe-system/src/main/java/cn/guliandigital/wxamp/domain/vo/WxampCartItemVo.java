package cn.guliandigital.wxamp.domain.vo;

import java.util.Date;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 个人用户购物车对象 t_wxamp_cart
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
public class WxampCartItemVo
{

    /** 数据库ID */
    // 数据库ID
    private String id;
    
    // 数据库名称
    private String name;
    
    // 数据库图标
    private String appletPic;
    
    // 书籍数量
    private Long volume;
    
    // 订购状态
    private String status;

    // 价格
    private Long price;
    
    // 购物车数量
    private Long quantity;
    
    // 选中状态
    private String selectStatus;
    
    

    
}
