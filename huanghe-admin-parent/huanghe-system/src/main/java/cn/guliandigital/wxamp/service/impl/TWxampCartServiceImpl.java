package cn.guliandigital.wxamp.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.wxamp.domain.TWxampCart;
import cn.guliandigital.wxamp.mapper.TWxampCartMapper;
import cn.guliandigital.wxamp.service.ITWxampCartService;

/**
 * 个人用户购物车Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
@Service
public class TWxampCartServiceImpl implements ITWxampCartService 
{
    @Autowired
    private TWxampCartMapper tWxampCartMapper;

    /**
     * 查询个人用户购物车
     * 
     * @param id 个人用户购物车主键
     * @return 个人用户购物车
     */
    @Override
    public TWxampCart selectTWxampCartById(String id)
    {
        return tWxampCartMapper.selectTWxampCartById(id);
    }

    /**
     * 查询个人用户购物车列表
     * 
     * @param tWxampCart 个人用户购物车
     * @return 个人用户购物车
     */
    @Override
    public List<TWxampCart> selectTWxampCartList(TWxampCart tWxampCart)
    {
        return tWxampCartMapper.selectTWxampCartList(tWxampCart);
    }

    /**
     * 新增个人用户购物车
     * 
     * @param tWxampCart 个人用户购物车
     * @return 结果
     */
    @Override
    public int insertTWxampCart(TWxampCart tWxampCart)
    {
        tWxampCart.setCreateTime(DateUtil.getCuurentDate());
        return tWxampCartMapper.insertTWxampCart(tWxampCart);
    }

    /**
     * 修改个人用户购物车
     * 
     * @param tWxampCart 个人用户购物车
     * @return 结果
     */
    @Override
    public int updateTWxampCart(TWxampCart tWxampCart)
    {
        tWxampCart.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampCartMapper.updateTWxampCart(tWxampCart);
    }

    /**
     * 批量删除个人用户购物车
     * 
     * @param ids 需要删除的个人用户购物车主键
     * @return 结果
     */
    @Override
    public int deleteTWxampCartByIds(String[] ids)
    {
        return tWxampCartMapper.deleteTWxampCartByIds(ids);
    }

    /**
     * 删除个人用户购物车信息
     * 
     * @param id 个人用户购物车主键
     * @return 结果
     */
    @Override
    public int deleteTWxampCartById(String id)
    {
        return tWxampCartMapper.deleteTWxampCartById(id);
    }

	@Override
	public TWxampCart selectTWxampCart(TWxampCart tWxampCart) {
		 return tWxampCartMapper.selectTWxampCart(tWxampCart);
	}
}
