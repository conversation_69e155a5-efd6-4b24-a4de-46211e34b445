package cn.guliandigital.wxamp.mapper;

import java.util.List;

import cn.guliandigital.wxamp.domain.TWxampOrder;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public interface TWxampOrderMapper 
{
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public TWxampOrder selectTWxampOrderById(String id);
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public TWxampOrder selectTWxampOrderByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param tWxampOrder 订单
     * @return 订单集合
     */
    public List<TWxampOrder> selectTWxampOrderList(TWxampOrder tWxampOrder);

    /**
     * 新增订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    public int insertTWxampOrder(TWxampOrder tWxampOrder);

    /**
     * 修改订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    public int updateTWxampOrder(TWxampOrder tWxampOrder);

    /**
     * 删除订单
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteTWxampOrderById(String id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTWxampOrderByIds(String[] ids);
}
