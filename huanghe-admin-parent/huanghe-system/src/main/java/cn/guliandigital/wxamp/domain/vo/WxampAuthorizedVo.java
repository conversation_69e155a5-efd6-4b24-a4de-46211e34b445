package cn.guliandigital.wxamp.domain.vo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.guliandigital.common.annotation.Excel;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
public class WxampAuthorizedVo
{
	 // 数据库ID
    private String id;
    
    // 数据库名称
    private String name;
    
    // 数据库图标
    private String appletPic;
    
    // 书籍数量
    private Long volume;

	// 授权开始日期
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;

    // 授权截至日期
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date expireTime;

    // 价格
    private BigDecimal price;

    // 年数量
    private Long years;
    
    private Integer status;
	
}
