package cn.guliandigital.wxamp.service;

import java.util.List;

import cn.guliandigital.order.domain.WxampOrderVo;
import cn.guliandigital.wxamp.domain.TWxampOrderItem;

/**
 * 订单详情Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public interface ITWxampOrderItemService 
{
    /**
     * 查询订单详情
     * 
     * @param id 订单详情主键
     * @return 订单详情
     */
    public TWxampOrderItem selectTWxampOrderItemById(String id);

    /**
     * 查询订单详情列表
     * 
     * @param tWxampOrderItem 订单详情
     * @return 订单详情集合
     */
    public List<TWxampOrderItem> selectTWxampOrderItemList(TWxampOrderItem tWxampOrderItem);

    /**
     * 新增订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    public int insertTWxampOrderItem(TWxampOrderItem tWxampOrderItem);

    /**
     * 修改订单详情
     * 
     * @param tWxampOrderItem 订单详情
     * @return 结果
     */
    public int updateTWxampOrderItem(TWxampOrderItem tWxampOrderItem);

    /**
     * 批量删除订单详情
     * 
     * @param ids 需要删除的订单详情主键集合
     * @return 结果
     */
    public int deleteTWxampOrderItemByIds(String[] ids);

    /**
     * 删除订单详情信息
     * 
     * @param id 订单详情主键
     * @return 结果
     */
    public int deleteTWxampOrderItemById(String id);

    WxampOrderVo selectTWxampOrderItemBy(String id);

    List<WxampOrderVo> selectTWxampOrderItemVO(WxampOrderVo wxampOrderVo);

    int updateTWxampOrderItemDelFlag(TWxampOrderItem tWxampOrderItem);
}
