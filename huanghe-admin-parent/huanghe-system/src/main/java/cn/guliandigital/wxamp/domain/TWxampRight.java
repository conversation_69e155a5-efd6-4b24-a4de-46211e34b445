package cn.guliandigital.wxamp.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 用户已购授权对象 t_wxamp_authorized
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public class TWxampRight extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 授权数据库ID */
    @Excel(name = "授权数据库ID")
    private String databaseId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 授权开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 授权截至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权截至日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 年数量 */
    @Excel(name = "年数量")
    private Long quantity;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** 更新人ID */
    @Excel(name = "更新人ID")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;


    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setDatabaseId(String databaseId) 
    {
        this.databaseId = databaseId;
    }

    public String getDatabaseId() 
    {
        return databaseId;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setQuantity(Long quantity) 
    {
        this.quantity = quantity;
    }

    public Long getQuantity() 
    {
        return quantity;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("databaseId", getDatabaseId())
            .append("userId", getUserId())
            .append("startTime", getStartTime())
            .append("expireTime", getExpireTime())
            .append("price", getPrice())
            .append("quantity", getQuantity())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }	
}
