package cn.guliandigital.wxamp.service;

import java.util.Collection;
import java.util.List;


import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.wxamp.domain.TWxampOrder;
import cn.guliandigital.wxamp.domain.vo.WxampCartItemVo;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public interface ITWxampOrderService 
{
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public TWxampOrder selectTWxampOrderById(String id);

    /**
     * 查询订单列表
     * 
     * @param tWxampOrder 订单
     * @return 订单集合
     */
    public List<TWxampOrder> selectTWxampOrderList(TWxampOrder tWxampOrder);

    /**
     * 新增订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    public int insertTWxampOrder(TWxampOrder tWxampOrder);

    /**
     * 修改订单
     * 
     * @param tWxampOrder 订单
     * @return 结果
     */
    public int updateTWxampOrder(TWxampOrder tWxampOrder);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteTWxampOrderByIds(String[] ids);

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteTWxampOrderById(String id);

    /**
     * 查询订单
     * @param outTradeNo
     * @return
     */
	public TWxampOrder selectTWxampOrderByOrderNo(String tradeNo);

	public void notifyOrder(TWxampOrder order, String transactionId);

	TWxampOrder createOrder(TPlatUser user, String wxOpenId, Collection<WxampCartItemVo> items);

}
