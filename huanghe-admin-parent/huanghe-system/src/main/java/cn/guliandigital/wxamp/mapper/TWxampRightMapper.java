package cn.guliandigital.wxamp.mapper;

import java.util.List;

import cn.guliandigital.wxamp.domain.TWxampRight;

/**
 * 用户已购授权Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public interface TWxampRightMapper 
{
    /**
     * 查询用户已购授权
     * 
     * @param id 用户已购授权主键
     * @return 用户已购授权
     */
    public TWxampRight selectTWxampRightById(String id);

    /**
     * 查询用户已购授权列表
     * 
     * @param tWxampRight 用户已购授权
     * @return 用户已购授权集合
     */
    public List<TWxampRight> selectTWxampRightList(TWxampRight tWxampRight);

    /**
     * 新增用户已购授权
     * 
     * @param tWxampRight 用户已购授权
     * @return 结果
     */
    public int insertTWxampRight(TWxampRight tWxampRight);

    /**
     * 修改用户已购授权
     * 
     * @param tWxampRight 用户已购授权
     * @return 结果
     */
    public int updateTWxampRight(TWxampRight tWxampRight);

    /**
     * 删除用户已购授权
     * 
     * @param id 用户已购授权主键
     * @return 结果
     */
    public int deleteTWxampRightById(String id);

    /**
     * 批量删除用户已购授权
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTWxampRightByIds(String[] ids);

	public TWxampRight selectTWxampRight(TWxampRight tWxampRight);
}
