package cn.guliandigital.wxamp.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.wxamp.domain.TWxampRight;
import cn.guliandigital.wxamp.mapper.TWxampRightMapper;
import cn.guliandigital.wxamp.service.ITWxampRightService;

/**
 * 用户已购授权Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
@Service
public class TWxampRightServiceImpl implements ITWxampRightService 
{
    @Autowired
    private TWxampRightMapper tWxampRightMapper;

    /**
     * 查询用户已购授权
     * 
     * @param id 用户已购授权主键
     * @return 用户已购授权
     */
    @Override
    public TWxampRight selectTWxampRightById(String id)
    {
        return tWxampRightMapper.selectTWxampRightById(id);
    }

    /**
     * 查询用户已购授权列表
     * 
     * @param tWxampRight 用户已购授权
     * @return 用户已购授权
     */
    @Override
    public List<TWxampRight> selectTWxampRightList(TWxampRight tWxampRight)
    {
        return tWxampRightMapper.selectTWxampRightList(tWxampRight);
    }

    /**
     * 新增用户已购授权
     * 
     * @param tWxampRight 用户已购授权
     * @return 结果
     */
    @Override
    public int insertTWxampRight(TWxampRight tWxampRight)
    {
        tWxampRight.setCreateTime(DateUtil.getCuurentDate());
        return tWxampRightMapper.insertTWxampRight(tWxampRight);
    }

    /**
     * 修改用户已购授权
     * 
     * @param tWxampRight 用户已购授权
     * @return 结果
     */
    @Override
    public int updateTWxampRight(TWxampRight tWxampRight)
    {
        tWxampRight.setUpdateTime(DateUtil.getCuurentDate());
        return tWxampRightMapper.updateTWxampRight(tWxampRight);
    }

    /**
     * 批量删除用户已购授权
     * 
     * @param ids 需要删除的用户已购授权主键
     * @return 结果
     */
    @Override
    public int deleteTWxampRightByIds(String[] ids)
    {
        return tWxampRightMapper.deleteTWxampRightByIds(ids);
    }

    /**
     * 删除用户已购授权信息
     * 
     * @param id 用户已购授权主键
     * @return 结果
     */
    @Override
    public int deleteTWxampRightById(String id)
    {
        return tWxampRightMapper.deleteTWxampRightById(id);
    }

	@Override
	public TWxampRight selectTWxampRight(TWxampRight tWxampRight) {
		return tWxampRightMapper.selectTWxampRight(tWxampRight);
	}
}
