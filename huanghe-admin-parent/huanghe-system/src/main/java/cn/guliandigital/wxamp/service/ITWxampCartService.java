package cn.guliandigital.wxamp.service;

import java.util.List;

import cn.guliandigital.wxamp.domain.TWxampCart;

/**
 * 个人用户购物车Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface ITWxampCartService 
{
    /**
     * 查询个人用户购物车
     * 
     * @param id 个人用户购物车主键
     * @return 个人用户购物车
     */
    public TWxampCart selectTWxampCartById(String id);

    /**
     * 查询个人用户购物车列表
     * 
     * @param tWxampCart 个人用户购物车
     * @return 个人用户购物车集合
     */
    public List<TWxampCart> selectTWxampCartList(TWxampCart tWxampCart);

    /**
     * 新增个人用户购物车
     * 
     * @param tWxampCart 个人用户购物车
     * @return 结果
     */
    public int insertTWxampCart(TWxampCart tWxampCart);

    /**
     * 修改个人用户购物车
     * 
     * @param tWxampCart 个人用户购物车
     * @return 结果
     */
    public int updateTWxampCart(TWxampCart tWxampCart);

    /**
     * 批量删除个人用户购物车
     * 
     * @param ids 需要删除的个人用户购物车主键集合
     * @return 结果
     */
    public int deleteTWxampCartByIds(String[] ids);

    /**
     * 删除个人用户购物车信息
     * 
     * @param id 个人用户购物车主键
     * @return 结果
     */
    public int deleteTWxampCartById(String id);

	public TWxampCart selectTWxampCart(TWxampCart tWxampCart);
}
