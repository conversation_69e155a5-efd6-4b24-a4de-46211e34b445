<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.utils.mapper.TUtilDictMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUtilDict" id="TUtilDictResult">
        <result property="id"    column="id"    />
        <result property="dictName"    column="dict_name"    />
        <result property="wordHead"    column="word_head"    />
        <result property="sHead"    column="s_head"    />
        <result property="likeSound"    column="like_sound"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="fontPath"    column="font_path"    />
        <result property="headUnicode"    column="head_unicode"    />
    </resultMap>

    <sql id="selectTUtilDictVo">
        select head_unicode, id, dict_name, word_head, s_head, like_sound, content, create_time, font_path from t_util_dict
    </sql>

    <select id="selectTUtilDictList" parameterType="TUtilDict" resultMap="TUtilDictResult">
        <include refid="selectTUtilDictVo"/>
        <where>  
            <if test="dictName != null  and dictName != ''"> and dict_name like concat('%', #{dictName}, '%')</if>
            <if test="likeSound != null  and likeSound != ''"> and like_sound = #{likeSound}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="fontPath != null  and fontPath != ''"> and font_path = #{fontPath}</if>
            <if test="headUnicode != null  and headUnicode != ''"> and head_unicode = #{headUnicode}</if>
            <if test="sHead!= null  and sHead != ''"> and s_head = #{sHead}</if>
        </where>
    </select>
    
    <select id="selectTUtilDictById" parameterType="String" resultMap="TUtilDictResult">
        <include refid="selectTUtilDictVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTUtilDict" parameterType="TUtilDict">
        insert into t_util_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dictName != null">dict_name,</if>
            <if test="wordHead != null">word_head,</if>
            <if test="sHead != null">s_head,</if>
            <if test="likeSound != null">like_sound,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="fontPath != null">font_path,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dictName != null">#{dictName},</if>
            <if test="wordHead != null">#{wordHead},</if>
            <if test="sHead != null">#{sHead},</if>
            <if test="likeSound != null">#{likeSound},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="fontPath != null">#{fontPath},</if>
         </trim>
    </insert>

    <update id="updateTUtilDict" parameterType="TUtilDict">
        update t_util_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="dictName != null">head_unicode = #{headUnicode},</if>
            <if test="dictName != null">dict_name = #{dictName},</if>
            <if test="sHead != null">s_head = #{sHead},</if>
            <if test="wordHead != null">word_head = #{wordHead},</if>
            <if test="likeSound != null">like_sound = #{likeSound},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="fontPath != null">font_path = #{fontPath},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTUtilDictById" parameterType="String">
        delete from t_util_dict where id = #{id}
    </delete>

    <delete id="deleteTUtilDictByIds" parameterType="String">
        delete from t_util_dict where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>