<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.utils.mapper.ShujuCalendarconversionMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="ShujuCalendarconversion" id="ShujuCalendarconversionResult">
        <result property="id"    column="id"    />
        <result property="calendarAD"    column="calendarAD"    />
        <result property="power"    column="power"    />
        <result property="period"    column="period"    />
        <result property="regime"    column="regime"    />
        <result property="emperorNo"    column="emperorNo"    />
        <result property="emperorName"    column="emperorName"    />
        <result property="yearNo"    column="yearNo"    />
        <result property="year"    column="year"    />
        <result property="affiliated"    column="affiliated"    />
        <result property="maxad"    column="maxad"    />
        <result property="minad"    column="minad"    />
    </resultMap>

    <sql id="selectShujuCalendarconversionVo">
        select id, calendarAD, power, period, regime, emperorNo, emperorName, yearNo, year, affiliated from t_util_calendarconversion
    </sql>

    <select id="selectShujuCalendarconversionList" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        <where>  
            <if test="calendarAD != null and calendarAD != ''"> and calendarAD = #{calendarAD}</if>
            <if test="power != null and power != ''"> and power = #{power}</if>
            <if test="regime != null and regime != ''"> and regime = #{regime} </if>
            <if test="period != null and period != ''"> and period = #{period} </if>
            <if test="emperorNo != null and emperorNo != ''"> and emperorNo = #{emperorNo} </if>
            <if test="emperorName != null and emperorName != ''"> and emperorName = #{emperorName} </if>
            <if test="yearNo != null and yearNo != ''">and yearNo = #{yearNo}</if>
        </where>
        order by id
    </select>
    
    <select id="selectShujuCalendarconversionById" parameterType="Long" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShujuCalendarconversion" parameterType="ShujuCalendarconversion" useGeneratedKeys="true" keyProperty="id">
        insert into t_util_calendarconversion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="calendarAD != null">calendarAD,</if>
            <if test="power != null">power,</if>
            <if test="period != null">period,</if>
            <if test="regime != null">regime,</if>
            <if test="emperorNo != null">emperorNo,</if>
            <if test="emperorName != null">emperorName,</if>
            <if test="yearNo != null">yearNo,</if>
            <if test="year != null">year,</if>
            <if test="affiliated != null">affiliated,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="calendarAD != null">#{calendarAD},</if>
            <if test="power != null">#{power},</if>
            <if test="period != null">#{period},</if>
            <if test="regime != null">#{regime},</if>
            <if test="emperorNo != null">#{emperorNo},</if>
            <if test="emperorName != null">#{emperorName},</if>
            <if test="yearNo != null">#{yearNo},</if>
            <if test="year != null">#{year},</if>
            <if test="affiliated != null">#{affiliated},</if>
         </trim>
    </insert>

    <update id="updateShujuCalendarconversion" parameterType="ShujuCalendarconversion">
        update t_util_calendarconversion
        <trim prefix="SET" suffixOverrides=",">
            <if test="calendarAD != null">calendarAD = #{calendarAD},</if>
            <if test="power != null">power = #{power},</if>
            <if test="period != null">period = #{period},</if>
            <if test="regime != null">regime = #{regime},</if>
            <if test="emperorNo != null">emperorNo = #{emperorNo},</if>
            <if test="emperorName != null">emperorName = #{emperorName},</if>
            <if test="yearNo != null">yearNo = #{yearNo},</if>
            <if test="year != null">year = #{year},</if>
            <if test="affiliated != null">affiliated = #{affiliated},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShujuCalendarconversionById" parameterType="Long">
        delete from t_util_calendarconversion where id = #{id}
    </delete>

    <delete id="deleteShujuCalendarconversionByIds" parameterType="String">
        delete from t_util_calendarconversion where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCalendarADByGanzhi" parameterType="String" resultMap="ShujuCalendarconversionResult">
        select distinct cc.calendarAD
        from t_util_calendarconversion cc where cc.power=#{ganzhi} order by cc.calendarAD
    </select>

    <select id="getPeriod" resultMap="ShujuCalendarconversionResult">
        select distinct cc.period from t_util_calendarconversion cc
    </select>

    <select id="getCalendarADByOr" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        <where>
            <if test="period != null "> and (period = #{period} or period=#{periodFan})</if>
            <if test="regime != null "> and (regime = #{regime} or regime=#{regimeFan})</if>
            <if test="emperorNo != null "> and (emperorNo = #{emperorNo} or emperorNo=#{emperorNoFan})</if>
            <if test="emperorName != null "> and (emperorName = #{emperorName} or emperorName=#{emperorNameFan})</if>
        </where>
    </select>
    <select id="getCalendarConversionByPeriodAndAD" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        <where>
            <if test="period != null "> and (period = #{period} or period=#{periodFan})</if>
            <if test="regime != null "> and (regime = #{regime} or regime=#{regimeFan})</if>
            <if test="emperorNo != null "> and (emperorNo = #{emperorNo} or emperorNo=#{emperorNoFan})</if>
            <if test="emperorName != null "> and (emperorName = #{emperorName} or emperorName=#{emperorNameFan})</if>
        </where>
        order by calendarAD,id
    </select>

    <select id="getRegimeOrMaxOrMinByPeriod" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        select regime,max(calendarAD) as maxad,min(calendarAD) as minad from t_util_calendarconversion
        <where>
            <if test="period != null and period != '' "> and (period = #{period} or period=#{periodFan})</if>
        </where>
        group by regime 
        order by minad ASC,	maxad ASC
    </select>

    <select id="getEmperorNameBy" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        select distinct emperorNo,emperorName from t_util_calendarconversion
        <where>
            <if test="period != null "> and (period = #{period} or period=#{periodFan})</if>
            <if test="regime != null "> and (regime = #{regime} or regime=#{regimeFan})</if>
        </where>
    </select>

    <select id="getCalendarConversionByAll" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        <where>
            yearNo like concat('%', #{yearNo}, '%') or yearNo like concat('%', #{yearNoFan}, '%')
            or
            regime like concat('%', #{regime}, '%') or regime like concat('%', #{regimeFan}, '%')
            or
            emperorNo like concat('%', #{emperorNo}, '%') or emperorNo like concat('%', #{emperorNoFan}, '%')
            or
            emperorName like concat('%', #{emperorName}, '%') or emperorName like concat('%', #{emperorNameFan}, '%')
        </where>
        order by calendarAD,yearNo,year
    </select>
    <select id="getCalendarConversion" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        <include refid="selectShujuCalendarconversionVo"/>
        <where>
            <if test="calendarAD != null "> and calendarAD = #{calendarAD}</if>
            <if test="power != null "> and (power = #{power} or power=#{powerFan})</if>
            <if test="period != null "> and (period = #{period} or period=#{periodFan})</if>
            <if test="regime != null "> and (regime = #{regime} or regime=#{regimeFan})</if>
            <if test="emperorNo != null "> and (emperorNo = #{emperorNo} or emperorNo=#{emperorNoFan})</if>
            <if test="emperorName != null "> and (emperorName = #{emperorName} or emperorName=#{emperorNameFan})</if>
        </where>
        order by calendarAD, yearNo asc
    </select>

    <select id="getCount" parameterType="String" resultType="Integer">
        select
          count(*)
        from
          t_util_calendarconversion
        where
          power like concat('%', #{parameter}, '%')
        or
          period like concat('%', #{parameter}, '%')
        or
          regime like concat('%', #{parameter}, '%')
        or
          emperorNo like concat('%', #{parameter}, '%')
        or
          emperorName like concat('%', #{parameter}, '%')
        or
          yearNo like concat('%', #{parameter}, '%')
        or
          affiliated like concat('%', #{parameter}, '%')
    </select>

    <select id="getRegime" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        SELECT DISTINCT
			regime 
		FROM
			( 
			select regime from t_util_calendarconversion
        <where>
            <if test="calendarAD != null and calendarAD != '' "> and calendarAD = #{calendarAD}</if>
            <if test="power != null and power != '' "> and power = #{power} </if>
            <if test="period != null and period != ''"> and period = #{period} </if>
            <if test="regime != null and regime != ''"> and regime = #{regime} </if>
            <if test="emperorNo != null and emperorNo != ''"> and emperorNo = #{emperorNo} </if>
            <if test="emperorName != null and emperorName != ''"> and emperorName = #{emperorName} </if>
            <if test="yearNo != null and yearNo != ''">and yearNo = #{yearNo}</if>
        </where>
        ORDER BY calendarAD ) temp
    </select>

    <select id="getRegimeByLike" parameterType="ShujuCalendarconversion" resultMap="ShujuCalendarconversionResult">
        select distinct regime from t_util_calendarconversion
        <where>
            <if test="yearNo != null and yearNo != ''"> or yearNo like concat('%', #{yearNo}, '%') or yearNo like concat('%', #{yearNoFan}, '%')</if>
            <if test="regime != null and regime != ''"> or regime like concat('%', #{regime}, '%') or regime like concat('%', #{regimeFan}, '%')</if>
            <if test="emperorNo != null and emperorNo != ''"> or emperorNo like concat('%', #{emperorNo}, '%') or emperorNo like concat('%', #{emperorNoFan}, '%')</if>
            <if test="emperorName != null and emperorName != ''"> or emperorName like concat('%', #{emperorName}, '%') or emperorName like concat('%', #{emperorNameFan}, '%')</if>
        </where>
        order by calendarAD,yearNo,year
    </select>
</mapper>