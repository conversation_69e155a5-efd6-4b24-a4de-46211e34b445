<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.plat.mapper.TPlatOrganTypeMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TPlatOrganType" id="TPlatOrganTypeResult">
        <result property="id"    column="ID"    />
        <result property="typeCode"    column="TYPE_CODE"    />
        <result property="typeName"    column="TYPE_NAME"    />
        <result property="typeStatus"    column="TYPE_STATUS"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="content"    column="CONTENT"    />
    </resultMap>

    <sql id="selectTPlatOrganTypeVo">
        select ID, TYPE_CODE, TYPE_NAME, TYPE_STATUS, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,CONTENT from t_plat_organ_type
    </sql>

    <select id="selectTPlatOrganTypeList" parameterType="TPlatOrganType" resultMap="TPlatOrganTypeResult">
        <include refid="selectTPlatOrganTypeVo"/>
        <where>  
            <if test="typeName != null  and typeName != ''"> and TYPE_NAME like concat('%', #{typeName}, '%')</if>
            <if test="typeStatus != null  and typeStatus != ''"> and TYPE_STATUS = #{typeStatus}</if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectByName" parameterType="String" resultMap= "TPlatOrganTypeResult">
        <include refid="selectTPlatOrganTypeVo"></include>
        <where>
            <if test="typeName != null  and typeName != ''"> and TYPE_NAME = #{typeName}</if>
        </where>
    </select>

    <select id="selectTPlatOrganTypeById" parameterType="String" resultMap="TPlatOrganTypeResult">
        <include refid="selectTPlatOrganTypeVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTPlatOrganType" parameterType="TPlatOrganType">
        insert into t_plat_organ_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="typeCode != null">TYPE_CODE,</if>
            <if test="typeName != null">TYPE_NAME,</if>
            <if test="typeStatus != null">TYPE_STATUS,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="content != null">CONTENT,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="typeCode != null">#{typeCode},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="typeStatus != null">#{typeStatus},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="content != null">#{content},</if>
         </trim>
    </insert>

    <update id="updateTPlatOrganType" parameterType="TPlatOrganType">
        update t_plat_organ_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeCode != null">TYPE_CODE = #{typeCode},</if>
            <if test="typeName != null">TYPE_NAME = #{typeName},</if>
            <if test="typeStatus != null">TYPE_STATUS = #{typeStatus},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="content != null">CONTENT = #{content},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTPlatOrganTypeById" parameterType="String">
        delete from t_plat_organ_type where ID = #{id}
    </delete>

    <delete id="deleteTPlatOrganTypeByIds" parameterType="String">
        delete from t_plat_organ_type where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>