<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.plat.mapper.TPlatOrganMapper">
	<cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TPlatOrgan" id="TPlatOrganResult">
        <result property="id" column="ID"/>
        <result property="orgCode" column="ORG_CODE"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="orgType" column="ORG_TYPE"/>
        <result property="typeName" column="TYPE_NAME"/>
        <result property="orgAddress" column="ORG_ADDRESS"/>
        <result property="orgStatus" column="ORG_STATUS"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="userName" column="USER_NAME"/>
        <result property="userId" column="USER_ID"/>
        <result property="authMethod" column="AUTH_METHOD"/>
        <result property="authStartTime" column="AUTH_START_TIME"/>
        <result property="authEndTime" column="AUTH_END_TIME"/>
        <result property="orderStatus" column="ORDER_STATUS"/>
        <result property="remark" column="REMARK"/>
        <result property="userTel" column="USER_TEL"/>
    </resultMap>

    <sql id="selectTPlatOrganVo">
        select ID, ORG_CODE, ORG_NAME, ORG_TYPE, ORG_ADDRESS, ORG_STATUS, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,REMARK,USER_TEL,USER_NAME from t_plat_organ
    </sql>

    <select id="selectTPlatOrganList" parameterType="TPlatOrgan" resultMap="TPlatOrganResult">
        SELECT
        o.ID,
        o.ORG_CODE,
        o.ORG_NAME,
        o.ORG_TYPE,
        o.ORG_STATUS,
        o.CREATE_TIME,
        t.TYPE_NAME,
        o.ORG_STATUS,
        o.USER_NAME,
        o.REMARK,
        o.USER_TEL
        FROM
        t_plat_organ o
        LEFT JOIN t_plat_organ_type t ON o.ORG_TYPE = t.id
        <where>
            <if test="userName != null  and userName != ''">and o.USER_NAME like concat('%', #{userName}, '%')</if>
            <if test="orgCode != null  and orgCode != ''">and o.ORG_CODE like concat('%', #{orgCode}, '%')</if>
            <if test="orgName != null  and orgName != ''">and o.ORG_NAME like concat('%', #{orgName}, '%')</if>
            <if test="userTel != null  and userTel != ''">and o.USER_TEL = #{userTel}</if>
            <if test="orgType != null  and orgType != ''">and o.ORG_TYPE = #{orgType}</if>
            <if test="orgStatus != null  and orgStatus != ''">and o.ORG_STATUS = #{orgStatus}</if>
        </where>
            order by CREATE_TIME desc
    </select>

    <select id="selectByName" parameterType="String" resultMap="TPlatOrganResult">
        <include refid="selectTPlatOrganVo"/>
        where ORG_NAME = #{orgName}
    </select>

    <select id="selectTPlatOrganById" parameterType="String" resultMap="TPlatOrganResult">
        <include refid="selectTPlatOrganVo"/>
        where ID = #{id}
    </select>

    <select id="selectTPlatOrganDetailById" parameterType="String" resultMap="TPlatOrganResult">
        SELECT
		o.ORG_NAME,
		t.TYPE_NAME AS ORG_TYPE,
		AUTH_METHOD,
		AUTH_START_TIME,
		AUTH_END_TIME,
		r.ORDER_STATUS
		FROM
			t_plat_organ o
		LEFT JOIN t_plat_organ_type t ON o.ORG_TYPE = t.id
		LEFT JOIN t_pro_order r ON r.ORG_ID = o.id
		WHERE 1 = 1        
        and   o.ID = #{id}
        and r.del_flag = 0
        order by r.ORDER_STATUS desc,r.CREATE_TIME desc 
        limit 1 
        
    </select>

    <insert id="insertTPlatOrgan" parameterType="TPlatOrgan">
        insert into t_plat_organ
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="orgCode != null">ORG_CODE,</if>
            <if test="orgName != null">ORG_NAME,</if>
            <if test="orgType != null">ORG_TYPE,</if>
            <if test="orgAddress != null">ORG_ADDRESS,</if>
            <if test="orgStatus != null">ORG_STATUS,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="remark != null">REMARK,</if>
            <if test="userTel != null">USER_TEL,</if>
            <if test="userName != null">USER_NAME,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="orgType != null">#{orgType},</if>
            <if test="orgAddress != null">#{orgAddress},</if>
            <if test="orgStatus != null">#{orgStatus},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userTel != null">#{userTel},</if>
            <if test="userName != null">#{userName},</if>
        </trim>
    </insert>

    <update id="updateTPlatOrgan" parameterType="TPlatOrgan">
        update t_plat_organ
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgCode != null">ORG_CODE = #{orgCode},</if>
            <if test="orgName != null">ORG_NAME = #{orgName},</if>
            <if test="orgType != null">ORG_TYPE = #{orgType},</if>
            <if test="orgAddress != null">ORG_ADDRESS = #{orgAddress},</if>
            <if test="orgStatus != null">ORG_STATUS = #{orgStatus},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="userTel != null">USER_TEL = #{userTel},</if>
            <if test="userName != null">USER_NAME = #{userName},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTPlatOrganById" parameterType="String">
        delete from t_plat_organ where ID = #{id}
    </delete>

    <delete id="deleteTPlatOrganByIds" parameterType="String">
        delete from t_plat_organ where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCountbyOrgType" resultType="Integer" parameterType="String">
        select count(*) from t_plat_organ where ORG_TYPE = #{orgTypeId}
    </select>
    <select id="newPlat" resultType="TPlatOrgan">
        select a.monthAddCount, b.totalCount
        from (select count(0) monthAddCount
        from t_plat_organ
        where DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= date(create_time)
     ) a,
        (select count(0) totalCount
        from t_plat_organ)
     b
    </select>
</mapper>