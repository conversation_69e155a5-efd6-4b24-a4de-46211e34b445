<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.plat.mapper.TPlatUserMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="3600000"/>
        <property name="size" value="1024"/>
        <property name="readOnly" value="false"/>
    </cache>
    <resultMap type="TPlatUser" id="TPlatUserResult">
        <result property="id" column="ID"/>
        <result property="appUserid" column="APP_USERID"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="loginName" column="LOGIN_NAME"/>
        <result property="loginPass" column="LOGIN_PASS"/>
        <result property="userName" column="USER_NAME"/>
        <result property="userSex" column="USER_SEX"/>
        <result property="userArea" column="USER_AREA"/>
        <result property="orderStatus" column="ORDER_STATUS"/>
        <result property="wxName" column="WX_NAME"/>
        <result property="wxCoverUrl" column="WX_COVER_URL"/>
        <result property="userTel" column="USER_TEL"/>
        <result property="userEmail" column="USER_EMAIL"/>
        <result property="userType" column="USER_TYPE"/>
        <result property="remark" column="REMARK"/>
        <result property="userSource" column="USER_SOURCE"/>
        <result property="userStatus" column="USER_STATUS"/>
        <result property="lastLoginTime" column="LAST_LOGIN_TIME"/>
        <result property="lastModpassTime" column="LAST_MODPASS_TIME"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="department" column="DEPARTMENT"/>
        <result property="professionTitle" column="PROFESSION_TITLE"/>
        <result property="clientIp" column="CLIENT_IP"/>
        <result property="ipArea" column="IP_AREA"/>
        <result property="realName" column="REAL_NAME"/>
    </resultMap>

    <sql id="selectTPlatUserVo">
        select ORDER_STATUS,
               ID,
               DEPARTMENT,
               PROFESSION_TITLE,
               APP_USERID,
               ORG_ID,
               LOGIN_NAME,
               LOGIN_PASS,
               USER_NAME,
               USER_SEX,
               USER_AREA,
               WX_NAME,
               WX_COVER_URL,
               USER_TEL,
               USER_EMAIL,
               USER_TYPE,
               REMARK,
               USER_SOURCE,
               USER_STATUS,
               LAST_LOGIN_TIME,
               LAST_MODPASS_TIME,
               CREATEBY_ID,
               CREATEBY_NAME,
               CREATE_TIME,
               UPDATEBY_ID,
               UPDATEBY_NAME,
               UPDATE_TIME,
               DEL_FLAG,
               CLIENT_IP,
               IP_AREA,
               REAL_NAME
        from t_plat_user
    </sql>

    <select id="selectTPlatUserList" parameterType="TPlatUser" resultMap="TPlatUserResult">
        <include refid="selectTPlatUserVo"/>
        <where>
            <if test="userName != null  and userName != ''">and USER_NAME like concat('%', #{userName}, '%')</if>
            <if test="userTel != null  and userTel != ''">and USER_TEL like concat('%', #{userTel}, '%')</if>
            <if test="userEmail != null  and userEmail != ''">and USER_EMAIL = #{userEmail}</if>
            <if test="loginName != null  and loginName != ''">and LOGIN_NAME = #{loginName}</if>
            <if test="loginPass != null  and loginPass != ''">and LOGIN_PASS = #{loginPass}</if>
            <if test="appUserid != null  and appUserid != ''">and APP_USERID = #{appUserid}</if>
            <if test="orgId != null  and orgId != ''">and ORG_ID = #{orgId}</if>
            <if test="userType != null  and userType != ''">and USER_TYPE = #{userType}</if>
            <if test="wxName != null  and wxName != ''">and WX_NAME like concat('%', #{wxName}, '%') or USER_NAME like
                concat('%', #{wxName}, '%')
            </if>
            <if test="orderStatus != null  and orderStatus != ''">and ORDER_STATUS = #{orderStatus}</if>
            <if test="userStatus != null  and userStatus != ''">and USER_STATUS = #{userStatus}</if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectTPlatUserListByName" parameterType="TPlatUser" resultMap="TPlatUserResult">
        <include refid="selectTPlatUserVo"/>
        <where>
            <if test="userName != null  and userName != ''">and USER_NAME = #{userName}</if>
        </where>
    </select>

    <select id="selectTPlatUserById" parameterType="String" resultMap="TPlatUserResult">
        <include refid="selectTPlatUserVo"/>
        where ID = #{id}
    </select>
    
    <select id="selectUserById" parameterType="String" resultMap="TPlatUserResult">
          select 
           	   u.ORDER_STATUS,
               u.ID,
               u.DEPARTMENT,
               u.PROFESSION_TITLE,
               u.APP_USERID,
               u.ORG_ID,
               u.LOGIN_NAME,
               u.LOGIN_PASS,
               u.USER_NAME,
               u.USER_SEX,
               u.USER_AREA,
               u.WX_NAME,
               u.WX_COVER_URL,
               u.USER_TEL,
               u.USER_EMAIL,
               u.USER_TYPE,
               u.REMARK,
               u.USER_SOURCE,
               u.USER_STATUS,
               u.LAST_LOGIN_TIME,
               u.LAST_MODPASS_TIME,
               u.CREATEBY_ID,
               u.CREATEBY_NAME,
               u.CREATE_TIME,
               u.UPDATEBY_ID,
               u.UPDATEBY_NAME,
               u.UPDATE_TIME,
               u.DEL_FLAG,
               u.CLIENT_IP,
               u.IP_AREA,
               u.REAL_NAME,
               g.ORG_NAME
               
        from t_plat_user u
        left join t_plat_organ g on u.org_id = g.id
        where u.ID = #{id}
    </select>

    <insert id="insertTPlatUser" parameterType="TPlatUser">
        insert into t_plat_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="department != null">DEPARTMENT,</if>
            <if test="professionTitle != null">PROFESSION_TITLE,</if>
            <if test="appUserid!=null">APP_USERID,</if>
            <if test="orderStatus!=null">ORDER_STATUS,</if>
            <if test="orgId != null">ORG_ID,</if>
            <if test="loginName != null">LOGIN_NAME,</if>
            <if test="loginPass != null">LOGIN_PASS,</if>
            <if test="userName != null">USER_NAME,</if>
            <if test="wxName != null">WX_NAME,</if>
            <if test="wxCoverUrl != null">WX_COVER_URL,</if>
            <if test="userTel != null">USER_TEL,</if>
            <if test="userEmail != null">USER_EMAIL,</if>
            <if test="userType != null">USER_TYPE,</if>
            <if test="userSex != null">USER_SEX,</if>
            <if test="userArea != null">USER_AREA,</if>
            <if test="remark != null">REMARK,</if>
            <if test="userSource != null">USER_SOURCE,</if>
            <if test="userStatus != null">USER_STATUS,</if>
            <if test="lastLoginTime != null">LAST_LOGIN_TIME,</if>
            <if test="lastModpassTime != null">LAST_MODPASS_TIME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="qqName != null">QQ_NAME,</if>
            <if test="qqCoverUrl != null">QQ_COVER_URL,</if>
            <if test="clientIp != null">CLIENT_IP,</if>
            <if test="ipArea != null">IP_AREA,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="department != null">#{department},</if>
            <if test="professionTitle != null">#{professionTitle},</if>
            <if test="appUserid!=null">#{appUserid},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="loginPass != null">#{loginPass},</if>
            <if test="userName != null">#{userName},</if>
            <if test="wxName != null">#{wxName},</if>
            <if test="wxCoverUrl != null">#{wxCoverUrl},</if>
            <if test="userTel != null">#{userTel},</if>
            <if test="userEmail != null">#{userEmail},</if>
            <if test="userType != null">#{userType},</if>
            <if test="userSex != null">#{userSex},</if>
            <if test="userArea != null">#{userArea},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userSource != null">#{userSource},</if>
            <if test="userStatus != null">#{userStatus},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="lastModpassTime != null">#{lastModpassTime},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="qqName != null"> #{qqName},</if>
            <if test="qqCoverUrl != null">#{qqCoverUrl},</if>
            <if test="clientIp != null">#{clientIp},</if>
            <if test="ipArea != null">#{ipArea},</if>
        </trim>
    </insert>

    <update id="updateTPlatUser" parameterType="TPlatUser">
        update t_plat_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="department != null">DEPARTMENT=#{department},</if>
            <if test="professionTitle != null">PROFESSION_TITLE=#{professionTitle},</if>
            <if test="orgId != null">ORG_ID = #{orgId},</if>
            <if test="orderStatus != null">ORDER_STATUS=#{orderStatus},</if>
            <if test="appUserid!=null">APP_USERID = #{appUserid},</if>
            <if test="loginName != null">LOGIN_NAME = #{loginName},</if>
            <if test="loginPass != null">LOGIN_PASS = #{loginPass},</if>
            <if test="userName != null">USER_NAME = #{userName},</if>
            <if test="wxName != null">WX_NAME = #{wxName},</if>
            <if test="wxCoverUrl != null">WX_COVER_URL = #{wxCoverUrl},</if>
            <if test="userTel != null">USER_TEL = #{userTel},</if>
            <if test="userEmail != null">USER_EMAIL = #{userEmail},</if>
            <if test="userType != null">USER_TYPE = #{userType},</if>
            <if test="userSex != null">USER_SEX = #{userSex},</if>
            <if test="userArea != null">USER_AREA = #{userArea},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="userSource != null">USER_SOURCE = #{userSource},</if>
            <if test="userStatus != null">USER_STATUS = #{userStatus},</if>
            <if test="lastLoginTime != null">LAST_LOGIN_TIME = #{lastLoginTime},</if>
            <if test="lastModpassTime != null">LAST_MODPASS_TIME = #{lastModpassTime},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="qqName != null">QQ_NAME =#{qqName},</if>
            <if test="qqCoverUrl != null">QQ_COVER_URL =#{qqCoverUrl},</if>
            <if test="clientIp != null">CLIENT_IP =#{clientIp},</if>
            <if test="ipArea != null">IP_AREA =#{ipArea},</if>
            <if test="realName != null">REAL_NAME =#{realName},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updateByOpenId" parameterType="String">
        update t_plat_user
        set
        <if test="userSex != null">USER_SEX =#{userSex}</if>
        <if test="wxName != null">WX_NAME =#{wxName}</if>
        <if test="userSex != null">USER_SEX =#{userSex}</if>
        <if test="qqName != null">QQ_NAME =#{qqName}</if>
        <if test="qqCoverUrl != null">QQ_COVER_URL =#{qqCoverUrl}</if>
        where APP_USERID = #{appUserid}
    </update>

    <delete id="deleteTPlatUserById" parameterType="String">
        delete
        from t_plat_user
        where ID = #{id}
    </delete>

    <delete id="deleteTPlatUserByIds" parameterType="String">
        delete from t_plat_user where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTPlatUserByOrgIds" parameterType="String">
        delete from t_plat_user where ORG_ID in
        <foreach item="orgId" collection="array" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </delete>

    <select id="selectLoginUser" parameterType="TPlatUser" resultMap="TPlatUserResult">
        <include refid="selectTPlatUserVo"/>
        <where>
            and USER_SOURCE!='Z'
            <if test="userTel != null  and userTel != ''">and (USER_TEL = #{userTel} or USER_NAME = #{userTel}) </if>
            <if test="userEmail != null  and userEmail != ''">and USER_EMAIL = #{userEmail}</if>
        </where>
    </select>
    <select id="selectByUnionid" resultMap="TPlatUserResult">
        <include refid="selectTPlatUserVo"/>
        where 1=1
        and APP_USERID=#{appUserid}

    </select>


</mapper>