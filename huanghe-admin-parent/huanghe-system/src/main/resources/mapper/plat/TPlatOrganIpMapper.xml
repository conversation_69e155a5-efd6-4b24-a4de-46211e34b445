<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.plat.mapper.TPlatOrganIpMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TPlatOrganIp" id="TPlatOrganIpResult">
        <result property="id"    column="ID"    />
        <result property="orgId"    column="ORG_ID"    />
        <result property="ipRange"    column="IP_RANGE"    />
        <result property="ipDesc"    column="IP_DESC"    />
        <result property="status"    column="STATUS"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTPlatOrganIpVo">
        select ID, ORG_ID, IP_RANGE, IP_DESC, STATUS, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_plat_organ_ip
    </sql>

    <select id="selectTPlatOrganIpList" parameterType="TPlatOrganIp" resultMap="TPlatOrganIpResult">
        <include refid="selectTPlatOrganIpVo"/>
        <where>  
            <if test="orgId != null  and orgId != ''"> and ORG_ID = #{orgId}</if>
        </where>
        order by IP_RANGE
    </select>
    
    <select id="selectTPlatOrganIpById" parameterType="String" resultMap="TPlatOrganIpResult">
        <include refid="selectTPlatOrganIpVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTPlatOrganIp" parameterType="TPlatOrganIp">
        insert into t_plat_organ_ip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="orgId != null and orgId != ''">ORG_ID,</if>
            <if test="ipRange != null">IP_RANGE,</if>
            <if test="ipDesc != null">IP_DESC,</if>
            <if test="status != null">STATUS,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orgId != null and orgId != ''">#{orgId},</if>
            <if test="ipRange != null">#{ipRange},</if>
            <if test="ipDesc != null">#{ipDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTPlatOrganIp" parameterType="TPlatOrganIp">
        update t_plat_organ_ip
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null and orgId != ''">ORG_ID = #{orgId},</if>
            <if test="ipRange != null">IP_RANGE = #{ipRange},</if>
            <if test="ipDesc != null">IP_DESC = #{ipDesc},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTPlatOrganIpById" parameterType="String">
        delete from t_plat_organ_ip where ID = #{id}
    </delete>

    <delete id="deleteTPlatOrganIpByIds" parameterType="String">
        delete from t_plat_organ_ip where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTPlatOrganIpByOrgId" parameterType="String">
        delete from t_plat_organ_ip where ORG_ID = #{orgId}
    </delete>

    <delete id="deleteTPlatOrganIpByOrgIds" parameterType="String">
        delete from t_plat_organ_ip where ORG_ID in
        <foreach item="orgId" collection="array" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </delete>
</mapper>