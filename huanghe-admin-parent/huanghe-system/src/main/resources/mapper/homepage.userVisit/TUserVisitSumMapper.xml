<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.homePage.mapper.TUserVisitSumMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserVisitSum" id="TUserVisitSumResult">
        <result property="id"    column="ID"    />
        <result property="orgId"    column="ORG_ID"    />
        <result property="orgName"    column="ORG_NAME"    />
        <result property="searchCount"    column="SEARCH_COUNT"    />
        <result property="visitCount"    column="VISIT_COUNT"    />
        <result property="loginCount"    column="LOGIN_COUNT"    />
        <result property="sumDate"    column="SUM_DATE"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="userSource"    column="USER_SOURCE"    />
        <result property="loginForm"    column="LOGIN_FORM"    />
    </resultMap>

    <sql id="selectTUserVisitSumVo">
        select ID, ORG_ID, ORG_NAME, SEARCH_COUNT, VISIT_COUNT, LOGIN_COUNT, SUM_DATE, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,USER_SOURCE,LOGIN_FORM from t_user_visit_sum
    </sql>

    <select id="selectTUserVisitSumList" parameterType="TUserVisitSum" resultMap="TUserVisitSumResult">
        <include refid="selectTUserVisitSumVo"/>
        <where>  
            <if test="orgId != null  and orgId != ''"> and ORG_ID = #{orgId}</if>
            <if test="orgName != null  and orgName != ''"> and ORG_NAME like concat('%', #{orgName}, '%')</if>
            <if test="searchCount != null "> and SEARCH_COUNT = #{searchCount}</if>
            <if test="visitCount != null "> and VISIT_COUNT = #{visitCount}</if>
            <if test="loginCount != null "> and LOGIN_COUNT = #{loginCount}</if>
            <if test="sumDate != null  and sumDate != ''"> and SUM_DATE = #{sumDate}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>            
        </where>

    </select>
    
    <select id="selectTUserVisitSumById" parameterType="String" resultMap="TUserVisitSumResult">
        <include refid="selectTUserVisitSumVo"/>
        where ID = #{id}
    </select>



    <select id="organCount" resultMap="TUserVisitSumResult"  resultType="TUserVisitSum">
        select 
        sum(LOGIN_COUNT) as LOGIN_COUNT,
		sum(VISIT_COUNT) as VISIT_COUNT,
		sum(SEARCH_COUNT) as SEARCH_COUNT,
		ORG_NAME
        from t_user_visit_sum
        where 1=1
        <if test="day != null ">
        and DATE_SUB(CURDATE(), INTERVAL #{day} DAY)&lt;= date(UPDATE_TIME)
        </if>
		GROUP BY ORG_NAME
        order by VISIT_COUNT desc
    </select>
    <select id="selectVisits" resultType="cn.guliandigital.homePage.domain.TUserVisitSum">
    select a.visitCount, b.totalCount ,a.loginCount, b.totalCountLogin
        from (select sum(VISIT_COUNT) visitCount,sum(LOGIN_COUNT) loginCount
        from t_user_visit_sum
        where DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt;= date(UPDATE_TIME)
        ) a,
        (select sum(VISIT_COUNT) totalCount,sum(LOGIN_COUNT) totalCountLogin
        from t_user_visit_sum
        ) b
    </select>
    <select id="selectTUserVisitSumListByTwelve" resultMap="TUserVisitSumResult" parameterType="TUserVisitSum">
        SELECT
            ID,
            ORG_ID,
            ORG_NAME,
            SEARCH_COUNT,
            VISIT_COUNT,
            LOGIN_COUNT,
            SUM_DATE,
            CREATEBY_ID,
            CREATEBY_NAME,
            CREATE_TIME,
            UPDATEBY_ID,
            UPDATEBY_NAME,
            UPDATE_TIME,
            DEL_FLAG,
            USER_SOURCE,
            LOGIN_FORM
        FROM
            t_user_visit_sum
        WHERE
            DEL_FLAG = 0
          AND SUM_DATE >= DATE_SUB(
                CURDATE(),
                INTERVAL 11 MONTH)

    </select>

    <insert id="insertTUserVisitSum" parameterType="TUserVisitSum">
        insert into t_user_visit_sum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="orgId != null and orgId != ''">ORG_ID,</if>
            <if test="orgName != null">ORG_NAME,</if>
            <if test="searchCount != null">SEARCH_COUNT,</if>
            <if test="visitCount != null">VISIT_COUNT,</if>
            <if test="loginCount != null">LOGIN_COUNT,</if>
            <if test="sumDate != null">SUM_DATE,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orgId != null and orgId != ''">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="visitCount != null">#{visitCount},</if>
            <if test="loginCount != null">#{loginCount},</if>
            <if test="sumDate != null">#{sumDate},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTUserVisitSum" parameterType="TUserVisitSum">
        update t_user_visit_sum
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null and orgId != ''">ORG_ID = #{orgId},</if>
            <if test="orgName != null">ORG_NAME = #{orgName},</if>
            <if test="searchCount != null">SEARCH_COUNT = #{searchCount},</if>
            <if test="visitCount != null">VISIT_COUNT = #{visitCount},</if>
            <if test="loginCount != null">LOGIN_COUNT = #{loginCount},</if>
            <if test="sumDate != null">SUM_DATE = #{sumDate},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserVisitSumById" parameterType="String">
        delete from t_user_visit_sum where ID = #{id}
    </delete>

    <delete id="deleteTUserVisitSumByIds" parameterType="String">
        delete from t_user_visit_sum where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>