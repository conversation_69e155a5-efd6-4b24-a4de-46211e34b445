<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TWxampNoticeMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TWxampNotice" id="TWxampNoticeResult">
        <result property="id"    column="ID"    />
        <result property="type"    column="TYPE"    />
        <result property="title"    column="TITLE"    />
        <result property="content"    column="CONTENT"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTWxampNoticeVo">
        select ID, TYPE, TITLE, CONTENT, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_wxamp_notice
    </sql>

    <select id="selectTWxampNoticeList" parameterType="TWxampNotice" resultMap="TWxampNoticeResult">
        <include refid="selectTWxampNoticeVo"/>
        <where>  
            <if test="type != null  and type != ''"> and TYPE = #{type}</if>
            <if test="title != null  and title != ''"> and TITLE = #{title}</if>
            <if test="content != null  and content != ''"> and CONTENT = #{content}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
    </select>
    
    <select id="selectTWxampNoticeById" parameterType="Integer" resultMap="TWxampNoticeResult">
        <include refid="selectTWxampNoticeVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTWxampNotice" parameterType="TWxampNotice" useGeneratedKeys="true" keyProperty="id">
        insert into t_wxamp_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">TYPE,</if>
            <if test="title != null">TITLE,</if>
            <if test="content != null">CONTENT,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTWxampNotice" parameterType="TWxampNotice">
        update t_wxamp_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">TYPE = #{type},</if>
            <if test="title != null">TITLE = #{title},</if>
            <if test="content != null">CONTENT = #{content},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTWxampNoticeById" parameterType="Integer">
        delete from t_wxamp_notice where ID = #{id}
    </delete>

    <delete id="deleteTWxampNoticeByIds" parameterType="String">
        delete from t_wxamp_notice where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByType" parameterType="String">
        delete from t_wxamp_notice where type = #{type}
    </delete>

</mapper>