<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TSysProjectMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TSysProject" id="TSysProjectResult">
        <result property="id"    column="ID"    />
        <result property="title"    column="TITLE"    />
        <result property="contentType"    column="CONTENT_TYPE"    />
        <result property="content"    column="CONTENT"    />
        <result property="iconUrl"    column="ICON_URL"    />
        <result property="publishStatus"    column="PUBLISH_STATUS"    />
        <result property="publishTime"    column="PUBLISH_TIME"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="display"    column="DISPLAY"    />
    </resultMap>

    <sql id="selectTSysProjectVo">
        select ID, TITLE, CONTENT_TYPE, CONTENT, ICON_URL, PUBLISH_STATUS, PUBLISH_TIME, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,DISPLAY from t_sys_project
    </sql>

    <select id="selectTSysProjectList" parameterType="TSysProject" resultMap="TSysProjectResult">
        <include refid="selectTSysProjectVo"/>
        <where>
            <if test="title != null  and title != ''"> and TITLE like concat('%', #{title}, '%')</if>
            <if test="contentType != null "> and CONTENT_TYPE = #{contentType}</if>
            <if test="publishStatus != null "> and PUBLISH_STATUS = #{publishStatus}</if>
        </where>

        order by PUBLISH_STATUS asc,DISPLAY asc
    </select>
    
    <select id="selectTSysProjectById" parameterType="String" resultMap="TSysProjectResult">
        <include refid="selectTSysProjectVo"/>
        where ID = #{id}

    </select>
        
    <insert id="insertTSysProject" parameterType="TSysProject">
        insert into t_sys_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="title != null">TITLE,</if>
            <if test="contentType != null">CONTENT_TYPE,</if>
            <if test="content != null">CONTENT,</if>
            <if test="iconUrl != null">ICON_URL,</if>
            <if test="publishStatus != null">PUBLISH_STATUS,</if>
            <if test="publishTime != null">PUBLISH_TIME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="display != null">DISPLAY,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="content != null">#{content},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="display != null">#{display},</if>
         </trim>
    </insert>

    <update id="updateTSysProject" parameterType="TSysProject">
        update t_sys_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">TITLE = #{title},</if>
            <if test="contentType != null">CONTENT_TYPE = #{contentType},</if>
            <if test="content != null">CONTENT = #{content},</if>
            <if test="iconUrl != null">ICON_URL = #{iconUrl},</if>
            <if test="publishStatus != null">PUBLISH_STATUS = #{publishStatus},</if>
            <if test="publishTime != null">PUBLISH_TIME = #{publishTime},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="display != null">DISPLAY = #{display},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTSysProjectById" parameterType="String">
        delete from t_sys_project where ID = #{id}
    </delete>

    <delete id="deleteTSysProjectByIds" parameterType="String">
        delete from t_sys_project where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>