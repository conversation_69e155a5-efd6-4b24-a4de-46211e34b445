<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TSysNewsMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TSysNews" id="TSysNewsResult">
        <result property="id"    column="ID"    />
        <result property="newsTitle"    column="NEWS_TITLE"    />
        <result property="newsContent"    column="NEWS_CONTENT"    />
        <result property="iconUrl"    column="ICON_URL"    />
        <result property="publishStatus"    column="PUBLISH_STATUS"    />
        <result property="publishTime"    column="PUBLISH_TIME"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="readCount"    column="READ_COUNT"    />
        <result property="source"    column="SOURCE"    />
        <result property="display"    column="DISPLAY"    />
    </resultMap>

    <sql id="selectTSysNewsVo">
        select ID, NEWS_TITLE, NEWS_CONTENT, ICON_URL, PUBLISH_STATUS, PUBLISH_TIME, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,READ_COUNT,SOURCE,DISPLAY from t_sys_news
    </sql>

    <select id="selectTSysNewsList" parameterType="TSysNews" resultMap="TSysNewsResult">
        <include refid="selectTSysNewsVo"/>
        <where>  
            <if test="newsTitle != null  and newsTitle != ''"> and NEWS_TITLE like concat('%', #{newsTitle}, '%')</if>
            <if test="publishStatus != null "> and PUBLISH_STATUS = #{publishStatus}</if>
        </where>
        order by  CREATE_TIME desc
    </select>
    
    <select id="selectTSysNewsById" parameterType="String" resultMap="TSysNewsResult">
        <include refid="selectTSysNewsVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTSysNews" parameterType="TSysNews">
        insert into t_sys_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="newsTitle != null">NEWS_TITLE,</if>
            <if test="newsContent != null">NEWS_CONTENT,</if>
            <if test="iconUrl != null">ICON_URL,</if>
            <if test="publishStatus != null">PUBLISH_STATUS,</if>
            <if test="publishTime != null">PUBLISH_TIME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="readCount != null">READ_COUNT,</if>
            <if test="source != null">SOURCE,</if>
            <if test="display != null">DISPLAY,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="newsTitle != null">#{newsTitle},</if>
            <if test="newsContent != null">#{newsContent},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="readCount != null">#{readCount},</if>
            <if test="source != null">#{source},</if>
            <if test="display != null">#{display},</if>
         </trim>
    </insert>

    <update id="updateTSysNews" parameterType="TSysNews">
        update t_sys_news
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsTitle != null">NEWS_TITLE = #{newsTitle},</if>
            <if test="newsContent != null">NEWS_CONTENT = #{newsContent},</if>
            <if test="iconUrl != null">ICON_URL = #{iconUrl},</if>
            <if test="publishStatus != null">PUBLISH_STATUS = #{publishStatus},</if>
            <if test="publishTime != null">PUBLISH_TIME = #{publishTime},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="readCount != null">READ_COUNT = #{readCount},</if>
            <if test="source != null">SOURCE = #{source},</if>
            <if test="display != null">DISPLAY = #{display},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTSysNewsById" parameterType="String">
        delete from t_sys_news where ID = #{id}
    </delete>

    <delete id="deleteTSysNewsByIds" parameterType="String">
        delete from t_sys_news where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>