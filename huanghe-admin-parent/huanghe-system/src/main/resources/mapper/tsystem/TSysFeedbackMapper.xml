<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TSysFeedbackMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TSysFeedback" id="TSysFeedbackResult">
        <result property="id"    column="ID"    />
        <result property="recContent"    column="REC_CONTENT"    />
        <result property="userId"    column="USER_ID"    />
        <result property="handleStatus"    column="HANDLE_STATUS"    />
        <result property="isLook"    column="is_look"    />
        <result property="handlebyId"    column="HANDLEBY_ID"    />
        <result property="handlebyName"    column="HANDLEBY_NAME"    />
        <result property="handleTime"    column="HANDLE_TIME"    />
        <result property="dataFrom"    column="DATA_FROM"    />
        <result property="feedbackContent"    column="FEEDBACK_CONTENT"    />
        <result property="bookMenuId"    column="BOOK_MENU_ID"    />
        <result property="bookMenuName"    column="BOOK_MENU_NAME"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="userName"    column="USER_NAME"    />
        <result property="userTel"    column="USER_TEL"    />
        <result property="userEmail"    column="USER_EMAIL"    />
        <result property="bookName"    column="BOOK_NAME"    />
        <result property="title"    column="TITLE"    />
    </resultMap>

    <sql id="selectTSysFeedbackVo">
        select f.TITLE, f.ID, REC_CONTENT, USER_ID, HANDLE_STATUS, HANDLEBY_ID, HANDLEBY_NAME, HANDLE_TIME, DATA_FROM, FEEDBACK_CONTENT, f.CREATEBY_ID, f.CREATEBY_NAME, f.CREATE_TIME, f.UPDATEBY_ID, f.UPDATEBY_NAME, f.UPDATE_TIME,
        USER_NAME,USER_EMAIL,USER_TEL,BOOK_MENU_ID,BOOK_MENU_NAME,BOOK_NAME
        from
          t_sys_feedback f
        left join
          t_plat_user u
        on
          USER_ID = u.ID
        left join
          t_pro_book_menu m
        on
          BOOK_MENU_ID = m.ID
        left join
          t_pro_books b
        on
          m.BOOK_ID = b.ID
    </sql>

    <select id="selectTSysFeedbackList" parameterType="TSysFeedback" resultMap="TSysFeedbackResult">
        <include refid="selectTSysFeedbackVo"/>
        <where>
            <if test="recContent != null  and recContent != ''"> and REC_CONTENT like concat('%', #{recContent}, '%')</if>
            <if test="userName != null  and userName != ''"> and USER_NAME like concat('%', #{userName}, '%')</if>
            <if test="userTel != null  and userTel != ''"> and USER_TEL like concat('%', #{userTel}, '%')</if>
            <if test="handleStatus != null "> and HANDLE_STATUS = #{handleStatus}</if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(f.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(f.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="isLook != null"> and is_look = #{isLook}</if>
        </where>
        ORDER BY HANDLE_STATUS desc, CASE WHEN HANDLE_STATUS = 1 THEN f.HANDLE_TIME  ELSE f.CREATE_TIME END DESC
    </select>
    
    <select id="selectTSysFeedbackById" parameterType="String" resultMap="TSysFeedbackResult">
        <include refid="selectTSysFeedbackVo"/>
        where f.ID = #{id}
    </select>
        
    <insert id="insertTSysFeedback" parameterType="TSysFeedback">
        insert into t_sys_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="recContent != null">REC_CONTENT,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="handleStatus != null">HANDLE_STATUS,</if>
            <if test="handlebyId != null">HANDLEBY_ID,</if>
            <if test="handlebyName != null">HANDLEBY_NAME,</if>
            <if test="handleTime != null">HANDLE_TIME,</if>
            <if test="dataFrom != null">DATA_FROM,</if>
            <if test="feedbackContent != null">FEEDBACK_CONTENT,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="bookMenuId != null">BOOK_MENU_ID,</if>
            <if test="bookMenuName != null">BOOK_MENU_NAME,</if>
            <if test="title != null">TITLE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="recContent != null">#{recContent},</if>
            <if test="userId != null">#{userId},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="handlebyId != null">#{handlebyId},</if>
            <if test="handlebyName != null">#{handlebyName},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="dataFrom != null">#{dataFrom},</if>
            <if test="feedbackContent != null">#{feedbackContent},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="bookMenuId != null">#{bookMenuId},</if>
            <if test="bookMenuName != null">#{bookMenuName},</if>
            <if test="title != null">#{title},</if>
         </trim>
    </insert>

    <update id="updateTSysFeedback" parameterType="TSysFeedback">
        update t_sys_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="recContent != null">REC_CONTENT = #{recContent},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="handleStatus != null">HANDLE_STATUS = #{handleStatus},</if>
            <if test="isLook != null">is_look = #{isLook},</if>
            <if test="handlebyId != null">HANDLEBY_ID = #{handlebyId},</if>
            <if test="handlebyName != null">HANDLEBY_NAME = #{handlebyName},</if>
            <if test="handleTime != null">HANDLE_TIME = #{handleTime},</if>
            <if test="dataFrom != null">DATA_FROM = #{dataFrom},</if>
            <if test="feedbackContent != null">FEEDBACK_CONTENT = #{feedbackContent},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="bookMenuId != null">BOOK_MENU_ID = #{bookMenuId},</if>
            <if test="bookMenuName != null">BOOK_MENU_NAME = #{bookMenuName},</if>
            <if test="title != null">TITLE = #{title},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTSysFeedbackById" parameterType="String">
        delete from t_sys_feedback where ID = #{id}
    </delete>

    <delete id="deleteTSysFeedbackByIds" parameterType="String">
        delete from t_sys_feedback where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>