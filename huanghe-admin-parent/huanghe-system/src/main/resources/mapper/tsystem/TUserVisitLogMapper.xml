<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.pvlog.mapper.TUserVisitLogMapper">
	<cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserVisitLog" id="TUserVisitLogResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="businessType" column="business_type"/>
        <result property="operName" column="oper_name"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operLocation" column="oper_location"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
        <result property="orgId" column="org_id"/>
        <result property="userSource"    column="USER_SOURCE"    />
        <result property="loginForm"    column="LOGIN_FORM"    />
        <result property="visitCount"    column="visitCount"    />
    </resultMap>

    <sql id="selectTUserVisitLogVo">
        select id, title, business_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, org_id,error_msg, oper_time,USER_SOURCE,LOGIN_FORM from t_user_visit_log
    </sql>

    <select id="selectTUserVisitLogList" parameterType="TUserVisitLog" resultMap="TUserVisitLogResult">
        <include refid="selectTUserVisitLogVo"/>
        <where>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="businessType != null ">and business_type = #{businessType}</if>
            <if test="operName != null  and operName != ''">and oper_name like concat('%', #{operName}, '%')</if>
            <if test="operUrl != null  and operUrl != ''">and oper_url = #{operUrl}</if>
            <if test="operIp != null  and operIp != ''">and oper_ip = #{operIp}</if>
            <if test="operLocation != null  and operLocation != ''">and oper_location = #{operLocation}</if>
            <if test="operParam != null  and operParam != ''">and oper_param = #{operParam}</if>
            <if test="jsonResult != null  and jsonResult != ''">and json_result = #{jsonResult}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="errorMsg != null  and errorMsg != ''">and error_msg = #{errorMsg}</if>
            <if test="operTime != null ">and oper_time = #{operTime}</if>
            <if test="orgId != null ">and org_id = #{orgId}</if>
        </where>
    </select>

    <select id="selectTUserVisitLogById" parameterType="Long" resultMap="TUserVisitLogResult">
        <include refid="selectTUserVisitLogVo"/>
        where id = #{id}
    </select>
    <select id="selectVisits" resultType="cn.guliandigital.pvlog.domain.TUserVisitLog">
        <if test="day != null ">
            SELECT T.time timeForVisits,COUNT(T.oper_ip) count FROM
            (SELECT HOUR(oper_time) time,oper_time,oper_ip FROM t_user_visit_log) T
            WHERE DATE_FORMAT(oper_time, '%Y-%m-%d') >=
            ((DATE_SUB(current_date(), INTERVAL #{day} DAY))) AND
            DATE_FORMAT(oper_time, '%Y-%m-%d') &lt;=
            ((DATE_SUB(current_date(), INTERVAL 0 DAY)))
            GROUP BY T.time
        </if>
    </select>
    <select id="aloneVisitsCounts" resultType="cn.guliandigital.pvlog.domain.TUserVisitLog" resultMap="TUserVisitLogResult">

		<if test="day != null ">
            SELECT T.time timeForVisits,COUNT(distinct  T.oper_ip) visitCount FROM
            (SELECT HOUR(oper_time) time,oper_time,oper_ip FROM t_user_visit_log) T
            WHERE DATE_FORMAT(oper_time, '%Y-%m-%d') >=
            ((DATE_SUB(current_date(), INTERVAL #{day} DAY))) AND
            DATE_FORMAT(oper_time, '%Y-%m-%d') &lt;=
            ((DATE_SUB(current_date(), INTERVAL 0 DAY)))
            GROUP BY T.time
        </if>
    </select>

    <insert id="insertTUserVisitLog" parameterType="TUserVisitLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_user_visit_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="businessType != null">business_type,</if>
            <if test="operName != null">oper_name,</if>
            <if test="operUrl != null">oper_url,</if>
            <if test="operIp != null">oper_ip,</if>
            <if test="operLocation != null">oper_location,</if>
            <if test="operParam != null">oper_param,</if>
            <if test="jsonResult != null">json_result,</if>
            <if test="status != null">status,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="operTime != null">oper_time,</if>
            <if test="orgId != null">org_id,</if>
            <if test="userSource != null">USER_SOURCE,</if>
            <if test="loginForm != null">LOGIN_FORM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="operName != null">#{operName},</if>
            <if test="operUrl != null">#{operUrl},</if>
            <if test="operIp != null">#{operIp},</if>
            <if test="operLocation != null">#{operLocation},</if>
            <if test="operParam != null">#{operParam},</if>
            <if test="jsonResult != null">#{jsonResult},</if>
            <if test="status != null">#{status},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="userSource != null">#{userSource},</if>
            <if test="loginForm != null">#{loginForm},</if>
        </trim>
    </insert>

    <update id="updateTUserVisitLog" parameterType="TUserVisitLog">
        update t_user_visit_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="operUrl != null">oper_url = #{operUrl},</if>
            <if test="operIp != null">oper_ip = #{operIp},</if>
            <if test="operLocation != null">oper_location = #{operLocation},</if>
            <if test="operParam != null">oper_param = #{operParam},</if>
            <if test="jsonResult != null">json_result = #{jsonResult},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="userSource != null">USER_SOURCE = #{userSource},</if>
            <if test="loginForm != null">LOGIN_FORM = #{loginForm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTUserVisitLogById" parameterType="Long">
        delete from t_user_visit_log where id = #{id}
    </delete>

    <delete id="deleteTUserVisitLogByIds" parameterType="String">
        delete from t_user_visit_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSumVisitLogList" parameterType="TUserVisitLog" resultMap="TUserVisitLogResult">
        SELECT
		org_id,
		business_type,
		count(0) as visitCount
		FROM
			t_user_visit_log
		WHERE
			1 = 1
		AND DATE_FORMAT(oper_time, '%Y-%m-%d') = #{beginTime}
		and org_id is not null
		GROUP BY
			org_id,
			business_type
    </select>
    
    <select id="selectSumLoginLogList" parameterType="TUserVisitLog" resultMap="TUserVisitLogResult">
        SELECT			
			USER_SOURCE,
			LOGIN_FORM,
			count( 0 ) AS visitCount 
			FROM
			t_user_visit_log 
		WHERE
			1 = 1
			and business_type = 2 
			AND DATE_FORMAT(oper_time, '%Y-%m-%d') = #{beginTime}
			
		GROUP BY			
			USER_SOURCE,
			LOGIN_FORM
    </select>
    
    <select id="testVisits" resultType="cn.guliandigital.pvlog.domain.TUserVisitLog">
        SELECT T.time timeForVisits,T.oper_time,COUNT(distinct T.oper_ip) visitCount FROM
        (SELECT HOUR(oper_time) time,oper_time ,oper_ip FROM t_user_visit_log) T
       WHERE DATE_FORMAT(oper_time, '%Y-%m-%d') >=
            ((DATE_SUB(current_date(), INTERVAL #{day} DAY))) AND
            DATE_FORMAT(oper_time, '%Y-%m-%d') &lt;=
            ((DATE_SUB(current_date(), INTERVAL 0 DAY)))
        GROUP BY T.time
    </select>
    <select id="testVisits1" resultType="cn.guliandigital.pvlog.domain.TUserVisitLog">
        select date_format(oper_time, '%m-%d') timeForVisits, count(oper_ip) count
        FROM t_user_visit_log
        WHERE DATE_FORMAT(oper_time, '%Y-%m-%d') >=
        ((DATE_SUB(current_date(), INTERVAL #{day} DAY))) AND
        DATE_FORMAT(oper_time, '%Y-%m-%d') &lt;=
        ((DATE_SUB(current_date(), INTERVAL 0 DAY)))
        group by timeForVisits
    </select>
    <select id="alone" resultType="cn.guliandigital.pvlog.domain.TUserVisitLog">
          select date_format(oper_time, '%m-%d') timeForVisits, count(distinct oper_ip) visitCount
        FROM t_user_visit_log
        WHERE DATE_FORMAT(oper_time, '%Y-%m-%d') >=
        ((DATE_SUB(current_date(), INTERVAL #{day} DAY))) AND
        DATE_FORMAT(oper_time, '%Y-%m-%d') &lt;=
        ((DATE_SUB(current_date(), INTERVAL 0 DAY)))
        group by timeForVisits
    </select>
</mapper>