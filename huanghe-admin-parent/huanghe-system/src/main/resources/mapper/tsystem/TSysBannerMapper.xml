<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TSysBannerMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TSysBanner" id="TSysBannerResult">
        <result property="id"    column="id"    />
        <result property="imageName"    column="image_name"    />
        <result property="imagePath"    column="image_path"    />
        <result property="status"    column="status"    />
        <result property="display"    column="display"    />
        <result property="createbyId"    column="createby_id"    />
        <result property="createbyName"    column="createby_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updatebyId"    column="updateby_id"    />
        <result property="updatebyName"    column="updateby_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectTSysBannerVo">
        select id, image_name, image_path, status, display, createby_id, createby_name, create_time, updateby_id, updateby_name, update_time, del_flag from t_sys_banner
    </sql>

    <select id="selectTSysBannerList" parameterType="TSysBanner" resultMap="TSysBannerResult">
        <include refid="selectTSysBannerVo"/>
        <where>
            <if test="imageName != null  and imageName != ''"> and image_name like concat('%', #{imageName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        <if test="display != null  and display != ''"> order by display desc,create_time desc</if>
    </select>
    
    <select id="selectTSysBannerById" parameterType="String" resultMap="TSysBannerResult">
        <include refid="selectTSysBannerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTSysBanner" parameterType="TSysBanner">
        insert into t_sys_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="imageName != null">image_name,</if>
            <if test="imagePath != null">image_path,</if>
            <if test="status != null">status,</if>
            <if test="display != null">display,</if>
            <if test="createbyId != null">createby_id,</if>
            <if test="createbyName != null">createby_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updatebyId != null">updateby_id,</if>
            <if test="updatebyName != null">updateby_name,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="imageName != null">#{imageName},</if>
            <if test="imagePath != null">#{imagePath},</if>
            <if test="status != null">#{status},</if>
            <if test="display != null">#{display},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTSysBanner" parameterType="TSysBanner">
        update t_sys_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="imagePath != null">image_path = #{imagePath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="display != null">display = #{display},</if>
            <if test="createbyId != null">createby_id = #{createbyId},</if>
            <if test="createbyName != null">createby_name = #{createbyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updatebyId != null">updateby_id = #{updatebyId},</if>
            <if test="updatebyName != null">updateby_name = #{updatebyName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSysBannerById" parameterType="String">
        delete from t_sys_banner where id = #{id}
    </delete>

    <delete id="deleteTSysBannerByIds" parameterType="String">
        delete from t_sys_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTSysBannerByIds" resultMap="TSysBannerResult">
        <include refid="selectTSysBannerVo"></include>
                where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
</mapper>