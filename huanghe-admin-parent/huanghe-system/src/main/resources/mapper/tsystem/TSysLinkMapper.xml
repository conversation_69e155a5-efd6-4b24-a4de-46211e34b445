<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.tsystem.mapper.TSysLinkMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TSysLink" id="TSysLinkResult">
        <result property="id"    column="ID"    />
        <result property="appId"    column="APP_ID"    />
        <result property="appPath"    column="APP_PATH"    />
        <result property="iconUrl"    column="ICON_URL"    />
        <result property="webName"    column="WEB_NAME"    />
        <result property="webUrl"    column="WEB_URL"    />
        <result property="webDisplay"    column="WEB_DISPLAY"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTSysLinkVo">
        select ID, WEB_NAME, WEB_URL, WEB_DISPLAY, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,ICON_URL,APP_ID,APP_PATH from t_sys_link
    </sql>

    <select id="selectTSysLinkList" parameterType="TSysLink" resultMap="TSysLinkResult">
        <include refid="selectTSysLinkVo"/>
        <where>  
            <if test="webName != null  and webName != ''"> and WEB_NAME like concat('%', #{webName}, '%')</if>
            <if test="webUrl != null  and webUrl != ''"> and WEB_URL = #{webUrl}</if>
            and  DEL_FLAG  = 0
        </where>

        order by WEB_DISPLAY
    </select>
    
    <select id="selectTSysLinkById" parameterType="String" resultMap="TSysLinkResult">
        <include refid="selectTSysLinkVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTSysLink" parameterType="TSysLink">
        insert into t_sys_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="appId != null">APP_ID,</if>
            <if test="appPath != null">APP_PATH,</if>
            <if test="iconUrl != null">ICON_URL,</if>

            <if test="webName != null">WEB_NAME,</if>
            <if test="webUrl != null">WEB_URL,</if>
            <if test="webDisplay != null">WEB_DISPLAY,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="appId != null">#{appId},</if>
            <if test="appPath != null">#{appPath},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="webName != null">#{webName},</if>
            <if test="webUrl != null">#{webUrl},</if>
            <if test="webDisplay != null">#{webDisplay},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTSysLink" parameterType="TSysLink">
        update t_sys_link
        <trim prefix="SET" suffixOverrides=",">

            <if test="iconUrl != null">ICON_URL = #{iconUrl},</if>
            <if test="appId != null">APP_ID = #{appId},</if>
            <if test="appPath != null">APP_PATH = #{appPath},</if>
            <if test="webName != null">WEB_NAME = #{webName},</if>
            <if test="webUrl != null">WEB_URL = #{webUrl},</if>
            <if test="webDisplay != null">WEB_DISPLAY = #{webDisplay},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTSysLinkById" parameterType="String">
        delete from t_sys_link where ID = #{id}
    </delete>

    <delete id="deleteTSysLinkByIds" parameterType="String">
        delete from t_sys_link where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTSysLinkDisplayMax" resultType="Integer">
        SELECT WEB_DISPLAY FROM t_sys_link order by WEB_DISPLAY desc limit 1
    </select>
</mapper>