<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.wxamp.mapper.TWxampCartMapper">
     
    <resultMap type="TWxampCart" id="TWxampCartResult">
        <result property="id"    column="ID"    />
        <result property="databaseId"    column="DATABASE_ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="quantity"    column="QUANTITY"    />
        <result property="selectStatus"    column="SELECT_STATUS"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTWxampCartVo">
        select ID, DATABASE_ID, USER_ID, QUANTITY, SELECT_STATUS, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_wxamp_cart
    </sql>

    <select id="selectTWxampCartList" parameterType="TWxampCart" resultMap="TWxampCartResult">
        <include refid="selectTWxampCartVo"/>
        <where>  
            <if test="databaseId != null  and databaseId != ''"> and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="quantity != null "> and QUANTITY = #{quantity}</if>
            <if test="selectStatus != null  and selectStatus != ''"> and SELECT_STATUS = #{selectStatus}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
    </select>
    
    
    <select id="selectTWxampCart" parameterType="TWxampCart" resultMap="TWxampCartResult">
        <include refid="selectTWxampCartVo"/>
        <where>  
            <if test="databaseId != null  and databaseId != ''"> and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="quantity != null "> and QUANTITY = #{quantity}</if>
            <if test="selectStatus != null  and selectStatus != ''"> and SELECT_STATUS = #{selectStatus}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
        LIMIT 1
    </select>
    
    <select id="selectTWxampCartById" parameterType="String" resultMap="TWxampCartResult">
        <include refid="selectTWxampCartVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTWxampCart" parameterType="TWxampCart">
        insert into t_wxamp_cart
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="databaseId != null">DATABASE_ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="quantity != null">QUANTITY,</if>
            <if test="selectStatus != null">SELECT_STATUS,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="databaseId != null">#{databaseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="selectStatus != null">#{selectStatus},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTWxampCart" parameterType="TWxampCart">
        update t_wxamp_cart
        <trim prefix="SET" suffixOverrides=",">
            <if test="databaseId != null">DATABASE_ID = #{databaseId},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="quantity != null">QUANTITY = #{quantity},</if>
            <if test="selectStatus != null">SELECT_STATUS = #{selectStatus},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTWxampCartById" parameterType="String">
        delete from t_wxamp_cart where ID = #{id}
    </delete>

    <delete id="deleteTWxampCartByIds" parameterType="String">
        delete from t_wxamp_cart where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>