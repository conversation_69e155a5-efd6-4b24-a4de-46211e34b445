<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.wxamp.mapper.TWxampOrderItemMapper">

    <resultMap type="TWxampOrderItem" id="TWxampOrderItemResult">
        <result property="id" column="ID"/>
        <result property="orderId" column="ORDER_ID"/>
        <result property="databaseId" column="DATABASE_ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="price" column="PRICE"/>
        <result property="quantity" column="QUANTITY"/>
        <result property="validStartTime" column="VALID_START_TIME"/>
        <result property="validExpireTime" column="VALID_EXPIRE_TIME"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <resultMap type="WxampOrderVo" id="WxampOrderVoResult">
        <result property="id" column="ID"/>
        <result property="orderId" column="ORDER_ID"/>
        <result property="databaseId" column="DATABASE_ID"/>
        <result property="databaseName" column="DB_NAME"/>
        <result property="orderValid" column="ORDER_VALID"/>
        <result property="userId" column="USER_ID"/>
        <result property="price" column="PRICE"/>
        <result property="quantity" column="QUANTITY"/>
        <result property="validStartTime" column="VALID_START_TIME"/>
        <result property="validExpireTime" column="VALID_EXPIRE_TIME"/>
        <result property="userName" column="USER_NAME"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="content" column="CONTENT"/>


    </resultMap>


    <sql id="selectTWxampOrderItemVo">
        select ID,
               ORDER_ID,
               DATABASE_ID,
               USER_ID,
               PRICE,
               QUANTITY,
               VALID_START_TIME,
               VALID_EXPIRE_TIME,
               CREATEBY_ID,
               CREATEBY_NAME,
               CREATE_TIME,
               UPDATEBY_ID,
               UPDATEBY_NAME,
               UPDATE_TIME,
               DEL_FLAG
        from t_wxamp_order_item
    </sql>



    <select id="selectTWxampOrderItemList" parameterType="TWxampOrderItem" resultMap="TWxampOrderItemResult">
        <include refid="selectTWxampOrderItemVo"/>
        <where>
            <if test="orderId != null  and orderId != ''"> and ORDER_ID = #{orderId}</if>
            <if test="databaseId != null  and databaseId != ''"> and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="price != null "> and PRICE = #{price}</if>
            <if test="quantity != null "> and QUANTITY = #{quantity}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
    </select>

    <select id="selectTWxampOrderItemVO" parameterType="WxampOrderVo" resultMap="WxampOrderVoResult">
        SELECT

        o.ORDER_NO,
        o.ORDER_VALID,

        item.ID,
        item.ORDER_ID,
        item.CREATE_TIME,
        item.PRICE,
        item.QUANTITY,
        item.VALID_EXPIRE_TIME,
        item.VALID_START_TIME,
        item.CONTENT,

        u.USER_NAME,
        d.DB_NAME
        FROM
        t_wxamp_order_item item
        LEFT JOIN t_wxamp_order o ON item.ORDER_ID = o.ID
        LEFT JOIN t_plat_user u ON o.USER_ID = u.ID
        LEFT JOIN t_pro_database d ON d.ID = item.DATABASE_ID
        <where>
            <if test="orderId != null  and orderId != ''">and ORDER_ID = #{orderId}</if>
            <if test="databaseId != null  and databaseId != ''">and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''">and USER_ID = #{userId}</if>
            <if test="price != null ">and PRICE = #{price}</if>
            <if test="orderValid != null ">and o.ORDER_VALID = #{orderValid}</if>
            <if test="quantity != null ">and QUANTITY = #{quantity}</if>
            <!--            <if test="validStartTime != null ">and VALID_START_TIME = #{validStartTime}</if>-->
            <!--            <if test="validExpireTime != null ">and VALID_EXPIRE_TIME = #{validExpireTime}</if>-->
            <if test="orderNo != null  and orderNo != ''">and o.ORDER_NO like concat('%', #{orderNo},
                '%')
            </if>
            <if test="searchTime!=null and searchTime.length >0">
                and item.VALID_START_TIME &gt;=#{validStartTime}
                and item.VALID_EXPIRE_TIME &lt;=#{validExpireTime}
            </if>
            <if test="userName != null  and userName != ''">and u.USER_NAME like concat('%', #{userName},
                '%')
            </if>
            <if test="createTime != null ">and CREATE_TIME = #{createTime}</if>
            and item.DEL_FLAG != 2
        </where>

    </select>

    <select id="selectTWxampOrderItemBy" resultMap="WxampOrderVoResult" parameterType="String">
        SELECT o.ORDER_NO,
               o.ORDER_VALID,

               item.ID,
               item.ORDER_ID,
               item.CREATE_TIME,
               item.PRICE,
               item.QUANTITY,
               item.VALID_EXPIRE_TIME,
               item.VALID_START_TIME,
               item.CONTENT,

               u.USER_NAME,
               d.DB_NAME
        FROM t_wxamp_order_item item
                 LEFT JOIN t_wxamp_order o ON item.ORDER_ID = o.ID
                 LEFT JOIN t_plat_user u ON o.USER_ID = u.ID
                 LEFT JOIN t_pro_database d ON d.ID = item.DATABASE_ID
        where
                item.ID = #{id}
    </select>


    <select id="selectTWxampOrderItemById" parameterType="String" resultMap="TWxampOrderItemResult">
        <include refid="selectTWxampOrderItemVo"/>
        where ID = #{id}
    </select>

    <insert id="insertTWxampOrderItem" parameterType="TWxampOrderItem">
        insert into t_wxamp_order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="orderId != null">ORDER_ID,</if>
            <if test="databaseId != null">DATABASE_ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="price != null">PRICE,</if>
            <if test="quantity != null">QUANTITY,</if>
            <if test="validStartTime != null">VALID_START_TIME,</if>
            <if test="validExpireTime != null">VALID_EXPIRE_TIME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="databaseId != null">#{databaseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="price != null">#{price},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="validStartTime != null">#{validStartTime},</if>
            <if test="validExpireTime != null">#{validExpireTime},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateTWxampOrderItem" parameterType="TWxampOrderItem">
        update t_wxamp_order_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">ORDER_ID = #{orderId},</if>
            <if test="databaseId != null">DATABASE_ID = #{databaseId},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="quantity != null">QUANTITY = #{quantity},</if>
            <if test="validStartTime != null">VALID_START_TIME = #{validStartTime},</if>
            <if test="validExpireTime != null">VALID_EXPIRE_TIME = #{validExpireTime},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <!--    <delete id="deleteTWxampOrderItemById" parameterType="String">-->
    <!--        delete-->
    <!--        from t_wxamp_order_item-->
    <!--        where ID = #{id}-->
    <!--    </delete>-->

    <update id="deleteTWxampOrderItemById" parameterType="String">
        update
            t_wxamp_order_item
        set DEL_FLAG=2
        where ID = #{id}
    </update>

    <!--    <delete id="deleteTWxampOrderItemByIds" parameterType="String">-->
    <!--        delete from t_wxamp_order_item where ID in-->
    <!--        <foreach item="id" collection="array" open="(" separator="," close=")">-->
    <!--            #{id}-->
    <!--        </foreach>-->
    <!--    </delete>-->

    <update id="deleteTWxampOrderItemByIds" parameterType="String">
        update
        t_wxamp_order_item
        set DEL_FLAG = 2 where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>