<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.wxamp.mapper.TWxampOrderMapper">
    
    <resultMap type="TWxampOrder" id="TWxampOrderResult">
        <result property="id"    column="ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="openId"    column="OPEN_ID"    />
        <result property="transactionId"    column="TRANSACTION_ID"    />
        <result property="orderNo"    column="ORDER_NO"    />
        <result property="orderAmount"    column="ORDER_AMOUNT"    />
        <result property="orderDescription"    column="ORDER_DESCRIPTION"    />
        <result property="orderValid"    column="ORDER_VALID"    />
        <result property="payResult"    column="PAY_RESULT"    />
        <result property="payType"    column="PAY_TYPE"    />
        <result property="shippingStatus"    column="SHIPPING_STATUS"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTWxampOrderVo">
        select ID, USER_ID, OPEN_ID, TRANSACTION_ID, ORDER_NO, ORDER_AMOUNT, ORDER_DESCRIPTION, ORDER_VALID, PAY_RESULT, PAY_TYPE, SHIPPING_STATUS, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_wxamp_order
    </sql>

    <select id="selectTWxampOrderList" parameterType="TWxampOrder" resultMap="TWxampOrderResult">
        <include refid="selectTWxampOrderVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="openId != null  and openId != ''"> and OPEN_ID = #{openId}</if>
            <if test="transactionId != null  and transactionId != ''"> and TRANSACTION_ID = #{transactionId}</if>
            <if test="orderNo != null  and orderNo != ''"> and ORDER_NO = #{orderNo}</if>
            <if test="orderAmount != null "> and ORDER_AMOUNT = #{orderAmount}</if>
            <if test="orderDescription != null  and orderDescription != ''"> and ORDER_DESCRIPTION = #{orderDescription}</if>
            <if test="orderValid != null  and orderValid != ''"> and ORDER_VALID = #{orderValid}</if>
            <if test="payResult != null  and payResult != ''"> and PAY_RESULT = #{payResult}</if>
            <if test="payType != null  and payType != ''"> and PAY_TYPE = #{payType}</if>
            <if test="shippingStatus != null"> and SHIPPING_STATUS = #{shippingStatus}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
    </select>
    
    <select id="selectTWxampOrderById" parameterType="String" resultMap="TWxampOrderResult">
        <include refid="selectTWxampOrderVo"/>
        where ID = #{id}
    </select>
    
    <select id="selectTWxampOrderByOrderNo" parameterType="String" resultMap="TWxampOrderResult">
        <include refid="selectTWxampOrderVo"/>
        where ORDER_NO = #{orderNo}
    </select>
        
    <insert id="insertTWxampOrder" parameterType="TWxampOrder">
        insert into t_wxamp_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="openId != null">OPEN_ID,</if>
            <if test="transactionId != null">TRANSACTION_ID,</if>
            <if test="orderNo != null">ORDER_NO,</if>
            <if test="orderAmount != null">ORDER_AMOUNT,</if>
            <if test="orderDescription != null">ORDER_DESCRIPTION,</if>
            <if test="orderValid != null">ORDER_VALID,</if>
            <if test="payResult != null">PAY_RESULT,</if>
            <if test="payType != null">PAY_TYPE,</if>
            <if test="shippingStatus != null">SHIPPING_STATUS,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="orderDescription != null">#{orderDescription},</if>
            <if test="orderValid != null">#{orderValid},</if>
            <if test="payResult != null">#{payResult},</if>
            <if test="payType != null">#{payType},</if>
            <if test="shippingStatus != null">#{shippingStatus},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTWxampOrder" parameterType="TWxampOrder">
        update t_wxamp_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="openId != null">OPEN_ID = #{openId},</if>
            <if test="transactionId != null">TRANSACTION_ID = #{transactionId},</if>
            <if test="orderNo != null">ORDER_NO = #{orderNo},</if>
            <if test="orderAmount != null">ORDER_AMOUNT = #{orderAmount},</if>
            <if test="orderDescription != null">ORDER_DESCRIPTION = #{orderDescription},</if>
            <if test="orderValid != null">ORDER_VALID = #{orderValid},</if>
            <if test="payResult != null">PAY_RESULT = #{payResult},</if>
            <if test="payType != null">PAY_TYPE = #{payType},</if>
            <if test="shippingStatus != null">SHIPPING_STATUS = #{shippingStatus},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTWxampOrderById" parameterType="String">
        delete from t_wxamp_order where ID = #{id}
    </delete>

    <delete id="deleteTWxampOrderByIds" parameterType="String">
        delete from t_wxamp_order where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>