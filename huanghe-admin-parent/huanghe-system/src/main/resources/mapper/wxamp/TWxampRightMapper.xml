<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.wxamp.mapper.TWxampRightMapper">
    
    <resultMap type="TWxampRight" id="TWxampRightResult">
        <result property="id"    column="ID"    />
        <result property="databaseId"    column="DATABASE_ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="expireTime"    column="EXPIRE_TIME"    />
        <result property="price"    column="PRICE"    />
        <result property="quantity"    column="QUANTITY"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="status"    column="STATUS"    />
    </resultMap>

    <sql id="selectTWxampRightVo">
        select ID, DATABASE_ID, USER_ID, START_TIME, EXPIRE_TIME, PRICE, QUANTITY, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,STATUS from t_wxamp_right
    </sql>

    <select id="selectTWxampRightList" parameterType="TWxampRight" resultMap="TWxampRightResult">
        <include refid="selectTWxampRightVo"/>
        <where>  
            <if test="databaseId != null  and databaseId != ''"> and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="expireTime != null "> and EXPIRE_TIME = #{expireTime}</if>
            <if test="price != null "> and PRICE = #{price}</if>
            <if test="quantity != null "> and QUANTITY = #{quantity}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
            <if test="status != null "> and STATUS = #{status}</if>
        </where>
    </select>
    
    <select id="selectTWxampRight" parameterType="TWxampRight" resultMap="TWxampRightResult">
        <include refid="selectTWxampRightVo"/>
        <where>  
            <if test="databaseId != null  and databaseId != ''"> and DATABASE_ID = #{databaseId}</if>
            <if test="userId != null  and userId != ''"> and USER_ID = #{userId}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="expireTime != null "> and EXPIRE_TIME = #{expireTime}</if>
            <if test="price != null "> and PRICE = #{price}</if>
            <if test="quantity != null "> and QUANTITY = #{quantity}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
        </where>
        LIMIT 1
    </select>
    
    <select id="selectTWxampRightById" parameterType="String" resultMap="TWxampRightResult">
        <include refid="selectTWxampRightVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTWxampRight" parameterType="TWxampRight">
        insert into t_wxamp_right
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="databaseId != null">DATABASE_ID,</if>
            <if test="userId != null">USER_ID,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="expireTime != null">EXPIRE_TIME,</if>
            <if test="price != null">PRICE,</if>
            <if test="quantity != null">QUANTITY,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="status != null">STATUS,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="databaseId != null">#{databaseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="price != null">#{price},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTWxampRight" parameterType="TWxampRight">
        update t_wxamp_right
        <trim prefix="SET" suffixOverrides=",">
            <if test="databaseId != null">DATABASE_ID = #{databaseId},</if>
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="expireTime != null">EXPIRE_TIME = #{expireTime},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="quantity != null">QUANTITY = #{quantity},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="status != null">STATUS = #{status},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTWxampRightById" parameterType="String">
        delete from t_wxamp_right where ID = #{id}
    </delete>

    <delete id="deleteTWxampRightByIds" parameterType="String">
        delete from t_wxamp_right where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>    