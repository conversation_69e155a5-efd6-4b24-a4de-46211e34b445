<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.search.mapper.TUserSearchHistoryMapper">
	<cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserSearchHistory" id="TUserSearchHistoryResult">
        <result property="id" column="ID"/>
        <result property="searchContent" column="SEARCH_CONTENT"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="searchContentJson" column="search_content_json" />
		<result property="searchType" column="SEARCH_TYPE" />
		
    </resultMap>

    <sql id="selectTUserSearchHistoryVo">
        select ID, SEARCH_CONTENT,search_content_json,
		search_type, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, 
        UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_user_search_history
    </sql>

    <select id="selectTUserSearchHistoryList" parameterType="TUserSearchHistory" resultMap="TUserSearchHistoryResult">
        <include refid="selectTUserSearchHistoryVo"/>
        <where>
            <if test="createbyId != null  and createbyId != ''">and CREATEBY_ID = #{createbyId}</if>
            <if test="searchContent != null">and SEARCH_CONTENT = #{searchContent} </if>
            <if test="dataFrom != null">and DATA_FROM = #{dataFrom} </if>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectTUserSearchHistoryById" parameterType="String" resultMap="TUserSearchHistoryResult">
        <include refid="selectTUserSearchHistoryVo"/>
        where ID = #{id}
    </select>
    <select id="searchTop" resultType="cn.guliandigital.product.search.domain.TUserSearchHistory">
        select SEARCH_CONTENT as searchContent, count(SEARCH_CONTENT) searchCount
        from t_user_search_history
        where 1=1
        and DEL_FLAG=0
        <if test="searchContent != null">and SEARCH_CONTENT like concat('%', #{searchContent}, '%') </if>
		<if test="searchType != null  and searchType != ''">and SEARCH_TYPE = #{searchType}</if>
        group by SEARCH_CONTENT order by searchCount desc
    </select>


    <insert id="insertTUserSearchHistory" parameterType="TUserSearchHistory">
        insert into t_user_search_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="searchContent != null">SEARCH_CONTENT,</if>
            <if test="searchContentJson != null">search_content_json,</if>
            <if test="searchType != null">search_type,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="dataFrom != null">DATA_FROM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="searchContent != null">#{searchContent},</if>
            <if test="searchContentJson != null">#{searchContentJson},</if>
            <if test="searchType != null">#{searchType},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="dataFrom != null">#{dataFrom},</if>
        </trim>
    </insert>

    <update id="updateTUserSearchHistory" parameterType="TUserSearchHistory">
        update t_user_search_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="searchContent != null">SEARCH_CONTENT = #{searchContent},</if>
            <if test="searchContentJson != null">search_content_json = #{searchContentJson},</if>
            <if test="searchType != null">search_type = #{searchType},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updateStaus">
        update t_user_search_history set DEL_FLAG=1  where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteTUserSearchHistoryById" parameterType="String">
        delete from t_user_search_history where ID = #{id}
    </delete>

    <delete id="deleteTUserSearchHistoryByIds" parameterType="String">
        delete from t_user_search_history where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>