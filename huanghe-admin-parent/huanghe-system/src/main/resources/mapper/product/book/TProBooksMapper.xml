<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="cn.guliandigital.product.book.mapper.TProBooksMapper">
    <cache
            type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="3600000"/>
        <property name="size" value="1024"/>
        <property name="readOnly" value="false"/>
    </cache>
    <resultMap type="TProBooks" id="TProBooksResult">
        <result property="display" column="DISPLAY"/>
        <result property="siClassificationId" column="SI_CLASSIFICATION_ID"/>
        <result property="uniqueId" column="UNIQUE_ID"/>
        <result property="bookName" column="BOOK_NAME"/>
        <result property="resourceClasses" column="RESOURCE_CLASSES"/>
        <result property="resourceType" column="RESOURCE_TYPE"/>
        <result property="siClassification" column="SI_CLASSIFICATION"/>
        <result property="classiMethodCode" column="CLASSI_METHOD_CODE"/>
        <result property="classiMethod" column="CLASSI_METHOD"/>
        <result property="classification" column="CLASSIFICATION"/>
        <result property="mainResponsibility" column="MAIN_RESPONSIBILITY"/>
        <result property="imageTextType" column="IMAGETEXT_TYPE"/>
        <result property="publishDate" column="PUBLISH_DATE"/>
        <result property="publisher" column="PUBLISHER"/>
        <result property="publishland" column="PUBLISHLAND"/>
        <result property="publishYear" column="PUBLISH_YEAR"/>
        <result property="proStatus" column="PRO_STATUS"/>
        <result property="openSource" column="OPEN_SOURCE"/>
        <result property="price" column="PRICE"/>
        <!-- <result property="discount" column="DISCOUNT"/> <result property="finalPrice"
            column="FINAL_PRICE"/> -->
        <result property="coverUrl" column="COVER_URL"/>
        <result property="thumbCoverUrl" column="THUMB_COVER_URL"/>
        <result property="bookDesc" column="BOOK_DESC"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="subjectWord" column="SUBJECT_WORD"/>
        <result property="revision" column="REVISION"/>
        <result property="dbName" column="DB_Name"/>
        <result property="dbId" column="DB_ID"/>
        <result property="resourceClassesId" column="resource_classes_id"/>
        <result property="numberCopies" column="NUMBER_COPIES"/>
        <result property="conglomeration" column="CONGLOMERATION"/>
        <result property="wordNum" column="WORD_NUM"/>
        <result property="picNum" column="PIC_NUM"/>
        <result property="resourceSize" column="resource_size"/>
		<result property="edition" column="edition" />
		<result property="dcMetaid" column="DC_METAID" />
		<result property="collection" column="collection" />
		<result property="impression" column="IMPRESSION" />
		<result property="kaibenInfo" column="KAIBEN_INFO" />
        <result property="parseStatus" column="parse_status" />
        <result property="ocrVersion" column="ocr_version" />
        <result property="layout" column="layout" />
        <result property="pdfPath" column="PDF_PATH" />
        <result property="coverName" column="COVER_NAME" />
        <result property="pdfName" column="PDF_NAME" />
        <result property="subjectText" column="SUBJECT_TEXT" />
    </resultMap>

    <sql id="selectTProBooksVo">
        select ID,
               db_id,
               UNIQUE_ID,
               BOOK_NAME,
               RESOURCE_CLASSES,
               RESOURCE_TYPE,
               SI_CLASSIFICATION,
               SI_CLASSIFICATION_ID,
               CLASSI_METHOD_CODE,
               CLASSI_METHOD,
               IMAGETEXT_TYPE,
               resource_classes_id,
               CLASSIFICATION,
               MAIN_RESPONSIBILITY,
               PUBLISH_DATE,
               PUBLISHER,
               PUBLISHLAND,
               PUBLISH_YEAR,
               PRO_STATUS,
               OPEN_SOURCE,
               PRICE,
               COVER_URL,
               THUMB_COVER_URL,
               BOOK_DESC,
               CREATEBY_ID,
               CREATEBY_NAME,
               CREATE_TIME,
               UPDATEBY_ID,
               UPDATEBY_NAME,
               UPDATE_TIME,
               DEL_FLAG,
               CONGLOMERATION,
               edition,
               DC_METAID,
               collection,
               parse_status,
               ocr_version,
               layout,
               PDF_PATH,
               COVER_NAME,
               PDF_NAME,
               revision,
               SUBJECT_TEXT
        from t_pro_books
    </sql>
    <select id="selectBook" resultMap="TProBooksResult"
            parameterType="String">
        <include refid="selectTProBooksVo"/>
        where ID in
        <foreach item="id" collection="array" open="(" separator=","
                 close=")">
            #{id}
        </foreach>

    </select>
    <select id="selectCountClassic" resultType="TProBooks">
        select count(0) count,SI_CLASSIFICATION
        siClassification,SI_CLASSIFICATION_ID,resource_classes_id
        from t_pro_books
        where SI_CLASSIFICATION is not null
        <if test="siClassification != null and siClassification != ''">and SI_CLASSIFICATION = #{siClassification}</if>
        group by SI_CLASSIFICATION

    </select>


    <select id="selectParent" resultMap="TProBooksResult"
            parameterType="String">
        select id from t_config_classic_tree
        where tree_pid in
        <foreach item="id" collection="array" open="(" separator=","
                 close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectRecourseStatisticsList"
            resultMap="TProBooksResult" resultType="TProBooks">
        select b.RESOURCE_CLASSES,
        b.BOOK_NAME,
        b.MAIN_RESPONSIBILITY,
        sum(turh.READ_TIME) readTime,
        (select distinct turh.CREATE_TIME from t_user_read_history) searchPeople ,
        d.DB_NAME
        from t_pro_books b
        LEFT JOIN t_pro_database d
        ON b.DB_ID = d.ID
        left join t_user_read_history turh on b.ID = turh.BOOK_ID
        and turh.READ_TIME is not null
        <if test="createTime != null and createTime != ''">and turh.CREATE_TIME={#createTime}</if>
    </select>

    <select id="selectTProBooksList" resultMap="TProBooksResult"
            resultType="TProBooks">
        select b.DEL_FLAG,b.UNIQUE_ID,b.REVISION,b.COVER_URL, b.CLASSI_METHOD_CODE
        ,b.THUMB_COVER_URL,b.SUBJECT_WORD ,b.CLASSI_METHOD, b.ID id,
        b.RESOURCE_CLASSES ,b. IMAGETEXT_TYPE,b.resource_classes_id,
        b.PUBLISHER, b.PUBLISHLAND ,b.BOOK_NAME,b.PUBLISH_DATE ,b.PRO_STATUS,
        d.DB_NAME,b.DB_ID,b.MAIN_RESPONSIBILITY,b.SI_CLASSIFICATION,b.SI_CLASSIFICATION_ID,
        b.DISPLAY,b.NUMBER_COPIES,b.CREATE_TIME,b.CONGLOMERATION,b.WORD_NUM,b.resource_size,b.PIC_NUM,b.edition,
        b.DC_METAID,b.UPDATE_TIME,b.parse_status, b.ocr_version,b.layout
        FROM t_pro_books b

        LEFT JOIN t_pro_database d
        ON b.DB_ID=d.ID
        where 1=1 and b.DEL_FLAG!=1
        <if test="siClassification != null and siClassification != ''">and b.SI_CLASSIFICATION =
            #{siClassification}
        </if>
        <if test="classiMethod != null and classiMethod != ''">and b.CLASSI_METHOD like concat('%', #{classiMethod},
            '%')
        </if>

        <if test="bookName != null and bookName != ''">and b.BOOK_NAME like concat('%', #{bookName}, '%')</if>
        <if test="resourceClasses != null and resourceClasses != ''">and b.RESOURCE_CLASSES = #{resourceClasses}</if>
        <if test="resourceClassesId != null and resourceClassesId != ''">and b.RESOURCE_CLASSES_ID =
            #{resourceClassesId}
        </if>
        <if test="uniqueId != null and uniqueId != ''">and b.UNIQUE_ID = #{uniqueId}</if>
        <if test="conglomeration != null and conglomeration != ''">and b.CONGLOMERATION = #{conglomeration}</if>
        <if test="publishDate != null and publishDate != ''">and b.PUBLISH_DATE = #{publishDate}</if>
        <if test="dbId != null and dbId != ''">and b.DB_ID = #{dbId}</if>
        <if test="mainResponsibility != null and mainResponsibility != ''">and b.MAIN_RESPONSIBILITY
            like concat('%', #{mainResponsibility}, '%')
        </if>
        <if test="publishDate != null and publishDate != ''">and b.PUBLISH_DATE like concat('%', #{publishDate}, '%')
        </if>
        <if test="publisher != null and publisher != ''">and b.PUBLISHER = #{publisher}</if>
        <if test="publishland != null and publishland != ''">and b.PUBLISHLAND = #{publishland}</if>
        <if test="publishYear != null and publishYear != ''">and b.PUBLISH_YEAR = #{publishYear}</if>
        <if test="imageTextType != null and imageTextType != ''">and b.IMAGETEXT_TYPE = #{imageTextType}</if>
        <if test="proStatus != null ">and b.PRO_STATUS = #{proStatus}</if>
        <if test="price != null ">and b.PRICE = #{price}</if>
        <if test="coverUrl != null and coverUrl != ''">and b.COVER_URL = #{coverUrl}</if>
        <if test="bookDesc != null and bookDesc != ''">and b.BOOK_DESC
            like concat('%', #{bookDesc}, '%')
        </if>
        <if test="dbId != null ">and b.DB_ID = #{dbId}</if>
        <if test="resourceClassesIds !=null and resourceClassesIds.length>0">
            and b.RESOURCE_CLASSES_ID in
            <foreach collection="resourceClassesIds" open="(" close=")" separator="," item="resourceClassesId">
                #{resourceClassesId}
            </foreach>
        </if>
        order by b.display IS NULL, b.display
    </select>
    <select id="selectClassic"
            resultType="cn.guliandigital.product.book.vo.ClassTreeType">
        select id, classic_id, id as value,tree_name label,tree_pid treePid
        from t_config_classic_tree
        where classic_id='2ba44c2f8aa64de1a60eed30e1627225'
    </select>
    <select id="selectFourPart"
            resultType="cn.guliandigital.product.book.vo.ClassTreeType">
        select id, classic_id, id as value,tree_name label,tree_pid treePid
        from t_config_classic_tree
        where classic_id='84617732b3374a5d90657916d05662e2'
    </select>

    <select id="selectOther"
            resultType="cn.guliandigital.product.book.vo.ClassTreeType">
        select id,
               tree_name as value,tree_name label,tree_pid treePid,
		(select count(0) from t_pro_books where CLASSIFICATION=tree_name and
		PRO_STATUS=1) otherCount
        from t_config_classic_tree
        where classic_id='2ba44c2f8aa64de1a60eed30e1627225'

    </select>

    <select id="selectDbName"
            resultType="cn.guliandigital.product.database.domain.TProDatabase">
        select DB_NAME dbName, ID dbId, PRICE price
        from t_pro_database
    </select>
    <select id="selectView" resultMap="TProBooksResult"
            resultType="TProBooks">
        select b.DEL_FLAG ,b.MAIN_RESPONSIBILITY , b.CLASSI_METHOD_CODE, b.ID , b.RESOURCE_CLASSES,
        b.PUBLISHER publisher , b.RESOURCE_CLASSES, b.PUBLISHLAND,b.IMAGETEXT_TYPE
        ,b.COVER_URL,b.THUMB_COVER_URL,b.SI_CLASSIFICATION_ID,b.RESOURCE_CLASSES_ID,
        b.BOOK_NAME,b.PUBLISH_DATE ,b.PRO_STATUS , d.DB_NAME,b.edition,b.DC_METAID, b.ocr_version,b.layout
        FROM t_pro_books b
        LEFT JOIN t_pro_database d
        ON b.DB_ID=d.ID
        where d.ID=#{id} and b.DEL_FLAG !=1
        <if test="bookName!= null and bookName != ''">and b.BOOK_NAME like concat('%', #{bookName}, '%')</if>
        <if test="resourceClasses != null and resourceClasses != ''">and b.RESOURCE_CLASSES_ID = #{resourceClasses}</if>
        <if test="publishDate != null and publishDate != ''">and b.PUBLISH_DATE like concat('%', #{publishDate}, '%')
        </if>
        <if test="proStatus != null ">and b.PRO_STATUS = #{proStatus}</if>

        <if test="mainResponsibility!= null and mainResponsibility != ''">and b.MAIN_RESPONSIBILITY like concat('%',
            #{mainResponsibility}, '%')
        </if>
    </select>
    <select id="selectsList" resultMap="TProBooksResult"
            resultType="TProBooks">
        SELECT
        b.ID,
        b.BOOK_NAME,
        b.CLASSI_METHOD_CODE,
        b.PUBLISH_DATE,
        b.MAIN_RESPONSIBILITY,
        b.PUBLISHER,
        b.RESOURCE_CLASSES,
        b.PUBLISHLAND,
        b.SI_CLASSIFICATION_ID,
        b.PRO_STATUS,
        b.COVER_URL,
        b.THUMB_COVER_URL,
        b.BOOK_DESC,
        d.db_name AS dbName,
        b.DEL_FLAG,
        b.IMAGETEXT_TYPE,
        b.RESOURCE_CLASSES_ID,
        b.edition,
        b.DC_METAID,
        b.collection,
        b.DISPLAY, b.ocr_version,b.layout
        FROM
        t_pro_books b

        LEFT JOIN t_pro_database d ON b.DB_ID = d.ID
        <if test="orgId!= null and orgId != ''">
            LEFT JOIN t_pro_order o ON o.KT_DATABASE LIKE CONCAT('%',
            d.DB_NAME, '%')
        </if>
        WHERE
        1 = 1

        <if test="orgId!= null and orgId != ''">and o.ORG_ID=#{orgId}</if>
        <if test="dbId!= null and dbId != ''">and b.DB_ID=#{dbId}</if>
        <if test="bookName!= null and bookName != ''">and b.BOOK_NAME like concat('%', #{bookName}, '%')</if>
        <if test="mainResponsibility!= null and mainResponsibility != ''">and b.MAIN_RESPONSIBILITY like concat('%',
            #{mainResponsibility}, '%')
        </if>
        <if test="resourceClasses != null and resourceClasses != ''">and b.RESOURCE_CLASSES like concat('%',
            #{resourceClasses}, '%')
        </if>
        <if test="siClassificationId!= null and siClassificationId != ''">and
            b.SI_CLASSIFICATION_ID=#{siClassificationId}
        </if>
        <if test="resourceClassesId!= null and resourceClassesId != ''">and b.RESOURCE_CLASSES_ID=#{resourceClassesId}
        </if>
        <if test="resourceClassesIds !=null and resourceClassesIds.length>0 != ''">
            and b.RESOURCE_CLASSES_ID in
            <foreach collection="resourceClassesIds" open="(" close=")" separator="," item="resourceClassesId">
                #{resourceClassesId}
            </foreach>
        </if>
        AND b.DEL_FLAG != 1
        AND b.PRO_STATUS = 1
    </select>
    <select id="selectsAllList" resultMap="TProBooksResult"
            resultType="TProBooks">
        SELECT
        b.ID,
        b.UNIQUE_ID,
        b.BOOK_NAME,
        b.CLASSI_METHOD_CODE,
        b.PUBLISH_DATE,
        b.MAIN_RESPONSIBILITY,
        b.PUBLISHER,
        b.RESOURCE_CLASSES,
        b.PUBLISHLAND,
        b.SI_CLASSIFICATION_ID,
        b.PRO_STATUS,
        b.COVER_URL,
        b.THUMB_COVER_URL,
        b.BOOK_DESC,        
        b.DEL_FLAG,
        b.IMAGETEXT_TYPE,
        b.RESOURCE_CLASSES_ID,
        b.edition,
        b.collection,
        b.DC_METAID, b.ocr_version,b.layout
        FROM
        t_pro_books b
      
        WHERE
        1 = 1
     	<if test="uniqueId != null and uniqueId != ''">and b.UNIQUE_ID = #{uniqueId}</if>
        <if test="dbId!= null and dbId != ''">and b.DB_ID=#{dbId}</if>
        <if test="imageTextType!= null and imageTextType != ''">and b.IMAGETEXT_TYPE=#{imageTextType}</if>
        <if test="bookName!= null and bookName != ''">and b.BOOK_NAME like concat('%', #{bookName}, '%')</if>
        <if test="mainResponsibility!= null and mainResponsibility != ''">and b.MAIN_RESPONSIBILITY like concat('%',
            #{mainResponsibility}, '%')
        </if>
        <if test="resourceClasses != null and resourceClasses != ''">and b.RESOURCE_CLASSES like concat('%',
            #{resourceClasses}, '%')
        </if>
        <if test="siClassificationId!= null and siClassificationId != ''">and
            b.SI_CLASSIFICATION_ID=#{siClassificationId}
        </if>
        <if test="resourceClassesId!= null and resourceClassesId != ''">and b.RESOURCE_CLASSES_ID=#{resourceClassesId}
        
        </if>
        <if test="dcMetaid !=null and dcMetaid != ''">and b.DC_METAID=#{dcMetaid} </if>
       
    </select>
    
    <select id="select" resultMap="TProBooksResult"
            parameterType="String">
        select b.BOOK_NAME, b.PUBLISH_DATE,b.DEL_FLAG ,b.MAIN_RESPONSIBILITY ,
        b.ID , b.RESOURCE_CLASSES, b.PUBLISHER,b.resource_classes_id,
        b.CLASSI_METHOD_CODE,
        publisher , b.RESOURCE_CLASSES, b.PUBLISHLAND ,b.BOOK_NAME ,b.PRO_STATUS ,
        b.SI_CLASSIFICATION_ID,d.DB_NAME
        ,b.BOOK_DESC,b.ocr_version,b.layout
        FROM t_pro_books b

        LEFT JOIN t_pro_database d
        ON b.DB_ID=d.ID
        where d.ID=#{id} and b.DEL_FLAG !=1
        <if test="bookName!= null and bookName != ''">and b.BOOK_NAME like concat('%', #{bookName}, '%')</if>
        <if test="dbId !=null and dbId != ''">and d.id = #{dbId},</if>
        order by b.PUBLISH_DATE des

    </select>
    <select id="selectTProBooksById" parameterType="String"
            resultMap="TProBooksResult">
        select b.COVER_URL,
               b.THUMB_COVER_URL,
               b.DEL_FLAG,
               b.REVISION,
               b.DISPLAY,
               b.SUBJECT_WORD,
               b.ID      id,
               b.RESOURCE_CLASSES,
               b.resource_classes_id,
               b.SI_CLASSIFICATION,
               b.MAIN_RESPONSIBILITY,
               b.SI_CLASSIFICATION_ID,
               b.CLASSI_METHOD_CODE,
               b.PUBLISHER,
               b.RESOURCE_CLASSES,
               b.PUBLISHLAND,
               b.IMAGETEXT_TYPE,
               b.BOOK_NAME,
               b.PUBLISH_DATE,
               b.PRO_STATUS,
               d.DB_NAME dbName,
               b.DB_ID,
               b.BOOK_DESC,
               b.MAIN_RESPONSIBILITY,
               b.UNIQUE_ID,
               b.NUMBER_COPIES,
               b.conglomeration,
               b.PIC_NUM,
               b.WORD_NUM,
               b.edition,
               b.DC_METAID,
               b.collection,
               b.IMPRESSION,
               b.KAIBEN_INFO,
               b.ocr_version,b.layout
        FROM t_pro_books b

                 LEFT JOIN t_pro_database d
                           ON b.DB_ID = d.ID
        where b.id = #{id}
          and b.DEL_FLAG !=1 and b.PRO_STATUS=1
        order by b.CREATE_TIME desc
    </select>
    <select id="selectTProBooksByName" parameterType="String"
            resultMap="TProBooksResult">
        select BOOK_NAME, ID
        FROM t_pro_books b
        where BOOK_NAME = #{bookName}

          and b.DEL_FLAG !=1
    </select>
    <update id="del" parameterType="String">
        update t_pro_books set DEL_FLAG =1
        where ID in
        <foreach item="id" collection="list" open="(" separator=","
                 close=")">
            #{id}
        </foreach>


    </update>
    <insert id="insertTProBooks" parameterType="TProBooks">
        insert into t_pro_books
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subjectWord != null">SUBJECT_WORD,</if>
            <if test="revision != null">REVISION,</if>
            <if test="id != null">ID,</if>
            <if test="display != null">DISPLAY,</if>
            <if test="uniqueId != null">UNIQUE_ID,</if>
            <if test="siClassificationId != null and siClassificationId!=''">SI_CLASSIFICATION_ID,</if>
            <if test="bookName != null and bookName != ''">BOOK_NAME,</if>
            <if test="resourceClasses != null">RESOURCE_CLASSES,</if>
            <if test="resourceClassesId != null">RESOURCE_CLASSES_ID,</if>
            <if test="resourceType != null">RESOURCE_TYPE,</if>
            <if test="siClassification != null">SI_CLASSIFICATION,</if>
            <if test="classiMethodCode != null">CLASSI_METHOD_CODE,</if>
            <if test="classiMethod != null">CLASSI_METHOD,</if>
            <if test="classification != null">CLASSIFICATION,</if>
            <if test="mainResponsibility != null">MAIN_RESPONSIBILITY,</if>
            <if test="publishDate != null">PUBLISH_DATE,</if>
            <if test="publisher != null">PUBLISHER,</if>
            <if test="publishland != null">PUBLISHLAND,</if>
            <if test="publishYear != null">PUBLISH_YEAR,</if>
            <if test="proStatus != null">PRO_STATUS,</if>
            <if test="openSource != null">OPEN_SOURCE,</if>
            <if test="price != null">PRICE,</if>
            <if test="imageTextType != null">IMAGETEXT_TYPE,</if>
            <if test="coverUrl != null">COVER_URL,</if>
            <if test="thumbCoverUrl != null">THUMB_COVER_URL,</if>
            <if test="bookDesc != null">BOOK_DESC,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="numberCopies != null">NUMBER_COPIES,</if>
            <if test="conglomeration != null">CONGLOMERATION,</if>
            <if test="dbId != null">db_id,</if>
            <if test="edition != null">edition,</if>
            <if test="dcMetaid != null">DC_METAID,</if>
            <if test="collection != null">collection,</if>
            <if test="impression != null">IMPRESSION,</if>
            <if test="kaibenInfo != null">KAIBEN_INFO,</if>
            <if test="parseStatus != null">parse_status,</if>
            <if test="ocrVersion != null">ocr_version,</if>
            <if test="layout != null">layout,</if>
            <if test="pdfPath != null">PDF_PATH,</if>
            <if test="coverName != null">COVER_NAME,</if>
            <if test="pdfName != null">PDF_NAME,</if>
            <if test="wordNum != null">WORD_NUM,</if>
            <if test="subjectText != null">SUBJECT_TEXT,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subjectWord != null">#{subjectWord},</if>
            <if test="revision != null">#{revision},</if>
            <if test="id != null">#{id},</if>
            <if test="display != null">#{display},</if>
            <if test="uniqueId != null">#{uniqueId},</if>
            <if test="siClassificationId != null and siClassificationId!=''">#{siClassificationId},</if>
            <if test="bookName != null and bookName != ''">#{bookName},</if>
            <if test="resourceClasses != null">#{resourceClasses},</if>
            <if test="resourceClassesId != null">#{resourceClassesId},</if>
            <if test="resourceType != null">#{resourceType},</if>
            <if test="siClassification != null">#{siClassification},</if>
            <if test="classiMethodCode != null">#{classiMethodCode},</if>
            <if test="classiMethod != null">#{classiMethod},</if>
            <if test="classification != null">#{classification},</if>
            <if test="mainResponsibility != null">#{mainResponsibility},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="publishland != null">#{publishland},</if>
            <if test="publishYear != null">#{publishYear},</if>
            <if test="proStatus != null">#{proStatus},</if>
            <if test="openSource != null">#{openSource},</if>
            <if test="price != null">#{price},</if>
            <if test="imageTextType != null">#{imageTextType},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="thumbCoverUrl != null">#{thumbCoverUrl},</if>
            <if test="bookDesc != null">#{bookDesc},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="numberCopies != null">#{numberCopies},</if>
            <if test="conglomeration != null">#{conglomeration},</if>
            <if test="dbId != null">#{dbId},</if>
            <if test="edition != null">#{edition},</if>
            <if test="dcMetaid != null">#{dcMetaid},</if>
            <if test="collection != null">#{collection},</if>
            <if test="impression != null">#{impression},</if>
            <if test="kaibenInfo != null">#{kaibenInfo},</if>
            <if test="parseStatus != null">#{parseStatus},</if>
            <if test="ocrVersion != null">#{ocrVersion},</if>
            <if test="layout != null">#{layout},</if>
            <if test="pdfPath != null">#{pdfPath},</if>
            <if test="coverName != null">#{coverName},</if>
            <if test="pdfName != null">#{pdfName},</if>
            <if test="wordNum != null">#{wordNum},</if>
            <if test="subjectText != null">#{subjectText},</if>
        </trim>
    </insert>

    <update id="updateTProBooks" parameterType="TProBooks">
        update t_pro_books
        <trim prefix="SET" suffixOverrides=",">
            <if test="dbId != null">DB_ID= #{dbId},</if>
            <if test="display != null">DISPLAY= #{display},</if>
            <if test="subjectWord != null">SUBJECT_WORD=#{subjectWord},</if>
            <if test="revision != null">REVISION=#{revision},</if>
            <if test="siClassificationId != null">SI_CLASSIFICATION_ID=#{siClassificationId},</if>
            <if test="uniqueId != null">UNIQUE_ID = #{uniqueId},</if>
            <if test="bookName != null and bookName != ''">BOOK_NAME = #{bookName},</if>
            <if test="resourceClasses != null">RESOURCE_CLASSES = #{resourceClasses},</if>
            <if test="resourceClassesId != null">RESOURCE_CLASSES_ID = #{resourceClassesId},</if>
            <if test="resourceType != null">RESOURCE_TYPE = #{resourceType},</if>
            <if test="siClassification != null">SI_CLASSIFICATION = #{siClassification},</if>
            <if test="classiMethodCode != null">CLASSI_METHOD_CODE = #{classiMethodCode},</if>
            <if test="classiMethod != null">CLASSI_METHOD = #{classiMethod},</if>
            <if test="classification != null">CLASSIFICATION = #{classification},</if>
            <if test="mainResponsibility != null">MAIN_RESPONSIBILITY = #{mainResponsibility},</if>
            <if test="publishDate != null">PUBLISH_DATE = #{publishDate},</if>
            <if test="publisher != null">PUBLISHER = #{publisher},</if>
            <if test="publishland != null">PUBLISHLAND = #{publishland},</if>
            <if test="publishYear != null">PUBLISH_YEAR = #{publishYear},</if>
            <if test="proStatus != null">PRO_STATUS = #{proStatus},</if>
            <if test="openSource != null">OPEN_SOURCE = #{openSource},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="imageTextType != null">IMAGETEXT_TYPE= #{imageTextType},</if>
            <if test="coverUrl != null">COVER_URL = #{coverUrl},</if>
            <if test="thumbCoverUrl != null">THUMB_COVER_URL = #{thumbCoverUrl},</if>
            <if test="bookDesc != null">BOOK_DESC = #{bookDesc},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="numberCopies != null">NUMBER_COPIES = #{numberCopies},</if>
            <if test="conglomeration != null">CONGLOMERATION = #{conglomeration},</if>
            <if test="picNum != null">PIC_NUM = #{picNum},</if>
            <if test="wordNum != null">WORD_NUM = #{wordNum},</if>
            <if test="resourceSize != null">resource_size = #{resourceSize},</if>
            <if test="edition != null">edition = #{edition},</if>
            <if test="dcMetaid != null">DC_METAID = #{dcMetaid},</if>
            <if test="collection != null">collection = #{collection},</if>
            <if test="impression != null">IMPRESSION = #{impression},</if>
            <if test="kaibenInfo != null">KAIBEN_INFO = #{kaibenInfo},</if>
            <if test="parseStatus != null">parse_status = #{parseStatus},</if>
            <if test="ocrVersion != null">ocr_version = #{ocrVersion},</if>
            <if test="layout != null">layout = #{layout},</if>
            <if test="pdfPath != null">PDF_PATH = #{pdfPath},</if>
            <if test="coverName != null">COVER_NAME = #{coverName},</if>
            <if test="pdfName != null">PDF_NAME = #{pdfName},</if>
            <if test="subjectText != null">SUBJECT_TEXT = #{subjectText},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updateOffStatu" parameterType="String">
        update t_pro_books set PRO_STATUS=0 where ID in
        <foreach item="id" collection="array" open="(" separator=","
                 close=")">
            #{id}
        </foreach>
    </update>

    <update id="upStatu" parameterType="String">
        update t_pro_books set PRO_STATUS=1 where ID in
        <foreach item="id" collection="array" open="(" separator=","
                 close=")">
            #{id}
        </foreach>
    </update>
    <delete id="deleteTProBooksById" parameterType="String">
        delete
        from t_pro_books
        where ID = #{id}
    </delete>
    <select id="check" resultMap="TProBooksResult">
        select ID, BOOK_NAME
        from t_pro_books
        where DEL_FLAG !=1

    </select>

    <select id="selectCount" parameterType="String"
            resultType="java.lang.Integer">
        select count(0)
        FROM t_pro_books b
                 LEFT JOIN t_pro_database d
                           ON b.DB_ID = d.ID
        where b.DB_ID = #{dbId}
          and b.DEL_FLAG !=1
    </select>
    <!--根据数据库id分组查询各个数据库的资源量-->
    <select id="selectCountGroupByDbId" parameterType="String"
            resultType="cn.guliandigital.product.book.vo.DataBaseVo">
        select d.ID dbId,count(DB_ID) dbCount
        FROM (select DB_ID from t_pro_books  where DEL_FLAG !=1 and pro_status!=0) b
                 RIGHT JOIN t_pro_database d ON b.DB_ID = d.ID
        GROUP BY d.ID
    </select>
    <delete id="deleteTProBooksByIds" parameterType="String">
        delete from t_pro_books where ID in
        <foreach item="id" collection="array" open="(" separator=","
                 close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectType" resultType="String">
        select RESOURCE_TYPE
        from t_pro_books
    </select>


    <select id="selectTProBooks" resultMap="TProBooksResult"
            parameterType="String">
        select b.SI_CLASSIFICATION,
               b.SI_CLASSIFICATION_ID,
               b.RESOURCE_CLASSES_ID,
               b.DEL_FLAG,
               b.REVISION,
               b.COVER_URL,
               b.SUBJECT_WORD,
               b.ID      id,
               b.CLASSI_METHOD_CODE,
               b.RESOURCE_CLASSES,
               b.PUBLISHER,
               b.PUBLISHLAND
                ,
               b.BOOK_NAME,
               b.PUBLISH_DATE,
               b.PRO_STATUS,
               d.DB_NAME dbName,
               b.DB_ID, b.ocr_version,b.layout
        FROM t_pro_books b
                 LEFT JOIN t_pro_database d
                           ON b.DB_ID = d.ID
        where b.DEL_FLAG !=1
		and b.ID=#{id}
    </select>

    <select id="selectAll" resultMap="TProBooksResult"
            parameterType="String">
        select b.ID id
        FROM t_pro_books b
        where b.DEL_FLAG !=1
		and BOOK_NAME = #{bookName}

    </select>

    <select id="selectWebTProBooksById" parameterType="String"
            resultMap="TProBooksResult">
        select b.CLASSI_METHOD_CODE,
               b.ID,
               b.UNIQUE_ID,
               b.BOOK_NAME,
               b.RESOURCE_CLASSES,
               b.SUBJECT_WORD,
               b.MAIN_RESPONSIBILITY,
               b.PUBLISH_DATE,
               b.PUBLISHER,
               b.REVISION,
               b.COVER_URL,
               b.BOOK_DESC,
               b.IMAGETEXT_TYPE,
               d.DB_NAME,
               b.RESOURCE_CLASSES_ID, b.ocr_version,b.layout
        from t_pro_books b
                 LEFT JOIN t_pro_database d
                           ON b.DB_ID = d.ID
        where b.ID = #{id}
          and b.DEL_FLAG !=1
    </select>
    <select id="recoursePercent" resultType="cn.guliandigital.product.book.domain.TProBooks">
        select count(books.id) dbCount,
               tpd.DB_NAME     dbName
        from t_pro_books books
                 left join t_pro_database tpd on books.DB_ID = tpd.id
        where books.DB_ID = tpd.id
        group by tpd.id
    </select>
    <select id="selectBooks" resultType="java.lang.Integer" parameterType="String">
        SELECT count(*) FROM t_pro_books where DB_ID = #{dbId} and DEL_FLAG !=1 and pro_status!=0
    </select>
    <select id="selectTProBooksEntity" resultType="cn.guliandigital.product.book.domain.TProBooks" resultMap="TProBooksResult">
        select b.COVER_URL,
        b.THUMB_COVER_URL,
        b.DEL_FLAG,
        b.REVISION,
        b.DISPLAY,
        b.SUBJECT_WORD,
        b.ID      id,
        b.RESOURCE_CLASSES,
        b.resource_classes_id,
        b.SI_CLASSIFICATION,
        b.MAIN_RESPONSIBILITY,
        b.SI_CLASSIFICATION_ID,
        b.CLASSI_METHOD_CODE,
        b.PUBLISHER,
        b.RESOURCE_CLASSES,
        b.PUBLISHLAND,
        b.IMAGETEXT_TYPE,
        b.BOOK_NAME,
        b.PUBLISH_DATE,
        b.PRO_STATUS,
        d.DB_NAME,
        b.DB_ID,
        b.BOOK_DESC,
        b.MAIN_RESPONSIBILITY,
        b.UNIQUE_ID,
        b.NUMBER_COPIES,
        b.conglomeration,
        b.PIC_NUM,
        b.WORD_NUM,
        b.edition,
        b.collection,
        b.DC_METAID,
        b.IMPRESSION,
        b.KAIBEN_INFO, b.ocr_version,b.layout
        FROM t_pro_books b
        LEFT JOIN t_pro_database d ON b.DB_ID = d.ID
        where 1=1
        <if test="id != null and id != ''">
            and b.id = #{id}
        </if>
        <if test="proStatus != null and proStatus != ''">
            and b.PRO_STATUS=#{proStatus}
        </if>
        and b.DEL_FLAG !=1
        order by b.CREATE_TIME desc LIMIT 1

    </select>
    <select id="selectBookByUniqueId" parameterType="java.lang.String" resultMap="TProBooksResult">
        select id,pdf_path,cover_url from t_pro_books
        where unique_id = #{uniqueId}  and RESOURCE_TYPE = #{resourceType} and DEL_FLAG = 0
    </select>
</mapper>