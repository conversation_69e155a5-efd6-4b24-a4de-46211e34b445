<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.database.mapper.TProDatabaseMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TProDatabase" id="TProDatabaseResult">
        <result property="dbId"    column="ID"    />
        <result property="dbNameEn"    column="DB_NAME_EN"    />

        <result property="dbCode"    column="DB_CODE"    />
        <result property="dbName"    column="DB_NAME"    />
        <result property="bookCount"    column="BOOK_COUNT"    />
        <result property="dbStatus"    column="DB_STATUS"    />
        <result property="price"    column="PRICE"    />
        <result property="discount"    column="DISCOUNT"    />
        <result property="finalPrice"    column="FINAL_PRICE"    />
        <result property="coverUrl"    column="COVER_URL"    />
        <result property="dbDesc"    column="DB_DESC"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="dataDisplay"    column="DATA_DISPLAY"    />
        <result property="appletPic"    column="APPLET_PIC"    />
        <result property="bigCoverUrl"    column="BIG_COVER_URL"    />
        <result property="appScPic"    column="app_sc_pic"    />
    </resultMap>

    <sql id="selectTProDatabaseVo">
        select DB_NAME_EN, ID, DB_CODE, DB_NAME,BIG_COVER_URL, BOOK_COUNT, DB_STATUS, PRICE, DISCOUNT, FINAL_PRICE, COVER_URL, DB_DESC, CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,DATA_DISPLAY from t_pro_database
    </sql>
    <select id="selectTProDatabaseList" parameterType="TProDatabase" resultMap="TProDatabaseResult">
        select COVER_URL, BIG_COVER_URL,DB_NAME,DB_NAME_EN, PRICE, ID, DB_CODE,count(DB_ID) BOOK_COUNT,DB_DESC,CREATE_TIME,DB_STATUS,DATA_DISPLAY from t_pro_database d
        left JOIN (select DB_ID from t_pro_books  where DEL_FLAG !=1 and pro_status!=0) b
        ON b.DB_ID = d.ID
		where d.DEL_FLAG!=1
        <if test="dbCode != null  and dbCode != ''"> and d.DB_CODE = #{dbCode}</if>
            <if test="dbName != null  and dbName != ''"> and d.DB_NAME like concat('%', #{dbName}, '%')</if>
            <if test="bookCount != null "> and d.BOOK_COUNT = #{bookCount}</if>
            <if test="dbStatus != null "> and d.DB_STATUS = #{dbStatus}</if>
            <if test="price != null "> and d.PRICE = #{price}</if>
            <if test="discount != null "> and d.DISCOUNT = #{discount}</if>
            <if test="finalPrice != null "> and d.FINAL_PRICE = #{finalPrice}</if>
            <if test="coverUrl != null  and coverUrl != ''"> and d.COVER_URL = #{coverUrl}</if>
            <if test="dbDesc != null  and dbDesc != ''"> and d.DB_DESC = #{dbDesc}</if>
            <if test="createbyId != null  and createbyId != ''"> and d.CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and d.CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and d.CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and d.UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and d.UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and d.UPDATE_TIME = #{updateTime}</if>
            <if test="delFlag != null "> and d.DEL_FLAG = #{delFlag}</if>
            and d.DEL_FLAG = 0
            GROUP BY d.ID
            order by d.DATA_DISPLAY,d.create_time desc
    </select>
    
    <select id="selectTProDatabaseById" parameterType="String" resultMap="TProDatabaseResult">
        <include refid="selectTProDatabaseVo"/>
        where ID = #{dbId}
        and DEL_FLAG = 0
    </select>
        
    <insert id="insertTProDatabase" parameterType="TProDatabase">
        insert into t_pro_database
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dbId != null">ID,</if>
            <if test="dbCode != null">DB_CODE,</if>
            <if test="dbNameEn != null">DB_NAME_EN,</if>

            <if test="dbName != null and dbName != ''">DB_NAME,</if>
            <if test="bookCount != null">BOOK_COUNT,</if>
            <if test="dbStatus != null">DB_STATUS,</if>
            <if test="price != null">PRICE,</if>
            <if test="discount != null">DISCOUNT,</if>
            <if test="finalPrice != null">FINAL_PRICE,</if>
            <if test="coverUrl != null">COVER_URL,</if>
            <if test="dbDesc != null">DB_DESC,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="dataDisplay != null">DATA_DISPLAY,</if>
            <if test="bigCoverUrl != null">BIG_COVER_URL,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dbId != null">#{dbId},</if>
            <if test="dbNameEn != null">#{dbNameEn},</if>
            <if test="dbCode != null">#{dbCode},</if>
            <if test="dbName != null and dbName != ''">#{dbName},</if>
            <if test="bookCount != null">#{bookCount},</if>
            <if test="dbStatus != null">#{dbStatus},</if>
            <if test="price != null">#{price},</if>
            <if test="discount != null">#{discount},</if>
            <if test="finalPrice != null">#{finalPrice},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="dbDesc != null">#{dbDesc},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="dataDisplay != null">#{dataDisplay},</if>
            <if test="bigCoverUrl != null">#{bigCoverUrl},</if>
         </trim>
    </insert>

    <update id="updateTProDatabase" parameterType="TProDatabase">
        update t_pro_database
        <trim prefix="SET" suffixOverrides=",">
            <if test="dbNameEn != null">DB_NAME_EN=#{dbNameEn},</if>
            <if test="dbCode != null">DB_CODE = #{dbCode},</if>
            <if test="dbName != null and dbName != ''">DB_NAME = #{dbName},</if>
            <if test="bookCount != null">BOOK_COUNT = #{bookCount},</if>
            <if test="dbStatus != null">DB_STATUS = #{dbStatus},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="discount != null">DISCOUNT = #{discount},</if>
            <if test="finalPrice != null">FINAL_PRICE = #{finalPrice},</if>
            <if test="coverUrl != null">COVER_URL = #{coverUrl},</if>
            <if test="dbDesc != null">DB_DESC = #{dbDesc},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="dataDisplay != null">DATA_DISPLAY = #{dataDisplay},</if>
            <if test="bigCoverUrl != null">BIG_COVER_URL = #{bigCoverUrl},</if>
        </trim>
        where ID = #{dbId}
    </update>

    <delete id="deleteTProDatabaseById" parameterType="String">
        delete from t_pro_database where ID = #{id}
    </delete>

    <delete id="deleteTProDatabaseByIds" parameterType="String">
        delete from t_pro_database where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

   <select id="selectRe" resultMap="TProDatabaseResult">
       select ID,DB_CODE,DB_NAME from t_pro_database
   </select>
    <update id="updateCount" parameterType="String">
 update t_pro_database set BOOK_COUNT =BOOK_COUNT+1 where ID=#{orgdbId}

    </update>

    <update id="updateC" parameterType="String">
        update t_pro_database set BOOK_COUNT =BOOK_COUNT-1 where ID=#{dbId}
    </update>


    <select id="selecDbId" resultMap="TProDatabaseResult" parameterType="String">
        select ID  from t_pro_database where DB_NAME=#{dbName}
    </select>

    <select id="selectTProDatabase" parameterType="TProDatabase" resultMap="TProDatabaseResult" >

        select COVER_URL,BIG_COVER_URL, DB_NAME, PRICE, ID, DB_CODE,count(DB_ID) BOOK_COUNT,DB_NAME_EN,DB_DESC,APPLET_PIC,app_sc_pic from t_pro_database d
        left JOIN (select DB_ID from t_pro_books  where DEL_FLAG !=1 and pro_status!=0) b
        ON b.DB_ID = d.ID
        where d.DEL_FLAG!=1
        <if test="dbCode != null  and dbCode != ''"> and d.DB_CODE = #{dbCode}</if>
        <if test="dbName != null  and dbName != ''"> and d.DB_NAME like concat('%', #{dbName}, '%')</if>
        <if test="bookCount != null "> and d.BOOK_COUNT = #{bookCount}</if>
        <if test="dbStatus != null "> and d.DB_STATUS = #{dbStatus}</if>
        <if test="price != null "> and d.PRICE = #{price}</if>
        <if test="discount != null "> and d.DISCOUNT = #{discount}</if>
        <if test="finalPrice != null "> and d.FINAL_PRICE = #{finalPrice}</if>
        <if test="coverUrl != null  and coverUrl != ''"> and d.COVER_URL = #{coverUrl}</if>
        <if test="dbDesc != null  and dbDesc != ''"> and d.DB_DESC = #{dbDesc}</if>
        <if test="createbyId != null  and createbyId != ''"> and d.CREATEBY_ID = #{createbyId}</if>
        <if test="createbyName != null  and createbyName != ''"> and d.CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
        <if test="createTime != null "> and d.CREATE_TIME = #{createTime}</if>
        <if test="updatebyId != null  and updatebyId != ''"> and d.UPDATEBY_ID = #{updatebyId}</if>
        <if test="updatebyName != null  and updatebyName != ''"> and d.UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
        <if test="updateTime != null "> and d.UPDATE_TIME = #{updateTime}</if>
        <if test="delFlag != null "> and d.DEL_FLAG = #{delFlag}</if>
        and d.DB_STATUS = 1
        GROUP BY d.ID
        order by d.DATA_DISPLAY
    </select>


</mapper>