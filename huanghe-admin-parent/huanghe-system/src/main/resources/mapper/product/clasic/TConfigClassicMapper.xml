<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.clasic.mapper.TConfigClassicMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TConfigClassic" id="TConfigClassicResult">
        <result property="id"    column="id"    />
        <result property="classicName"    column="classic_name"    />
        <result property="classicCode"    column="classic_code"    />
        <result property="classicDesc"    column="classic_desc"    />
        <result property="createbyId"    column="createby_id"    />
        <result property="createbyName"    column="createby_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="display"    column="display"    />
        <result property="classicStatus"    column="classic_status"    />
    </resultMap>

    <sql id="selectTConfigClassicVo">
        select id, classic_name, classic_code, classic_desc, createby_id, createby_name, create_time,display,classic_status from t_config_classic
    </sql>

    <select id="selectTConfigClassicList" parameterType="TConfigClassic" resultMap="TConfigClassicResult">
        <include refid="selectTConfigClassicVo"/>
        <where>  
            <if test="classicName != null  and classicName != ''"> and classic_name like concat('%', #{classicName}, '%')</if>
            <if test="classicCode != null  and classicCode != ''"> and classic_code = #{classicCode}</if>
            <if test="classicDesc != null  and classicDesc != ''"> and classic_desc = #{classicDesc}</if>
            <if test="createbyId != null  and createbyId != ''"> and createby_id = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and createby_name like concat('%', #{createbyName}, '%')</if>
        </where>
        order by create_time,display desc
    </select>
    
    <select id="selectTConfigClassicById" parameterType="String" resultMap="TConfigClassicResult">
        <include refid="selectTConfigClassicVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTConfigClassic" parameterType="TConfigClassic">
        insert into t_config_classic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="classicName != null and classicName != ''">classic_name,</if>
            <if test="classicCode != null and classicCode != ''">classic_code,</if>
            <if test="classicDesc != null">classic_desc,</if>
            <if test="createbyId != null">createby_id,</if>
            <if test="createbyName != null">createby_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="display != null">display,</if>
            <if test="classicStatus != null">classic_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="classicName != null and classicName != ''">#{classicName},</if>
            <if test="classicCode != null and classicCode != ''">#{classicCode},</if>
            <if test="classicDesc != null">#{classicDesc},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="display != null">#{display},</if>
            <if test="classicStatus != null">#{classicStatus},</if>
         </trim>
    </insert>

    <update id="updateTConfigClassic" parameterType="TConfigClassic">
        update t_config_classic
        <trim prefix="SET" suffixOverrides=",">
            <if test="classicName != null and classicName != ''">classic_name = #{classicName},</if>
            <if test="classicCode != null and classicCode != ''">classic_code = #{classicCode},</if>
            <if test="classicDesc != null">classic_desc = #{classicDesc},</if>
            <if test="createbyId != null">createby_id = #{createbyId},</if>
            <if test="createbyName != null">createby_name = #{createbyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="display != null">display = #{display},</if>
            <if test="classicStatus != null">classic_status = #{classicStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTConfigClassicById" parameterType="String">
        delete from t_config_classic where id = #{id}
    </delete>

    <delete id="deleteTConfigClassicByIds" parameterType="String">
        delete from t_config_classic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>