<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.clasic.mapper.TConfigClassicTreeMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TConfigClassicTree" id="TConfigClassicTreeResult">
        <result property="id"    column="id"    />
        <result property="classicId"    column="classic_id"    />
        <result property="treeCode"    column="tree_code"    />
        <result property="treeName"    column="tree_name"    />
        <result property="treePid"    column="tree_pid"    />
        <result property="treeDesc"    column="tree_desc"    />
        <result property="display"    column="display"    />
        <result property="createbyId"    column="createby_id"    />
        <result property="createbyName"    column="createby_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="treeNameS"    column="tree_name_s"    />
    </resultMap>


    <sql id="selectTConfigClassicTreeVo">
        select id, classic_id, tree_code, tree_name, tree_pid, tree_desc, display, createby_id, createby_name, create_time,tree_name_s from t_config_classic_tree
    </sql>


    <select id="selectByName" parameterType="String" resultMap="TConfigClassicTreeResult">
        <include refid="selectTConfigClassicTreeVo"></include>
        where tree_Name = #{treeName}
    </select>

    <select id="selectTConfigClassicTreeList" parameterType="TConfigClassicTree" resultMap="TConfigClassicTreeResult">
        <include refid="selectTConfigClassicTreeVo"/>
        <where>
            <if test="classicId != null  and classicId != ''"> and classic_id = #{classicId}</if>
            <if test="treeCode != null  and treeCode != ''"> and tree_code = #{treeCode}</if>
            <if test="treeName != null  and treeName != ''"> and tree_name like concat('%', #{treeName}, '%')</if>
            <if test="treeNameS != null  and treeNameS != ''"> and tree_name_s = #{treeNameS}</if>
            <if test="treePid != null  and treePid != ''"> and tree_pid = #{treePid}</if>
            <if test="treeDesc != null  and treeDesc != ''"> and tree_desc = #{treeDesc}</if>
            <if test="display != null "> and display = #{display}</if>
            <if test="createbyId != null  and createbyId != ''"> and createby_id = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and createby_name like concat('%', #{createbyName}, '%')</if>
        </where>
        order by display asc
    </select>

    <select id="selectTConfigClassicTreeById" parameterType="String" resultMap="TConfigClassicTreeResult">
        <include refid="selectTConfigClassicTreeVo"/>
        where id = #{id}
    </select>

    <select id="select" parameterType="String" resultMap="TConfigClassicTreeResult">
        <include refid="selectTConfigClassicTreeVo"/>

    </select>
    <select id="selectClassicTreeById" parameterType="String" resultMap="TConfigClassicTreeResult">
        <include refid="selectTConfigClassicTreeVo"/>
        where classic_id=#{id} order by display
    </select>

    <select id="selectBySiName" parameterType="String" resultMap="TConfigClassicTreeResult">
        select id from t_config_classic_tree  where  tree_name=#{SiClassification}
    </select>
    <select id="selectHasChild" parameterType="TConfigClassicTree"
            resultType="cn.guliandigital.product.clasic.domain.TConfigClassicTree">
      select  id,tree_name from t_config_classic_tree where tree_pid =#{treePid}
    </select>
	<select id="selectP" parameterType="TConfigClassicTree"
	        resultType="cn.guliandigital.product.clasic.domain.TConfigClassicTree">
	     select  tree_code treeCode from t_config_classic_tree where id =#{treePid}
	</select>

    <select id="selectFourPartBehind"   resultType="cn.guliandigital.product.book.vo.ClassTreeType">
        select id, classic_id, tree_name as value,tree_name label,tree_pid treePid
        from t_config_classic_tree
        where classic_id='0da44b8378db4a21b795c4cf97ec52b0'
       <choose>
        <when test="treePid != null and treePid != ''">and tree_pid= #{treePid}
        </when>
        <otherwise>
          and (tree_pid is null or tree_pid ='')

        </otherwise>
       </choose>
    </select>

    <select id="selectById"   resultType="cn.guliandigital.product.book.vo.ClassTreeType" parameterType="String">
        select id, classic_id, tree_name as value,tree_name label,tree_pid treePid,display as display
        from t_config_classic_tree
        where id=#{treePid}
    </select>

     <select id="selectResourceClass"   resultType="cn.guliandigital.product.book.vo.ClassTreeType">
        select id, classic_id, tree_name as value,tree_name label,tree_name_s as valueSim,tree_name_s  labelSim, tree_pid treePid,tree_code as treeCode
        from t_config_classic_tree
        where classic_id='2ba44c2f8aa64de1a60eed30e1627225'
       <choose>
        <when test="treePid != null and treePid != ''">and tree_pid= #{treePid}
        </when>
        <otherwise>
          and (tree_pid is null or tree_pid ='')
        </otherwise>
       </choose>
       order by display asc
    </select>

    <select id="selectParent" resultMap="TConfigClassicTreeResult" parameterType="String">
        select tree_pid from t_config_classic_tree
        where id=  #{id}

    </select>
    <insert id="insertTConfigClassicTree" parameterType="TConfigClassicTree">
        insert into t_config_classic_tree
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="classicId != null and classicId != ''">classic_id,</if>
            <if test="treeCode != null and treeCode != ''">tree_code,</if>
            <if test="treeName != null and treeName != ''">tree_name,</if>
            <if test="treePid != null">tree_pid,</if>
            <if test="treeDesc != null">tree_desc,</if>
            <if test="display != null">display,</if>
            <if test="createbyId != null">createby_id,</if>
            <if test="createbyName != null">createby_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="treeNameS != null">tree_name_s,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="classicId != null and classicId != ''">#{classicId},</if>
            <if test="treeCode != null and treeCode != ''">#{treeCode},</if>
            <if test="treeName != null and treeName != ''">#{treeName},</if>
            <if test="treePid != null">#{treePid},</if>
            <if test="treeDesc != null">#{treeDesc},</if>
            <if test="display != null">#{display},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="treeNameS != null">#{treeNameS},</if>
         </trim>

    </insert>

    <update id="updateTConfigClassicTree" parameterType="TConfigClassicTree">
        update t_config_classic_tree
        <trim prefix="SET" suffixOverrides=",">
            <if test="classicId != null and classicId != ''">classic_id = #{classicId},</if>
            <if test="treeCode != null and treeCode != ''">tree_code = #{treeCode},</if>
            <if test="treeName != null and treeName != ''">tree_name = #{treeName},</if>
            <if test="treePid != null">tree_pid = #{treePid},</if>
            <if test="treeDesc != null">tree_desc = #{treeDesc},</if>
            <if test="display != null">display = #{display},</if>
            <if test="createbyId != null">createby_id = #{createbyId},</if>
            <if test="createbyName != null">createby_name = #{createbyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="treeNameS != null">tree_name_s = #{treeNameS},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTConfigClassicTreeById" parameterType="String">
        delete from t_config_classic_tree where id = #{id}
    </delete>

    <delete id="deleteTConfigClassicTreeByIds" parameterType="String">
        delete from t_config_classic_tree where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>