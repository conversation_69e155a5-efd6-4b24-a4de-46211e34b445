<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.userbook.mapper.TUserBookshelfClassesMapper">
    <!-- <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache> -->
    <resultMap type="TUserBookshelfClasses" id="TUserBookshelfClassesResult">
        <result property="id"    column="ID"    />
        <result property="classesName"    column="CLASSES_NAME"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="bookNum"    column="BOOK_NUM"    />
    </resultMap>

    <sql id="selectTUserBookshelfClassesVo">
        select ID, CLASSES_NAME,CREATE_TIME,DEL_FLAG from t_user_bookshelf_classes
    </sql>

    <select id="selectTUserBookshelfClassesList" parameterType="TUserBookshelfClasses" resultMap="TUserBookshelfClassesResult">
        SELECT
        ID,
        CLASSES_NAME,
        CREATE_TIME,
        (
        SELECT
        count(*) AS BOOK_NUM
        FROM
        t_user_bookshelf b LEFT JOIN t_pro_books book ON book.ID = b.BOOK_ID
        WHERE
        book.DEL_FLAG=0  and
        b.CLASSES_ID = c.ID
        )  as BOOK_NUM
        FROM
        t_user_bookshelf_classes c
        <where>  
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
        </where>
        order by CREATE_TIME desc
    </select>
    
    <select id="selectClassesList" parameterType="TUserBookshelfClasses" resultMap="TUserBookshelfClassesResult">
        SELECT
		ID,
		CLASSES_NAME,
		CREATE_TIME		
		FROM
		t_user_bookshelf_classes 
        <where>  
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="classesName != null  and classesName != ''"> and CLASSES_NAME = #{classesName}</if>
        </where>
    </select>
    
    <select id="selectTUserBookshelfClassesById" parameterType="String" resultMap="TUserBookshelfClassesResult">
        <include refid="selectTUserBookshelfClassesVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTUserBookshelfClasses" parameterType="TUserBookshelfClasses">
        insert into t_user_bookshelf_classes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="classesName != null">CLASSES_NAME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="classesName != null">#{classesName},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTUserBookshelfClasses" parameterType="TUserBookshelfClasses">
        update t_user_bookshelf_classes
        <trim prefix="SET" suffixOverrides=",">
            <if test="classesName != null">CLASSES_NAME = #{classesName},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserBookshelfClassesById" parameterType="String">
        delete from t_user_bookshelf_classes where ID = #{id}
    </delete>

    <delete id="deleteTUserBookshelfClassesByIds" parameterType="String">
        delete from t_user_bookshelf_classes where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>