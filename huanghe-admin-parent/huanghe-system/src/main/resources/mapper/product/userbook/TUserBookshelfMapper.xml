<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.userbook.mapper.TUserBookshelfMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserBookshelf" id="TUserBookshelfResult">
        <result property="id"    column="ID"    />
        <result property="classesId"    column="CLASSES_ID"    />
        <result property="bookId"    column="BOOK_ID"    />
        <result property="bookName"    column="BOOK_NAME"    />
        <result property="author"    column="AUTHOR"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="coverUrl"    column="COVER_URL"    />
        <result property="imageTextType"    column="IMAGETEXT_TYPE"    />

    </resultMap>

    <sql id="selectTUserBookshelfVo">
        s.ID, CLASSES_ID, BOOK_ID, s.BOOK_NAME, AUTHOR,COVER_URL
    </sql>

    <select id="selectTUserBookshelfList" parameterType="TUserBookshelf" resultMap="TUserBookshelfResult">
        select
          s.ID, CLASSES_ID, BOOK_ID, s.BOOK_NAME, AUTHOR,COVER_URL,IMAGETEXT_TYPE
        from
        t_user_bookshelf s ,t_pro_books b
        where
            s.BOOK_ID = b.ID
            <if test="classesId != null  and classesId != ''"> and CLASSES_ID = #{classesId}</if>
            <if test="createbyId != null  and createbyId != ''"> and s.CREATEBY_ID = #{createbyId}</if>
            <if test="bookId != null  and bookId != ''"> and BOOK_ID = #{bookId}</if>
        order by s.create_Time DESC
    </select>
    
    <select id="selectTUserBookshelfById" parameterType="String" resultMap="TUserBookshelfResult">
       
        select <include refid="selectTUserBookshelfVo"/>
		FROM
			t_user_bookshelf s LEFT JOIN 
			t_pro_books b
		on s.BOOK_ID = b.ID
		WHERE s.ID = #{id} 
    </select>
        
    <insert id="insertTUserBookshelf" parameterType="TUserBookshelf">
        insert into t_user_bookshelf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="classesId != null and classesId != ''">CLASSES_ID,</if>
            <if test="bookId != null and bookId != ''">BOOK_ID,</if>
            <if test="bookName != null">BOOK_NAME,</if>
            <if test="author != null">AUTHOR,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="classesId != null and classesId != ''">#{classesId},</if>
            <if test="bookId != null and bookId != ''">#{bookId},</if>
            <if test="bookName != null">#{bookName},</if>
            <if test="author != null">#{author},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTUserBookshelf" parameterType="TUserBookshelf">
        update t_user_bookshelf
        <trim prefix="SET" suffixOverrides=",">
            <if test="classesId != null and classesId != ''">CLASSES_ID = #{classesId},</if>
            <if test="bookId != null and bookId != ''">BOOK_ID = #{bookId},</if>
            <if test="bookName != null">BOOK_NAME = #{bookName},</if>
            <if test="author != null">AUTHOR = #{author},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserBookshelfById" parameterType="String">
        delete from t_user_bookshelf where ID = #{id}
    </delete>

    <delete id="deleteTUserBookshelfByIds" parameterType="String">
        delete from t_user_bookshelf where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTUserBookshelfByBookId" parameterType="String">
        delete from t_user_bookshelf where BOOK_ID = #{bookId}
    </delete>

</mapper>