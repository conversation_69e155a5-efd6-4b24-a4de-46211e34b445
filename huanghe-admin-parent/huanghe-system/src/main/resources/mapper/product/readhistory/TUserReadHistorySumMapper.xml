<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.readhistory.mapper.TUserReadHistorySumMapper">
	<cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserReadHistorySum" id="TUserReadHistorySumResult">
        <result property="id" column="ID"/>
        <result property="bookId" column="BOOK_ID"/>
        <result property="readTime" column="READ_TIME"/>
        <result property="readPeoples" column="READ_PEOPLES"/>
        <result property="searchCount" column="search_count"/>
        <result property="visitCount" column="visit_count"/>        
        <result property="readDate" column="READ_DATE"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="mainResponsibility" column="MAIN_RESPONSIBILITY"/>
        <result property="bookName" column="BOOK_NAME"/>
        <result property="thumbCoverUrl" column="THUMB_COVER_URL"/>
        <result property="publisher" column="PUBLISHER"/>
        <result property="resourceType" column="RESOURCE_TYPE"/>
        <result property="resourceClasses" column="RESOURCE_CLASSES"/>
    </resultMap>

    <sql id="selectTUserReadHistorySumVo">
        select
          ID, BOOK_ID, READ_TIME, READ_PEOPLES, READ_DATE, ORG_ID,search_count,visit_count
        from
          t_user_read_history_sum 
          
    </sql>

    <select id="selectTUserReadHistorySumList" parameterType="TUserReadHistorySum"
            resultMap="TUserReadHistorySumResult">
        SELECT
        min(s.ID),
        s.BOOK_ID,
        sum( s.READ_TIME ) AS READ_TIME,
        sum( s.READ_PEOPLES ) AS READ_PEOPLES,
        min(s.READ_DATE) as  READ_DATE,
        min(s.search_count )AS search_count,
        min(s.ORG_ID) AS ORG_ID,
        min(s.visit_count)AS visit_count,
        min(b.book_name)AS book_name,
        min(b.RESOURCE_TYPE)AS RESOURCE_TYPE,
        min(b.RESOURCE_CLASSES)AS RESOURCE_CLASSES,
        min(b.MAIN_RESPONSIBILITY)AS MAIN_RESPONSIBILITY,
        min(b.PUBLISHER)AS PUBLISHER
		FROM
			t_user_read_history_sum s
		LEFT JOIN t_pro_books b ON s.book_id = b.id
		WHERE
			1 = 1
        <if test="orgId != null  and orgId != ''">and s.ORG_ID = #{orgId}</if>
        <if test="mainResponsibility != null  and mainResponsibility != ''">and b.MAIN_RESPONSIBILITY like concat('%',
            #{mainResponsibility}, '%')
        </if>
        <if test="bookName != null  and bookName != ''">and b.book_name like concat('%', #{bookName}, '%')</if>        
        <if test="resourceClasses != null  and resourceClasses != ''">and b.RESOURCE_CLASSES like concat('%', #{resourceClasses}, '%')</if>
        <if test="beginTime != null  and beginTime != ''">and s.READ_DATE <![CDATA[ >= ]]> #{beginTime}</if>
        <if test="endTime != null  and endTime != ''">and s.READ_DATE <![CDATA[ <= ]]> #{endTime}</if>
        group by BOOK_ID
    </select>


    <insert id="insertTUserReadHistorySum" parameterType="TUserReadHistorySum">
        insert into t_user_read_history_sum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="copyCount != null">COPY_COUNT,</if>
            <if test="quoteCount != null">QUOTE_COUNT,</if>
            <if test="bookId != null">BOOK_ID,</if>
            <if test="readTime != null">READ_TIME,</if>
            <if test="readPeoples != null">READ_PEOPLES,</if>
            <if test="readDate != null">READ_DATE,</if>
            <if test="orgId != null">ORG_ID,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="visitCount != null">visit_count,</if>
            <if test="searchCount != null">search_count,</if>
            <if test="noteCount != null">note_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="copyCount != null">#{copyCount},</if>
            <if test="quoteCount != null">#{quoteCount},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="readPeoples != null">#{readPeoples},</if>
            <if test="readDate != null">#{readDate},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="visitCount != null">#{visitCount},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="noteCount != null">#{noteCount},</if>
        </trim>
    </insert>

    <update id="updateTUserReadHistorySum" parameterType="TUserReadHistorySum">
        update t_user_read_history_sum
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">BOOK_ID = #{bookId},</if>
            <if test="copyCount != null">COPY_COUNT =#{copyCount},</if>
            <if test="quoteCount != null">QUOTE_COUNT =#{quoteCount},</if>
            <if test="readTime != null">READ_TIME = #{readTime},</if>
            <if test="readPeoples != null">READ_PEOPLES = #{readPeoples},</if>
            <if test="visitCount != null">visit_count = #{visitCount},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="readDate != null">READ_DATE = #{readDate},</if>
            <if test="orgId != null">ORG_ID = #{orgId},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="noteCount != null">note_count = #{noteCount},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserReadHistorySumById" parameterType="String">
        delete from t_user_read_history_sum where ID = #{id}
    </delete>
    <update id="updateReadTimeAndSearchPeoPle" parameterType="TUserReadHistorySum">
    update  t_user_read_history_sum set READ_TIME=#{readTime},READ_PEOPLES=#{readPeople}
    where ORG_ID=#{orgId} and BOOK_ID =#{bookId}  and READ_DATE=DATE_FORMAT(NOW(),'%Y-%m-%d')
</update>
    <select id="selectId" resultMap="TUserReadHistorySumResult" parameterType="TUserReadHistorySum">
        select 
        id  
        from t_user_read_history_sum 
        where 1=1 
        and ORG_ID=#{orgId} 
        and BOOK_ID =#{bookId} 
        and READ_DATE=#{queryDate}
    </select>
    <select id="seletorganCount" resultType="TUserReadHistorySum" parameterType="TUserReadHistorySum">
        SELECT
			max(b.BOOK_NAME) bookName,
			max(b.RESOURCE_CLASSES) resourceClasses,
			max(d.DB_NAME) dbName,
            sum(h.QUOTE_COUNT) quoteCount,
            sum(h.COPY_COUNT) copyCount,
			sum(h.VISIT_COUNT) visitCount,
			sum(h.NOTE_COUNT) noteCount,
			sum(h.SEARCH_COUNT) searchCount
		FROM
			t_user_read_history_sum h
			LEFT JOIN t_pro_books b ON b.ID = h.BOOK_ID
			LEFT JOIN t_pro_database d ON d.id = b.DB_ID
		WHERE
			1 = 1
		AND b.DEL_FLAG != 1
        <if test="bookName != null  and bookName != ''">and b.book_name like concat('%', #{bookName}, '%')</if>

        GROUP BY h.BOOK_ID
    </select>
    <delete id="deleteTUserReadHistorySumByIds" parameterType="String">
        delete from t_user_read_history_sum where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>