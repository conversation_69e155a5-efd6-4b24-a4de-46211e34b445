<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.readhistory.mapper.TUserReadHistoryMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TUserReadHistory" id="TUserReadHistoryResult">
        <result property="id"    column="ID"    />
        <result property="bookId"    column="BOOK_ID"    />
        <result property="menuId"    column="MENU_ID"    />
        <result property="menuName"    column="MENU_NAME"    />
        <result property="menuPath"    column="MENU_PATH"    />
        <result property="readTime"    column="READ_TIME"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="mainResponsibility"    column="MAIN_RESPONSIBILITY"    />
        <result property="bookName"    column="BOOK_NAME"    />
        <result property="thumbCoverUrl"    column="THUMB_COVER_URL"    />
        <result property="orgId"    column="ORG_ID"    />
        <result property="readType"    column="READ_TYPE"    />
        <result property="imageTextType"    column="IMAGETEXT_TYPE"    />
        <result property="readMode"    column="READ_MODE"    />
    </resultMap>

    <sql id="selectTUserReadHistoryVo">
        select
          h.ID, h.BOOK_ID, MENU_ID, h.MENU_NAME, MENU_PATH, READ_TIME,MAIN_RESPONSIBILITY,
          BOOK_NAME,h.CREATE_TIME,b.THUMB_COVER_URL,h.READ_TYPE,b.IMAGETEXT_TYPE,h.READ_MODE
        from
          t_user_read_history h,
          t_pro_books b,
          t_pro_book_menu m
    </sql>

    <select id="selectTUserReadHistoryList" parameterType="TUserReadHistory" resultMap="TUserReadHistoryResult">
        <include refid="selectTUserReadHistoryVo"/>
        <where>
            h.BOOK_ID = b.ID and h.MENU_ID = m.ID
            <if test="createbyId != null  and createbyId != ''"> and h.CREATEBY_ID = #{createbyId}</if>
            <if test="dataFrom != null">and DATA_FROM = #{dataFrom} </if>
        </where>
    </select>
    
    <select id="selectTUserReadHistoryById" parameterType="String" resultMap="TUserReadHistoryResult">
        <include refid="selectTUserReadHistoryVo"/>
        where ID = #{id} and  h.BOOK_ID = b.ID and h.MENU_ID = m.ID
    </select>
        
    <insert id="insertTUserReadHistory" parameterType="TUserReadHistory">
        insert into t_user_read_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="bookId != null">BOOK_ID,</if>
            <if test="menuId != null">MENU_ID,</if>
            <if test="menuName != null">MENU_NAME,</if>
            <if test="menuPath != null">MENU_PATH,</if>
            <if test="readTime != null">READ_TIME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="orgId != null">ORG_ID,</if>
            <if test="readType != null">READ_TYPE,</if>
            <if test="readMode != null">READ_MODE,</if>
            <if test="dataFrom != null">DATA_FROM,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="menuName != null">#{menuName},</if>
            <if test="menuPath != null">#{menuPath},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="readType != null">#{readType},</if>
            <if test="readMode != null">#{readMode},</if>
            <if test="dataFrom != null">#{dataFrom},</if>
         </trim>
    </insert>

    <update id="updateTUserReadHistory" parameterType="TUserReadHistory">
        update t_user_read_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">BOOK_ID = #{bookId},</if>
            <if test="menuId != null">MENU_ID = #{menuId},</if>
            <if test="menuName != null">MENU_NAME = #{menuName},</if>
            <if test="menuPath != null">MENU_PATH = #{menuPath},</if>
            <if test="readTime != null">READ_TIME = #{readTime},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="orgId != null">ORG_ID = #{orgId},</if>
            <if test="readType != null">READ_TYPE = #{readType},</if>
            <if test="readMode != null">READ_MODE = #{readMode},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserReadHistoryById" parameterType="String">
        delete from t_user_read_history where ID = #{id}
    </delete>
	<select id="selectReadTimeAndSearchPeoPle" resultMap="TUserReadHistoryResult" parameterType="String">
	
		select 
		book_id, 
		ORG_ID,
		sum(READ_TIME) READ_TIME,		
		count(distinct CREATEBY_ID) readPeople,
		count(id) as visitCount
		from t_user_read_history
		where 1=1 
		and DATE_FORMAT(CREATE_TIME,'%Y-%m-%d')=#{queryDate}
		and READ_TYPE = 'R'
		GROUP BY book_id, ORG_ID
	
	</select>
	<select id="selectQueryCount" resultMap="TUserReadHistoryResult" parameterType="String">
	
		select 
		book_id, 
		ORG_ID,
		count(id) as searchCount,
		READ_TYPE
		from t_user_read_history
		where 1=1 
		and DATE_FORMAT(CREATE_TIME,'%Y-%m-%d')=#{queryDate}		
		GROUP BY book_id, ORG_ID,READ_TYPE
	
	</select>

    <delete id="deleteTUserReadHistoryByIds" parameterType="String">
        delete from t_user_read_history where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTUserReadHistoryByBookId" parameterType="String">
        delete from t_user_read_history where book_id = #{bookId}
    </delete>

</mapper>