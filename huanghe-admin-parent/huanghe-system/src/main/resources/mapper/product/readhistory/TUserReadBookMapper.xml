<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.readhistory.mapper.TUserReadBookMapper">
    
    <resultMap type="TUserReadBook" id="TUserReadBookResult">
        <result property="id"    column="ID"    />
        <result property="bookId"    column="BOOK_ID"    />
        <result property="menuId"    column="MENU_ID"    />
        <result property="menuName"    column="MENU_NAME"    />
        <result property="menuPath"    column="MENU_PATH"    />
        <result property="orgId"    column="ORG_ID"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="mainResponsibility"    column="MAIN_RESPONSIBILITY"    />
        <result property="bookName"    column="BOOK_NAME"    />
        <result property="thumbCoverUrl"    column="THUMB_COVER_URL"    />       
        <result property="imageTextType"    column="IMAGETEXT_TYPE"    />
        <result property="readMode"    column="READ_MODE"    />
        <result property="pageNo"    column="PAGE_NO"    />
    </resultMap>

    <sql id="selectTUserReadBookVoT">
        select ID, BOOK_ID, MENU_ID, MENU_NAME, MENU_PATH, ORG_ID, CREATEBY_ID, 
        CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME,
         DEL_FLAG from t_user_read_book
    </sql>
    <sql id="selectTUserReadBookVo">
        select
          h.ID, h.BOOK_ID, h.MENU_ID, m.MENU_NAME, m.FULL_MENU_NAME as MENU_PATH, MAIN_RESPONSIBILITY,
          BOOK_NAME,h.CREATE_TIME,h.UPDATE_TIME,b.THUMB_COVER_URL,b.IMAGETEXT_TYPE,
          h.READ_MODE,h.PAGE_NO
        from
          t_user_read_book h,
          t_pro_books b,
          t_pro_book_menu m
    </sql>

    <select id="selectTUserReadBookList" parameterType="TUserReadBook" resultMap="TUserReadBookResult">
        <include refid="selectTUserReadBookVo"/>
        <where>  
        	h.BOOK_ID = b.ID and h.MENU_ID = m.ID
            <if test="createbyId != null  and createbyId != ''"> and h.CREATEBY_ID = #{createbyId}</if>
            <if test="bookId != null  and bookId != ''"> and h.BOOK_ID = #{bookId}</if>             
        </where>
    </select>
    <select id="selectTUserReadBookListOrderBy" parameterType="TUserReadBook" resultMap="TUserReadBookResult">
        <include refid="selectTUserReadBookVo"/>
        <where>
            h.BOOK_ID = b.ID and h.MENU_ID = m.ID
            <if test="createbyId != null  and createbyId != ''"> and h.CREATEBY_ID = #{createbyId}</if>
            <if test="bookId != null  and bookId != ''"> and h.BOOK_ID = #{bookId}</if>
             order by h.UPDATE_TIME desc
        </where>
    </select>
    <select id="selectList" parameterType="TUserReadBook" resultMap="TUserReadBookResult">
        <include refid="selectTUserReadBookVoT"/>
        <where>        	
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="bookId != null  and bookId != ''"> and BOOK_ID = #{bookId}</if>             
        </where>
    </select>
    
    <select id="selectTUserReadBookById" parameterType="String" resultMap="TUserReadBookResult">
        <include refid="selectTUserReadBookVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTUserReadBook" parameterType="TUserReadBook">
        insert into t_user_read_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="bookId != null">BOOK_ID,</if>
            <if test="menuId != null">MENU_ID,</if>
            <if test="menuName != null">MENU_NAME,</if>
            <if test="menuPath != null">MENU_PATH,</if>
            <if test="orgId != null">ORG_ID,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="readMode != null">READ_MODE,</if>
            <if test="pageNo != null">PAGE_NO,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="menuName != null">#{menuName},</if>
            <if test="menuPath != null">#{menuPath},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="readMode != null">#{readMode},</if>
            <if test="pageNo != null">#{pageNo},</if>
         </trim>
    </insert>

    <update id="updateTUserReadBook" parameterType="TUserReadBook">
        update t_user_read_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">BOOK_ID = #{bookId},</if>
            <if test="menuId != null">MENU_ID = #{menuId},</if>
            <if test="menuName != null">MENU_NAME = #{menuName},</if>
            <if test="menuPath != null">MENU_PATH = #{menuPath},</if>
            <if test="orgId != null">ORG_ID = #{orgId},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="readMode != null">READ_MODE = #{readMode},</if>
            <if test="pageNo != null">PAGE_NO = #{pageNo},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTUserReadBookById" parameterType="String">
        delete from t_user_read_book where ID = #{id}
    </delete>

    <delete id="deleteTUserReadBookByIds" parameterType="String">
        delete from t_user_read_book where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTUserBookByBookId" parameterType="String">
        delete from t_user_read_book where BOOK_ID = #{bookId}
    </delete>

</mapper>