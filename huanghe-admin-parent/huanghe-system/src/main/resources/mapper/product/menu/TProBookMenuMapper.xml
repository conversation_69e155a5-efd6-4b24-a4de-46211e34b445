<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.product.menu.mapper.TProBookMenuMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TProBookMenu" id="TProBookMenuResult">
        <result property="id"    column="ID"    />
        <result property="menuName"    column="MENU_NAME"    />
        <result property="bookId"    column="BOOK_ID"    />
        <result property="pid"    column="PID"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="display"    column="DISPLAY"    />
        <result property="fullMenuName"    column="FULL_MENU_NAME"    />
        <result property="startPageNo"    column="START_PAGE_NO"    />
        <result property="level"    column="LEVEL"    />
    </resultMap>

    <sql id="selectTProBookMenuVo">
        select ID, MENU_NAME, BOOK_ID, PID,DISPLAY, FULL_MENU_NAME,START_PAGE_NO,`LEVEL`,
        CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, 
        DEL_FLAG from t_pro_book_menu
    </sql>

    <select id="selectTProBookMenuList" parameterType="TProBookMenu" resultMap="TProBookMenuResult">
        <include refid="selectTProBookMenuVo"/>
        <where>  
            <if test="bookId != null  and bookId != ''"> and BOOK_ID = #{bookId}</if>
            <if test="pid != null  and pid != ''"> and PID = #{pid}</if>
            <if test="fullMenuName != null  and fullMenuName != ''"> and FULL_MENU_NAME = #{fullMenuName}</if>
            <if test="display != null"> and DISPLAY = #{display}</if>
        </where>
        <if test="params.orderbydisplay != null"> order by DISPLAY asc</if>
    </select>
    
    <select id="selectTProBookMenuById" parameterType="String" resultMap="TProBookMenuResult">
        <include refid="selectTProBookMenuVo"/>
        where ID = #{id}
    </select>
    
    <select id="selectTProBookMenuByPid" parameterType="String" resultMap="TProBookMenuResult">
        <include refid="selectTProBookMenuVo"/>
        where pid = #{pid}
        order by display asc
        limit 1
    </select>
    
    <insert id="insertTProBookMenu" parameterType="TProBookMenu">
        insert into t_pro_book_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="menuName != null">MENU_NAME,</if>
            <if test="bookId != null">BOOK_ID,</if>
            <if test="pid != null">PID,</if>
            <if test="display != null">DISPLAY,</if>
            <if test="fullMenuName != null">FULL_MENU_NAME,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="startPageNo != null">START_PAGE_NO,</if>
            <if test="level != null">`LEVEL`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="menuName != null">#{menuName},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="pid != null">#{pid},</if>
            <if test="display != null">#{display},</if>
            <if test="fullMenuName != null">#{fullMenuName},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="startPageNo != null">#{startPageNo},</if>
            <if test="level != null">#{level},</if>
         </trim>
    </insert>

    <update id="updateTProBookMenu" parameterType="TProBookMenu">
        update t_pro_book_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuName != null">MENU_NAME = #{menuName},</if>
            <if test="bookId != null">BOOK_ID = #{bookId},</if>
            <if test="pid != null">PID = #{pid},</if>
            <if test="display != null">DISPLAY = #{display},</if>
            <if test="fullMenuName != null">FULL_MENU_NAME = #{fullMenuName},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="startPageNo != null">START_PAGE_NO = #{startPageNo},</if>
            <if test="level != null">`LEVEL` = #{level},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTProBookMenuById" parameterType="String">
        delete from t_pro_book_menu where ID = #{id}
    </delete>

	<delete id="deleteTProBookMenuByBookId" parameterType="String">
        delete from t_pro_book_menu where BOOK_ID = #{bookId}
    </delete>
    <delete id="deleteTProBookMenuByIds" parameterType="String">
        delete from t_pro_book_menu where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>