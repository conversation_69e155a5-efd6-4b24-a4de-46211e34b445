<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.storage.storage.mapper.TTaskSettleMapper">
    <!-- <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache> -->
    <resultMap type="TTaskSettle" id="TTaskSettleResult">
        <result property="id"    column="ID"    />
        <result property="dataPath"    column="DATA_PATH"    />
        <result property="dataStatus"    column="DATA_STATUS"    />
        <result property="parseStartTime"    column="PARSE_START_TIME"    />
        <result property="parseEndTime"    column="PARSE_END_TIME"    />
        <result property="dataRemark"    column="DATA_REMARK"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
        <result property="readMode"    column="READ_MODE"    />
        <result property="uniqueId"    column="UNIQUE_ID"    />
        <result property="parseMethod"    column="PARSE_METHOD"    />
        <result property="source"    column="source"    />
        <result property="bookName"    column="BOOK_NAME"    />
		<result property="dbName"    column="DB_NAME"    />
		<result property="inStatus"    column="in_status"    />
		<result property="pdfCount"    column="pdf_count"    />
		<result property="fullCount"    column="full_count"    />
		<result property="dataJson"    column="data_json"    />

    </resultMap>

    <sql id="selectTTaskSettleVo">
        select  ID, DATA_PATH,READ_MODE, DATA_STATUS, PARSE_START_TIME, PARSE_END_TIME, DATA_REMARK, 
        CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG,
        PARSE_METHOD,UNIQUE_ID,source,BOOK_NAME,in_status,DB_NAME,pdf_count,full_count,data_json from t_task_settle
    </sql>

    <select id="selectTTaskSettleList" parameterType="TTaskSettle" resultMap="TTaskSettleResult">
        <include refid="selectTTaskSettleVo"/>
        <where>  
            <if test="uniqueId != null  and uniqueId != ''"> and UNIQUE_ID = #{uniqueId}</if>
            <if test="dataPath != null  and dataPath != ''"> and DATA_PATH = #{dataPath}</if>
            <if test="readMode != null  and readMode != ''"> and READ_MODE = #{readMode}</if>
            <if test="dataStatus != null  and dataStatus != ''"> and DATA_STATUS = #{dataStatus}</if>
            <if test="dataStatusNot != null  and dataStatusNot != ''"> and DATA_STATUS != #{dataStatusNot}</if>
            <if test="parseStartTime != null "> and PARSE_START_TIME = #{parseStartTime}</if>
            <if test="parseEndTime != null "> and PARSE_END_TIME = #{parseEndTime}</if>
            <if test="dataRemark != null  and dataRemark != ''"> and DATA_REMARK = #{dataRemark}</if>
            <if test="createbyId != null  and createbyId != ''"> and CREATEBY_ID = #{createbyId}</if>
            <if test="createbyName != null  and createbyName != ''"> and CREATEBY_NAME like concat('%', #{createbyName}, '%')</if>
            <if test="createTime != null "> and CREATE_TIME = #{createTime}</if>
            <if test="updatebyId != null  and updatebyId != ''"> and UPDATEBY_ID = #{updatebyId}</if>
            <if test="updatebyName != null  and updatebyName != ''"> and UPDATEBY_NAME like concat('%', #{updatebyName}, '%')</if>
            <if test="updateTime != null "> and UPDATE_TIME = #{updateTime}</if>
            <if test="inStatus != null  and inStatus != ''"> and in_status = #{inStatus}</if>
            <if test="delFlag != null "> and DEL_FLAG = #{delFlag}</if>
            <if test="bookName != null and bookName != ''"> and BOOK_NAME like concat('%', #{bookName}, '%')</if>
             and DEL_FLAG!=1
             order by CREATE_TIME desc
        </where>
    </select>
    
    <select id="selectTTaskSettleById" parameterType="string" resultMap="TTaskSettleResult">
        <include refid="selectTTaskSettleVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTTaskSettle" parameterType="TTaskSettle">
        insert into t_task_settle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="readMode != null">READ_MODE,</if>
            <if test="dataPath != null">DATA_PATH,</if>
            <if test="dataStatus != null">DATA_STATUS,</if>
            <if test="parseStartTime != null">PARSE_START_TIME,</if>
            <if test="parseEndTime != null">PARSE_END_TIME,</if>
            <if test="dataRemark != null">DATA_REMARK,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="parseMethod != null">PARSE_METHOD,</if>
            <if test="uniqueId != null">UNIQUE_ID,</if>
            <if test="inStatus != null">in_status,</if>
            <if test="dbName != null">db_name,</if>
            <if test="source != null">source,</if>
            <if test="bookName != null">book_name,</if>
            <if test="pdfCount != null">pdf_count,</if>
            <if test="fullCount != null">full_count,</if>
            <if test="dataJson != null">data_json,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="readMode != null">#{readMode},</if>
            <if test="dataPath != null">#{dataPath},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="parseStartTime != null">#{parseStartTime},</if>
            <if test="parseEndTime != null">#{parseEndTime},</if>
            <if test="dataRemark != null">#{dataRemark},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="parseMethod != null">#{parseMethod},</if>
            <if test="uniqueId != null">#{uniqueId},</if>
            <if test="inStatus != null">#{inStatus},</if>
            <if test="dbName != null">#{dbName},</if>
            <if test="source != null">#{source},</if>
            <if test="bookName != null">#{bookName},</if>
            <if test="pdfCount != null">#{pdfCount},</if>
            <if test="fullCount != null">#{fullCount},</if>
            <if test="dataJson != null">#{dataJson},</if>
         </trim>
    </insert>

    <update id="updateTTaskSettle" parameterType="TTaskSettle">
        update t_task_settle
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataPath != null">DATA_PATH = #{dataPath},</if>
            <if test="readMode != null">READ_MODE =#{readMode},</if>
            <if test="dataStatus != null">DATA_STATUS = #{dataStatus},</if>
            <if test="parseStartTime != null">PARSE_START_TIME = #{parseStartTime},</if>
            <if test="parseEndTime != null">PARSE_END_TIME = #{parseEndTime},</if>
            <if test="dataRemark != null">DATA_REMARK = #{dataRemark},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="parseMethod != null">PARSE_METHOD = #{parseMethod},</if>
            <if test="uniqueId != null">UNIQUE_ID = #{uniqueId},</if>
            <if test="dbName != null">db_name = #{dbName},</if>
            <if test="source != null">source = #{source},</if>
            <if test="bookName != null">book_name = #{bookName},</if>
            <if test="inStatus != null">in_status = #{inStatus},</if>
            <if test="pdfCount != null">pdf_count = #{pdfCount},</if>
            <if test="fullCount != null">full_count = #{fullCount},</if>
            <if test="dataJson != null">data_json = #{dataJson},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTTaskSettleById" parameterType="string">
        delete from t_task_settle where ID = #{id}
    </delete>

    <delete id="deleteTTaskSettleByIds" parameterType="string">
        delete from t_task_settle where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updateStatus" parameterType="string">
        update t_task_settle set DATA_STATUS=1,PARSE_START_TIME=now() where id=#{id}
    </update>
    <update id="updateSettle">
        update t_task_settle set DEL_FLAG=1 where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>