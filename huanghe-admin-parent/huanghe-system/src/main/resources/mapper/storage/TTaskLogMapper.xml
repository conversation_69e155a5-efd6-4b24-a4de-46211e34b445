<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.storage.log.mapper.TTaskLogMapper">
    <!-- <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache> -->
    <resultMap type="TTaskLog" id="TTaskLogResult">
        <result property="id"    column="ID"    />
        <result property="taskId"    column="TASK_ID"    />
        <result property="fileName"    column="FILE_NAME"    />
        <result property="parseStartTime"    column="PARSE_START_TIME"    />
        <result property="parseEndTime"    column="PARSE_END_TIME"    />
        <result property="dataStatus"    column="DATA_STATUS"    />
        <result property="errorMsg"    column="ERROR_MSG"    />
        <result property="retryTimes"    column="RETRY_TIMES"    />
        <result property="dataDesc"    column="DATA_DESC"    />
        <result property="imagetextType"    column="IMAGE_TEXT_TYPE"    />
        <result property="createbyId"    column="CREATEBY_ID"    />
        <result property="createbyName"    column="CREATEBY_NAME"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updatebyId"    column="UPDATEBY_ID"    />
        <result property="updatebyName"    column="UPDATEBY_NAME"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
        <result property="delFlag"    column="DEL_FLAG"    />
    </resultMap>

    <sql id="selectTTaskLogVo">
        select ID, TASK_ID, FILE_NAME, PARSE_START_TIME, PARSE_END_TIME, DATA_STATUS, ERROR_MSG, RETRY_TIMES, DATA_DESC, IMAGE_TEXT_TYPE,CREATEBY_ID, CREATEBY_NAME, CREATE_TIME, UPDATEBY_ID, UPDATEBY_NAME, UPDATE_TIME, DEL_FLAG from t_task_log
    </sql>

    <select id="selectTTaskLogList" parameterType="TTaskLog" resultMap="TTaskLogResult">
        <include refid="selectTTaskLogVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and FILE_NAME like concat('%', #{fileName}, '%')</if>
			<if test="dataStatus != null and dataStatus != ''">and DATA_STATUS = #{dataStatus}</if>
            <if test="imagetextType != null and imagetextType != '' and imagetextType=='other'">
               and IMAGE_TEXT_TYPE not in("S","O","P") OR  IMAGE_TEXT_TYPE IS null
            </if>
            and TASK_ID = #{taskId}
        </where>
    </select>
    
    <select id="selectTTaskLogById" parameterType="String" resultMap="TTaskLogResult">
        <include refid="selectTTaskLogVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTTaskLog" parameterType="TTaskLog">
        insert into t_task_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="taskId != null and taskId != ''">TASK_ID,</if>
            <if test="fileName != null">FILE_NAME,</if>
            <if test="parseStartTime != null">PARSE_START_TIME,</if>
            <if test="parseEndTime != null">PARSE_END_TIME,</if>
            <if test="dataStatus != null">DATA_STATUS,</if>
            <if test="errorMsg != null">ERROR_MSG,</if>
            <if test="retryTimes != null">RETRY_TIMES,</if>
            <if test="dataDesc != null">DATA_DESC,</if>
            <if test="imagetextType != null">IMAGE_TEXT_TYPE,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="parseStartTime != null">#{parseStartTime},</if>
            <if test="parseEndTime != null">#{parseEndTime},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="retryTimes != null">#{retryTimes},</if>
            <if test="dataDesc != null">#{dataDesc},</if>
            <if test="imagetextType != null">#{imagetextType},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateTTaskLog" parameterType="TTaskLog">
        update t_task_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">TASK_ID = #{taskId},</if>
            <if test="fileName != null">FILE_NAME = #{fileName},</if>
            <if test="parseStartTime != null">PARSE_START_TIME = #{parseStartTime},</if>
            <if test="parseEndTime != null">PARSE_END_TIME = #{parseEndTime},</if>
            <if test="dataStatus != null">DATA_STATUS = #{dataStatus},</if>
            <if test="errorMsg != null">ERROR_MSG = #{errorMsg},</if>
            <if test="retryTimes != null">RETRY_TIMES = #{retryTimes},</if>
            <if test="dataDesc != null">DATA_DESC = #{dataDesc},</if>
            <if test="imagetextType != null">IMAGE_TEXT_TYPE = #{imagetextType},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTTaskLogById" parameterType="String">
        delete from t_task_log where ID = #{id}
    </delete>

    <delete id="deleteTTaskLogByIds" parameterType="String">
        delete from t_task_log where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>