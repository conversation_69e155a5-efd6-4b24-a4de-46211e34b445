<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>huanghe</artifactId>
        <groupId>cn.guliandigital</groupId>
        <version>3.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>huanghe-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>cn.guliandigital</groupId>
            <artifactId>huanghe-common</artifactId>
        </dependency>
        <!-- 阿里云短信服务sdk-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.3</version>
        </dependency>
        <!-- 邮件服务-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
         <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.5</version>
 		</dependency>
         <dependency>
		    <groupId>com.github.houbb</groupId>
		    <artifactId>opencc4j</artifactId>
		    <version>1.0.2</version>
		</dependency>
		
		<dependency>
            <groupId>com.google.typography</groupId>
            <artifactId>sfnttool</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>
        
    </dependencies>

</project>