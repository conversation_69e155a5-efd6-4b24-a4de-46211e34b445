<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.guliandigital</groupId>
	<artifactId>huanghe</artifactId>
	<version>3.0.0</version>

	<name>huanghe</name>
	<description>黄河大典平台</description>

	<properties>
		<huanghe.version>3.0.0</huanghe.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<mybatis.boot.version>1.3.2</mybatis.boot.version>
		<druid.version>1.1.14</druid.version>
		<bitwalker.version>1.21</bitwalker.version>
		<swagger.version>2.9.2</swagger.version>
		<kaptcha.version>2.3.2</kaptcha.version>
		<pagehelper.boot.version>1.2.5</pagehelper.boot.version>
		<fastjson.version>1.2.70</fastjson.version>
		<oshi.version>3.9.1</oshi.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.fileupload.version>1.3.3</commons.fileupload.version>
		<poi.version>3.17</poi.version>
		<velocity.version>1.7</velocity.version>
		<jwt.version>0.9.0</jwt.version>
		<elasticsearch.version>6.8.23</elasticsearch.version>
	</properties>

	<!-- 依赖声明 -->
	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>1.12.1</version>
			</dependency>

			<!-- SpringBoot的依赖配置 -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>2.2.13.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!--阿里数据库连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>

			<!-- 解析客户端操作系统、浏览器等 -->
			<dependency>
				<groupId>eu.bitwalker</groupId>
				<artifactId>UserAgentUtils</artifactId>
				<version>${bitwalker.version}</version>
			</dependency>

			<!-- pagehelper 分页插件 -->
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.boot.version}</version>
			</dependency>

			<!-- 获取系统信息 -->
			<dependency>
				<groupId>com.github.oshi</groupId>
				<artifactId>oshi-core</artifactId>
				<version>${oshi.version}</version>
			</dependency>

			<!-- swagger2 -->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger.version}</version>
				<exclusions>
					<exclusion>
						<groupId>io.swagger</groupId>
						<artifactId>swagger-annotations</artifactId>
					</exclusion>
					<exclusion>
						<groupId>io.swagger</groupId>
						<artifactId>swagger-models</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- swagger2-UI -->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${swagger.version}</version>
			</dependency>

			<!--io常用工具类 -->
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>

			<!--文件上传工具类 -->
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons.fileupload.version}</version>
			</dependency>

			<!-- excel工具 -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<!--velocity代码生成使用模板 -->
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity</artifactId>
				<version>${velocity.version}</version>
			</dependency>

			<!-- 阿里JSON解析器 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<!--Token生成与解析 -->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>${jwt.version}</version>
			</dependency>

			<!--验证码 -->
			<dependency>
				<groupId>com.github.penggle</groupId>
				<artifactId>kaptcha</artifactId>
				<version>${kaptcha.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>1.12.1</version>
			</dependency>
			
			<dependency>
			  <groupId>com.github.wechatpay-apiv3</groupId>
			  <artifactId>wechatpay-java</artifactId>
			  <version>0.2.12</version>
			</dependency>

			<!-- 代码生成 -->
			 <dependency>
				<groupId>cn.guliandigital</groupId>
				<artifactId>huanghe-generator</artifactId>
				<version>${huanghe.version}</version>
			</dependency>

			<!-- 核心模块 -->
			<dependency>
				<groupId>cn.guliandigital</groupId>
				<artifactId>huanghe-framework</artifactId>
				<version>${huanghe.version}</version>
			</dependency>

			<!-- 系统模块 -->
			<dependency>
				<groupId>cn.guliandigital</groupId>
				<artifactId>huanghe-system</artifactId>
				<version>${huanghe.version}</version>
			</dependency>

			<!-- 通用工具 -->
			<dependency>
				<groupId>cn.guliandigital</groupId>
				<artifactId>huanghe-common</artifactId>
				<version>${huanghe.version}</version>
			</dependency>

			<!--把es版本管理起来，因为springboot2.2.13中使用的es是6.8.13 -->
			<dependency>
				<groupId>org.elasticsearch</groupId>
				<artifactId>elasticsearch</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.plugin</groupId>
				<artifactId>transport-netty4-client</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>transport</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>x-pack-transport</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>elasticsearch-rest-client</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>elasticsearch-rest-high-level-client</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- es相关 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-elasticsearch</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.elasticsearch.client</groupId>
					<artifactId>transport</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.plugin</groupId>
			<artifactId>transport-netty4-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>transport</artifactId>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>x-pack-transport</artifactId>
		</dependency>
	</dependencies>

	<modules>
		<module>huanghe-admin</module>
		<module>huanghe-api</module>
		<module>huanghe-framework</module>
		<module>huanghe-system</module>
		<module>huanghe-common</module>
		<module>huanghe-generator</module>
	</modules>
	<packaging>pom</packaging>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>