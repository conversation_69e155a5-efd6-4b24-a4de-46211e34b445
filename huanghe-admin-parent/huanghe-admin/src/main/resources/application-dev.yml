# 项目相关配置
huanghe:
  # 名称
  name: huanghe
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/huanghe/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: C://Users//zhangyaxi//1024//tmp//huanghe//uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  #是否是https访问
  sslEnabled: true
  # 下载接口地址
  downloadPath: /common/download/resource?name=
  # 图片加密下载
  picEncPath: /wapi/picenc/resource?name=
  #系统访问url 解析xml数据时使用
  platHttpUrl: /wapi/common/download?filename=
  #校验端口
  healthPort: 20201
  tif2jpghttp: http://**************:18607/tif2jpg/v2
  tifcompresshttp: http://**************:18608/tifcompress/v2
  domainUrl: https://huanghe.ancientbooks.cn

  #word转pdf接口
  pdfWordUrl: http://**************:6080/api/v1/convert/file/pdf
  #获取pdf目录接口
  pdfMenuUrl: http://**************:18610/apipdf/getBookmarks
  #获取pdf每页内容接口
  pdfContentUrl: http://**************:18610/apipdf/getPdfPageContent
  #pdf接口key
  pdfApiKey: 1b2c7c50-034c-49fb-9372-36270b0ea5ce
  
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 10077
  servlet:
    # 应用的访问路径
    context-path: /huanghe
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30
    basedir: /home/<USER>/tmp/huanghe-admin
# 日志配置
logging:
  level:
    cn.guliandigital: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *****************************************************************************************************************************************************
        username: root
        password: root
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
            
  
   
  data:
    mongodb:
      uri: mongodb://*************:27017/huanghe_test
      
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  300MB
      # 设置总上传的文件大小
      max-request-size:  600MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 密码
    #password: chensr2019
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  enabled: true
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 180


elasticsearch:
  cluster-nodes: **************:9300
  cluster-name: bigdata_center
  cluster-password: elastic:zcm2023
  ssl-enabled: false
  cert-path: D:\siming-admin-parent\elastic-certificates.p12


# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: cn.guliandigital.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: false
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


aliyun:
  sendsms:
    regionId: cn-hangzhou
    accessKeyId:
    secret:
    sysDomain: dysmsapi.aliyuncs.com
    action: SendSms
    signName:
    templateCode:
    

submail:
  projectId: d4XEk2
  appkey: d89a018c439d81c5168d77dc75e07f8a
  appid: 98317
  
      