package cn.guliandigital.web.controller.product.classic;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;

/**
 * 分类树Controller
 *
 * <AUTHOR>
 * @date 2020-09-07
 */
@RestController
@RequestMapping("/product/tree")
public class TConfigClassicTreeController extends BaseController {
    @Autowired
    private ITConfigClassicTreeService tConfigClassicTreeService;

    /**
     * 查询分类树列表
     */
    @PreAuthorize("@ss.hasPermi('product:tree:list')")
    @GetMapping("/list")
    public TableDataInfo list(TConfigClassicTree tConfigClassicTree) {
        startPage();
        List<TConfigClassicTree> list = tConfigClassicTreeService.selectTConfigClassicTreeList(tConfigClassicTree);
        return getDataTable(list);
    }

    /**
     * 导出分类树列表
     */


    /**
     * 获取分类树详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(tConfigClassicTreeService.selectTConfigClassicTreeById(id));
    }



    @GetMapping(value = "/q/{treePid}")
    public AjaxResult getPCode(@PathVariable("treePid") String treePid) {
        return AjaxResult.success(tConfigClassicTreeService.selectP(treePid));
    }
/**
 * <AUTHOR>
 * @Description
 * @Date 2020/9/9 11:17
 * @param null:
 **/
    /**
     * 新增分类树
     */

    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:tree:add')")
    @Log(title = "分类树", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TConfigClassicTree tConfigClassicTree) {
        return toAjax(tConfigClassicTreeService.insertTConfigClassicTree(tConfigClassicTree));
    }

    /**
     * 修改分类树
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:tree:edit')")
    @Log(title = "分类树", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TConfigClassicTree tConfigClassicTree) {

        return toAjax(tConfigClassicTreeService.updateTConfigClassicTree(tConfigClassicTree));
    }

    /**
     * 删除分类树
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:tree:remove')")
    @Log(title = "分类树", businessType = BusinessType.DELETE)
    @DeleteMapping("/{treePid}/{id}")
    public void remove(@PathVariable("treePid") String treePid, @PathVariable("id") String  id) {
        tConfigClassicTreeService.deleteTConfigClassicTreeByIds(id);
    }

    @GetMapping("/treeselect")
    public AjaxResult treeselect(String treeId) {
        List<TConfigClassicTree> trees = tConfigClassicTreeService.selectClassicTreeById(treeId);
        //System.out.println(trees);
        return AjaxResult.success(trees);
    }





}
