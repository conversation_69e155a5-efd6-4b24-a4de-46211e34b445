package cn.guliandigital.web.controller.tsystem;

import cn.guliandigital.common.enums.HandleStatus;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.tsystem.domain.TSysFeedback;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.tsystem.service.ITSysFeedbackService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 意见反馈Controller
 * 
 * <AUTHOR>
 * @date 2020-09-06
 */
@RestController
@RequestMapping("/tsystem/feedback")
public class TSysFeedbackController extends BaseController
{
    @Autowired
    private ITSysFeedbackService tSysFeedbackService;

    /**
     * 查询意见反馈列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysFeedback tSysFeedback)
    {
        startPage();
        List<TSysFeedback> list = tSysFeedbackService.selectTSysFeedbackList(tSysFeedback);
        return getDataTable(list);
    }

    /**
     * 导出意见反馈列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:export')")
    @Log(title = "意见反馈", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @RepeatSubmit
    public AjaxResult export(TSysFeedback tSysFeedback)
    {
        List<TSysFeedback> list = tSysFeedbackService.selectTSysFeedbackList(tSysFeedback);
        ExcelUtil<TSysFeedback> util = new ExcelUtil<TSysFeedback>(TSysFeedback.class);
        return util.exportExcel(list, "feedback");
    }

    /**
     * 获取意见反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysFeedbackService.selectTSysFeedbackById(id));
    }

    /**
     * 新增意见反馈
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:add')")
    @Log(title = "意见反馈", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TSysFeedback tSysFeedback)
    {
        return toAjax(tSysFeedbackService.insertTSysFeedback(tSysFeedback));
    }

    /**
     * 回复意见反馈
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:reply')")
    @Log(title = "意见反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody TSysFeedback tSysFeedback)
    {
        tSysFeedback.setHandlebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysFeedback.setHandlebyName(SecurityUtils.getUsername());
        tSysFeedback.setHandleTime(DateUtil.getCuurentDate());
        tSysFeedback.setHandleStatus(HandleStatus.DONE.getCode());
        tSysFeedback.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysFeedback.setUpdatebyName(SecurityUtils.getLoginUser().getUsername());
        return toAjax(tSysFeedbackService.updateTSysFeedback(tSysFeedback));
    }

    /**
     * 删除意见反馈
     */
    @PreAuthorize("@ss.hasPermi('tsystem:feedback:remove')")
    @Log(title = "意见反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tSysFeedbackService.deleteTSysFeedbackByIds(ids));
    }
}
