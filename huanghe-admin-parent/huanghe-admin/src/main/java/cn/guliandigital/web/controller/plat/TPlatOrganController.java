package cn.guliandigital.web.controller.plat;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.model.AddOrganData;
import cn.guliandigital.common.core.domain.model.EditOrganData;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.enums.OrganStatus;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.order.domain.TProOrder;
import cn.guliandigital.order.service.ITProOrderService;
import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.plat.service.ITPlatOrganService;
import cn.guliandigital.plat.service.ITPlatUserService;
import lombok.extern.slf4j.Slf4j;

/**
 * 平台机构Controller
 *
 * <AUTHOR>
 * @date 2020-09-14
 */

@Slf4j
@RestController
@RequestMapping("/plat/organ")
public class TPlatOrganController extends BaseController {
    @Autowired
    private ITPlatOrganService tPlatOrganService;

    @Autowired
    private ITPlatUserService tPlatUserService;

    @Autowired
    private ITProOrderService tProOrderService;


    /**
     * 查询平台机构列表
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:list')")
    @GetMapping("/list")
    public TableDataInfo list(TPlatOrgan tPlatOrgan) {
        startPage();
        List<TPlatOrgan> list = tPlatOrganService.selectTPlatOrganList(tPlatOrgan);
        return getDataTable(list);
    }

    /**
     * 导出平台机构列表
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:export')")
    @Log(title = "平台机构", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @RepeatSubmit
    public AjaxResult export(TPlatOrgan tPlatOrgan) {
        List<TPlatOrgan> list = new ArrayList<>();
        if(StringUtil.isEmpty(tPlatOrgan.getIds())){
           list = tPlatOrganService.selectTPlatOrganList(tPlatOrgan);
        }else {
            for (String id : tPlatOrgan.getIds()) {
                TPlatOrgan tPlatOrgan1 = tPlatOrganService.selectOrganById(id);
                list.add(tPlatOrgan1);
            }

        }

        ExcelUtil<TPlatOrgan> util = new ExcelUtil<TPlatOrgan>(TPlatOrgan.class);
        return util.exportExcel(list, "机构");
    }

    /**
     * 获取平台机构详细信息
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {

        try {
            return AjaxResult.success(tPlatOrganService.selectTPlatOrganById(id));
        } catch (Exception e) {
            log.error("error", e);
            return AjaxResult.error("获取数据失败");
        }
    }

    /**
     * 新增平台机构
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:add')")
    @Log(title = "平台机构", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody AddOrganData addOrganData) {
//        //校验管理员用户名是否存在，如果存在禁止新增
//        String loginName = addOrganData.getLoginName();
//        TPlatUser tPlatUser = new TPlatUser();
//        tPlatUser.setLoginName(loginName);
//        List<TPlatUser> list = tPlatUserService.selectTPlatUserList(tPlatUser);
//        if(list != null && list.size() > 0){
//            return AjaxResult.error("机构管理员登录名已存在");
//        }
//        //校验手机号
//        tPlatUser = new TPlatUser();
//        tPlatUser.setUserTel(addOrganData.getUserTel());
//        list = tPlatUserService.selectTPlatUserList(tPlatUser);
//        if(list != null && list.size() > 0){
//	        return AjaxResult.error("联系人电话已经被注册！");
//        }
//        //校验邮箱
//        tPlatUser = new TPlatUser();
//        tPlatUser.setUserEmail(addOrganData.getUserEmail());
//        list = tPlatUserService.selectTPlatUserList(tPlatUser);
//        if(list != null && list.size() > 0){
//	       	return AjaxResult.error("联系人邮箱已经被注册！");
//        }

            return toAjax(tPlatOrganService.insertTPlatOrgan(addOrganData));

    }

    /**
     * 修改平台机构
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:edit')")
    @Log(title = "平台机构", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody EditOrganData editOrganData) throws Exception {
//        String loginName = editOrganData.getLoginName();
//        TPlatUser tPlatUser = new TPlatUser();
//        tPlatUser.setLoginName(loginName);
//        List<TPlatUser> list = tPlatUserService.selectTPlatUserList(tPlatUser);
//        if (list != null && list.size() > 0) {
//            if (!editOrganData.getUserId().equals(list.get(0).getId())) {
//                return AjaxResult.error("机构管理员登录名已存在");
//            }
//        }
        return toAjax(tPlatOrganService.updateTPlatOrgan(editOrganData));

//        try {
//            return toAjax(tPlatOrganService.updateTPlatOrgan(editOrganData));
//        } catch (Exception e) {
//            log.error("error", e);
//            return AjaxResult.error("更新数据失败");
//        }
    }

    /**
     * 修改平台机构状态
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:edit')")
    @Log(title = "平台机构", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    @RepeatSubmit
    public AjaxResult updateStatus(@RequestBody TPlatOrgan tPlatOrgan) {
        return toAjax(tPlatOrganService.updateTPlatOrganStatus(tPlatOrgan));
    }

    /**
     * 删除平台机构
     */
    @PreAuthorize("@ss.hasPermi('plat:organ:remove')")
    @Log(title = "平台机构", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String[] ids) {
        try {
            for (String id : ids) {
                TProOrder tProOrder = new TProOrder();
                tProOrder.setOrgId(id);
                EditOrganData editOrganData1 = tPlatOrganService.selectTPlatOrganById(id);
                if(StringUtil.equals(editOrganData1.getOrgStatus(), OrganStatus.OK.getCode())){
                    return AjaxResult.error("该机构启用状态，不能删除！");
                }
                List<TProOrder> list = tProOrderService.selectTProOrderList(tProOrder);
                if (list != null && !list.isEmpty()) {
                    EditOrganData editOrganData = tPlatOrganService.selectTPlatOrganById(id);
                    return AjaxResult.error("机构" + editOrganData.getOrgName() + "存在订单，禁止删除");
                }
            }
            return toAjax(tPlatOrganService.deleteTPlatOrganByIds(ids));
        } catch (Exception e) {
            log.error("error", e);
            return AjaxResult.error("删除数据失败");
        }
    }

    /**
     * 状态修改
     */
    @Log(title = "机构管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TPlatOrgan tPlatOrgan) throws Exception {
        return toAjax(tPlatOrganService.updateTPlatOrganByBr(tPlatOrgan));
    }

}
