package cn.guliandigital.web.controller.tsystem;

import java.util.List;

import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.tsystem.domain.TSysProject;
import cn.guliandigital.tsystem.service.ITSysProjectService;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 工程项目简介Controller
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@RestController
@RequestMapping("/system/project")
public class TSysProjectController extends BaseController
{
    @Autowired
    private ITSysProjectService tSysProjectService;

    /**
     * 查询工程项目简介列表
     */
    @PreAuthorize("@ss.hasPermi('system:project:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysProject tSysProject)
    {
        startPage();
        List<TSysProject> list = tSysProjectService.selectTSysProjectList(tSysProject);
        return getDataTable(list);
    }

    /**
     * 导出工程项目简介列表
     */
    @PreAuthorize("@ss.hasPermi('system:project:export')")
    @Log(title = "工程项目简介", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TSysProject tSysProject)
    {
        List<TSysProject> list = tSysProjectService.selectTSysProjectList(tSysProject);
        ExcelUtil<TSysProject> util = new ExcelUtil<TSysProject>(TSysProject.class);
        return util.exportExcel(list, "project");
    }

    /**
     * 获取工程项目简介详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:project:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysProjectService.selectTSysProjectById(id));
    }

    /**
     * 新增工程项目简介
     */
    @PreAuthorize("@ss.hasPermi('system:project:add')")
    @Log(title = "工程项目简介", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TSysProject tSysProject)
    {
        return toAjax(tSysProjectService.insertTSysProject(tSysProject));
    }

    /**
     * 图片上传
     */
    @Log(title = "资讯海报上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    @RepeatSubmit
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception{
        if (!file.isEmpty())
        {
            String avatar = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("imgUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     * 修改工程项目简介
     */
    @PreAuthorize("@ss.hasPermi('system:project:edit')")
    @Log(title = "工程项目简介", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TSysProject tSysProject)
    {
        return toAjax(tSysProjectService.updateTSysProject(tSysProject));
    }

    /**
     * 删除工程项目简介
     */
    @PreAuthorize("@ss.hasPermi('system:project:remove')")
    @Log(title = "工程项目简介", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tSysProjectService.deleteTSysProjectByIds(ids));
    }


    /**
     * 上架项目工程
     */
    @PreAuthorize("@ss.hasPermi('system:news:up')")
    @Log(title = "资讯发布", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/up/{id}")
    @RepeatSubmit
    public AjaxResult upProject(@PathVariable("id") String id)
    {
        return toAjax(tSysProjectService.upPublishStatus(id));
    }

    /**
     * 上下项目工程
     */
    @PreAuthorize("@ss.hasPermi('system:news:down')")
    @Log(title = "资讯发布", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/down/{id}")
    @RepeatSubmit
    public AjaxResult downProject(@PathVariable("id") String id)
    {
        return toAjax(tSysProjectService.downPublishStatus(id));
    }


    /**
     * 修改项目工程
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:publish')")
    @Log(title = "资讯发布", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/publish/{id}")
    @RepeatSubmit
    public AjaxResult publish(@PathVariable("id") String id)
    {
        return toAjax(tSysProjectService.updatePublishStatus(id));
    }


}
