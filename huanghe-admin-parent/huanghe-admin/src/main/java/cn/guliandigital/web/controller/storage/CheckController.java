package cn.guliandigital.web.controller.storage;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.TxtUtil;
import cn.guliandigital.product.book.domain.TBookMenuContentMongo;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.service.TBookMenuContentMongoService;
import cn.guliandigital.product.menu.domain.TProBookMenu;
import cn.guliandigital.product.menu.service.ITProBookMenuService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName CheckController
 * @Description TODO 资源列表
 * <AUTHOR>
 * @Date 2021/3/25 16:10
 */
@Slf4j
@RestController
@RequestMapping("/data/checktag")
public class CheckController extends BaseController {

    
	@Autowired
    private TBookMenuContentMongoService tbookMenuContentMongoService;
    
	@Autowired
    private ITProBooksService tProBooksService;
	
	@Autowired
    private ITProBookMenuService tProBookMenuService;
	

    @RequestMapping(value = "/check", method = RequestMethod.GET)
    @ApiOperation(value = "校验")
    @ResponseBody
    public Object check() {
        try {
        	File ff1 =  new File("D:\\四明文库-check");
        	if(!ff1.exists()) {
        		boolean ismk = ff1.mkdirs();
        		if(!ismk) {
                	log.info("文件夹{}创建失败",ff1.getAbsolutePath());
                }
        	}
        	File ff =  new File("D:\\四明文库-check\\checktag.txt");
        	if(ff.exists()) {
        		boolean isdelete = ff.delete();
        		if(!isdelete) {
                	log.info("文件{}删除失败",ff.getAbsolutePath());
                }
        	}
        	
        	
        	Map<String, Object> condition = new HashMap<String, Object>();
            List<TBookMenuContentMongo> list = tbookMenuContentMongoService.findListByCondition(condition);
            int size = list.size();
            int idx = 0;
            int index = 0;
            for (TBookMenuContentMongo bean : list) {
            	idx++;
            	log.info("==>开始解析第{}个，共{}个",idx,size);
                String text = bean.getBookContent();
                
                String result = haveXmlTag(text);
                if(!Strings.isNullOrEmpty(result)) {
                	String bookId = bean.getBookId();
                	String charpterId = bean.getMenuId();
                	index++;
                	TProBooks book = tProBooksService.selectTProBooksById(bookId);
                	
                	TProBookMenu menu = tProBookMenuService.selectTProBookMenuById(charpterId);
                	String zlh = book.getUniqueId();
                	String content = charpterId+"#"+zlh+"#"+bookId;
                	
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", "================>"+index+"<================");
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", "http://jinling.ancientbooks.cn/#/myBook/resourceReading/"+bookId+"/"+charpterId);
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", book.getBookName()+"#"+menu.getMenuName());
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", content);
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", result);
                	TxtUtil.contentToTxt("D:\\四明文库-check\\checktag.txt", "================>"+index+"<================");
                }
                
            }
            log.info("===============> 全部检查结束 <====================");
            log.info("===============> D:\\四明文库-check====================");
            return AjaxResult.success("校验成功");
        } catch (Exception e) {
            log.error("查询列表出现异常，", e);
            return AjaxResult.error("校验失败");
        }

    }

   


	public String haveXmlTag(String str) {
		if(Strings.isNullOrEmpty(str)) {
			return str;
		}
    	StringBuffer buff = new StringBuffer();
    	String patstr = "(<[\u4e00-\u9fa5]{1,2}) ";
    	Pattern p = Pattern.compile( patstr );
	    Matcher m = p.matcher(str);		
	    int idx = 0;
	    while(m.find()){	
	    	idx ++;
	       String pagestr = m.group();       
	       log.info(pagestr);
	       int start = m.start();
	       int _s = start;
	       if(start  > 100) {
	    	   _s = start -100;
	       }
	       String sss = StringUtil.substring(str, _s, start+100);
	       buff.append(sss);
	       if(idx > 20) {
	    	   break;
	       }
	    }	
    	
	    return buff.toString();
    }

    public static void main(String[] args) throws Exception {
    	String str = "<正文 type='2'></正文><span type='test'></span>";
    	CheckController cc = new CheckController();
    	
    	log.info("==>"+cc.haveXmlTag(str)) ;
    }

}
