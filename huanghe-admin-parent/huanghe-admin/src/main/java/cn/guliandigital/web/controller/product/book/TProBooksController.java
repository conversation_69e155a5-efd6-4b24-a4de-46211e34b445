package cn.guliandigital.web.controller.product.book;/*
package cn.guliandigital.web.controller.book;


/**
 * 产品资源管理Controller
 *
 * <AUTHOR>
 * @date 2020-09-09
 */

import java.awt.image.BufferedImage;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.enums.ProStatusEnum;
import cn.guliandigital.common.enums.PushStatusEnum;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import org.apache.commons.lang3.StringUtils;
import com.google.common.base.Strings;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.jms.core.JmsMessagingTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.houbb.opencc4j.util.ZhConverterUtil;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.model.LoginUser;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.AuthorUtil;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.framework.web.service.TokenService;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.vo.ClassTreeType;
import cn.guliandigital.product.database.domain.TProDatabase;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/product/books")
public class TProBooksController extends BaseController {
	
    @Autowired
    private ITProBooksService tProBooksService;
    
    @Autowired
    private TokenService tokenService;

    @Autowired
    private ITConfigClassicTreeService tConfigClassicTreeService;
    
    @javax.annotation.Resource
    private ResourceLoader resourceLoader;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;

    @Autowired
    private JmsMessagingTemplate jmsTemplate;

    /**
     * 查询产品资源管理列表
     */

    @PreAuthorize("@ss.hasPermi('product:books:list')")
    @GetMapping("/list")
    public TableDataInfo list(HttpServletRequest request,TProBooks tProBooks) {
        String resourceClassesId = tProBooks.getResourceClassesId();
        if(!StringUtil.isEmpty(resourceClassesId)){
            String[] split = resourceClassesId.split("/");
            resourceClassesId=split[split.length-1];
            // 判断
            TConfigClassicTree selectConfigClassicTree = new TConfigClassicTree();
            selectConfigClassicTree.setTreePid(resourceClassesId);

            List<TConfigClassicTree> selectTConfigClassicTreeList = tConfigClassicTreeService.selectTConfigClassicTreeList(selectConfigClassicTree);
            if(StringUtil.isNotEmpty(selectTConfigClassicTreeList)) {
                //一级id
                String finalResourceClassesId = resourceClassesId;
                long count = selectTConfigClassicTreeList.stream().filter(f -> finalResourceClassesId.equals(f.getId())).count();
                if (count==0){
                    selectTConfigClassicTreeList.add(new TConfigClassicTree(){{setId(finalResourceClassesId);}});
                }
                String[] resourceClassesIds = selectTConfigClassicTreeList.stream().map(TConfigClassicTree::getId)
                        .filter(StringUtil::isNotEmpty).toArray(String[]::new);
                tProBooks.setResourceClassesIds(resourceClassesIds);
                tProBooks.setResourceClassesId(null);
            }else {
                tProBooks.setResourceClassesId(resourceClassesId);
            }
        }
        startPage();
        List<TProBooks> list = tProBooksService.selectTProBooksList(tProBooks);

        String url = serverConfig.getUrl();
        for (TProBooks book : list) {
            if(!Strings.isNullOrEmpty(book.getCoverUrl())) {
                if(!book.getCoverUrl().startsWith("http")){
                    book.setCoverUrl(url + downloadPath + book.getCoverUrl());
                }
            }
            if(!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
                if(!book.getThumbCoverUrl().startsWith("http")){
                    book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
                }
            }
            String author = book.getMainResponsibility();
            book.setMainResponsibility(AuthorUtil.getFormalAuthor(author));
        }
        return getDataTable(list);
    }

    /**
     * @param
     * <AUTHOR>
     * @Description
     * @Date 2020/9/14 查出资源类型
     **/
    @GetMapping("/type")
    public TableDataInfo type(ClassTreeType type) {
        List<ClassTreeType> orgList = tProBooksService.selectType(type);
        List<ClassTreeType> rellist =  orgList.stream()
                .filter(t -> StringUtil.isEmpty(t.getTreePid()))
                .collect(Collectors.toList());
        for (ClassTreeType item : rellist) {
            tProBooksService.getList(item, orgList, item.getTreePid());
        }
        return getDataTable(rellist);
    }


    @GetMapping("/dbName")
    public List<TProDatabase> dbName(TProDatabase tProDatabase) {
        return tProBooksService.selectdbName(tProDatabase);
    }
    /**
     * 获取产品资源管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:books:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        String url = serverConfig.getUrl();
        TProBooks dto=new TProBooks();
        dto.setId(id);
        TProBooks book = tProBooksService.selectTProBooksEntity(dto);
        if(!Strings.isNullOrEmpty(book.getCoverUrl())) {
            if(!book.getCoverUrl().startsWith("http")){
                book.setCoverUrl(url + downloadPath + book.getCoverUrl());
            }
        }
        if(!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
            if(!book.getThumbCoverUrl().startsWith("http")){
                book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
            }
        }
        ZhConverterUtil.convertToTraditional(book.getResourceClasses());
        return AjaxResult.success(book);
    }

	 /**
	 * <AUTHOR>
	 * @Description
	 * @Date 2020/9/16 17:16
	 * @param null:
	 **/
    /**
     * 新增产品资源管理
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:add')")
    @Log(title = "产品资源管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TProBooks tProBooks) {
        return toAjax(tProBooksService.insertTProBooks(tProBooks));
    }
/**
 * <AUTHOR>
 * @Description 四部分类法
 * @Date 2020/10/16 13:37
 * @param type:
 **/

    @GetMapping("/typeSi")
    public TableDataInfo fourPartType(ClassTreeType type) {
        List<ClassTreeType> orgList = tProBooksService.selectFourPart(type);
        List<ClassTreeType> rellist = orgList.stream()
                .filter(t -> StringUtil.isEmpty(t.getTreePid()))
                .collect(Collectors.toList());
        for (ClassTreeType item : rellist) {
            tProBooksService.getList(item, orgList, item.getTreePid()); }

        ClassTreeType classTreeType = rellist.get(0);
        List<ClassTreeType> classTreeTypes = new ArrayList<>();
        for (ClassTreeType child : classTreeType.getChildren()) {
            classTreeTypes.add(child);
        }

        return getDataTable(classTreeTypes);
    }
    /**
     * 修改
     */

    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:edit')")
    @Log(title = "产品资源管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TProBooks tProBooks) {
        //判断修改时图片是否修改，如果没有修改，需要把之前图片的前缀截取掉
        String url = serverConfig.getUrl();
		if (!Strings.isNullOrEmpty(tProBooks.getCoverUrl())) {
			if (tProBooks.getCoverUrl().startsWith("http")) {
                tProBooks.setCoverUrl(StringUtils.replace(tProBooks.getCoverUrl(), url+downloadPath, ""));
			}

		}
        if (!Strings.isNullOrEmpty(tProBooks.getThumbCoverUrl())){
            if (tProBooks.getThumbCoverUrl().startsWith("http")){
                tProBooks.setThumbCoverUrl(StringUtils.replace(tProBooks.getCoverUrl(), url+downloadPath, ""));
            }
        }
        return toAjax(tProBooksService.updateTProBooks(tProBooks));
    }

    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:edit')")
    @Log(title = "产品资源管理", businessType = BusinessType.UPDATE)
    @PutMapping("/delFlag")
    public AjaxResult del(@RequestBody List<TProBooks> tProBooks) {
    	for(TProBooks books : tProBooks) {
    		tProBooksService.updateTProBook4Delete(books);
    	}
        return AjaxResult.success();

    }

    @RepeatSubmit
    @GetMapping("/importTemplate")
    public void downloadBlacklistTemplate(HttpServletRequest request, HttpServletResponse response){
        try (OutputStream out = response.getOutputStream()){
            String fileName = "import.xlsx";
            // 在resouce下新建个template文件夹，然后把我们的模板文件testExcel.xlsx拖进去
            // 图片里的位置
            String path = "templates/import.xlsx";
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:"+path);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "fileName");

            XSSFWorkbook wb = new XSSFWorkbook(resource.getInputStream());
            wb.write(out);
        } catch (Exception e) {
            log.error("loadQuestionTemplate======", e);
        }
    }

    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:remove')")
    @Log(title = "产品资源管理", businessType = BusinessType.UPDATE)
    @PutMapping("/pro/{id}")
    public AjaxResult delBooks(@RequestBody TProBooks tProBooks) {

        return toAjax(tProBooksService.updateTProBook4Delete(tProBooks));

    }

    /**
     * @param
     * <AUTHOR>
     * @Description 下架（修改状态）
     * @Date 2020/9/10 13:46
     **/
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:status')")
    @Log(title = "产品资源管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}")
    public AjaxResult putAway(@PathVariable("id") String[] ids) {
        //根据id查询资源
        List<TProBooks> tProBookList = tProBooksService.selectBook(ids);
        List<TProBooks> collect = tProBookList.stream().filter(item -> item.getProStatus().equals(ProStatusEnum.OFFLINE.getCode())).collect(Collectors.toList());
        if(StringUtil.isNotEmpty(collect)){
            return AjaxResult.error("您选中的资源中存在已下架的图书，请重新选择");
        }
        int offLine = tProBooksService.updateStatuOffLine(ids);
        return toAjax(offLine);
    }

    /**
     * @param ids:
     * <AUTHOR>
     * @Description 上架
     * @Date 2020/9/23 10:13
     **/
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:books:status')")
    @Log(title = "产品资源管理", businessType = BusinessType.UPDATE)
    @PutMapping("/statusUp/{id}")
    public AjaxResult up(@PathVariable("id") String[] ids) {
        //根据id查询资源
        List<TProBooks> tProBookList = tProBooksService.selectBook(ids);
        List<TProBooks> collect_ = tProBookList.stream().filter(item -> item.getProStatus().equals(ProStatusEnum.ONLINE.getCode())).collect(Collectors.toList());
        if(StringUtil.isNotEmpty(collect_)){
            return  AjaxResult.error("您选中的资源中存在已上架的图书，请重新选择");
        }
        Map<String, List<TProBooks>> collect = tProBookList.stream()
                .filter(item -> item.getParseStatus() != null)
                .collect(Collectors.groupingBy(TProBooks::getParseStatus));
        List<TProBooks> books_1 = collect.get(PushStatusEnum.PUSH_STATUS_1.getCode());
        List<TProBooks> books_2 = collect.get(PushStatusEnum.PUSH_STATUS_2.getCode());
        List<TProBooks> books_3 = collect.get(PushStatusEnum.PUSH_STATUS_3.getCode());
        int num=0;
        if ((StringUtil.isNotEmpty(books_1)||StringUtil.isNotEmpty(books_3))&&StringUtil.isNotEmpty(books_2)){
            ids = books_2.stream().map(TProBooks::getId).toArray(String[]::new);
            if (StringUtil.isNotEmpty(books_1)){
                num+=books_1.size();
            }
            if (StringUtil.isNotEmpty(books_2)){
                num+=books_2.size();
            }
        }else if ((StringUtil.isNotEmpty(books_1)||StringUtil.isNotEmpty(books_3))&&StringUtil.isEmpty(books_2)){
            return AjaxResult.error("资源不符合上架条件");
        }
        int online = tProBooksService.updateStatuOnline(ids);
        if (num>0){
            return AjaxResult.success("有"+num+"条资源不符合上架条件，其他资源上架成功");
        }
        return toAjax(online);
    }

    /**
     * @param
     * <AUTHOR>
     * @Description 资源导入
     * @Date 2020/9/10 16:59
     **/
    @RepeatSubmit
    @Log(title = "资源管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('product:books:import')")
    @PostMapping("/importData")
    public AjaxResult  importData(MultipartFile file, Boolean updateSupport) throws Exception {
        ExcelUtil<TProBooks> util = new ExcelUtil<TProBooks>(TProBooks.class);
        List<TProBooks> booksList = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message =tProBooksService.importBooks(booksList, updateSupport, operName);
        return   AjaxResult.success(message);

    }
    @Log(title = "海报上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
        	try {
        		
        		// 文件大小；其中file.length()获取的是字节，除以1024可以得到以kb为单位的文件大小       
        		long size = file.getSize() / 1024;        
        		// 图片对象        
        		BufferedImage bufferedImage = ImageIO.read(file.getInputStream());        
        		// 宽度        
        		int width = bufferedImage.getWidth();        
        		// 高度        
        		int height = bufferedImage.getHeight();    
        		
        		// 打印信息
        		log.info("图片大小：	{}kb；图片宽度：{}像素；图片高度：{}像素", size, width, height); 
        	/*	if(height > Constants.PIC_MAX_HEIGHT || width > Constants.PIC_MAX_WIDTH) {
                    return  new AjaxResult(501, "图片尺寸不能超过524*400像素!");
//                    return AjaxResult.error("图片尺寸不能超过524*400像素!");
        		}*/
        		if(size/1024 > 2) {
        			return AjaxResult.error("图片大小不能超过2MB!");
        		}
	            String avatar = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
	            AjaxResult ajax = AjaxResult.success();
	            ajax.put("imgUrl", avatar);
	            return ajax;
        	}catch(Exception e) {
        		return AjaxResult.error(e.getMessage());
        	}
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     * @param
     * <AUTHOR>
     * @Description
     * @Date 2020/9/25 12:04
     **/


    @PostMapping("/check")
    public List<TProBooks> check() {
        return tProBooksService.check();
    }

    /**
     * 把书从数据库剔除
     */
    @RepeatSubmit
    @PutMapping("/deleteFromDb/{id}")
    public void deleteFromDb(@PathVariable("id") String id) {
//         tProBooksService.deleteFromDb(id);
    }
}


