package cn.guliandigital.web.controller.tsystem;

import java.util.List;

import cn.guliandigital.common.utils.Base64Utils;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.tsystem.domain.TWxampNotice;
import cn.guliandigital.tsystem.service.ITWxampNoticeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.core.page.TableDataInfo;

/**
 * 关于我们、使用帮助Controller
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
@RestController
@RequestMapping("/tsystem/notice")
public class TWxampNoticeController extends BaseController
{
    @Autowired
    private ITWxampNoticeService tWxampNoticeService;
    @Autowired
    private ServerConfig serverConfig;
    @Value("${huanghe.downloadPath}")
    private  String downloadPath;

    /**
     * 查询关于我们、使用帮助列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(TWxampNotice tWxampNotice)
    {
        startPage();
        List<TWxampNotice> list = tWxampNoticeService.selectTWxampNoticeList(tWxampNotice);
        return getDataTable(list);
    }

    /**
     * 导出关于我们、使用帮助列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:export')")
    @Log(title = "关于我们、使用帮助", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TWxampNotice tWxampNotice)
    {
        List<TWxampNotice> list = tWxampNoticeService.selectTWxampNoticeList(tWxampNotice);
        ExcelUtil<TWxampNotice> util = new ExcelUtil<TWxampNotice>(TWxampNotice.class);
        return util.exportExcel(list, "notice");
    }

    /**
     * 获取关于我们、使用帮助详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return AjaxResult.success(tWxampNoticeService.selectTWxampNoticeById(id));
    }

    /**
     * 新增关于我们、使用帮助
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:add')")
    @Log(title = "关于我们、使用帮助", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TWxampNotice tWxampNotice)
    {
        String noticeContent = tWxampNotice.getContent();
        if (StringUtils.isNotEmpty(noticeContent)){
            String url = serverConfig.getUrl();
            String content = Base64Utils.base64ToPng(noticeContent, url,downloadPath);
            tWxampNotice.setContent(content);
        }
        return toAjax(tWxampNoticeService.insertTWxampNotice(tWxampNotice));
    }

    /**
     * 修改关于我们、使用帮助
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:edit')")
    @Log(title = "关于我们、使用帮助", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TWxampNotice tWxampNotice)
    {
        String noticeContent = tWxampNotice.getContent();
        if (StringUtils.isNotEmpty(noticeContent)){
            String url = serverConfig.getUrl();
            String content = Base64Utils.base64ToPng(noticeContent, url,downloadPath);
            tWxampNotice.setContent(content);
        }
        return toAjax(tWxampNoticeService.updateTWxampNotice(tWxampNotice));
    }

    /**
     * 删除关于我们、使用帮助
     */
    @PreAuthorize("@ss.hasPermi('tsystem:notice:remove')")
    @Log(title = "关于我们、使用帮助", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(tWxampNoticeService.deleteTWxampNoticeByIds(ids));
    }
}
