package cn.guliandigital.web.controller.tsystem;

import java.util.List;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import cn.guliandigital.tsystem.domain.TSysLink;
import cn.guliandigital.common.core.domain.model.DisplayParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.tsystem.service.ITSysLinkService;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 友情链接Controller
 * 
 * <AUTHOR>
 * @date 2020-09-11
 */
@RestController
@RequestMapping("/tsystem/link")
public class TSysLinkController extends BaseController
{
    @Autowired
    private ITSysLinkService tSysLinkService;

    /**
     * 查询友情链接列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysLink tSysLink)
    {
        startPage();
        List<TSysLink> list = tSysLinkService.selectTSysLinkList(tSysLink);
        return getDataTable(list);
    }

    /**
     * 导出友情链接列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:export')")
    @Log(title = "友情链接", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @RepeatSubmit
    public AjaxResult export(TSysLink tSysLink)
    {
        List<TSysLink> list = tSysLinkService.selectTSysLinkList(tSysLink);
        ExcelUtil<TSysLink> util = new ExcelUtil<TSysLink>(TSysLink.class);
        return util.exportExcel(list, "link");
    }

    /**
     * 获取友情链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysLinkService.selectTSysLinkById(id));
    }

    /**
     * 新增友情链接
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:add')")
    @Log(title = "友情链接", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TSysLink tSysLink)
    {
        return toAjax(tSysLinkService.insertTSysLink(tSysLink));
    }

    /**
     * 修改友情链接
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:edit')")
    @Log(title = "友情链接", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TSysLink tSysLink)
    {
        return toAjax(tSysLinkService.updateTSysLink(tSysLink));
    }

    /**
     * 删除友情链接
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:remove')")
    @Log(title = "友情链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tSysLinkService.deleteTSysLinkByIds(ids));
    }

    /**
     * 修改友情链接排序
     */
    @PreAuthorize("@ss.hasPermi('tsystem:link:edit')")
    @Log(title = "友情链接排序", businessType = BusinessType.UPDATE)
    @PutMapping("/updateWebDisplay")
    @RepeatSubmit
    public AjaxResult updateWebDisplay(@RequestBody DisplayParam displayParam)
    {
        return toAjax(tSysLinkService.updateWebDisplay(displayParam));
    }


    /**
     * 图片上传
     */
    @Log(title = "图片上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    @RepeatSubmit
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception{
        if (!file.isEmpty())
        {
            String avatar = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("imgUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }
}
