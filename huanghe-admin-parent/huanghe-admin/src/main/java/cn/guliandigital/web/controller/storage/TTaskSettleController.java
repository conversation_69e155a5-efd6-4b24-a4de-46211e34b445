package cn.guliandigital.web.controller.storage;

import java.io.File;
import java.util.Date;
import java.util.List;

import cn.guliandigital.analysis.asyn.SynBooksConsumer;
import cn.guliandigital.common.core.domain.entity.SysUser;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.enums.DataStatusEnum;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.product.book.service.IApiService;
import cn.guliandigital.storage.storage.domain.TTaskSettle;
import cn.guliandigital.storage.storage.service.ITTaskSettleService;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据入库配置Controller
 *
 * <AUTHOR>
 * @date 2020-10-15
 */
@Slf4j
@RestController
@RequestMapping("/data/settle")
public class TTaskSettleController extends BaseController {
    @Autowired
    private ITTaskSettleService tTaskSettleService;


    @Autowired
    private SynBooksConsumer synbooksConsumer;


    @Autowired
    private IApiService apiService;
    
    /**
     * 查询数据入库配置列表
     */

    @GetMapping("/list")
    public TableDataInfo list(TTaskSettle tTaskSettle) {
        startPage();
        List<TTaskSettle> list = tTaskSettleService.selectTTaskSettleList(tTaskSettle);
        return getDataTable(list);
    }


    /**
     * 获取数据入库配置详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(tTaskSettleService.selectTTaskSettleById(id));
    }

    /**
     * 新增数据入库配置
     */
    @PreAuthorize("@ss.hasPermi('data:settle:add')")
    @Log(title = "数据入库配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTaskSettle tTaskSettle) {
        File file = new File(tTaskSettle.getDataPath());
        if (file.exists()){
            if (file.isDirectory()){
                tTaskSettle.setBookName(file.getName());
            }else {
                return AjaxResult.error("解析文件格式不正确");
            }
        }else {
            return AjaxResult.error("解析文件不存在");
        }
        return toAjax(tTaskSettleService.insertTTaskSettle(tTaskSettle));
    }

    /**
     * 修改数据入库配置
     */
    @PreAuthorize("@ss.hasPermi('data:settle:edit')")
    @Log(title = "数据入库配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTaskSettle tTaskSettle) {
        return toAjax(tTaskSettleService.updateTTaskSettle(tTaskSettle));
    }

    /**
     * @param
     * <AUTHOR>
     * @Description 修改状态
     * @Date 15:53
     **/
    //@PreAuthorize("@ss.hasPermi('data:settle:run')")
    @Log(title = "数据入库配置", businessType = BusinessType.UPDATE)
    @PutMapping("/upStatus/{id}")
    public AjaxResult editStatus(@PathVariable("id") String id) {
        //启动线程
        TTaskSettle task = tTaskSettleService.selectTTaskSettleById(id);
        SysUser user = SecurityUtils.getLoginUser().getUser();
        task.setUpdatebyId(user.getUserId().toString());
        task.setUpdatebyName(user.getNickName());
        task.setParseStartTime(new Date());
        task.setDataStatus(DataStatusEnum.ING.getCode());
        tTaskSettleService.updateTTaskSettle(task);
        //推送数据
        String json = JSONObject.toJSONString(task);
        try {
            synbooksConsumer.listenQueue(json);
        } catch (Exception e) {
            log.error("sendData,",e);
        }
        return AjaxResult.success("文件正在解析中请稍后查看");
    }


    /**
     * 删除数据入库配置
     */
    @PreAuthorize("@ss.hasPermi('data:settle:remove')")
    @Log(title = "数据入库配置", businessType = BusinessType.DELETE)
    @PutMapping("/updateSettle/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(tTaskSettleService.updateSettle(ids));
    }


    
    public TTaskSettle findTaskByReadmode(List<TTaskSettle> list , String readMod) {
    	
    	TTaskSettle bean = null;
    	for(TTaskSettle task : list) {
    		if (StringUtil.equals(task.getReadMode(), readMod)) {
    			bean =  task;
    			break;
            } 
    	}
    	
    	return bean;    	
    }

    @PostMapping("/getPun")
    public AjaxResult getPun(String inputPath,String outPath) {
        try{
            apiService.punRecognition(inputPath,outPath);
        }catch (Exception ex){

        }
        return toAjax(1);
    }

    @PostMapping("/getT2s")
    public AjaxResult getT2s(String inputPath,String outPath,Integer transMode) {
        try{
            apiService.getT2s(inputPath,outPath,transMode);
        }catch (Exception ex){

        }
        return toAjax(1);
    }

    @PostMapping("/auto")
    public AjaxResult auto(@RequestBody String data) {
        try{

        }catch (Exception ex){

        }
        return toAjax(1);
    }

}
