//package cn.guliandigital.web.controller.storage;
//
//import java.awt.image.BufferedImage;
//import java.io.File;
//import java.io.FileInputStream;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.util.List;
//import java.util.Map;
//
//import javax.imageio.ImageIO;
//
//import org.apache.commons.io.FilenameUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.google.common.collect.Lists;
//
//import cn.guliandigital.common.config.HuangHeConfig;
//import cn.guliandigital.common.core.controller.BaseController;
//import cn.guliandigital.common.core.domain.AjaxResult;
//import cn.guliandigital.common.utils.ImageUtils;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @ClassName CheckController
// * @Description TODO 资源列表
// * <AUTHOR>
// * @Date 2021/3/25 16:10
// */
//@Slf4j
//@RestController
//@RequestMapping("/data/format")
//public class FormatPicController extends BaseController {
//
//	@Autowired
//	private HuangHeConfig qiluwenkuConfig;
//
//	@Value("${huanghe.profile}")
//	private String profile;
//	
//
//	@RequestMapping(value = "/pic", method = RequestMethod.GET)
//	@ApiOperation(value = "校验")
//	@ResponseBody
//	public Object pic() {
//		try {
//			//D:/huanghe/uploadPath
//			
//			List<String> excludes = Lists.newArrayList();
//			excludes.add("fonts");
//			excludes.add("tif2jpg");
//			excludes.add("2021");
//			excludes.add("2022");
//			excludes.add("copyright");
//						
//			String rootPath = HuangHeConfig.getUploadPath();
//			
//			File rp = new File(rootPath);
//			List<File> bookList = Lists.newArrayList();
//			for(File ff : rp.listFiles()) {
//				String fname = ff.getName();
//				if(excludes.contains(fname)) {
//					continue;
//				}
//				bookList.add(ff);				
//			}
//			
//			int size = bookList.size();
//			int idx = 0;
//			int platHeight = 0;
//			for(File book : bookList) {
//				idx ++;				
//				Path picPath = Paths.get(book.getAbsolutePath(), "ystx");
//				File picFile =  picPath.toFile();
//				log.info("==>解析路径：{}",picFile.getAbsolutePath());
//				int fidx = 0;
//				File[] pics = picFile.listFiles();
//				for(File pic : pics) {
//					String fullpath = pic.getAbsolutePath();
//					fidx ++;
//					String filename = pic.getName();
//					if(StringUtil.contains(filename, "format")) {
//						continue;
//					}
//					log.info("==>开始解析[{}:{}]->[{}:{}],filename={}", idx, size, fidx, pics.length, pic.getAbsolutePath());
//					
//					FileInputStream fis = new FileInputStream(pic);
//					BufferedImage image = ImageIO.read(fis);
//					int picHeight = image.getHeight();
//					int picWidth = image.getWidth();
//					
//					fis.close();
//					log.info("Image Height : {}", picHeight);
//					log.info("Image Width : {}", picWidth);
//					//压缩
//			        String _picPath = pic.getParent() + File.separator + FilenameUtils.getBaseName(pic.getAbsolutePath()) +"_format." +FilenameUtils.getExtension(pic.getAbsolutePath());
//
//			       	int h = 4300;
//			       	int w = 3100;
//			       	if(picHeight >= 4300 && picWidth >= 3100) {
//			       		h = 4300;		       	
//			    		w = 3100;
//			    	}else {
//				        Map<String,Integer> deMap = ImageUtils.deduceHeight(platHeight, picHeight, picWidth);
//				        log.info("deMap:{}",deMap);
//				        w = deMap.get("width");
//				        h = deMap.get("height");
//			    	}
//			       	log.info("==>result:H:{}", h);
//			       	log.info("==>result:W:{}", w);
//			        ImageUtils.compressPicSize(fullpath, _picPath, h, w);
//			        log.info("==>压缩图片：{}",_picPath);		       
//				}				
//			}
//
//			
//			log.info("===============> 全部转换结束 <====================");
//			return AjaxResult.success("转换成功");
//		} catch (Exception e) {
//			log.error("查询列表出现异常，", e);
//			return AjaxResult.error("转换失败");
//		} finally {
//			
//		}
//
//	}
//
//	public static void main(String[] args) throws Exception {
//		
//	}
//
//}
