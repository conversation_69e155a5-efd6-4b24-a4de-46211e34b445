package cn.guliandigital.web.controller.storage;

import java.util.List;

import cn.guliandigital.storage.log.domain.TTaskLog;
import cn.guliandigital.storage.log.service.ITTaskLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;

import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.core.page.TableDataInfo;

/**
 * 数据解析日志Controller
 * 
 * <AUTHOR>
 * @date 2020-10-15
 */
@RestController
@RequestMapping("/data/log")
public class TTaskLogController extends BaseController
{
    @Autowired
    private ITTaskLogService tTaskLogService;

    /**
     * 查询数据解析日志列表
     */

    @PostMapping("/list")
    public TableDataInfo list(@RequestBody TTaskLog tTaskLog)
    {
        startPage();
        List<TTaskLog> list = tTaskLogService.selectTTaskLogList(tTaskLog);
        return getDataTable(list);
    }


    /**
     * 获取数据解析日志详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)

    {
        return AjaxResult.success(tTaskLogService.selectTTaskLogById(id));
    }

    /**
     * 新增数据解析日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "数据解析日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTaskLog tTaskLog)
    {
        return toAjax(tTaskLogService.insertTTaskLog(tTaskLog));
    }

    /**
     * 修改数据解析日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:edit')")
    @Log(title = "数据解析日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTaskLog tTaskLog)
    {
        return toAjax(tTaskLogService.updateTTaskLog(tTaskLog));
    }

    /**
     * 删除数据解析日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "数据解析日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tTaskLogService.deleteTTaskLogByIds(ids));
    }
}
