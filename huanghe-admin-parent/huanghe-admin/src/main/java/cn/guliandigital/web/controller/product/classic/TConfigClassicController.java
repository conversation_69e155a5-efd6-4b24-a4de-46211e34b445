package cn.guliandigital.web.controller.product.classic;

import java.util.List;

import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.product.database.domain.TProDatabase;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.product.clasic.domain.TConfigClassic;
import cn.guliandigital.product.clasic.service.ITConfigClassicService;
import cn.guliandigital.common.core.page.TableDataInfo;

/**
 * 分类定义Controller
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
@RestController
@RequestMapping("/product/classic")
public class TConfigClassicController extends BaseController
{
    @Autowired
    private ITConfigClassicService tConfigClassicService;

    /**
     * 查询分类定义列表
     */

    @GetMapping("/list")
    public TableDataInfo list(TConfigClassic tConfigClassic)
    {
        startPage();
        List<TConfigClassic> list = tConfigClassicService.selectTConfigClassicList(tConfigClassic);
        return getDataTable(list);
    }

/**
 * <AUTHOR>
 * @Description 
 * @Date 2020/9/9 11:17
 * @param null:
 **/
    /**
     * 获取分类定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:classic:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tConfigClassicService.selectTConfigClassicById(id));
    }

    /**
     * 新增分类定义
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:classic:add')")
    @Log(title = "分类新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TConfigClassic tConfigClassic)
    {
        return toAjax(tConfigClassicService.insertTConfigClassic(tConfigClassic));
    }

    /**
     * 修改分类定义
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('product:classic:edit')")
    @Log(title = "分类修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TConfigClassic tConfigClassic)
    {
        return toAjax(tConfigClassicService.updateTConfigClassic(tConfigClassic));
    }

    /**
     * 删除分类定义
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('system:classic:remove')")
    @Log(title = "分类删除", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tConfigClassicService.deleteTConfigClassicByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:classic:remove')")
    @Log(title = "分类批量删除", businessType = BusinessType.DELETE)
    @PutMapping("/batch")
    public AjaxResult removeBatch(@RequestBody List<TConfigClassic> tConfigClassics)
    {
        return toAjax(tConfigClassicService.batchDel(tConfigClassics));
    }


    /**
     * 状态修改
     */
    @Log(title = "分类管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TConfigClassic tConfigClassic)
    {
        return toAjax(tConfigClassicService.changeStatus(tConfigClassic));
    }

}
