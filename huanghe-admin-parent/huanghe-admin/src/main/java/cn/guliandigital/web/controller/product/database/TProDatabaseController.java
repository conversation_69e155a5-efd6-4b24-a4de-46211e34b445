package cn.guliandigital.web.controller.product.database;


import java.util.List;

import cn.guliandigital.common.core.domain.entity.SysUser;
import cn.guliandigital.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;

import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.database.domain.TProDatabase;
import cn.guliandigital.product.database.service.ITProDatabaseService;

/**
 * 数据库管理Controller
 *
 * <AUTHOR>
 * @date 2020-09-14
 */
@RestController
@RequestMapping("/product/database")
public class TProDatabaseController extends BaseController {
    @Autowired
    private ITProDatabaseService tProDatabaseService;
    @Autowired
    private ITProBooksService itProBooksService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;
    /**
     * 查询数据库管理列表
     */
    @PreAuthorize("@ss.hasPermi('database:database:list')")
    @GetMapping("/list")
    public TableDataInfo list(TProDatabase tProDatabase) {
        startPage();
        List<TProDatabase> list = tProDatabaseService.selectTProDatabaseList(tProDatabase);
        String url = serverConfig.getUrl();
        for (TProDatabase object : list) {
            if(!Strings.isNullOrEmpty(object.getCoverUrl())) {
                if(!object.getCoverUrl().startsWith("http")){
                    object.setCoverUrl(url + downloadPath + object.getCoverUrl());
                }
            }
        }
        return getDataTable(list);
    }


    /**
     * 获取数据库管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('database:database:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        TProDatabase tProDatabase = tProDatabaseService.selectTProDatabaseById(id);
        String url = serverConfig.getUrl();
            if(!Strings.isNullOrEmpty(tProDatabase.getCoverUrl())) {
                if(!tProDatabase.getCoverUrl().startsWith("http")){
                    tProDatabase.setCoverUrl(url + downloadPath + tProDatabase.getCoverUrl());
                }
            }
        return AjaxResult.success(tProDatabase);
    }

    /**
     * 新增数据库管理
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('database:database:add')")
    @Log(title = "数据库管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TProDatabase tProDatabase) {
        return toAjax(tProDatabaseService.insertTProDatabase(tProDatabase));
    }

    /**
     * 修改数据库管理
     */
    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('database:database:edit')")
    @Log(title = "数据库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TProDatabase tProDatabase) {
        //判断修改时图片是否修改，如果没有修改，需要把之前图片的前缀截取掉
        String url = serverConfig.getUrl();
        if (!Strings.isNullOrEmpty(tProDatabase.getCoverUrl())) {
            if (tProDatabase.getCoverUrl().startsWith("http")) {
                tProDatabase.setCoverUrl(StringUtils.replace(tProDatabase.getCoverUrl(), url + downloadPath, ""));
            }
        }
        if(!Strings.isNullOrEmpty(tProDatabase.getAppletPic())){
            if (tProDatabase.getAppletPic().startsWith("http")){
                tProDatabase.setAppletPic(StringUtils.replace(tProDatabase.getAppletPic(), url+downloadPath, ""));
            }
        }
        return toAjax(tProDatabaseService.updateTProDatabase(tProDatabase));
    }

    /**
     * 删除数据库管理
     */

    @RepeatSubmit
    @PreAuthorize("@ss.hasPermi('database:database:remove')")
    @Log(title = "数据库管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(tProDatabaseService.deleteTProDatabaseByIds(ids));
    }

    /**
     * @param
     * <AUTHOR>
     * @Description 通过数据库id 查询资源详情
     * @Date 2020/9/15 14:42
     **/

    @GetMapping("/recourse/{id}")
    public TableDataInfo getRecourse(TProBooks tProBooks) {
        startPage();
        String resourceClasses = tProBooks.getResourceClasses();
        if (StringUtils.isNotEmpty(resourceClasses)){
        	if(StringUtils.contains(resourceClasses, "/")) {
	            String classId = resourceClasses.split("/")[1];
	            tProBooks.setResourceClasses(classId);
	        }else {
	        	//tProBooks.setResourceClasses(resourceClasses);
	        }
        }
        List<TProBooks> list=itProBooksService.selectByDbId(tProBooks);
        return getDataTable(list);
    }


    /**
     * <AUTHOR> @Description
     * @Date  16:50
     * @param null:查重
     **/
   @PostMapping("/selectRe")
    public List<TProDatabase> selectRe(){
        return tProDatabaseService.selectRe();
    }


    /**
     * 状态修改
     */
    @Log(title = "数据库管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TProDatabase tProDatabase)
    {
        return toAjax(tProDatabaseService.updateTProDatabase(tProDatabase));
    }


}

