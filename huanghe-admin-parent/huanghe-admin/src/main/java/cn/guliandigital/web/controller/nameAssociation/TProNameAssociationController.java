package cn.guliandigital.web.controller.nameAssociation;

import com.google.common.collect.Lists;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.nameAssociation.domain.TProNameAssociation;
import cn.guliandigital.nameAssociation.service.ITProNameAssociationService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务关联管理Controller
 *
 * <AUTHOR>
 * @date 2020-12-06
 */
@RestController
@RequestMapping("/wapi/association")
public class TProNameAssociationController extends BaseController {
    @Autowired
    private ITProNameAssociationService tProNameAssociationService;

    /**
     * 查询任务关联管理列表
     */
    //@PreAuthorize("@ss.hasPermi('nameAssociation:association:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody TProNameAssociation tProNameAssociation) {
    	if(Strings.isNullOrEmpty(tProNameAssociation.getPname())) {
    		return AjaxResult.error("缺少必要参数");
    	}
        List<TProNameAssociation> grouplist = tProNameAssociationService.selectGroupList(tProNameAssociation);
        List<Integer> groupList = Lists.newArrayList();
        for (TProNameAssociation bean : grouplist) {
            groupList.add(bean.getGroupId());
        }
        List<TProNameAssociation> result = null;
        if (groupList.size() > 0) {
            List<TProNameAssociation> list = tProNameAssociationService.selectNamesList(groupList);
            result = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TProNameAssociation::getPname))), ArrayList::new));
            result.removeIf(p -> p.getPname().equals(tProNameAssociation.getPname()));
        } else {
            result = Lists.newArrayList();
        }
        return AjaxResult.success(result);
    }

    /**
     * 导出任务关联管理列表
     */
    @PreAuthorize("@ss.hasPermi('nameAssociation:association:export')")
    @Log(title = "任务关联管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TProNameAssociation tProNameAssociation) {
        List<TProNameAssociation> list = tProNameAssociationService.selectTProNameAssociationList(tProNameAssociation);
        ExcelUtil<TProNameAssociation> util = new ExcelUtil<TProNameAssociation>(TProNameAssociation.class);
        return util.exportExcel(list, "association");
    }

    /**
     * 获取任务关联管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('nameAssociation:association:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(tProNameAssociationService.selectTProNameAssociationById(id));
    }

    /**
     * 新增任务关联管理
     */
    @PreAuthorize("@ss.hasPermi('nameAssociation:association:add')")
    @Log(title = "任务关联管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TProNameAssociation tProNameAssociation) {
        return toAjax(tProNameAssociationService.insertTProNameAssociation(tProNameAssociation));
    }

    /**
     * 修改任务关联管理
     */
    @PreAuthorize("@ss.hasPermi('nameAssociation:association:edit')")
    @Log(title = "任务关联管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TProNameAssociation tProNameAssociation) {
        return toAjax(tProNameAssociationService.updateTProNameAssociation(tProNameAssociation));
    }

    /**
     * 删除任务关联管理
     */
    @PreAuthorize("@ss.hasPermi('nameAssociation:association:remove')")
    @Log(title = "任务关联管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(tProNameAssociationService.deleteTProNameAssociationByIds(ids));
    }

    @PostMapping("/proName")
    public AjaxResult getExcel(MultipartFile file) throws IOException {
        Workbook workbook = ExcelUtil.getWorkBook(file);
        //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
        Set<TProNameAssociation> objectList = new HashSet<>();
        List<String[]> list = new ArrayList<>();
        List<Integer> errorList = new ArrayList<>();
        int firstCellNum = 0;
        int lastCellNum = 0;
        if (workbook != null) {
            //getNumberOfSheets() 获取工作薄中的sheet个数
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                //获得当前sheet工作表
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    continue;
                }
                //获得当前sheet的开始行
                int firstRowNum = sheet.getFirstRowNum();
                //获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum();
                //循环除了所有行,如果要循环除第一行以外的就firstRowNum+1
                for (int rowNum = firstRowNum + 1; rowNum <= lastRowNum; rowNum++) {
                    //获得当前行
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    //获得当前行的开始列
                    firstCellNum = row.getFirstCellNum();
                    //获得当前行的列数
                    lastCellNum = row.getLastCellNum();
                    //创建列数大小的数组，存放每一行的每列的值
                    String[] cells = new String[row.getLastCellNum()];
                    //循环当前行
                    for (int cellNum = 1; cellNum < lastCellNum; cellNum++) {
                        Cell cell = row.getCell(cellNum);
                        String cellValue = ExcelUtil.getCellValues(cell);
                        if (cellNum == 0 && "".equals(cellValue)) {
                            errorList.add(rowNum);
                            break;
                        }
                        cells[cellNum] = cellValue;
                        if (cellValue == null || cellValue.equals("")) {
                            break;
                        }
                        TProNameAssociation tProNameAssociation = new TProNameAssociation();
                        tProNameAssociation.setId(IdUtils.simpleUUID());
                        tProNameAssociation.setGroupId(rowNum+1);
                        tProNameAssociation.setPname(cellValue);
                        list.add(cells);
                        objectList.add(tProNameAssociation);
                        tProNameAssociationService.insertTProNameAssociation(tProNameAssociation);

                    }

                }
                return AjaxResult.success(objectList);
            }
        }
        if (!errorList.isEmpty()) {
            return AjaxResult.error("导入缺失,请填写完成再次导入,行号： " + errorList);
        }
        return AjaxResult.error();
    }
}
