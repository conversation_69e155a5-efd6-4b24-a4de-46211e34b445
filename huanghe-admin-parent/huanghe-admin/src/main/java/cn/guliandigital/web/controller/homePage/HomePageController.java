package cn.guliandigital.web.controller.homePage;

import java.util.List;
import java.util.Map;

import cn.guliandigital.common.core.page.PageDomain;
import cn.guliandigital.common.core.page.TableSupport;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.sql.SqlUtil;
import cn.guliandigital.homePage.domain.HomeCommonVo;
import cn.guliandigital.homePage.domain.OrganUserType;
import cn.guliandigital.homePage.domain.ResourceStatistic;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.houbb.paradise.common.util.CollectionUtil;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.entity.SysUser;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.homePage.domain.TUserVisitSum;
import cn.guliandigital.homePage.service.HomePageService;
import cn.guliandigital.homePage.service.TUserVisitSumService;
import cn.guliandigital.plat.domain.TPlatOrgan;
import cn.guliandigital.product.readhistory.domain.TUserReadHistorySum;
import cn.guliandigital.product.readhistory.service.ITUserReadHistorySumService;
import cn.guliandigital.product.search.domain.TUserSearchHistory;
import cn.guliandigital.product.search.service.ITUserSearchHistoryService;
import cn.guliandigital.pvlog.domain.TUserVisitLog;
import cn.guliandigital.pvlog.service.ITUserVisitLogService;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName HomePageController
 * @Description 首页
 * <AUTHOR>
 * @Date 2020/11/4 10:53
 */

@Slf4j
@RestController
@RequestMapping("/homePage")
public class HomePageController extends BaseController {
    @Autowired
    private HomePageService homePageService;

    @Autowired
    private ITProBooksService itProBooksService;
    @Autowired
    private ITUserSearchHistoryService tUserSearchHistoryService;
    
    @Autowired
    private TUserVisitSumService tUserVisitSumService;
    @Autowired
    private ITUserReadHistorySumService tUserReadHistorySumService;
    @Autowired
    private ITUserVisitLogService itUserVisitLogService;


    /**
     * <AUTHOR>
     * @Description 新增用户量
     * @Date 2020/11/4 11:08
     **/
    @GetMapping("/newUser")
    public AjaxResult newUser() {
        SysUser sysUser = homePageService.newUser();
        return AjaxResult.success(sysUser);
    }

    /**
     * @param :
     * @Description 新增机构
     * @Date 2020/11/5 18:17
     **/
    @GetMapping("/newPlat")
    public AjaxResult newPlat() {
        TPlatOrgan plat = homePageService.newPlat();
        return AjaxResult.success(plat);
    }

    /**
     * @param :
     * @Description 搜索历史搜索词排行
     * @Date 2020/11/6 11:08
     **/
    @GetMapping("/searchTop")
    public TableDataInfo searchTop(TUserSearchHistory tUserSearchHistory) {
        startPage();
        tUserSearchHistory.setSearchType("F");
        List<TUserSearchHistory> list = homePageService.searchTop(tUserSearchHistory);
        if(CollectionUtil.isNotEmpty(list)) {
        	
        }
        return getDataTable(list);
    }

    /**
     * @param tUserSearchHistories:
     * @Description 批量删除
     * @Date 2020/11/6 17:30
     **/
    @PutMapping("/delSearchContent")
    @PreAuthorize("@ss.hasPermi('search:search:remove')")
    public AjaxResult delSearchContent(@RequestBody List<TUserSearchHistory> tUserSearchHistories) {

        return toAjax(tUserSearchHistoryService.updateStaus(tUserSearchHistories));
    }

    /**
     * @Description 删除
     * @Date 2020/11/6 17:30
     **/
    @PutMapping("/delSearchContent/{id}")
    public AjaxResult delSearch(@PathVariable("id") String[] ids) {

        return toAjax(tUserSearchHistoryService.delBydelFlag(ids));
    }

    /**
     * @param :
     * @Description 最近七天访问量
     * @Date 2020/11/9 11:10
     **/
    @GetMapping("/visits")
    public AjaxResult visits() {
        TUserVisitSum tUserVisitSum = tUserVisitSumService.selectVisits();
        return AjaxResult.success(tUserVisitSum);
    }

    //资源占比
    @GetMapping("/recoursePercent")
    public AjaxResult recoursePercent() {
        List<TProBooks> tProBooks = itProBooksService.recoursePercent();
        return AjaxResult.success(tProBooks);
    }
    //授权机构统计
    @GetMapping("/organCount/{day}")
    public TableDataInfo organCount(TUserVisitSum tUserVisitSum) {
        startPage();
        List<TUserVisitSum> tUserVisitSum1 = tUserVisitSumService.organCount(tUserVisitSum);
        return getDataTable(tUserVisitSum1);
    }
    //资源统计
    @GetMapping("/recourseCount")
    public TableDataInfo recourseCount(TUserReadHistorySum tUserReadHistorySum) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtil.isNotNull(pageNum) && StringUtil.isNotNull(pageSize))
        {
            String orderByColumn = ServletUtils.getParameter(TableSupport.ORDER_BY_COLUMN);
            String isAsc = ServletUtils.getParameter(TableSupport.IS_ASC);
            String orderBy="";
            if (StringUtil.isNotEmpty(orderByColumn)&&StringUtil.isNotEmpty(isAsc)){
                 orderBy ="sum("+StringUtil.toUnderScoreCase(orderByColumn) + ") " + isAsc;
            }
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
        List<TUserReadHistorySum> tUserReadHistorySums = tUserReadHistorySumService.organCount(tUserReadHistorySum);
        return getDataTable(tUserReadHistorySums);
    }
    //独立访客
    @GetMapping("/aloneVisits/{day}")
   public AjaxResult aloneVisits(TUserVisitLog tUserVisitLog) {
     //  独立访客
       List<Map<String, Object>> list=itUserVisitLogService.selectVisits(tUserVisitLog);

       //itUserVisitLogService.selectVisits(tUserVisitLog);

       return AjaxResult.success(list);
   }
/**
 * <AUTHOR>
 * @Description 最近30 天和7天
 * @Date  16:12
 * @param tUserVisitLog:
 **/
    @GetMapping("/aloneVisits1/{day}")
    public AjaxResult aloneVisits1(TUserVisitLog tUserVisitLog) {
        //独立访客
        List<Map<String, Object>> list=itUserVisitLogService.selectVisits1(tUserVisitLog);

        log.info("aaaa"+list.size());
        //itUserVisitLogService.selectVisits(tUserVisitLog);

        return AjaxResult.success(list);
    }
    @GetMapping("/aloneVisitsCounts/{day}")
    public AjaxResult aloneVisitsCounts(TUserVisitLog tUserVisitLog) {
        // 访客次数
        List<Map<String, Object>> list= itUserVisitLogService.aloneVisitsCounts(tUserVisitLog);

        return AjaxResult.success(list);
    }
    /**
     * <AUTHOR>
     * @Description 最近30 天和7天
     * @Date  16:12
     * @param tUserVisitLog:
     **/
    @GetMapping("/aloneVisitsCounts1/{day}")
    public AjaxResult aloneVisitsCounts1(TUserVisitLog tUserVisitLog) {
        // 访客次数
         List<Map<String, Object>> list= itUserVisitLogService.aloneVisitsCounts1(tUserVisitLog);
       log.info(list+"11111111111");
        return AjaxResult.success(list);
    }


    /**
     * 机构用户类型
     */
    @GetMapping("/organUser")
    public AjaxResult getOrganUserType(){
       List<OrganUserType> list = homePageService.getOrganUserType();
        return AjaxResult.success(list);
    }


    /**
     * 用户登录量查询
     */
    @GetMapping("/userLoginSum/{type}")
    public AjaxResult getUserLoginSum(@PathVariable String type){
        Map userLoginSum = homePageService.getUserLoginSum(type);
        return AjaxResult.success(userLoginSum);
    }

    /**
     * 各编资源占比
     * @param id
     * @return
     */
    @GetMapping("/ResourceProportion/{id}")
    public AjaxResult getResourceProportionDeatil(@PathVariable String id){
       Map<String,Object>  map=  homePageService.getResourceProportionDeatil(id);
        return AjaxResult.success(map);
    }


    /**
     * 数据量统计
     * @param id
     * @return
     */
    @GetMapping("/dataStatistics/{id}")
    public AjaxResult getdataStatisticsDeatil(@PathVariable String id){
        HomeCommonVo homeCommonVo = homePageService.getdataStatisticsDeatil(id);
        return AjaxResult.success(homeCommonVo);
    }


    /**
     * 各编资源统计
     * @param resourceStatistic
     * @return
     */

    @GetMapping("/ResourceStatistic")
    public TableDataInfo getResourceStatistic(ResourceStatistic resourceStatistic) {
        //各编资源统计
        List<ResourceStatistic> list= homePageService.getResourceStatistic(resourceStatistic);
        return getDataTable(list);
    }




}


