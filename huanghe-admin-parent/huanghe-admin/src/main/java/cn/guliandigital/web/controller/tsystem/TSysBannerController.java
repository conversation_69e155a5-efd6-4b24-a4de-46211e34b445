package cn.guliandigital.web.controller.tsystem;


import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.tsystem.domain.TSysBanner;
import cn.guliandigital.tsystem.service.ITSysBannerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Slf4j
@RequestMapping("/tsystem/banner")
public class TSysBannerController extends BaseController
{
    @Autowired
    private ITSysBannerService tSysBannerService;
    
    /**
     * 查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysBanner tSysBanner)
    {
        startPage();
        tSysBanner.setDisplay("1");
        tSysBanner.setDelFlag(0);
        return getDataTable(tSysBannerService.selectTSysBannerList(tSysBanner));
    }

    /**
     * 根据分组查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:list')")
    @GetMapping("/listByGroup/{groupName}")
    public TableDataInfo listByGroup(@PathVariable String groupName)
    {
        startPage();
        List<TSysBanner> list = tSysBannerService.selectTSysBannerByGroupName(groupName);
        return getDataTable(list);
    }

    /**
     * 根据分组和地市查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:list')")
    @GetMapping("/listByGroupAndArea/{groupName}/{area}")
    public TableDataInfo listByGroupAndArea(@PathVariable String groupName, @PathVariable String area)
    {
        startPage();
        List<TSysBanner> list = tSysBannerService.selectTSysBannerByGroupNameAndArea(groupName, area);
        return getDataTable(list);
    }

    /**
     * 导出轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:export')")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TSysBanner tSysBanner)
    {
        List<TSysBanner> list = tSysBannerService.selectTSysBannerList(tSysBanner);
        ExcelUtil<TSysBanner> util = new ExcelUtil<TSysBanner>(TSysBanner.class);
        return util.exportExcel(list, "banner");
    }

    /**
     * 获取轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysBannerService.selectTSysBannerById(id));
    }

    /**
     * 新增轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:add')")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TSysBanner tSysBanner)
    {
        // 验证必填字段
        if (StringUtil.isEmpty(tSysBanner.getGroupName())) {
            return AjaxResult.error("分组名称不能为空");
        }
        if (StringUtil.isEmpty(tSysBanner.getFilePath())) {
            return AjaxResult.error("文件路径不能为空");
        }
        if (StringUtil.isEmpty(tSysBanner.getFileType())) {
            return AjaxResult.error("文件类型不能为空");
        }
        
        return toAjax(tSysBannerService.insertTSysBanner(tSysBanner));
    }

    /**
     * 修改轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:edit')")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TSysBanner tSysBanner)
    {
        // 验证必填字段
        if (StringUtil.isEmpty(tSysBanner.getGroupName())) {
            return AjaxResult.error("分组名称不能为空");
        }
        if (StringUtil.isEmpty(tSysBanner.getFilePath())) {
            return AjaxResult.error("文件路径不能为空");
        }
        if (StringUtil.isEmpty(tSysBanner.getFileType())) {
            return AjaxResult.error("文件类型不能为空");
        }
        
        return toAjax(tSysBannerService.updateTSysBanner(tSysBanner));
    }

    /**
     * 删除轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:remove')")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        //根据id查询轮播图
        List<TSysBanner> tSysBanners = tSysBannerService.selectTSysBannerByIds(ids);
        if (StringUtil.isNotEmpty(tSysBanners)){
            List<TSysBanner> collect = tSysBanners.stream().filter(item -> item.getStatus().equals("0")).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(collect)){
                return AjaxResult.error("存在上架状态的数据，请重新选择");
            }
        }
        return toAjax(tSysBannerService.deleteTSysBannerByIds(ids));
    }
    
    /**
     * 文件上传
     */
    @Log(title = "轮播图文件上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            // 文件大小；其中file.length()获取的是字节，除以1024可以得到以kb为单位的文件大小
            long size = file.getSize() / 1024;
            
            // 获取文件类型
            String originalFilename = file.getOriginalFilename();
            String fileType = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toUpperCase();
            }
            
            // 如果是图片文件，检查尺寸
            if ("JPG".equals(fileType) || "JPEG".equals(fileType) || "PNG".equals(fileType)) {
                BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
                int width = bufferedImage.getWidth();
                int height = bufferedImage.getHeight();
                log.info("图片大小：{}kb；图片宽度：{}像素；图片高度：{}像素", size, width, height);
            }

            String filePath = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("filePath", filePath);
            ajax.put("fileType", fileType);
            return ajax;
        }
        return AjaxResult.error("上传文件异常，请联系管理员");
    }

    /**
     * 状态修改
     */
    @Log(title = "轮播图状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TSysBanner tSysBanner) throws Exception {
        return toAjax(tSysBannerService.updateTSysBannerByStatus(tSysBanner));
    }
}
