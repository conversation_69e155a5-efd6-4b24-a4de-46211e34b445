package cn.guliandigital.web.controller.tsystem;


import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.tsystem.domain.TSysBanner;
import cn.guliandigital.tsystem.service.ITSysBannerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Slf4j
@RequestMapping("/tsystem/banner")
public class TSysBannerController extends BaseController
{
    @Autowired
    private ITSysBannerService tSysBannerService;
    /**
     * 查询轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysBanner tSysBanner)
    {
        startPage();
        tSysBanner.setDisplay("1");
        return getDataTable(tSysBannerService.selectTSysBannerList(tSysBanner));
    }

    /**
     * 导出轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:export')")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TSysBanner tSysBanner)
    {
        List<TSysBanner> list = tSysBannerService.selectTSysBannerList(tSysBanner);
        ExcelUtil<TSysBanner> util = new ExcelUtil<TSysBanner>(TSysBanner.class);
        return util.exportExcel(list, "banner");
    }

    /**
     * 获取轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysBannerService.selectTSysBannerById(id));
    }

    /**
     * 新增轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:add')")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TSysBanner tSysBanner)
    {
        TSysBanner query = new TSysBanner();
        if (tSysBanner.getImageName().equals("默认banner")) {
            tSysBanner.setDisplay("1");
            if (StringUtil.isNotEmpty(tSysBanner.getImageName())) {
                query.setImageName(tSysBanner.getImageName());
            }
            List<TSysBanner> tSysBanners = tSysBannerService.selectTSysBannerList(query);
            if (StringUtil.isNotEmpty(tSysBanners)) {
                return AjaxResult.error("默认banner已存在！请更换名称");
            }
        }
        return toAjax(tSysBannerService.insertTSysBanner(tSysBanner));
    }

    /**
     * 修改轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:edit')")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TSysBanner tSysBanner)
    {
        TSysBanner query = new TSysBanner();
        if (tSysBanner.getImageName().equals("默认banner")) {
            if (StringUtil.isNotEmpty(tSysBanner.getImageName())) {
                query.setImageName(tSysBanner.getImageName());
            }
            List<TSysBanner> tSysBanners = tSysBannerService.selectTSysBannerList(query);
            if (StringUtil.isNotEmpty(tSysBanners)) {
                return AjaxResult.error("默认banner已存在！请更换名称");
            }
        }
        return toAjax(tSysBannerService.updateTSysBanner(tSysBanner));
    }

    /**
     * 删除轮播图
     */
    @PreAuthorize("@ss.hasPermi('tsystem:banner:remove')")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        //根据id查询轮播
        List<TSysBanner> tSysBanners = tSysBannerService.selectTSysBannerByIds(ids);
        if (StringUtil.isNotEmpty(tSysBanners)){
            List<TSysBanner> collect = tSysBanners.stream().filter(item -> item.getStatus().equals("0")).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(collect)){
                return AjaxResult.error("存在开启状态的数据，请重新选择");
            }
            List<TSysBanner> collect2 = tSysBanners.stream().filter(item -> item.getImageName().equals("默认banner")).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(collect2)){
                return AjaxResult.error("包含默认banner，请重新选择");
            }
        }
        return toAjax(tSysBannerService.deleteTSysBannerByIds(ids));
    }
    @Log(title = "海报上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            // 文件大小；其中file.length()获取的是字节，除以1024可以得到以kb为单位的文件大小
            long size = file.getSize() / 1024;
            // 图片对象
            BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
            // 宽度
            int width = bufferedImage.getWidth();
            // 高度
            int height = bufferedImage.getHeight();

            // 打印信息
            log.info("图片大小：	{}kb；图片宽度：{}像素；图片高度：{}像素", size, width, height);
//    		if(height > Constants.PIC_MAX_HEIGHT || width > Constants.PIC_MAX_WIDTH) {
//    			return AjaxResult.error("图片尺寸不能超过640*320像素!");
//    		}
//            if(size/1024 > 10) {
//                return AjaxResult.error("图片大小不能超过10MB!");
//            }

            String avatar = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("imgUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     * 状态修改
     */
    @Log(title = "机构管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TSysBanner tSysBanner) throws Exception {
        return toAjax(tSysBannerService.updateTSysBannerByStatus(tSysBanner));
    }
}
