package cn.guliandigital.web.controller.tsystem;

import java.util.List;

import cn.guliandigital.common.config.HuangHeConfig;
import cn.guliandigital.tsystem.domain.TSysNews;
import cn.guliandigital.common.utils.file.FileUploadUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.guliandigital.common.annotation.Log;
import cn.guliandigital.common.annotation.RepeatSubmit;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.tsystem.service.ITSysNewsService;
import cn.guliandigital.common.utils.poi.ExcelUtil;
import cn.guliandigital.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 资讯管理Controller  
 * 
 * <AUTHOR>
 * @date 2020-09-10
 */
@RestController
@RequestMapping("/tsystem/news")
public class TSysNewsController extends BaseController
{
    @Autowired
    private ITSysNewsService tSysNewsService;

    /**
     * 查询资讯管理列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSysNews tSysNews)
    {
        startPage();
        List<TSysNews> list = tSysNewsService.selectTSysNewsList(tSysNews);
        return getDataTable(list);
    }

    /**
     * 导出资讯管理列表
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:export')")
    @Log(title = "资讯管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @RepeatSubmit
    public AjaxResult export(TSysNews tSysNews)
    {
        List<TSysNews> list = tSysNewsService.selectTSysNewsList(tSysNews);
        ExcelUtil<TSysNews> util = new ExcelUtil<TSysNews>(TSysNews.class);
        return util.exportExcel(list, "news");
    }

    /**
     * 获取资讯管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysNewsService.selectTSysNewsById(id));
    }

    /**
     * 新增资讯管理
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:add')")
    @Log(title = "资讯管理", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TSysNews tSysNews)
    {
        return toAjax(tSysNewsService.insertTSysNews(tSysNews));
    }

    /**
     * 修改资讯管理
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:edit')")
    @Log(title = "资讯管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TSysNews tSysNews)
    {
        return toAjax(tSysNewsService.updateTSysNews(tSysNews));
    }

    /**
     * 删除资讯管理
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:remove')")
    @Log(title = "删除资讯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tSysNewsService.deleteTSysNewsByIds(ids));
    }

    /**
     * 资讯海报上传
     */
    @Log(title = "资讯海报上传", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    @RepeatSubmit
    public AjaxResult upload(@RequestParam("uploadfile") MultipartFile file) throws Exception{
        if (!file.isEmpty())
        {
            String avatar = FileUploadUtils.upload(HuangHeConfig.getUploadPath(), file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("imgUrl", avatar);
            return ajax;
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    /**
     * 修改资讯管理
     */
    @PreAuthorize("@ss.hasPermi('tsystem:news:publish')")
    @Log(title = "资讯发布", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/publish/{id}")
    @RepeatSubmit
    public AjaxResult publish(@PathVariable("id") String id)
    {
        return toAjax(tSysNewsService.updatePublishStatus(id));
    }
}
