<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-config.dtd">

<configuration>
    <settings>
       <!-- 这个配置使全局的映射器启用或禁用缓存 -->
		<setting name="cacheEnabled" value="false"/> 
		<!-- 全局启用或禁用延迟加载。当禁用时，所有关联对象都会即时加载。 -->  
		<setting name="lazyLoadingEnabled" value="false"/> 
		<!-- 设置关联对象加载的形态，此处为按需加载字段(加载字段由SQL指定)，不会加载关联表的所有字段，以提高性能 -->  
		<setting name="aggressiveLazyLoading" value="true" />
		<!-- 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果 -->   
		<setting name="multipleResultSetsEnabled" value="true"/> 
		<!-- 允许使用列标签代替列名 -->    
		<setting name="useColumnLabel" value="true"/> 
		<!-- 允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖 -->
		<setting name="useGeneratedKeys" value="false"/> 
		
		<!-- 日志打印类别  STDOUT_LOGGING 控制台  SLF4J 日志文件-->
	    <setting name="logImpl" value="SLF4J" />
	    <!-- 配置和设定执行器，SIMPLE 执行器执行其它语句。REUSE 执行器可能重复使用prepared statements 语句，BATCH执行器可以重复执行语句和批量更新。 -->  
		<setting name="defaultExecutorType" value="SIMPLE"/> 
		<!-- 设置超时时间，它决定驱动等待一个数据库响应的时间。 -->  
		<setting name="defaultStatementTimeout" value="25000"/> 
		<!-- 空值处理 -->
		<setting name="callSettersOnNulls" value="true"/>
		<setting name="jdbcTypeForNull" value="NULL"/>
    </settings>

</configuration> 