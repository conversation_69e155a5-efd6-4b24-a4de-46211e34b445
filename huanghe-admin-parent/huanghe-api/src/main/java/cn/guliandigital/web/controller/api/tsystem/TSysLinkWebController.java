package cn.guliandigital.web.controller.api.tsystem;

import cn.guliandigital.common.annotation.PvLog;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.enums.BusinessType;
import cn.guliandigital.tsystem.domain.TSysLink;
import cn.guliandigital.tsystem.service.ITSysLinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 友情链接前端Controller
 * 
 * <AUTHOR>
 * @date 2020-09-22
 */
@RestController
@RequestMapping("/wapi/tsystem/link/web")
public class TSysLinkWebController extends BaseController
{
    @Autowired
    private ITSysLinkService tSysLinkService;

    /**
     * 查询友情链接列表
     */
    //@PvLog(title = "友情链接", businessType = BusinessType.OTHER)
    @GetMapping("/list")
    public TableDataInfo list(TSysLink tSysLink)
    {
        List<TSysLink> list = tSysLinkService.selectTSysLinkList(tSysLink);
        return getDataTable(list);
    }

}
