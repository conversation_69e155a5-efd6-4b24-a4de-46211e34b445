package cn.guliandigital.web.controller.api.tsystem;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.tsystem.domain.TSysBanner;
import cn.guliandigital.tsystem.service.ITSysBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图Controller
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Slf4j
@Api(tags = "首页轮播图")
@RequestMapping("/wapi/tsystem/banner/web")
public class TSysBannerWebController extends BaseController
{
    @Autowired
    private ITSysBannerService tSysBannerService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath2}")
    private String downloadPath;

    /**
     * 查询轮播图列表
     */
    @ApiOperation(value = "查询轮播图")
    @GetMapping("/list")
    public AjaxResult list(TSysBanner tSysBanner)
    {
        List<TSysBanner> list = tSysBannerService.selectTSysBannerList(tSysBanner);
        String url = serverConfig.getUrl();
        for (TSysBanner sysBanner : list) {
            if(Strings.isNotBlank(sysBanner.getImagePath())) {
                if(!sysBanner.getImagePath().startsWith("http")){
                    sysBanner.setImagePath(url + downloadPath + sysBanner.getImagePath());
                }
            }
        }
        //过滤掉下架状态的数据
        list = list.stream().filter(t -> t.getStatus().equals("0")).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 获取轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('busis:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        TSysBanner data = tSysBannerService.selectTSysBannerById(id);
        String url = serverConfig.getUrl();
            if(Strings.isNotBlank(data.getImagePath())) {
                if(!data.getImagePath().startsWith("http")){
                    data.setImagePath(url + downloadPath + data.getImagePath());
                }
        }
        return AjaxResult.success(data);
    }
}
