package cn.guliandigital.web.controller.api.tsystem;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.tsystem.domain.TSysBanner;
import cn.guliandigital.tsystem.service.ITSysBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图Web API Controller
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Slf4j
@Api(tags = "轮播图API")
@RequestMapping("/wapi/tsystem/banner/web")
public class TSysBannerWebController extends BaseController
{
    @Autowired
    private ITSysBannerService tSysBannerService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath2}")
    private String downloadPath;

    /**
     * 查询轮播图列表
     */
    @ApiOperation(value = "查询轮播图列表")
    @GetMapping("/list")
    public AjaxResult list(TSysBanner tSysBanner)
    {
        tSysBanner.setDelFlag(0);
        List<TSysBanner> list = tSysBannerService.selectTSysBannerList(tSysBanner);
        String url = serverConfig.getUrl();
        for (TSysBanner sysBanner : list) {
            if(Strings.isNotBlank(sysBanner.getFilePath())) {
                if(!sysBanner.getFilePath().startsWith("http")){
                    sysBanner.setFilePath(url + downloadPath + sysBanner.getFilePath());
                }
            }
        }
        //过滤掉下架状态的数据
        list = list.stream().filter(t -> t.getStatus().equals("0")).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 根据分组查询轮播图列表
     */
    @ApiOperation(value = "根据分组查询轮播图列表")
    @GetMapping("/listByGroup/{groupName}")
    public AjaxResult listByGroup(@ApiParam(value = "分组名称", required = true) @PathVariable String groupName)
    {
        List<TSysBanner> list = tSysBannerService.selectTSysBannerByGroupName(groupName);
        String url = serverConfig.getUrl();
        for (TSysBanner sysBanner : list) {
            if(Strings.isNotBlank(sysBanner.getFilePath())) {
                if(!sysBanner.getFilePath().startsWith("http")){
                    sysBanner.setFilePath(url + downloadPath + sysBanner.getFilePath());
                }
            }
        }
        //过滤掉下架状态的数据
        list = list.stream().filter(t -> t.getStatus().equals("0")).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 根据分组和地市查询轮播图列表
     */
    @ApiOperation(value = "根据分组和地市查询轮播图列表")
    @GetMapping("/listByGroupAndArea/{groupName}/{area}")
    public AjaxResult listByGroupAndArea(
            @ApiParam(value = "分组名称", required = true) @PathVariable String groupName,
            @ApiParam(value = "地市", required = true) @PathVariable String area)
    {
        List<TSysBanner> list = tSysBannerService.selectTSysBannerByGroupNameAndArea(groupName, area);
        String url = serverConfig.getUrl();
        for (TSysBanner sysBanner : list) {
            if(Strings.isNotBlank(sysBanner.getFilePath())) {
                if(!sysBanner.getFilePath().startsWith("http")){
                    sysBanner.setFilePath(url + downloadPath + sysBanner.getFilePath());
                }
            }
        }
        //过滤掉下架状态的数据
        list = list.stream().filter(t -> t.getStatus().equals("0")).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 根据分组查询上架的轮播图
     */
    @ApiOperation(value = "根据分组查询上架的轮播图")
    @GetMapping("/getActiveByGroup/{groupName}")
    public AjaxResult getActiveByGroup(@ApiParam(value = "分组名称", required = true) @PathVariable String groupName)
    {
        TSysBanner query = new TSysBanner();
        query.setGroupName(groupName);
        query.setStatus("0"); // 上架状态
        query.setDelFlag(0);
        List<TSysBanner> list = tSysBannerService.selectTSysBannerList(query);
        
        String url = serverConfig.getUrl();
        for (TSysBanner sysBanner : list) {
            if(Strings.isNotBlank(sysBanner.getFilePath())) {
                if(!sysBanner.getFilePath().startsWith("http")){
                    sysBanner.setFilePath(url + downloadPath + sysBanner.getFilePath());
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取轮播图详细信息
     */
    @ApiOperation(value = "获取轮播图详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "轮播图ID", required = true) @PathVariable("id") String id)
    {
        TSysBanner banner = tSysBannerService.selectTSysBannerById(id);
        if (banner != null) {
            String url = serverConfig.getUrl();
            if(Strings.isNotBlank(banner.getFilePath())) {
                if(!banner.getFilePath().startsWith("http")){
                    banner.setFilePath(url + downloadPath + banner.getFilePath());
                }
            }
        }
        return AjaxResult.success(banner);
    }

    /**
     * 获取视频轮播图
     */
    @ApiOperation(value = "获取视频轮播图")
    @GetMapping("/video")
    public AjaxResult getVideoBanners()
    {
        return getActiveByGroup("G1");
    }

    /**
     * 获取书卷轮播图
     */
    @ApiOperation(value = "获取书卷轮播图")
    @GetMapping("/book")
    public AjaxResult getBookBanners()
    {
        return getActiveByGroup("G2");
    }

    /**
     * 获取专题轮播图
     */
    @ApiOperation(value = "获取专题轮播图")
    @GetMapping("/topic")
    public AjaxResult getTopicBanners()
    {
        return getActiveByGroup("G3");
    }

    /**
     * 获取地图轮播图
     */
    @ApiOperation(value = "获取地图轮播图")
    @GetMapping("/map")
    public AjaxResult getMapBanners()
    {
        return getActiveByGroup("G4");
    }
}
