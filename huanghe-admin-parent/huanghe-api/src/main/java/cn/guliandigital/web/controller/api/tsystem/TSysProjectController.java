package cn.guliandigital.web.controller.api.tsystem;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.PublishStatus;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.html.DelTagsUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.tsystem.domain.TSysProject;
import cn.guliandigital.tsystem.domain.vo.ProjectVo;
import cn.guliandigital.tsystem.service.ITSysProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 工程项目简介Controller
 *
 * <AUTHOR>
 * @date 2023-07-06
 */
@Api(tags = "黄河大典编纂出版工程")
@RestController
@RequestMapping("/wapi/tsystem/project/web")
public class TSysProjectController extends BaseController {
    @Autowired
    private ITSysProjectService tSysProjectService;


    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;


    @Autowired
    private RedisCache redisCache;


    /**
     * 查询工程项目简介
     */
    @ApiOperation(value = "项目工程列表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "title", value = "项目标题")
    })
    public TableDataInfo list(TSysProject tSysProject) {
        startPage();
        tSysProject.setPublishStatus(Integer.parseInt(PublishStatus.DONE.getCode()));
        List<TSysProject> list = tSysProjectService.selectTSysProjectList(tSysProject);
        //补全图片地址
        String url = serverConfig.getUrl();
        for (TSysProject project : list) {
            if (project.getIconUrl() != null && !project.getIconUrl().startsWith("http")) {
                project.setIconUrl(url + downloadPath + project.getIconUrl());

            }
            String content = project.getContent();
            if (StringUtil.isEmpty(content)) {
            } else {
                String text = DelTagsUtil.delHTMLTagPattern(content);
                if (text.length() > 300) {
                    text = StringUtil.substring(text, 0, 300) + "...";
                }
                project.setContent(text);
            }

        }

        return getDataTable(list);
    }


    /**
     * 获取工程项目简介详细信息
     */
    @ApiOperation(value = "项目工程详情")
    @GetMapping(value = "/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true)
    })
    public AjaxResult getInfo(@PathVariable("id") String id) {
        TSysProject tSysProject = tSysProjectService.selectTSysProjectById(id);
        if(Objects.isNull(tSysProject)){
          return AjaxResult.error("此条资讯不存在");
        }
        //补全图片地址
        String url = serverConfig.getUrl();
        if (tSysProject.getIconUrl() != null && !tSysProject.getIconUrl().startsWith("http")) {
            tSysProject.setIconUrl(url + downloadPath + tSysProject.getIconUrl());

        }

        //获取redis阅读次数
        Object cacheObject = redisCache.getCacheObject(RedisConstants.HUANGHE_PROJECT_ID + id);
        tSysProject.setReadCount((Integer) cacheObject);

        //获取前一个后一个相关信息
        List<TSysProject> list = tSysProjectService.selectTSysProjectList(new TSysProject(){{setPublishStatus(Integer.parseInt(PublishStatus.DONE.getCode()));}});
        for (int i = 0; i < list.size(); i++) {
            if (ObjectUtils.equals(tSysProject.getId(), list.get(i).getId())) {
                if (i - 1 >= 0) {
                    ProjectVo projectVo = new ProjectVo();
                    projectVo.setTitle(list.get(i - 1).getTitle());
                    projectVo.setId(list.get(i - 1).getId());
                    tSysProject.setPrevious(projectVo);
                }
                if (list.size() > i + 1) {
                    ProjectVo projectVo = new ProjectVo();
                    projectVo.setTitle(list.get(i + 1).getTitle());
                    projectVo.setId(list.get(i + 1).getId());
                    tSysProject.setNext(projectVo);
                }
            }
        }

        return AjaxResult.success(tSysProject);

    }

}
