package cn.guliandigital.web.controller.api.tsystem;

import java.util.List;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.enums.PublishStatus;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.html.DelTagsUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.tsystem.domain.TSysNews;
import cn.guliandigital.tsystem.domain.vo.NewsVo;
import cn.guliandigital.tsystem.service.ITSysNewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资讯管理前端Controller
 * 
 * <AUTHOR>
 * @date 2020-09-22
 */
@RestController
@Api(tags = "新闻相关")
@RequestMapping("/wapi/tsystem/news/web")
public class TSysNewsWebController extends BaseController
{
    @Autowired
    private ITSysNewsService tSysNewsService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;


    @Autowired
    private RedisCache redisCache;

    /**
     * 查询资讯管理列表
     */    
    @GetMapping("/list")
    public TableDataInfo list(HttpServletRequest request,TSysNews tSysNews)
    {
        startPage();
        tSysNews.setPublishStatus("1");
        List<TSysNews> list = tSysNewsService.selectTSysNewsList(tSysNews);
        //补全图片地址
        String url = serverConfig.getUrl();
        for (TSysNews news : list) {
            if(news.getIconUrl() != null && !news.getIconUrl().startsWith("http")){
                    news.setIconUrl(url+ downloadPath + news.getIconUrl());

            }
            
            String content = news.getNewsContent();
            String text = DelTagsUtil.delHTMLTagPattern(content);
         
            if(text.length() > 300) {
            	text = StringUtil.substring(text, 0, 300)+"...";
            }
            news.setNewsContent(text);
        }



        return getDataTable(list);
    }

    /**
     * 获取资讯管理详细信息
     */
    @ApiOperation(value = "查询新闻详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
    	
    	TSysNews news = tSysNewsService.selectTSysNewsWebById(id);
        if(Objects.isNull(news)){
            return AjaxResult.error("此条资讯不存在");
        }
        if(!news.getPublishStatus().equals(PublishStatus.DONE.getCode())){
            return AjaxResult.error("此条资讯已下架");

        }
    	 //补全图片地址
        String url = serverConfig.getUrl();       
        if(news.getIconUrl() != null && !news.getIconUrl().startsWith("http")){
                news.setIconUrl(url+ downloadPath + news.getIconUrl());

        }
        //redis获取阅读次数
        Object cacheObject = redisCache.getCacheObject(RedisConstants.HUANGHE_NEWS_ID + id);
        news.setReadCount((Integer)cacheObject);



        //获取前一个后一个相关信息
        List<TSysNews> list = tSysNewsService.selectTSysNewsList(new TSysNews(){{setPublishStatus(PublishStatus.DONE.getCode());}});
        for (int i=0; i<list.size();i++){
            if (ObjectUtils.equals(news.getId(),list.get(i).getId())){
                if(i-1>=0){
                    NewsVo newsVo = new NewsVo();
                    newsVo.setTitle(list.get(i-1).getNewsTitle());
                    newsVo.setId(list.get(i-1).getId());
                    news.setPrevious(newsVo);
                }
                if(list.size()>i+1){
                    NewsVo newsVo = new NewsVo();
                    newsVo.setTitle(list.get(i+1).getNewsTitle());
                    newsVo.setId(list.get(i+1).getId());
                    news.setNext(newsVo);
                }
            }
        }




//        list.stream().map(e->{
//
//            if(ObjectUtils.equals(e.getId(),news.getId())){
//
//            }
//        })


        return AjaxResult.success(news);
    }
}
