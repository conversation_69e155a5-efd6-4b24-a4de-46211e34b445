package cn.guliandigital.web.controller.api.tsystem;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import cn.guliandigital.common.enums.DataForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.domain.model.FeedBackCount;
import cn.guliandigital.common.enums.HandleStatus;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import cn.guliandigital.tsystem.domain.TSysFeedback;
import cn.guliandigital.tsystem.service.ITSysFeedbackService;

/**
 * 意见反馈Controller
 * 
 * <AUTHOR>
 * @date 2020-09-06
 */
@RestController
@RequestMapping("/wapi/tsystem/feedback/web")
public class TSysFeedbackWebController extends BaseController
{
    @Autowired
    private ITSysFeedbackService tSysFeedbackService;

    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;

    /**
     * 查询意见反馈列表（我的反馈）
     */
    @GetMapping("/list")
    public Object list(HttpServletRequest request, TSysFeedback tSysFeedback)
    {
        
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        startPage();
        /** ---获取用户信息   end  -- **/
        tSysFeedback.setUserId(user.getId());
        List<TSysFeedback> list = tSysFeedbackService.selectTSysFeedbackList(tSysFeedback);
        return getDataTable(list);
    }


    /**
     * 获取意见反馈详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tSysFeedbackService.selectTSysFeedbackById(id));
    }

    /**
     * 新增意见反馈
     */
    @PostMapping
    public AjaxResult add(HttpServletRequest request, @RequestBody TSysFeedback tSysFeedback)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        /** ---获取用户信息   end  -- **/
        tSysFeedback.setUserId(user.getId());
        tSysFeedback.setCreatebyName(user.getUserName());
        tSysFeedback.setDataFrom(DataForm.P.getCode());
        return toAjax(tSysFeedbackService.insertTSysFeedback(tSysFeedback));
    }

    /**
     * 删除意见反馈
     */
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id)
    {
        return toAjax(tSysFeedbackService.deleteTSysFeedbackById(id));
    }

    @GetMapping("/getCount")
    public AjaxResult getCount(HttpServletRequest request)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        /** ---获取用户信息   end  -- **/
        TSysFeedback tSysFeedback = new TSysFeedback();
        tSysFeedback.setUserId(user.getId());
        tSysFeedback.setHandleStatus(HandleStatus.NOT.getCode());
        //未处理条数
        List<TSysFeedback> notList = tSysFeedbackService.selectTSysFeedbackList(tSysFeedback);
        //已处理条数
        tSysFeedback.setHandleStatus(HandleStatus.DONE.getCode());
        List<TSysFeedback> doneList = tSysFeedbackService.selectTSysFeedbackList(tSysFeedback);
        FeedBackCount feedBackCount = new FeedBackCount();
        feedBackCount.setNotCount(notList.size());
        feedBackCount.setDoneCount(doneList.size());
        return AjaxResult.success(feedBackCount);
    }
}
