package cn.guliandigital.web.controller.api.product;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import cn.guliandigital.common.enums.DataForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.product.search.domain.TUserSearchHistory;
import cn.guliandigital.product.search.service.ITUserSearchHistoryService;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户检索历史Controller
 * 
 * <AUTHOR>
 * @date 2020-10-08
 */

@Slf4j
@RestController
@RequestMapping("/wapi/search/history")
public class TUserSearchHistoryController extends BaseController
{
    @Autowired
    private ITUserSearchHistoryService tUserSearchHistoryService;

    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;

    /**
     * 查询用户检索历史列表
     */
    @GetMapping("/list")
    public Object list(HttpServletRequest request, TUserSearchHistory tUserSearchHistory)
    {
        
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
//        user = new TPlatUser();
//        user.setId("958d9b0d594241ea8426180d0978cf8b");
        startPage();
        /** ---获取用户信息   end  -- **/
        tUserSearchHistory.setCreatebyId(user.getId());
        tUserSearchHistory.setDataFrom(DataForm.P.getCode());
        List<TUserSearchHistory> listRow = tUserSearchHistoryService.selectTUserSearchHistoryList(tUserSearchHistory);
        log.info("==>查询记录数：{}",listRow.size());
        for (TUserSearchHistory item : listRow) {
            if (("A").equals(item.getSearchType())) {
                if (StringUtil.isNotBlank(item.getSearchContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(item.getSearchContent());
                    String searchContent = jsonObject.getString("searchList");
                    List<Map> list = JSONObject.parseArray(searchContent, Map.class);
                    String queryField="";
                    String combinationCondition="";
                    //String keyWord="";
                    StringBuffer newsKeyWordBuffer = new StringBuffer();
                    String key="";
                    for (int i=0;i<list.size();i++){
	                      String _keyWord = list.get(i).get("keyword").toString();
	                      if(Strings.isNullOrEmpty(_keyWord)) {
	                    	  continue;
	                      }
	                      if(StringUtil.isNotBlank(list.get(i).get("queryField").toString())){
	                          queryField = list.get(i).get("queryField").toString();
	                      }
	                      if(StringUtil.isNotBlank(list.get(i).get("combinationCondition").toString())){
	                         combinationCondition = list.get(i).get("combinationCondition").toString();
	                      }
	                      //newsKeyWord=newsKeyWord+combinationCondition+queryField+"--"+_keyWord;
	                      newsKeyWordBuffer.append(combinationCondition).append(queryField).append("--").append(_keyWord);
                    }
                    String newsKeyWord = newsKeyWordBuffer.toString();
                    key=newsKeyWord.replace("bookName","书名")
                            .replace("menuName","篇章名")
                            .replace("congbian","丛编")
                            .replace("mainResponsibility","篇章作者")
                            .replace("publisher","出版社")
                            .replace("bookDesc","图书简介")                            
                            .replace("fullText","全文")
                            .replace("and","与")
                            .replace("or","或")
                            .replace("not","非");
                    item.setSearchContent(key);
                }
            }
        }
        
        return getDataTable(listRow);
    }

    /**
     * 获取用户检索历史详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tUserSearchHistoryService.selectTUserSearchHistoryById(id));
    }

    /**
     * 新增用户检索历史
     */
    @PostMapping
    public AjaxResult add(@RequestBody TUserSearchHistory tUserSearchHistory)
    {
        tUserSearchHistory.setDataFrom(DataForm.P.getCode());
        return toAjax(tUserSearchHistoryService.insertTUserSearchHistory(tUserSearchHistory));
    }

    /**
     * 修改用户检索历史
     */
    @PutMapping
    public AjaxResult edit(@RequestBody TUserSearchHistory tUserSearchHistory)
    {
        return toAjax(tUserSearchHistoryService.updateTUserSearchHistory(tUserSearchHistory));
    }

    /**
     * 删除用户检索历史
     */
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id)
    {
        return toAjax(tUserSearchHistoryService.deleteTUserSearchHistoryById(id));
    }
}
