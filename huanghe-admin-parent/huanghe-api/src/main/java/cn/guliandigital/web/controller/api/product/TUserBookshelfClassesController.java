package cn.guliandigital.web.controller.api.product;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import cn.guliandigital.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.exception.CustomException;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.product.userbook.domain.TUserBookshelf;
import cn.guliandigital.product.userbook.domain.TUserBookshelfClasses;
import cn.guliandigital.product.userbook.service.ITUserBookshelfClassesService;
import cn.guliandigital.product.userbook.service.ITUserBookshelfService;
import cn.guliandigital.session.util.SecurityPlatuserUtils;

/**
 * 用户书架分类Controller
 * 
 * <AUTHOR>
 * @date 2020-09-23
 */
@RestController
@RequestMapping("/wapi/product/bookclasses")
public class TUserBookshelfClassesController extends BaseController
{
    @Autowired
    private ITUserBookshelfClassesService tUserBookshelfClassesService;

    @Autowired
    private ITUserBookshelfService tUserBookshelfService;

    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;
    /**
     * 查询用户书架分类列表
     */
    @GetMapping("/list")
    public Object list(HttpServletRequest request)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        /** ---获取用户信息   end  -- **/
        TUserBookshelfClasses tUserBookshelfClasses = new TUserBookshelfClasses();
        tUserBookshelfClasses.setCreatebyId(user.getId());
        List<TUserBookshelfClasses> list = tUserBookshelfClassesService.selectTUserBookshelfClassesList(tUserBookshelfClasses);
        return getDataTable(list);
    }

    /**
     * 获取用户书架分类详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tUserBookshelfClassesService.selectTUserBookshelfClassesById(id));
    }

    /**
     * 新增用户书架分类
     */
    @PostMapping
    public AjaxResult add(HttpServletRequest request, @RequestBody TUserBookshelfClasses tUserBookshelfClasses)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        if(Strings.isNullOrEmpty(tUserBookshelfClasses.getClassesName())) {
        	return AjaxResult.error("分类名称不能为空！");
        }
        if(tUserBookshelfClasses.getClassesName().length() > 100) {
        	return AjaxResult.error("分类名称不能超过100字符！");
        }
        
        /** ---获取用户信息   end  -- **/
        //先判断是否已存在
        TUserBookshelfClasses query = new TUserBookshelfClasses();
        query.setClassesName(tUserBookshelfClasses.getClassesName());
        query.setCreatebyId(user.getId());
        List<TUserBookshelfClasses> list = tUserBookshelfClassesService.selectClassesList(query);
        if(list != null  && list.size() > 0) {
        	 return AjaxResult.error("分类名称已存在，请不要重复创建！");
        }
        tUserBookshelfClasses.setCreatebyId(user.getId());
        tUserBookshelfClasses.setCreatebyName(user.getUserName());
        tUserBookshelfClasses.setId(IdUtils.simpleUUID());
        return toAjax(tUserBookshelfClassesService.insertTUserBookshelfClasses(tUserBookshelfClasses));
    }

    /**
     * 修改用户书架分类
     */
    @PutMapping
    public AjaxResult edit(HttpServletRequest request, @RequestBody TUserBookshelfClasses tUserBookshelfClasses)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        /** ---获取用户信息   end  -- **/
        //先判断是否已存在
        TUserBookshelfClasses tUserBookshelfClasses1 = tUserBookshelfClassesService.selectTUserBookshelfClassesById(tUserBookshelfClasses.getId());
        if(!tUserBookshelfClasses.getClassesName().equals(tUserBookshelfClasses1.getClassesName())){
            TUserBookshelfClasses query = new TUserBookshelfClasses();
            query.setClassesName(tUserBookshelfClasses.getClassesName());
            query.setCreatebyId(user.getId());
            List<TUserBookshelfClasses> list = tUserBookshelfClassesService.selectClassesList(query);
            if(list != null  && list.size() > 0) {
                return AjaxResult.error("分类名称已存在，请不要重复创建！");
            }
        }

        tUserBookshelfClasses.setUpdatebyId(user.getId());
        tUserBookshelfClasses.setUpdatebyName(user.getUserName());
        return toAjax(tUserBookshelfClassesService.updateTUserBookshelfClasses(tUserBookshelfClasses));
    }

    /**
     * 删除用户书架分类
     */
	@DeleteMapping("/remove/{id}")
    public AjaxResult remove(HttpServletRequest request, @PathVariable("id") String id)
    {
        /** ---获取用户信息 start -- **/
        TPlatUser user = null;
        try {
            user = securityPlatuserUtils.getUser(request);
        }catch(CustomException e) {
            int code = e.getCode();
            String msg = e.getMessage();
            return AjaxResult.error(code, msg);
        }
        /** ---获取用户信息   end  -- **/
        TUserBookshelf tUserBookshelf = new TUserBookshelf();
        tUserBookshelf.setClassesId(id);
        tUserBookshelf.setCreatebyId(user.getId());
        if(tUserBookshelfService.selectTUserBookshelfList(tUserBookshelf).size() > 0){
            return AjaxResult.error("书架分类下存在图书，禁止删除");
        }
        return toAjax(tUserBookshelfClassesService.deleteTUserBookshelfClassesById(id));
    }
}
