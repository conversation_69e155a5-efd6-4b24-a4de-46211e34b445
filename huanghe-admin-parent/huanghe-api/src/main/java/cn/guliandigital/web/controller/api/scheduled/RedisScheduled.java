package cn.guliandigital.web.controller.api.scheduled;

import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.log.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Slf4j
@Component
public class RedisScheduled {

    @Autowired
    RedisCache redisCache;

    /**
     * 定时任务每日凌晨清理redis
     */
    @Scheduled(cron =  "0 0 0 * * ?")
    public void redisClearCache() {

        log.info("开始执行每日零点执行的定时器++++清除redis");
        //前缀获取rediskey
        Collection<String> keys = redisCache.keys("huanghe:user:menu:id:*");
        //删除rediskey
        redisCache.deleteObject(keys);


    }

}
