package cn.guliandigital;

import javax.annotation.PostConstruct;

import org.apache.catalina.connector.Connector;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */ 
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
//启用缓存
@EnableCaching
//启用异步
@EnableAsync
@EnableScheduling
public class ApiApplication
{
    public static void main(String[] args)
    {
        //System.setProperty("spring.devtools.restart.enabled", "false");
    	System.setProperty("thumbnailator.conserveMemoryWorkaround", "true");
        SpringApplication.run(ApiApplication.class, args);
        //new ThreadServer().startServer();
//        ThreadServer ts = new ThreadServer();
//        Thread th = new Thread(ts);
//        th.start();
        
        System.out.println("====》黄河大典项目启动成功《====");
        
        //Tif2JpgSchedule sch = new Tif2JpgSchedule();
        //sch.schedule();
    }
    
    
    @Bean
    public ConfigurableServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
                connector.setProperty("relaxedQueryChars", "|{}[]");
            }
        });
        return factory;
    }
    
    
    @PostConstruct
    void init() {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
    }
}
