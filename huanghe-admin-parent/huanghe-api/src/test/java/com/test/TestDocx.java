package com.test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;



public class TestDocx {

	
	public static void main(String[] args) throws Exception {
	    Path path = Paths.get("C:\\Users\\<USER>\\Desktop", "export_docx_template.docx");
	    InputStream inputStream = new FileInputStream(path.toFile());
	    XWPFDocument document = new XWPFDocument(inputStream);
	    XWPFParagraph paragraph = document.createParagraph();
	    String text = "𧥚 15平面：𫂆𫓨𪯋  扩A：㒈㝲 𰥍";
	    while (!text.isEmpty()) {
	        XWPFRun xwpfRun = paragraph.createRun();
	        String font = null;
	        int beginIndex = 1;
	        String substring = text.substring(0, beginIndex);
	        if (Character.isHighSurrogate(substring.charAt(0))) {
	            beginIndex = 2;
	            substring = text.substring(0, beginIndex);
	            String hexString = Integer.toHexString(substring.codePointAt(0));
	            if (StringUtils.startsWithIgnoreCase(hexString, "F")) {
	                font = "中华书局宋体15平面";
	            } else if (StringUtils.startsWith(hexString, "2")) {
	                font = "中华书局宋体02平面";
	            } else if (StringUtils.startsWith(hexString, "3")) {
	                font = "中华书局宋体03平面";
	            }
	            xwpfRun.setText(substring);
	            if (font != null) {
	                xwpfRun.setFontFamily(font);
	            }
	        } else {
	            xwpfRun.setText(substring);
	        }
	        text = text.substring(beginIndex);
	    }
	    Path tempDocxPath = Paths.get("C:\\Users\\<USER>\\Desktop", System.currentTimeMillis() + ".docx");
	    FileOutputStream out = new FileOutputStream(tempDocxPath.toFile());
	    document.write(out);
	    IOUtils.closeQuietly(document);
	    IOUtils.closeQuietly(out);
	    System.out.println("==>生成完毕<==");
	}
}
