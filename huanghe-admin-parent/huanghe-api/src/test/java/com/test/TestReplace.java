package com.test;

import cn.guliandigital.storage.storage.service.impl.XmlToolUtils;

public class TestReplace {

	public static void main(String[] args) {
//		String httpUrl = "http://114.255.120.44/huanghe/wapi/common/download?filename=";
//		String bce = "<img src='http:/127.0.0.1:8088/wapi/common/download?filename=/2021/10/27/f3591c8d-ca11-48c5-97ea-c901cd8f32d6.jpg'  class=\"chatu\"><img src='http:/127.0.0.1:8088/wapi/common/download?filename=/2021/10/27/4fe52a3f-6780-4ca2-9741-f85f3ad323cd.jpg'  class=\"chatu\">";
//		String newbce = StringUtil.replace(bce, "http:/127.0.0.1:8088/wapi/common/download?filename=", httpUrl+"/");
//        newbce = FilenameUtils.normalize(newbce,true);
//        System.out.println("==>处理后httpUrl：{}"+httpUrl);
//        System.out.println("==>处理后content：{}"+newbce);
		
		String sss = "<p>瞵率䦰䍦<span class=\"zhu\">梚<span class=\"big01\">鿄</span>拔蔏虮䦰儱鰇</span></p>\r\n" + 
				"<p>观㽏㦱簩喂䔣扬<span class=\"zhu\">熎遐扬諐㦱悘旞頕㦱扬諐簩幅旞对铋䚃羫盦狎扬昜盪扬铋僮㡠㦱挆狎扬蘈宠扬观㠁扬铋僮㦱幅秬濼扬铋僮㡠幅跷払扬割抢扬鱠詨扬铋僮簩幅佒㗈扬䭪倒旞铋僮㡠簩帘观㠁扬㕥圉㹶汶胩㦱帘秬濼扬㕥貇㹶汶胩簩㢤䕅䖰餈噟狎笙誐庸</span>䦰䍦拸㩿纑跷㢤皓籙䖰帘旕㽏袼棐尞䚃㦱鱠㓆欌<span class=\"zhu\">釫郝畢䖰㺁</span>㩎妆磖幔皛幔帘悘烦簩尞簩藌欌顅麙杆䖰湢<span class=\"zhu\">㐐鲸搰跷宿藌烦叾</span>皛</p>";
		//String enccontent_new = StringUtil.replaceAll(sss, "(<span class=\"big01\">[\u4e00-\u9fff]{1,2}</span>)","");
		//System.out.println("==>处理后content："+enccontent_new);
		//String ttt = "鿄";
		//System.out.println(UnicodeUtils.string2Unicode(ttt));
		String ttt = XmlToolUtils.removeBigwordStr(sss,"big01");
		System.out.println(ttt);
		
	}
	
	

}
