package com.test;

import org.dom4j.DocumentException;

import com.mysql.cj.log.Log;

import cn.guliandigital.common.utils.StringUtil;

public class Test {

	
	public static void main(String[] args) throws DocumentException {
//		String content = "<段落 first_indent=\"2\" succeed_indent=\"0\" id=\"ZSK12445-000001-*********\"><正文>勅賜宋板景㝎建康志紙墨精好重加装訂常置案頭翻閱適陽湖孫觀察星衍僑居金<字体 type=\"超大字2\">&#x28e67;</字体><原书页面>ZSK12445-000019-L00003</原书页面>謁余道故授觀此本觀察以為宜廣<字体 type=\"超大字2\">&#x23d11;</字体>傳乃集都人士之好古者醵金校刊余與幕中诸友亦助貲以成其事自辛酉春正迄夏五書成觀察乞叙于余余惟自古建都之地故實繁多<字体 type=\"超大字2\">&#x2315c;</字体>有名人撰述成志其後或為新志竄亂刪落或佚其原本今闗中惟宋敏求長安志及程大昌雍録僅存中<原书页面>ZSK12445-000020-L00004</原书页面>州有宋敏求河南志竟不可得吾浙嘉泰㑹稽志至元嘉禾志雖存亦無刊本金<字体 type=\"超大字2\">&#x28e67;</字体>為吴晉宋齊梁陳南唐建都之地賴有此志及至正金<字体 type=\"超大字2\">&#x28e67;</字体>新志以徴文獻考宋時有景㝎志乾道慶元二志各二百數十版並存府學傳曰舊章不可忘也又曰足則吾䏻徴之矣夫山川城<字体 type=\"超大字2\">&#x28dc2;</字体>河渠関隘金石名<原书页面>ZSK12445-000021-L00005</原书页面>迹<字体 type=\"超大字2\">&#x20a44;</字体>存逾古逾不可廢必得博聞強識之士訂正之若新志<字体 type=\"超大字2\">&#x20a44;</字体>增職官科舉財賦額程之属胥史之有文者皆䏻為之且馬制帥官此邦時具有政績經世之學時措之宜<字体 type=\"超大字2\">&#x2315c;</字体>見斯志故于中江載唐景福時作五堰江<字体 type=\"超大字2\">&#x23d11;</字体>漸狹至東垻成而中江不復自陽羨入海可證禹貢三江古說之不謬于丹<原书页面>ZSK12445-000022-L00006</原书页面>楊絳巖兩湖載唐已来斗門蓄洩之制知湖隄築而上元句容水利之可用于破岡埭載吴陳勲<字体 type=\"超大字2\">&#x231e3;</字体>十四埭通吴㑹船艦之制知廢閘修而句容雲陽水運之可通其提領茶鹽自載申請六事則苛取牙儈之積弊皆可禁抑其設平止平糴诸倉積榖至十五萬石則以陳易新之利益甚大其沿<原书页面>ZSK12445-000023-L00007</原书页面>江置寨募兵至三千三百人則弭盗應變之有偹無患盖馬制帥固政事才其與周君著書考古又其餘事余莅茲三載居心行政日夜思<字体 type=\"超大字2\">&#x20a44;</字体>以不負</正文></段落>";
//		content = "<正文 xmlns=\"http://shangyuan/shuju_yuliao\">國朝朱氏<字体 type=\"超大字2\">𢑴</字体>尊䟦此書云訪之三十年始従<原书页面>ZSK12445-000018-L00002</原书页面>曹通政子清借録之故世間傳本絶少迨<字体 type=\"超大字2\">𨳩</字体></正文>";
//		Document document = DocumentHelper.parseText(content);
//		String defaultNamespace = document.getRootElement().getNamespaceURI();
//		
//		Map nsMap = null;
//		nsMap = new HashMap();
//		nsMap.put("xmlns", defaultNamespace);
//		XPath x = document.createXPath("//xmlns:字体");
//		x.setNamespaceURIs(nsMap);
//		
//		List<Node> selectNodes = x.selectNodes(document);
//		for (Node node : selectNodes) {
//			System.out.println(node.asXML());
//			Element font = DocumentHelper.createElement("font");
//			font.addText(node.getText());
//			List elepar = node.getParent().content();
//			// 用content标签替换文本节点
//			elepar.set(elepar.indexOf(node), font);
//		}
//		System.out.println(document.asXML());
		/*
		 *  F是责任方式
		 *	@是朝代
		 *	#是作者名称
		 *	C代表同一个责任方式出现了两次或两次以上
		 */
		//@清#吕燕昭F修@清#姚鼐F纂 = [清]吕燕昭修、[清]姚鼐纂

		System.out.println(StringUtil.length("……"));
//		StringBuffer buff = new StringBuffer();
//		String authorStr = "@清#吕燕昭F修@清#姚鼐F纂";
//		String[] authors = StringUtil.split(authorStr, "@");
//		for(String author : authors) {
//			System.out.println("====>"+author);
//			author = StringUtil.replace(author, "F", " ");
//			if(StringUtil.indexOf(author, "#") != -1) {
//				String[] authornames = StringUtil.split(author, "#");
//				for(int i = 0; i < authornames.length ; i++) {
//					if( i == 0) {
//						buff.append("[" + authornames[i] +"]");
//					}else {
//						buff.append(authornames[i]+",");
//					}
//				}
//			}else {
//				buff.append(author);
//			}
//			
//		}
//		buff = buff.delete(buff.length()-1, buff.length());
//		System.out.println("==>"+buff.toString());			
		
		
//		String str = "我们开始测试";
//		String u4 = UnicodeUtils.string2Unicode(str);								
//		String[] u4arry = u4.split("\\\\u");
//		StringBuilder usb = new StringBuilder();
//		for(int i =0; i< u4arry.length;i++){
//			
//			String code = u4arry[i];
//			if(Strings.isNullOrEmpty(code)) {
//				continue;
//			}
//			String unicode = "\\u"+ code;
//			String word = UnicodeUtils.unicode2String(unicode);
//			System.out.println(word);
//		}
		
//		String enccontent = "<img src='http://114.255.120.44/jinling/wapi/common/download?filename=/2020/12/14/912053ca-e9b6-43aa-aa81-daf45fc4850b.jpg'  class=\"chatu\"></p>dfdfdfdf<img src='http://114.255.120.44/jinling/wapi/common/download?filename=/2020/12/14/912053ca-e9b6-43aa-aa81-daf45fc4850b.jpg'  class=\"chatu\">";
//		enccontent="<div class=\"biaoti1\">江浦縣志卷之一</div><p><span class=\"luokuan\">江浦縣行取知縣李維樾重修</span></p><p>紀主記事事之大者曰㳂革曰攻戰曰禨祥均足以資理而鏡來也江浦置雖二百餘年顧自有土以來載籍斌斌可據爰<span class=\"big02\">&#x25874;</span>往昔本其事而記之法竊史綱政詳　聖代永爲一邑綱維作縣紀</p><p>唐帝堯八十載地屬揚州<span class=\"zhu\">揚州之域北至淮東南至海沿于江海達于淮泗吾浦地介江淮南北屬揚域無疑但國邑之名莫考耳</span></p><p>周靈王十三年爲棠邑地屬楚<span class=\"zhu\">棠邑之置已久至是始見春秋間即今之六合也浦析六合而置故特紀之下叙和滁<img src=\"http://njxadmin.njcbs.cn/jinling/wapi/common/download?filename=/dfb52b0f92e5432aba623bd779ddf51e/zt/Z-ZSK75499-000038-L00022-4-7.png\" class=\"zitu\"></img>此</span></p>";
//		//enccontent = StringUtil.replaceAll(enccontent, "<img src='.*'  class=\"chatu\">","");
//    	enccontent = StringUtil.replaceAll(enccontent, "<img src='[a-zA-Z0-9\\:\\/=\\?\\-\\.]*'  class=\"chatu\">","");
//    	
////    	String regxpForImgTag = "<img\\s+[^>]+/>";
////    	Pattern pattern = Pattern.compile(regxpForImgTag);
////    	Matcher matcher = pattern.matcher(enccontent);
////    	while (matcher.find()) {
////    	    String temp = matcher.group();
////    	    System.out.println(temp);
////    	}
//    	System.out.println(enccontent);
		
		
//		String test = "時至狂<strong style='color:red' id='top'>不</strong>&#<strong style='color:red' id='top'>x2d8e3</strong>;大浪佀屋山欲浮王侯旦智禹所啾萬鬼啄石它山";
//		List<String> bigwordUicode = Lists.newArrayList();
//		bigwordUicode.add("不");
//		bigwordUicode.add("&#x2d8e3;");
////		String result = HighlightUtils.replaceSpan(test, bigwordUicode);
////		System.out.println(result);
////		String result2 = HighlightUtils.addBigwordSpan(test);
////		System.out.println(result2);
////		String _result = HighlightUtils.formatCorrectHight(result, "不&#x2d8e3;");
//
//		System.out.println(StringUtil.generateVerifyCode(6));
//
//		Long userId = null;
//
//		//long _uid = userId;
//
//		Long aaa = 10L;
//		boolean result = aaa != userId;
//		System.out.println(result);
		String sss = "HSB000008";
		boolean result = StringUtil.equalsAny(sss, "HSB0008","HSB00008");
		System.out.println(result);
	}
}
