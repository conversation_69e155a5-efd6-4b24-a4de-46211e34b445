import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Huanghe-Token';

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function setUserCookies(key,value) {
  return Cookies.set(key, value)
}
export function removeCookies(key) {
  return Cookies.remove(key)
}