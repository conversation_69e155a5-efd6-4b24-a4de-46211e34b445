<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="题名说明" prop="bookDesc">
        <el-input
          v-model="queryParams.bookDesc"
          placeholder="请输入题名说明"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="作者" prop="author">
        <el-input
          v-model="queryParams.author"
          placeholder="请输入作者"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="出版年代" prop="publishedYear">
        <el-input
          v-model="queryParams.publishedYear"
          placeholder="请输入出版年代"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="版本" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入版本"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>  -->
      <el-form-item label="地点" prop="mapId">
        <el-select
          style="height: 100%"
          v-model="queryParams.mapId"
          placeholder="所有"
          prop="dbName"
          clearable
          @change="changeCatalog"
        >
          <el-option
            v-for="i in mapList"
            :key="i.id"
            :label="i.placeName"
            :value="i.id"
            >{{ i.placeName }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="bookName">
        <el-input
          v-model="queryParams.bookName"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="书内容图片中间路径" prop="bookImgPath">
        <el-input
          v-model="queryParams.bookImgPath"
          placeholder="请输入书内容图片中间路径"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="0:未上架 1：已上架" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择0:未上架 1：已上架" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button> -->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniPrograms:books:add']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniPrograms:books:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniPrograms:books:remove']"
          >删除</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniPrograms:books:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :height="tableHeight"
      style="width: 100%; margin-top: 30px"
      :data="booksList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="60px" />
      <!-- <el-table-column label="主键id" align="center" prop="id" /> -->
      <el-table-column
        label="地点名称"
        align="center"
        prop="placeName"
        width="100px"
        :show-overflow-tooltip="true"
        sortable
      />
      <el-table-column
        label="书名"
        align="center"
        prop="bookName"
        :show-overflow-tooltip="true"
        sortable
      />
      <!-- <el-table-column label="题名说明" align="center" prop="bookDesc" /> -->
      <el-table-column
        label="作者"
        align="center"
        prop="author"
        sortable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="出版年代"
        align="center"
        prop="publishedYear"
        sortable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="版本"
        align="center"
        prop="version"
        sortable
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column label="封面路径" align="center" prop="coverUrl" /> -->
      <!-- <el-table-column label="简介" align="center" prop="intro" /> -->
      <!-- <el-table-column label="书内容图片中间路径" align="center" prop="bookImgPath" /> -->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope" align="center">
          <el-switch
            width="150"
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#E0E0E0"
            inactive-text="未上架"
            inactive-value="0"
            class="tablescope"
            active-text="已上架"
            active-value="1"
            @change="handleUpdateStatus(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
       <el-table-column
        label="排序"
        align="center"
        prop="display"
        sortable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:books:edit']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:books:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改地方志书库对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="650px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="地点名称" prop="placeName">
          <el-select
            v-model="form.placeName"
            placeholder="所有"
            prop="placeName"
          >
            <el-option
              v-for="item in mapList"
              :key="item.id"
              :value="item.placeName"
              :label="item.placeName"
              >{{ item.placeName }}</el-option
            >
          </el-select>
        </el-form-item>
        <el-form-item label="书籍名称" prop="bookName">
          <el-input v-model="form.bookName" placeholder="请输入地方志名称" />
        </el-form-item>
        <!-- <el-form-item label="题名说明" prop="bookDesc">
          <el-input v-model="form.bookDesc" placeholder="请输入题名说明" />
        </el-form-item> -->

        <el-form-item label="出版年代" prop="publishedYear">
          <el-input v-model="form.publishedYear" placeholder="请输入出版年代" />
        </el-form-item>

        <el-form-item label="封面" prop="coverUrl">
          <el-upload
            ref="upload"
            action="#"
            list-type="picture"
            :http-request="requestUpload"
            :before-upload="beforeUpload"
            :on-remove="handleRemoveCoverUrl"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="coverUrlList"
          >
            <el-button icon="el-icon-upload" size="small" type="primary"
              >选择文件</el-button
            >
          </el-upload>
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="书内容图片" prop="bookImgPath">
          <el-upload
            ref="upload"
            action="#"
            list-type="picture"
            :http-request="requestImgUpload"
            :before-upload="beforeUpload"
            :on-remove="handleRemoveBookImgPathCoverUrl"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="bookImgPathList"
          >
            <el-button icon="el-icon-upload" size="small" type="primary"
              >选择文件</el-button
            >
          </el-upload>
        </el-form-item>
        <el-form-item label="简介" prop="intro">
          <el-input
            v-model="form.intro"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
          <el-form-item label="排序" prop="display" >
            <el-input-number
              v-model="form.display"
              controls-position="right"
              :min="1"
            />
          </el-form-item>
        <!--
        </el-form-item> -->
        <!-- <el-form-item label="0:未上架 1：已上架">
          <el-radio-group v-model="form.status">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBooks,
  getBooks,
  delBooks,
  addBooks,
  updateBooks,
  exportBooks,
} from "@/api/miniPrograms/books";
import { uploadAvatar } from "@/api/product/book/books";
import { listMap } from "@/api/miniPrograms/map";
import Item from "../../../layout/components/Sidebar/Item.vue";
export default {
  components: { Item },
  name: "Books",
  data() {
    return {
      tableHeight: window.innerHeight - 330,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //封面
      coverUrlList: [],
      //书中内容图片
      bookImgPathList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      file: "",
      // 地方志书库表格数据
      booksList: [],
      mapList: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bookName: null,
        bookDesc: null,
        author: null,
        publishedYear: null,
        version: null,
        coverUrl: null,
        intro: null,
        mapId: null,
        bookImgPath: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getMapList();
  },
  methods: {
    /** 查询地方志书库列表 */
    getList() {
      this.loading = true;
      listBooks(this.queryParams).then((response) => {
        this.booksList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
           this.coverUrlList = [];
                this.bookImgPathList = [];
      this.form = {
        id: null,
        bookName: null,
        bookDesc: null,
        author: null,
        publishedYear: null,
        version: null,
        coverUrl: null,
        intro: null,
        mapId: null,
        display:null,
        bookImgPath: null,
        createTime: null,
        updateTime: null,
        status: "0",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.coverUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },

    requestImgUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.bookImgPath = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },

    handleRemoveCoverUrl(file, coverUrlList) {
      this.form.coverUrl = "";
    },
    handleRemoveBookImgPathCoverUrl(file, coverUrlList) {
      this.form.bookImgPath = "";
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //上传图片限制
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
    },
    getMapList() {
      this.loading = true;
      listMap().then((response) => {
        //debugger;
        this.mapList = response.rows;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加地方志书库";
    },
    /**
     * 改状态
     *
     */
    handleUpdateStatus(row) {
      this.reset();
      const id = row.id;
      getBooks(id).then((response) => {
        this.form = response.data;
        //debugger;
        if (this.form.status == "0") {
          this.form.status = "1";
        } else if (this.form.status == "1") {
          this.form.status = "0";
        }
        updateBooks(this.form).then((response) => {
          if (response.code === 200) {
            this.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          }
        });
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.coverUrlList = [];
      getBooks(id).then((response) => {
        //debugger
        this.form = response.data;
        //debugger;
        if (this.form.coverUrl != null) {
          let obj1 = new Object();
          obj1.url = this.form.coverUrl;
          this.coverUrlList.push(obj1);
        }
        if (this.form.bookImgPath != null) {
          let obj = new Object();
          obj.url = this.form.bookImgPath;
          this.bookImgPathList.push(obj);
        }
        if (!this.form.coverUrl) {
          
          this.coverUrlList = [];
        }
        if (!this.form.bookImgPath) {
          this.bookImgPathList = [];
        }
   
        this.open = true;
        this.title = "修改地方志书库";
      });
    },
    //上传封面
    handleCoverUrl() {
      this.upload.title = "选择文件";
      this.upload.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        //debugger;

        if (valid) {
          if (this.form.id != null) {
            console.log(this.form.placeName);
            this.mapList.forEach((e) => {
              if (e.placeName == this.form.placeName) {
                this.form.mapId = e.id;
              }
            });
            updateBooks(this.form).then((response) => {
              console.log(this.form);
              //debugger;
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                     this.coverUrlList = [];
                this.bookImgPathList = [];
                this.open = false;
                this.getList();
              }
            });
          } else {
            addBooks(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.coverUrlList = [];
                this.bookImgPathList = [];
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("确认删除吗？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delBooks(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("确认删除吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportBooks(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () {});
    },
  },
};
</script>
<style lang="scss">
.tablescope {
  .el-switch__label--left {
    position: relative;
    left: 80px;
    color: #fff;
    z-index: -1111;
  }
  .el-switch__core {
    width: 80px !important;
  }
  .el-switch__label--right {
    position: relative;
    right: 80px;
    color: #fff;
    z-index: -1111;
  }
  .el-switch__label--right.is-active {
    z-index: 1111;
    color: #fff !important;
  }
  .el-switch__label--left.is-active {
    z-index: 1111;
    color: #9c9c9c !important;
  }
}
</style>
