<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="用户openid" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入用户openid"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="用户unionid" prop="unionid">
        <el-input
          v-model="queryParams.unionid"
          placeholder="请输入用户unionid"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="意见内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="联系方式" prop="contact">
        <el-input
          v-model="queryParams.contact"
          placeholder="请输入联系方式"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nick">
        <el-input
          v-model="queryParams.nick"
          placeholder="请输入用户昵称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="0:未处理 1：已处理" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择0:未处理 1：已处理" clearable size="small">
          <el-option label="请选择字典生成" value=""/>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="queryParams.remarks"
          placeholder="请输入备注"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <!-- <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button> -->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:opinion:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:opinion:edit']"
        >修改
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniPrograms:opinion:remove']"
        >删除
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:opinion:export']"
        >导出
        </el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="opinionList" @selection-change="handleSelectionChange"
              @cell-click="updateRemarks" :height="tableHeight"
      style="width: 100%; margin-top: 30px" >

      <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="序号" align="center" type="index" width="60px" />
      <!-- <el-table-column label="主键id" align="center" prop="id"/> -->
      <el-table-column label="用户openid" align="center" prop="openid" sortable/>
      <!-- <el-table-column label="用户unionid" align="center" prop="unionid"/> -->
      <el-table-column label="用户昵称" align="center" prop="nick" sortable/>
      <el-table-column label="反馈内容" align="center" prop="content" sortable/>
      <el-table-column label="反馈时间" align="center" prop="createTime"/>

      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope" align="center">
          <el-switch
            width="150"
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#E0E0E0"
            inactive-text="未处理"
            inactive-value="0"
            class="tablescope"
            active-text="已处理"
            active-value="1"
            @change="handleUpdateStatus(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:opinion:edit']"
          >修改
          </el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniPrograms:opinion:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="title" :visible.sync="remarksDialogs" width="400px" append-to-body>
      <el-form ref="remarksForm" :model="remarksForm">
        <el-form-item prop="remarks">
          <el-input class="remarkClass" type="textarea" v-model="remarksForm.remarks"/>
        </el-form-item>

      </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitRemarksForm">确 定</el-button>
          <el-button @click="cancelRemarksForm">取 消</el-button>
        </div>
    </el-dialog>

    <!-- 添加或修改地方志意见反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户openid" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入用户openid"/>
        </el-form-item>
        <el-form-item label="用户unionid" prop="unionid">
          <el-input v-model="form.unionid" placeholder="请输入用户unionid"/>
        </el-form-item>
        <el-form-item label="反馈内容" prop="content">
          <el-input v-model="form.content" placeholder="请输入反馈内容"/>
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系方式"/>
        </el-form-item>
        <el-form-item label="用户昵称" prop="nick">
          <el-input v-model="form.nick" placeholder="请输入用户昵称"/>
        </el-form-item>
        <el-form-item label="0:未处理 1：已处理">
          <el-radio-group v-model="form.status">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addOpinion,
  delOpinion,
  exportOpinion,
  getOpinion,
  listOpinion,
  updateOpinion
} from "@/api/miniPrograms/opinion";

export default {
  name: "Opinion",
  data() {
    return {
      remarksDialogs: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 地方志意见反馈表格数据
      opinionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
          tableHeight: window.innerHeight - 330,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        openid: null,
        unionid: null,
        content: null,
        contact: null,
        nick: null,
        status: null,
        remarks: null
      },
      // 表单参数
      form: {},
      remarksForm: {},
      // 表单校验
      rules: {
        createTime: [
          {required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询地方志意见反馈列表 */
    getList() {
      this.loading = true;
      listOpinion(this.queryParams).then(response => {
        this.opinionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    resetRemarkForm() {
      this.remarksForm = {
        remarks: null
      };
      this.resetForm("remarksForm");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        openid: null,
        unionid: null,
        content: null,
        contact: null,
        createTime: null,
        nick: null,
        status: "0",
        remarks: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加地方志意见反馈";
    },
    //改备注
    updateRemarks(row, column, cell, event) {
      console.log(row)
      if (column.property == "remarks") {
        this.remarksDialogs = true;

        if(row.nick!=null){
             this.title = "为用户" + row.nick + "添加备注"
        }else{
              this.title = "为用户添加备注"
        }
          this.remarksForm = row
      }


    },
      //备注提交按钮
    submitRemarksForm() {
      
      this.reset();

        updateOpinion(this.remarksForm).then((response) => {
          if (response.code === 200) {
            this.msgSuccess("保存成功");
            this.remarksDialogs = false;
            this.getList();
          }
        });
    },
    /**改状态
     */
    handleUpdateStatus(row) {
      this.reset();
      const id = row.id;
      getOpinion(id).then((response) => {
        this.form = response.data;
        
        if (this.form.status == "0") {
          this.form.status = "1";
        } else if (this.form.status == "1") {
          this.form.status = "0";
        }
        updateOpinion(this.form).then((response) => {
          if (response.code === 200) {
            this.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          }
        });
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOpinion(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改地方志意见反馈";
      });
    },

    cancelRemarksForm() {
      this.remarksDialogs = false;
      this.resetRemarkForm();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOpinion(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addOpinion(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('确认删除吗？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delOpinion(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(function () {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('确认删除吗？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return exportOpinion(queryParams);
      }).then(response => {
        this.download(response.msg);
      }).catch(function () {
      });
    }
  }
};
</script>
<style lang="scss">
.tablescope {
  .el-switch__label--left {
    position: relative;
    left: 80px;
    color: #fff;
    z-index: -1111;
  }

  .el-switch__core {
    width: 80px !important;
  }

  .el-switch__label--right {
    position: relative;
    right: 80px;
    color: #fff;
    z-index: -1111;
  }

  .el-switch__label--right.is-active {
    z-index: 1111;
    color: #fff !important;
  }

  .el-switch__label--left.is-active {
    z-index: 1111;
    color: #9c9c9c !important;
  }
}

.remarkClass {
  .el-textarea__inner {
    height: 200px
  }
}
</style>
