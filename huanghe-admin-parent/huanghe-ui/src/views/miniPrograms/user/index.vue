<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="openid" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!--      <el-form-item label="unionid" prop="unionid">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.unionid"-->
      <!--          placeholder="请输入unionid"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="头像url" prop="headUrl">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.headUrl"-->
      <!--          placeholder="请输入头像url"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账号" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="密码" prop="password">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.password"-->
      <!--          placeholder="请输入密码"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!-- <el-form-item label="登录时间" prop="loginTime">
        <el-date-picker clearable size="small" style="width: 200px"
                        v-model="queryParams.loginTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="邮件地址" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="加密盐" prop="salt">-->
      <!--        <el-input-->
      <!--          v-model="queryParams.salt"-->
      <!--          placeholder="请输入加密盐"-->
      <!--          clearable-->
      <!--          size="small"-->
      <!--          @keyup.enter.native="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="用户状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable size="small">
          <el-option label="禁用" value="0"></el-option>
          <el-option label="正常" value="1"></el-option>
          <el-option label="锁定" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>

      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:user:add']"
        >新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:user:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:user:remove']"
        >删除
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:user:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table :height="tableHeight"
      style="width: 100%; margin-top: 30px" v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
         <el-table-column label="序号" align="center" type="index" width="60px" />
      <!-- <el-table-column label="主键id" align="center" prop="id" /> -->
      <!-- <el-table-column label="openid" align="center" prop="openid" /> -->
      <!-- <el-table-column label="unionid" align="center" prop="unionid" /> -->
      <el-table-column label="昵称" align="center" prop="nickname" sortable/>
      <!-- <el-table-column label="头像url" align="center" prop="headUrl" /> -->
      <el-table-column label="手机号" align="center" prop="phone" sortable/>
      <el-table-column label="账号" align="center" prop="username"/>
      <!-- <el-table-column label="密码" align="center" prop="password"/> -->
      <el-table-column label="登录时间" align="center" prop="loginTime" width="180" sortable>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.loginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮件地址" align="center" prop="email" sortable/>
      <!-- <el-table-column label="加密盐" align="center" prop="salt" /> -->
      <el-table-column label="用户状态" align="center" prop="status" sortable>
        <template slot-scope="scope">
          <el-tag
            :type="
              scope.row.status == '1'
                ? 'success'
                : scope.row.status == '0'
                ? 'warning'
                :scope.row.status == '2'
                ? 'danger'
                :'info'
            "
          >
            {{
              scope.row.status == "1"
                ? "正常"
                : scope.row.status == "0"
                ? "禁用"
                : scope.row.status == '2'
                  ? '锁定'
                  : "无"
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniPrograms:user:query']"
          >查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form"  label-width="80px" disabled="true">
        <el-form-item label="openid" prop="openid">
          <el-input v-model="form.openid" />
        </el-form-item>
        <el-form-item label="unionid" prop="unionid">
          <el-input v-model="form.unionid"  >
          </el-input>
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" />
        </el-form-item>
        <!-- <el-form-item label="头像url" prop="headUrl">
          <el-input v-model="form.headUrl"/>
        </el-form-item> -->
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input v-model="form.username" />
        </el-form-item>
        <!-- <el-form-item label="密码" prop="password">
          <el-input v-model="form.password"/>
        </el-form-item> -->
        <el-form-item label="登录时间" prop="loginTime">
          <template slot-scope="scope">
              <span>{{
                  parseTime(scope.row.loginTime, "{y}-{m}-{d} {h}:{i}:{s}")
                }}</span>
          </template>

        </el-form-item>
        <el-form-item label="邮件地址" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        <!-- <el-form-item label="加密盐" prop="salt">
          <el-input v-model="form.salt" placeholder="请输入加密盐" />
        </el-form-item> -->
        <el-form-item label="用户状态" prop="status" v-if="form.status==0">
          <el-input value="禁用">
          </el-input>
        </el-form-item>
        <el-form-item label="用户状态" prop="status" v-if="form.status==1">
          <el-input value="正常">
          </el-input>
        </el-form-item>
        <el-form-item label="用户状态" prop="status" v-if="form.status==2">
          <el-input value="锁定">
          </el-input>
        </el-form-item>
        <el-form-item label="用户状态" prop="status" v-if="form.status==null">
          <el-input value="无">
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="用户状态" prop="status" v-if="form.status==1">

        <el-input t v-model="form.status" >

          </el-input>

      </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="submitForm">确 定</el-button> -->
         <el-button type="primary" @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addUser, delUser, exportUser, getUser, listUser, updateUser} from "@/api/miniPrograms/user";

export default {
  name: "User",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
          tableHeight: window.innerHeight - 330,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        openid: null,
        unionid: null,
        nickname: null,
        headUrl: null,
        phone: null,
        username: null,
        password: null,
        loginTime: null,
        email: null,
        salt: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [
          {required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        openid: null,
        unionid: null,
        nickname: null,
        headUrl: null,
        phone: null,
        username: null,
        password: null,
        loginTime: null,
        email: null,
        createTime: null,
        salt: null,
        status: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getUser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看用户";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUser(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addUser(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('确认删除吗？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delUser(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(function () {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('确认删除吗？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return exportUser(queryParams);
      }).then(response => {
        this.download(response.msg);
      }).catch(function () {
      });
    }
  }
};
</script>
