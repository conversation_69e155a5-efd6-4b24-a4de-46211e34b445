<template>
    <div :class="className" :style="{ height: height, width: width, top: top }" />
</template>


<script>
import resize from './mixins/resize'
import echarts from 'echarts'
import { getResourceProportion } from "@/api/homePage/homePage";


export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        top: {
            type: String,
            default: '130px'
        },


        // dbId: {
        //     type: String,
        //     default: '',
        // }

    },
    data() {
        return {
            chart: null,
            servicedata: [],
            total: 0,
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        });
        // 这将使图例在水平方向上居中

        this.$on('parent-event', this.test); // 监听父组件传递过来的事件，并在触发时执行handleParentEvent方法  
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    created() {
        this.setDbId("000000");
    },




    methods: {

        setDbId(id) {
            this.dbId = id
            console.log(this.dbId)

            getResourceProportion(id).then((res) => {
                this.servicedata = [];
                this.total = 0;
                if (res.code == 200) {
                    const map = new Map(Object.entries(res.data));
                    map.forEach((value, key) => {
                        this.servicedata.push({ value: value, name: key });
                        this.total = this.total + value
                    });
                    this.initChart();
                }


            })

        },

        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')

            this.chart.setOption({

                title: {
                    // text: this.total + "千字",
                    text: this.total == 0 ? '暂无数据' : this.total + "千字",

                    // subtext: 'Fake Data',
                    left: 'center',
                    top: '45%',
                    textStyle: {
                        fontSize: 30
                    }
                },

                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        const str = `${params.data.value}千字`
                        return str
                    },

                },

                legend: {
                    orient: 'vertical',
                    right: 'right',
                    bottom: '20%',
                },




                series: [
                    {
                        name: '',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: true,
                            formatter(param) {
                                // correct the percentage
                                return param.name + ' (' + param.percent + '%)';
                            }
                        },

                        emphasis: {

                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: this.servicedata
                    }
                ]
            })
        }
    }
}
</script>