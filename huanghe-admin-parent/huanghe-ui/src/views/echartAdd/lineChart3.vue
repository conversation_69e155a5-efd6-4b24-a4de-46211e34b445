<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>


<script>
import resize from './mixins/resize'
import echarts from 'echarts'
import { getUserLoginSum } from "@/api/homePage/homePage";


export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '400px'
        }
    },
    data() {
        return {
            chart: null,
            dateList: [],
            pcData: [],
            appData: [],
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        });
        // 这将使图例在水平方向上居中

    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },

    created() {
        this.getlist();
              
    },

    methods: {
        getlist() {
            let type = "USER_SOURCE";
            getUserLoginSum(type).then((res => {
                if(res.code==200){
                    let pcMap = res.data.PC;
               let appMap = res.data.APP;
            
                const pcmap = new Map(Object.entries(pcMap));  
                const appmap = new Map(Object.entries(appMap)); 

                // this.dateList = res.data.date

                pcmap.forEach((value, key) => {  
                    this.dateList.push(key)
                    let pnum = 0;
                        value.forEach((v) => {
                            if (v.loginCount != null) {
                                pnum = v.loginCount + pnum
                            }

                        })

                   this.pcData.push(pnum)
                });
                // this.dateList.sort((a, b) => new Date(a) - new Date(b));  
                appmap.forEach((value, key) => { 
                    let appnum = 0;
                        value.forEach((v) => {
                            if (v.loginCount != null) {
                                appnum = v.loginCount + appnum
                            }

                        }) 
                   this.appData.push(appnum)
                });
                console.log("pcmap",pcmap)
                console.log("appmap",appmap)


                this.initChart();
                }
            }))
        },
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')

            this.chart.setOption({
                title: {
                    text: ''
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['PC', '移动',]
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
             
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.dateList
                },
                yAxis: {
                    type: 'value'
                },
                series: [

                    {
                        name: 'PC',
                        type: 'line',
                        // stack: 'Total',
                        data: this.pcData
                    },
                    {
                        name: '移动',
                        type: 'line',
                        // stack: 'Total',
                        data: this.appData
                    }
                ]
            })
        }
    }
}
</script>