<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>


<script>
import resize from './mixins/resize'
import echarts from 'echarts'

import { getUserLoginSum } from "@/api/homePage/homePage";

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '400px'
        }
    },
    data() {
        return {
            chart: null,
            dateList: [],
            oData: [],
            uData: [],

        }
    },
    created() { this.getlist() }
    ,
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        });
        // 这将使图例在水平方向上居中

    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },

    methods: {

        getlist() {
            let type = "LOGIN_FORM";
            getUserLoginSum(type).then((res => {
                if (res.code == 200) {
                    let oMap = res.data.o;
                    let uMap = res.data.u;

                    console.log(12, res)

                    const omap = new Map(Object.entries(oMap));
                    const upmap = new Map(Object.entries(uMap));

                    // this.dateList = res.data.date

                    omap.forEach((value, key) => {
                        this.dateList.push(key)
                        let onum = 0;
                        value.forEach((v) => {
                            if (v.loginCount != null) {
                                onum = v.loginCount + onum
                            }

                        })

                        this.oData.push(onum)
                    });

                    // this.dateList.sort((a, b) => new Date(b) - new Date(a));  



                    console.log("omap", omap)
                    console.log("upmap", upmap)


                    upmap.forEach((value, key) => {

                        let unum = 0;
                        value.forEach((v) => {
                            if (v.loginCount != null) {
                                unum = v.loginCount + unum
                            }

                        })

                        this.uData.push(unum)
                    });




                    this.initChart();
                }
            }))
        },


        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')

            this.chart.setOption({
                title: {
                    text: '用户登录量(近一年)'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['个人', '机构',]
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
            
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.dateList
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '个人',
                        type: 'line',
                        // stack: 'Total',
                        data: this.uData
                    },
                    {
                        name: '机构',
                        type: 'line',
                        // stack: 'Total',
                        data: this.oData
                    },

                ]
            })
        }
    }
}
</script>