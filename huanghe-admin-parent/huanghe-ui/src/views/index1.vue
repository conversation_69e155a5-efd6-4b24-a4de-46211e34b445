<template>
  <div class="dashboard-editor-container">

    <!-- :style="{ height: topHeight + 'px' }" -->
    <div ref="printDiv" class="top">

      <div style="width: -webkit-fill-available;">
        <div style="height: 100%" class="bo">

          <panel-group />

          <div style="
              height: 65%;
              width: 32.5%;
              float: left;
              box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
            ">
            <div class="chart-wrapper">
              <pie-chart />
            </div>
          </div>

          <div style="
              height: 65%;
              width: 65.8%;
              float: left;
              box-shadow: 3px 3px 20px hsl(0, 0%, 85%);
              margin-left: 10px;
            ">
            <div class="color">
              <div class="sel">
                <el-select v-model="value1" @change="changeValue1" size="mini">
                  <el-option v-for="item in options1" :key="item.value1" :label="item.label1" :value="item.value1">
                  </el-option>
                </el-select>
              </div>
              <div class="bb">
                <LineChart ref="aa" />
              </div>
            </div>
          </div>
        </div>

        <div class="search">
          <searchTop />
        </div>
      </div>


    </div>
    <!-- :style="{ height: tableHeight2 + 'px' }"  -->
    <div class="side1">
      <div class="tb_1">

        <pieChart2 />
        <div style="height: 10px;background-color: #f5f5f5;"></div>
        <organCount />
      </div>

      <div class="tb_2">
        <lineChart2 />
        <!-- <div style="height: 15%"></div> -->
        <lineChart3 />
      </div>
    </div>

    <div class="side2">
      <div class="tb_3">
        <!-- style="margin-top: 20px;" -->
        <div>
          <div class="topFont">
            <span>各编资源占比</span>
          </div>
          <!-- style="margin-top: 20px;" -->
          <div class="tag-group">
            <el-tag v-for="(item, index) in items" :key="item.label" type="type" effect="plain" class="tag"
              @click="getInfo(item.id, $event, index)" :style="{ background: clikcIndex2 == index ? '#FFFF00' : '', }"
              ref="tagref">
              {{ item.label }}
            </el-tag>
          </div>
          <!-- <div style="height: 50%;"></div> -->
          <pieChart3 ref="pieChart3" :id="dbId" />
        </div>
      </div>

      <div class="tb_4">

        <div>
          <recourseCount />
        </div>

        <div>
          <div class="topFont">
            <span>数据量统计（GB）</span>
          </div>
          <div class="tb_4_1">
            <el-button class="dataButton" v-for="(item, index) in databaseList " type="text"
              @click="getdataStatistics(item.id, $event, index)"
              :style="{ color: clikcIndex == index ? 'red' : '#303133', }" ref="myButton">● {{ item.name }}</el-button>
          </div>
          <div class="tb_4_2">
            <div class="data">

              <p style="font-size: 15px;">
                <span class="dataSpan" v-text="dataSize"></span>
                <span>GB</span>
              </p>
            </div>


          </div>
        </div>
      </div>
    </div>


    <div class="side3">
      <resourceStatistics />


    </div>

    <!-- <div :style="{ height: tableHeight + 'px' }" class="side">
        
        <div class="tb">
          <organCount />
        </div>

      
        <div class="tb1">
          <recourseCount />
        </div>
      </div>  -->



  </div>
</template>


<script>
import PanelGroup from "./dashboard/PanelGroup";
import LineChart from "./dashboard/LineChart";
import RaddarChart from "./dashboard/RaddarChart";
import PieChart from "./dashboard/PieChart";
import BarChart from "./dashboard/BarChart";
import searchTop from "./dashboard/searchTop";
import organCount from "./dashboard/organCount";
import recourseCount from "./dashboard/recourseCount";
import { printExcel } from "@/utils/common";
import html2canvas from "html2canvas";

import pieChart2 from "./echartAdd/pieChart2";
import pieChart3 from "./echartAdd/pieChart3";
import lineChart2 from "./echartAdd/lineChart2";
import lineChart3 from "./echartAdd/lineChart3";
import resourceStatistics from "./echartAdd/resourceStatistics";


import { listDatabase } from "@/api/product/database/database";
import { getdataStatisticsDeatil, } from "@/api/homePage/homePage";



export default {
  inject: ["reload"],
  name: "Index",
  components: {
    recourseCount,
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart,
    searchTop,
    organCount,

    lineChart2,
    pieChart2,
    pieChart3,
    lineChart3,
    resourceStatistics
  },







  data() {
    return {
      tableHeight: window.innerHeight / 2 - 120,
      tableHeight2: window.innerHeight / 1,
      tableHeight3: window.innerHeight / 1.5 + 20,
      tableHeight4: window.innerHeight / 2 - 170,
      searchHeight: window.innerHeight - 400,
      topHeight: window.innerHeight / 2,
      //tableH: window.innerHeight - 550,
      editorContainerHeight: window.innerHeight + 800,
      tagList: [],
      dbId: null,
      dataSize: null,
      databaseList: [{ name: '全部', id: "000000" }],
      items: [
        { type: '', label: '总体占比', id: "000000" },

      ],
      type: '',
      options1: [
        {
          value1: 0,
          label1: "今天",
        },
        {
          value1: 1,
          label1: "昨天",
        },

        {
          value1: 7,
          label1: "最近7天",
        },
        {
          value1: 30,
          label1: "最近30天",
        },
      ],
      value1: 0,

      imgUrl: "",
      isShow: true,
      open: false,
      defaultColor: "rgba(0, 0, 0, 0.45098039215686275);",
      index: [],
      clikcIndex: 0,
      clikcIndex2: 0


    };
  },
  mounted() {
    this.getdataStatistics("000000");
    this.clikcIndex = 0

  },
  created() {
    //debugger;
    this.reload();
    this.getTagList();
  },
  methods: {

    getTagList() {
      listDatabase().then((res) => {
        this.tagList = res.rows;
        this.tagList.forEach(e => {
          this.items.push({ type: "", label: e.dbName, id: e.dbId })
          this.databaseList.push({ name: e.dbName, id: e.dbId })
        });
      })
    },


    getdataStatistics(id, event, index) {
      this.clikcIndex = index;
      console.log(11, this.clikcIndex)

      getdataStatisticsDeatil(id).then((res) => {
        if (res.code == 200) {
          this.dataSize = res.data.value

          //event.target.style.color = 'red';
        }
      });

    },



    getInfo(id, event, index) {
      this.clikcIndex2 = index;
      this.$refs.pieChart3.setDbId(id);
      setTimeout(() => {
        this.$refs.pieChart3.initChart();
      }, 2000);


    },
    changeValue1(value1) {
      this.$refs.aa.changeValue1(value1);

    },
    printExcel() {
      //printExcel("printDiv");
      this.clickGeneratePicture();
    },

    clickGeneratePicture() {
      //生成图片
      html2canvas(this.$refs.printDiv, {
        //width: "1000",
        scale: 1,
        // x: 0,
        // y: 0,
        useCORS: true,
        //backgroundColor: null,
        height: this.topHeight + "",
      }).then((canvas) => {
        // 转成图片，生成图片地址
        this.imgUrl = canvas.toDataURL("image/jpeg"); //可将 canvas 转为 base64 格式
        alert(this.imgUrl);
        //this.imgUrl = dataURL;
        if (this.imgUrl !== "") {
          this.open = true;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.topFont {
  color: hsl(207, 82%, 47%);
  font-size: 15px;
  font-weight: bold;
  // float: left;
  margin: 10px 0px 0px 5px;
}

.dashboard-editor-container {
  display: flex;
  flex-direction: column;

  .chart-wrapper {
    background: #fff;
    padding: 16px 5px 0px 5px;
    height: 100%;
  }

  ::v-deep .el-form-item {
    margin-bottom: 3px;
  }

  .color {
    background: #fff;
    width: 100%;
    height: 98%;
    margin-left: 0px;
  }

  .sel {
    width: 20%;
    height: 10%;
    /* margin-left: 2%;
    margin-bottom: 2%;
    margin-top: 2%; */
    margin: 5px;
    float: left;
  }

  .bb {
    width: 100%;
    height: 90%;
    margin-top: -19px;
    float: right;
  }

  .top {
    width: 100%;
    height: 460px;
    display: flex;
    flex-direction: row;
    align-items: stretch;
  }

  .side1 {
    width: 100%;
    height: 810px;
    margin-top: 6px;
    display: flex;
    flex-direction: row;
    align-items: stretch;
  }

  .side2 {
    width: 100%;
    height: 715px;
    /* float: left; */
    margin-top: 0px;
    display: flex;
    flex-direction: row;
    align-items: stretch;
  }

  .side3 {
    width: 100%;
    /* float: left; */
    margin: 10px 0px;
    display: flex;
    flex-direction: row;
    align-items: stretch;

  }

  .tag-group {
    margin-top: 10px;

    .tag {
      cursor: pointer;
      height: 28px;
      line-height: 26px;
      margin: 5px 6px;
    }
  }

}

@media (max-width: 800px) {
  .chart-wrapper {
    padding: 8px;
  }
}







.bo {
  width: 78%;
  float: left;
  margin-top: 0px;
}

/* .aaa {
  color: #000;
  background-color: #bde7e9;
  float: right;
  width: 380px;
  height: 400px;
  margin-top: 0%;
  margin-left: 0;
} */
.tb {
  width: 48%;
  float: left;
  height: 100%;
  margin-top: 12px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.tb1 {
  width: 50%;
  float: right;
  height: 100%;
  margin-top: 12px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.tb_1 {
  width: 50%;
  float: left;
  height: 100%;
  margin-top: 0px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.tb_2 {
  width: 49%;
  margin-left: 1%;
  float: right;
  height: 100%;
  margin-top: 0px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}


.tb_3 {
  width: 50%;
  float: left;
  //height: 100%;
  margin-top: 12px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.tb_4 {
  width: 49%;
  margin-left: 1%;
  float: right;
  //height: 100%;
  margin-top: 12px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}


.search {
  height: 97%;
  padding: 0px 0px 5px 0px;
  width: 22%;
  float: right;
  margin-top: 10px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.spec-dialog .el-dialog__body {
  padding: 3px 30px;
  overflow-y: auto;
  height: calc(100vh - 140px);
}

.tb_4_1 {
  width: 70%;
  float: left;
  margin-top: 3%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  // margin-left: 2%;
  // box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}


.tb_4_2 {
  width: 30%;
  float: right;
  margin-top: 3%;

  display: flex;
  justify-content: center;
  align-items: center;
  height: 25dvh;
  /* 可根据需要调整 */
  // background-color: rgba(242, 242, 242, 0.7254901960784313);

  // box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
}

.dataButton {
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  text-align: left;
  line-height: 18px;
  width: 18%;
  margin: 5px;
  margin-left: 25px;
}

.dataSpan {

  font-size: 25px;
  font-weight: 400;
  font-style: normal;
  color: rgba(0, 0, 0, 0.8509803921568627);

}

.data {
  
  background-color: rgba(242, 242, 242, 0.7254901960784313);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70%;
  height: 70%;
  margin-bottom: 25%
}

.div_sp {
  color: #fff;
}
</style>
<!-- <style>
.el-header,
.el-footer {
  background-color: #B3C0D1;
  color: #333;
  text-align: center;
  line-height: 60px;
}

.el-aside {
  background-color: #D3DCE6;
  color: #333;
  text-align: center;
  line-height: 200px;
}

.el-main {
  background-color: #E9EEF3;
  color: #333;
  text-align: center;
  line-height: 160px;
}

body>.el-container {
  margin-bottom: 40px;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
  line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
  line-height: 320px;
}
</style>  -->