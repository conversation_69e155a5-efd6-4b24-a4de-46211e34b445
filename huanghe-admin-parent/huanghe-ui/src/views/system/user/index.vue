<template>
  <div class="app-container">
    <div>
      <!-- <el-row :gutter="40"> -->
      <!--部门数据-->
      <!-- <el-col :span="4" :xs="24">
          <div class="head-container">
            <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
              style="margin-bottom: 20px" />
          </div>
          <div class="head-container">
            <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="tree" default-expand-all @node-click="handleNodeClick" />
          </div>
        </el-col> -->
      <!--用户数据-->
      <!-- <el-col :span="20" :xs="24"> -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="真实姓名" prop="userName">
          <el-input v-model="queryParams.userName" placeholder="请输入" clearable size="small" style="width: 200px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input v-model="queryParams.phonenumber" placeholder="请输入" clearable size="small" style="width: 200px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime" label-width="68px">
          <el-date-picker clearable size="small" style="width: 150px" v-model="queryParams.createTime" type="date"
            value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status" label-width="40px">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable size="small" style="width: 200px">
            <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="授权结束时间" prop="authEndTime" label-width="98px">
            <el-date-picker clearable size="small" style="width: 200px" v-model="queryParams.authEndTime" type="date"
              value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          </el-form-item> -->


        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
          </el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:user:add']">新增
          </el-button>
        </el-col>
        <!-- <el-col :span="1.5">
              <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                v-hasPermi="['system:user:edit']">修改
              </el-button>
            </el-col> -->
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:user:remove']">删除
          </el-button>
        </el-col>
        <!-- <el-col :span="1.5">
              <el-button
                type="info"
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['system:user:import']"
                >导入</el-button
              >
            </el-col> -->
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:user:export']">导出
          </el-button>
        </el-col>
        <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>

      <el-table style="margin-top: 30px" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium'
        :header-row-style="{ height: '20px' }" height="calc(100vh - 290px)"
        :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" v-loading="loading" :data="userList"
        @selection-change="handleSelectionChange" class="tableS">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column min-width="30" align="center" label="序号" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{
              (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
            }}</span>
          </template>
        </el-table-column> <!--<el-table-column label="用户编号" align="center" prop="userId" v-if="show"/>-->
        <el-table-column label="真实姓名" align="center" prop="userName" />
        <el-table-column label="角色" align="center" prop="rolesName" />
        <el-table-column label="手机号码" align="center" prop="phonenumber" />
        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160" sortable>
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:user:edit']">修改
            </el-button>
            <el-button v-if="scope.row.userId !== 1" size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)" v-hasPermi="['system:user:remove']">删除
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-key" @click="handleResetPwd(scope.row)"
              v-hasPermi="['system:user:resetPwd']">重置密码
            </el-button>
            <el-button v-if="scope.row.userId !== 1" size="mini" type="text" icon="el-icon-unlock" @click="handleUnlock(scope.row)"
              v-hasPermi="['system:user:unlock']">解锁
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- </el-col> -->
      <!-- </el-row> -->
    </div>
    <div style="margin-bottom: 0; height: 10%">
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <el-dialog :title="pass" :visible.sync="openPass" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="openPassForm" :model="openPassForm" :rules="rulesPass" label-width="80px">
        <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="openPassForm.password" placeholder="请输入" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPass">确 定</el-button>
        <el-button @click="cancelPass">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">

            <el-form-item label="角色" prop="roleIds">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
               {{form.deptId}}
              <treeselect
                v-model="form.deptId"
                :options="deptOptions"
                :disable-branch-nodes="true"
                :show-count="true"
                @input="changeValue"
                placeholder="请选择归属部门"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择用户性别">
                <el-option v-for="dict in sexOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="岗位">
              <el-select
                v-model="form.postIds"
                multiple
                placeholder="请选择岗位"
              >
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->

        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="5" resize='none'></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body
      :close-on-click-modal="false">
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />
          是否更新已经存在的用户数据
          <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板
          </el-link>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addUser,
  changeUserStatus,
  delUser,
  exportUser,
  getUser,
  importTemplate,
  listUser,
  resetUserPwd,
  updateUser,
  unlock,
} from "@/api/system/user";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  components: { Treeselect },
  data() {
    const passwordRe = (rule, value, callback) => {
      //带特殊符号
      const pass = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
      if (!value) {
        callback(new Error("密码不能为空"));
      } else if (!pass.test(value) || value == "0") {
        callback(new Error("密码需至少8位，包含数字、小写和大写字母"));
      } else {
        callback();
      }
    };
    return {
      tableHeight: window.innerHeight - 400,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      openPassForm: {
        password: ''
      },
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      userId: '',
      openPass: false,
      pass: '',
      rulesPass: {
        password: [
          { required: true, validator: passwordRe, trigger: 'blur' }
        ],
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
        createTime: undefined,
      },
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "真实姓名不能为空", trigger: "blur" },
        ],
        nickName: [
          { required: true, message: "真实姓名不能为空", trigger: "change" },
        ],
        deptId: [
          { required: true, message: "归属部门不能为空", trigger: "input" },
        ],
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            // pattern: /(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9]{6,20})$/,
            pattern: /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$/,
            message: "密码需至少8位，包含数字、小写和大写字母",
            trigger: "blur",
          },
        ],
        email: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        phonenumber: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "角色不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then((response) => {
      this.sexOptions = response.data;
    });
    this.getConfigKey("sys.user.initPassword").then((response) => {
      //this.initPassword = response.msg;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {


          //debugger
          this.userList = response.rows;

          console.log(this.userList)

          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.getList();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要' + text + "'" + row.userName + "'" + '用户吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      getUser().then((response) => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = "添加用户";
        this.form.password = this.initPassword;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      const userId = row.userId || this.ids;
      getUser(userId).then((response) => {
        this.form = response.data;
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.form.postIds = response.postIds;
        this.form.roleIds = response.roleIds;
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      //debugger
      this.pass = '重置密码'
      this.openPass = true;
      this.userId = row.userId;
    },
    submitPass() {
      this.$refs["openPassForm"].validate((valid) => {
        if (valid) {
          resetUserPwd(this.userId, this.openPassForm.password).then((response) => {
            console.log(this.userId + this.openPassForm.password)
            if (response.code === 200) {
              this.msgSuccess("修改成功，新密码是：" + this.openPassForm.password);
              this.openPass = false;
            }
          });
        }

      })
    },
    cancelPass() {
      this.openPass = false;
      this.resetP();

    },
    resetP() {
      this.form = {
        password: undefined,
        userId: undefined,

      };
      this.resetForm("openPassForm");
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg);
                this.open = false;
                this.getList();
              }
            });
          } else {
            addUser(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg);
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {

      const userIds = row.userId || this.ids;
      //debugger
      var txt;
      if (this.ids.length > 1) {
        txt = "是否确认删除所选用户？"
      } else {
        txt = "是否确认删除当前用户？"
      }
      this.$confirm(
        txt,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () {
        });
    },
    /** 导出按钮操作 */
    handleExport(row) {
      //debugger
      const userIds = row.userId || this.ids;
      const queryParams = this.queryParams;
      var txt;
      if (this.ids.length >= 1) {
        txt = "是否确认导出所选数据"
      } else {
        txt = "是否确认导出列表所有数据？"
      }
      this.$confirm(txt, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          const data = {
            userIdArr: userIds,
            userName: ''
          }
          return exportUser(data);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () {

        });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    changeValue() {
      this.$refs["form"].validateField("deptId");
    },
    handleUnlock(row) {
      unlock(row.userName).then((response) => {
       this.msgSuccess("解锁成功");
      });
    }
  },
};
</script>
<style>
.pagination-container .el-pagination {
  margin-bottom: 0px;
  bottom: 0px;
  float: right;
  right: 10px;
}
</style>
<style>
.outside {
  width: 100%;
  height: 90%;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.middletb {
  margin-left: 5px;
  width: 99%;
  height: 600px;
  margin-top: 30px;
}

.middle {
  margin-top: 10px;
  width: 99%;
}

.tableS {
  margin-top: 0px;
}
</style>
