<template>
  <div class="app-container">
    <div style="display: flex">
      <div :style="{ width: treeShow ? '50%' : '100%' }">
        <!-- <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item label="分类名称" prop="classicName">
            <el-input
              v-model="queryParams.classicName"
              placeholder="请输入"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="分类编号" prop="classicCode">
            <el-input
              v-model="queryParams.classicCode"
              placeholder="请输入"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="cyan"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form> -->

        <div div_row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" size="mini" @click="handleAdd" v-hasPermi="['product:classic:add']">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="batchDeletes"
              v-hasPermi="['product:classic:remove']">删除</el-button>
          </el-col>

          <!-- <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar> -->
        </el-row>
      </div>
        <!-- 分类对话框  -->
        <div>
          <el-dialog :title="title" :visible.sync="openClassic" append-to-body :close-on-click-modal="false">
            <el-form ref="classiForm" :model="classiForm" label-width="100px">
              <el-form-item label="分类编号" prop="classicCode" label-width="100px">
                <div class="div-border">{{ classiForm.classicCode }}</div>
              </el-form-item>
              <el-form-item label="分类名称" prop="classicName" label-width="100px">
                <div class="div-border">{{ classiForm.classicName }}</div>
              </el-form-item>

              <el-form-item label="分类描述" prop="classicDesc" label-width="100px" style="border: none">
                <div class="div-border">{{ classiForm.classicDesc }}</div>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="canceForm">关闭</el-button>
            </div>
          </el-dialog>
        </div>

        <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="opencl" width="500px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="80px" readonly="read">
            <!-- <el-form-item label="分类编号" prop="classicCode">
              <el-input
                show-word-limit
                maxlength="30"
                v-model="form.classicCode"
                placeholder="请输入分类编号"
                :disabled="aaa"
              />
            </el-form-item> -->

            <el-form-item label="分类名称" prop="classicName">
              <el-input v-model="form.classicName" show-word-limit maxlength="10" placeholder="请输入分类名称" :disabled="aaa" />
            </el-form-item>
            <el-form-item label="排序" prop="display" required="true">
              <el-input-number v-model="form.display" controls-position="right" :max="20000" :min="0"></el-input-number>
            </el-form-item>

            <el-form-item label="状态" prop="classicStatus">
              <el-switch v-model="form.classicStatus" active-value="1" inactive-value="0" active-color="#13ce66"
                inactive-color="#000000">
              </el-switch>
            </el-form-item>

            <el-form-item label="分类描述" prop="classicDesc">
              <el-input show-word-limit maxlength="100" v-model="form.classicDesc" placeholder="请输入分类描述" :disabled="aaa"
                type="textarea" :rows="5" resize='none' />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <div v-show="editAndInsert">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="close">取 消</el-button>
            </div>
            <div v-show="view">
              <el-button @click="close">关闭</el-button>
            </div>
          </div>
        </el-dialog>
        <el-table style="margin-top: 30px" v-loading="loading" :data="treeList" @selection-change="handleSelectionChange"
        :row-style="{ height: '40px' }"
          :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }" height="calc(100vh - 250px)"
          :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column min-width="60" align="center" label="序号" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}</span>
            </template>
          </el-table-column> 
          <el-table-column
              label="分类编号"
              align="center"
              prop="classicCode"
            />
          <el-table-column label="分类名称" align="center" prop="classicName" />
          <el-table-column label="排序" align="center" prop="display" />
          <el-table-column label="创建时间" align="center" prop="createTime" min-width="200px">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.classicStatus" active-value="1" inactive-value="0"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="400">
            <template slot-scope="scope">
              <!-- <el-button
                  size="medium"
                  type="text"
                  icon="el-icon-view"
                  @click="handleQueryById(scope.row)"
                  v-hasPermi="['product:classic:query']"
                  >查看</el-button
                > -->

              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['product:classic:edit']">编辑</el-button>

              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['product:classic:remove']">删除</el-button>

              <el-button size="mini" icon="el-icon-edit" type="text" @click="getTreeselect(scope.row)">分类树结构</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
      </div>
      <el-scrollbar style="width: 95%; float: left; height: 800px" wrapClass="scrollbar-wrap" ref="scrollbarContainer"
        v-show="treeShow">
        <div style="width: 99%; float: right; height: 98%">
          <el-button style="float: right; margin-right: 15px" type="text" icon="el-icon-close" @click="closeTable">
            关闭
          </el-button>
          <div style="margin-top: 40px">
            <el-select v-model="treeNodeID" placeholder="请选择">
              <el-option v-for="item in treeList" :key="item.id" :label="item.classicName" :value="item.id"></el-option>
            </el-select>
          </div>
          <div style="margin-top: 10px">
            <el-button @click="addCategoryDialog()" size="mini" style="margin: 6px" type="warning"
              v-hasPermi="['product:tree:add']">增加 根节点</el-button>
          </div>
          <div style="margin-top: 10px">
            <el-tree :data="treeOptions" node-key="id" accordion ref="tree" @node-click="handleNodeClick"
              :expand-on-click-node="false" default-expand-all>
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>
                  {{ data.label }}
                  <span style="margin-left: 30px"></span>
                  {{ data.treeName }}
                </span>

                <span style="margin-right: 30px">
                  <el-button type="text" size="mini" @click="() => addCategoryDialog(data)" icon="el-icon-edit"
                    v-hasPermi="['product:tree:add']">添加</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="() => delCategoryDialog(data)"
                    v-hasPermi="['product:tree:remove']" class="feblei_del">删除</el-button>
                  <el-button type="text" size="mini" @click="() => upCategoryDialog(data)" icon="el-icon-edit"
                    v-hasPermi="['product:tree:edit']">编辑</el-button>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 添加或修改分类树对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="addform" :model="addform" :rules="rules" label-width="80px">
        <el-form-item label="分类编号" prop="classicCode">
          <el-input v-model="addform.classicCode" placeholder="分类编号" />
        </el-form-item>
        <el-form-item label="分类名称" prop="classicName">
          <el-input v-model="addform.classicName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述" prop="classicDesc">
          <el-input v-if="title.search('查看') != -1" v-model="form.classicDesc" placeholder="请输入分类描述" type="textarea"
            :rows="5" resize='none' />
          <span v-else />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div v-show="editAndInsert">
          <el-button type="primary" @click="submitCategory">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
        <div v-show="view">
          <el-button @click="cancel">关闭</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 编辑 -->

    <el-dialog :close-on-click-modal="false" :title="treeTitle" :visible.sync="addtreedia" width="500px">
      <el-form ref="treeForm" :model="treeForm" label-width="90px" :rules="rules">
        <el-form-item label="分类树编号" prop="treeCode" label-width="100px">
          <el-input v-model="treeForm.treeCode" placeholder="请输入分类树编号"></el-input>
        </el-form-item>

        <el-form-item label="上级编号" prop="treePid" label-width="100px">
          <el-input v-model="treeForm.treePid1" :disabled="true" />
        </el-form-item>
        <el-form-item label="分类树名称" prop="treeName" label-width="100px">
          <el-input v-model="treeForm.treeName" placeholder="请输入分类树名称"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="display" label-width="100px">
          <el-input-number v-model="treeForm.display" controls-position="right" :min="1" :max="20000"
            style="width: 370px" />
        </el-form-item>
        <el-form-item label="描述" prop="treeDesc" label-width="100px">
          <el-input v-model="treeForm.treeDesc" type="textarea" :rows="5" resize='none' :maxlength="200" show-word-limit
            placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="c">取 消</el-button>
        <el-button type="primary" @click="submitCategory()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listClassic,
  getClassic,
  delClassic,
  addClassic,
  updateClassic,
  exportClassic,
  treeCatalog,
  batch,
  changeClassicStatus,
} from "@/api/product/classic/classic";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import {
  listTree,
  getTree,
  delTree,
  addTree,
  updateTree,
  exportTree,
  treePCode,
} from "@/api/product/classic/tree";
export default {
  name: "Tree",
  components: {
    Treeselect,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      readonly: false,
      showSearch: true,
      read: false,
      openClassic: false,
      treeShow: false,
      treeOptions: undefined,
      // 总条数
      total: 0,
      // 分类树表格数据
      treeList: [],
      // 弹出层标题
      title: "",
      treeTitle: "",
      // 是否显示弹出层
      open: false,
      scrollHeight: "0px",
      opencl: false,
      classiForm: {},
      selectData: [],
      editAndInsert: false,
      view: false,
      aaa: false,
      /**树添加弹框 */
      addtreedia: false,
      tableHeight: window.innerHeight - 500,
      editCategoryByParentIdDialogVisible: false, //树节点编辑
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        treeCode: null,
        treeName: null,
        treePid: null,
        treeDesc: null,
        treeDisplay: null,
        createbyId: null,
        createbyName: null,
      },
      // 表单参数
      addform: {},
      form: {

      },
      //树表单参数
      treeForm: {},
      treeNode: null,
      treeNodeID: null,
      // 表单校验
      rules: {
        treeCode: [
          {
            required: true,
            message: "分类树编号不能为空",
            trigger: "blur",
          },
        ],
        classicCode: [
          {
            required: true,
            message: "分类编号不能为空",
            trigger: "blur",
          },
        ],
        classicName: [
          {
            required: true,
            message: "分类名称不能为空",
            trigger: "blur",
          },
        ],
        treeName: [
          {
            required: true,
            message: "分类树名称不能为空",
            trigger: "blur",
          },
        ],
      },

      currentClickObj: {
        id: "",
      },
    };
  },
  created() {
    this.getList();
  },
  watch: {
    treeNodeID(v) {
      this.currentClickObj.id = v;
      this.getTreeselect(this.currentClickObj);
    },
  },
  methods: {
    //
    handleStatusChange(row) {
      let text = row.classicStatus === "0" ? "下架" : "上架";
      this.$confirm(
        "确认要" + text + "'" + row.classicName + "'" + "吗?",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          console.log(row.id);

          return changeClassicStatus(row.id, row.classicStatus);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.classicStatus = row.classicStatus === "0" ? "1" : "0";
        });
    },
    resetCl() {
      this.resetForm("classiForm");
      this.getList();
    },
    canceForm() {
      this.openClassic = false;
      this.resetCl();
    },
    getTreeselect(row) {

      this.treeNode = row;
      this.treeNodeID = row.id;
      const id = row.id || this.ids;
      this.classicCode = row.classicCode;
      treeCatalog({
        treeId: id,
      }).then((response) => {
        this.treeOptions = this.handleTree(
          response.data,
          "",
          "treePid",
          "treeName"
        );

        this.treeShow = true;
        return response;
      });
    },
    // mounted() {
    //   this.scrollHeight = window.innerWidth * 0.8 + "px";
    // },
    closeTable() {
      this.treeShow = false;
    },
    handleTree(
      data,
      rootId = 0,
      parentId = "parentId",
      label = "label",
      id = "id",
      children = "children",
      treeCode = "treeCode"
    ) {
      //对源数据深度克隆
      const cloneData = JSON.parse(JSON.stringify(data));
      //循环所有项
      const treeData = cloneData.filter((father) => {
        let branchArr = cloneData.filter((child) => {
          //返回每一项的子级数组
          return father[id] === child[parentId];
        });
        branchArr.length > 0 ? (father[children] = branchArr) : "";
        father.label = father[label];

        //返回第一层
        return father[parentId] === rootId;
      });
      return treeData != "" ? treeData : data;
    },

    handleNodeClick(data) {
      this.queryParams.classicId = data.classicId;
      this.getList();
    },
    /** 分数数据 */
    getList() {
      this.loading = true;
      listClassic(this.queryParams).then((response) => {
        this.treeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    close() {
      this.opencl = false;
      this.reset();
    },
    cancel() {
      this.open = false;
      this.reset();
    },

    c() {
      this.addtreedia = false;
      this.reset();
    },
    /**
     * 批量删除分类
     */
    batchDeletes() {

      this.selectData.forEach((e) => {
        this.$confirm("确认删除吗", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          batch(this.selectData)
            .then((response) => {
              this.msgSuccess("删除成功");
              this.getList();
            })
            .catch(function () { });
        });
      });
    },
    /**
     *tree添加的弹框
     */

    addCategoryDialog(node) {
      this.addtreedia = true;
      this.treeTitle = "添加节点";
      this.classicId = this.treeNodeID;
      if (node == null) {
        this.treeForm = {
          classicId: this.treeNode.id,
          treePid: "",
          display: 1,
          treeDesc: null,
          treeCode: null,
          treeName: null,
        };
      } else {
        this.treePid = node.id;
        this.treeForm = {
          treePid: node.id,
          treePid1: node.treeCode,
          display: 1,
          treeDesc: null,
          treeCode: null,
          treeName: null,
        };
      }
      this.addtreedia = true;
    },
    upCategoryDialog(node) {

      const treePid = node.treePid;
      this.addtreedia = true;
      this.treeTitle = "修改节点";
      treePCode(treePid).then((response) => {

        const tree1 = response.data.treeCode;
        this.treeForm = {
          id: node.id,
          treePid1: tree1,
          display: node.display,
          treeDesc: node.treeDesc,
          treeCode: node.treeCode,
          treeName: node.treeName,
        };
      });

      this.addtreedia = true;
    },

    delCategoryDialog(node) {
      this.$confirm("是否确认删除该树节点?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delTree(node.id);
        })
        .then(() => {
          this.getTreeselect(this.currentClickObj);
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        classicId: null,
        treeCode: null,
        treeName: null,
        treePid: null,
        treeDesc: null,
        display: null,
        createbyId: null,
        createbyName: null,
        createTime: null,

      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.selectData = selection;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.read = false;
      this.editAndInsert = true;
      this.view = false;
      this.opencl = true;
      this.aaa = false;
      this.title = "添加分类";
    },
    /** 修改按钮操作 */

    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getClassic(id).then((response) => {
        this.form = response.data;
        this.id = response.id;
        this.view = false;

        this.editAndInsert = true;
        this.opencl = true;
        this.aaa = false;
        this.title = "修改分类";
      });
    },

    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateClassic(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
              this.opencl = false;
            });
          } else {
            addClassic(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
              this.opencl = false;
            });
          }
        }
      });
    },
    handleQueryById(row) {
      this.reset();
      const id = row.id;
      getClassic(id).then((response) => {
        this.form = response.data;
        this.view = true;
        this.editAndInsert = false;
        this.opencl = true;
        this.aaa = true;
        this.title = "查看";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;

      this.$confirm("是否确认删除?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delClassic(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },

    /**
     * 添加编辑树节点
     */
    /** 提交按钮 */
    submitCategory() {
      this.$refs["treeForm"].validate((valid) => {
        if (valid) {
          this.treeForm.classicId = this.treeNodeID;

          if (this.treeForm.id != null) {
            updateTree(this.treeForm).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.addtreedia = false;
                this.getTreeselect(this.currentClickObj);
              }
            });
          } else {
            addTree(this.treeForm).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.addtreedia = false;
                this.getTreeselect(this.currentClickObj);
              }
            });
          }
        }
      });
    },
  },
};
</script>

<style >
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}

.dialog-footer {
  margin-top: -60px;
}

.feblei_del .el-icon-delete {
  color: #1890ff !important;
}

.mb8 {
  margin-bottom: -10px !important;
}

.tableS {
  margin-top: 0px;
}

.div_row{
  margin-bottom: -10px !important;
}
</style>

