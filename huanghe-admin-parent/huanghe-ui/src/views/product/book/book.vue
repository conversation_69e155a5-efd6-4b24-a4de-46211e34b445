<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="题名" prop="bookName" label-width="100px">
        <el-input v-model="queryParams.bookName" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="mainResponsibility" label-width="100px" label="作者">
        <el-input v-model="queryParams.mainResponsibility" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-row :gutter="30" class="mb8">
        <el-col :span="6"> -->
      <el-form-item label="资源分类" label-width="100px" prop="resourceClassesId">
        <el-cascader size="small" clearable :options="typeList" v-model="resourceClassesQuery" :show-all-levels="true"
          :props="{ checkStrictly: true }"></el-cascader>
      </el-form-item>
      <el-form-item label="丛编" label-width="100px">
        <!-- <el-select
          style="height: 100%"
          v-model="queryParams.dbId"
          placeholder="请选择"
          prop="dbName"
          clearable
          @change="changeCatalog"
        >
          <el-option
            v-for="i in dbList"
            :key="i.dbId"
            :label="i.dbName"
            :value="i.dbId"
            >{{ i.dbName }}
          </el-option>
        </el-select> -->
        <el-input v-model="queryParams.conglomeration" placeholder="请输入" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label-width="115px" prop="publishDate" label="出版日期">
        <el-input clearable v-model="queryParams.publishDate" placeholder="请输入" @keyup.enter.native="handleQuery"
          size="small"></el-input>
      </el-form-item>

      <el-form-item label="状态" label-width="90px" prop="proStatus">
        <el-select size="small" placeholder="请选择状态" v-model="queryParams.proStatus" clearable style="width: 150px">
          <el-option label="下架" value="0"></el-option>
          <el-option label="上架" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="图文类型" label-width="90px" prop="imageTextType">
        <el-select v-model="queryParams.imageTextType" placeholder="请选择图文类型" style="width:100%">
          <el-option v-for="item in imagetextTypeArray" :key="item.dictValue" :label="item.dictLabel"
            :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
      <!-- </el-col>
        <el-col :span="6"> -->

      <!-- </el-col>

        <el-col :span="6"> -->
      <!-- <el-form-item prop="publisher" label="出版社" label-width="100px">
        <el-input
          v-model="queryParams.publisher"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- </el-col>
        <el-col :span="6"> -->
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
        </el-button>
      </el-form-item>

      <!-- </el-col>
      </el-row> -->
    </el-form>

    <div div_row>
      <el-row :gutter="10" class="mb8">
        <!-- <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['product:books:add']"
              >新增
              </el-button
              >
            </el-col> -->
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-edit" size="mini" :disabled="multiple" @click="updateStatus"
            v-hasPermi="['product:books:status']">上架
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" icon="el-icon-edit" size="mini" :disabled="multiple" @click="updateStatusSoldOut"
            v-hasPermi="['product:books:status']">下架
          </el-button>
        </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          @click="handleImport"
          v-hasPermi="['product:books:import']"
          >导入</el-button
        >
      </el-col> -->
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
            v-hasPermi="['product:books:remove']">删除
          </el-button>
        </el-col>

        <!-- <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
      </el-row>
    </div>

    <!-- <el-scrollbar
        wrapClass="scrollbar-wrap"
        :style="{ width: scrollHeight }"
        ref="scrollbarContainer"
      >  -->
    <el-table style="width: 100%; margin-top: 30px" v-loading="loading" :data="booksList"
      @selection-change="handleSelectionChange" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
      size='medium' :header-row-style="{ height: '20px' }" height="calc(100vh - 340px)"
      :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="55" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :show-overflow-tooltip="true"
        label="题名"
        align="center"
        prop="bookName"
        sortable
        width="250px"
      /> -->
      <!-- <el-table-column label="唯一标识符" align="center" prop="uniqueId" min-width="15%" show-overflow-tooltip></el-table-column> -->
      <el-table-column label="题名" align="center" prop="bookName" min-width="250px" />
      <el-table-column :show-overflow-tooltip="true" label="作者" align="center" prop="mainResponsibility"
        min-width="150px" />
      <el-table-column :show-overflow-tooltip="true" label="资源分类" align="center" prop="resourceClasses"
        min-width="150px" />
      <el-table-column label="图文类型" align="center" prop="imageTextType" :formatter="imagetextTypeFormat" />

      <el-table-column label="出版者" align="center" prop="publisher" :show-overflow-tooltip="true" />

      <el-table-column label="出版日期" align="center" prop="publishDate" :show-overflow-tooltip="true" />
      <el-table-column label="丛编" align="center" prop="conglomeration" />
      <el-table-column label="排序" align="center" prop="display" />
      <el-table-column label="状态" align="center" prop="proStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.proStatus == '1' ? 'success' : 'danger'">
            {{ scope.row.proStatus == "1" ? "上架" : "下架" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="150px">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="150px">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="190px" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            icon="el-icon-edit"
            type="text"
            @click="handleQueryById(scope.row)"
            v-hasPermi="['product:books:query']"
            >查看</el-button
          > -->
          <el-button size="mini" type="text" icon="el-icon-view" @click="selectInfo(scope.row)"
            v-hasPermi="['product:books:query']">查看
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['product:books:edit']">编辑
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['product:books:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--  </el-scrollbar>  -->

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <div>
      <el-dialog :title="title" :visible.sync="openBook" width="1000px" append-to-body>
        <el-form ref="formBook" :model="formBook" label-width="100px">
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="书名" prop="bookName" label-width="100px">
                <div class="div-border">{{ formBook.bookName }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="主要责任者" prop="mainResponsibility" label-width="100px">
                <div class="div-border">{{ formBook.mainResponsibility }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="资源分类" prop="resourceClasses" label-width="100px">
                <div class="div-border">{{ formBook.resourceClasses }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="出版日期" prop="publishDate" label-width="100px">
                <div class="div-border">{{ formBook.publishDate }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="主题词" prop="subjectWord" label-width="100px">
                <div class="div-border">{{ formBook.subjectWord }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="版次" prop="revision" label-width="100px">
                <div class="div-border">{{ formBook.revision }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="丛编" label-width="100px">
                <div class="div-border">{{ formBook.conglomeration }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="出版社" prop="publisher" label-width="100px">
                <div class="div-border">{{ formBook.publishland }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="封面高清图" prop="coverUrl" label-width="100px">
                <div>
                  <el-image :src="file" class="image" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="封面缩略图" prop="thumbCoverUrl" label-width="100px">
                <div>
                  <el-image :src="file" class="image" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40" class="mb8">
            <el-col :span="20">
              <el-form-item label="简介" prop="bookDesc " label-width="100px" style="height: 400px">
                <el-scrollbar wrapClass="scrollbar-wrap" :style="{ width: scrollHeight }" ref="scrollbarContainer">
                  <div class="border">{{ formBook.bookDesc }}</div>
                </el-scrollbar>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="cancelBook">关闭</el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :before-upload="beforeAvatarUpload"
        :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <!-- 导入 -->
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />
          是否更新已经存在的用户数据
          <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板
          </el-link>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :before-upload="beforeAvatarUpload" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />
          是否更新已经存在的资源
          <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板
          </el-link>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看 -->

    <div></div>

    <!-- 添加或修改产品资源管理对话框 -->
    <el-dialog append-to-body customClass="customWidth" :title="title" :visible.sync="open"
      :close-on-click-modal="false">
      <div class="formDiv">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="书名" prop="bookName" label-width="100px">
                <el-input v-model.trim="form.bookName" placeholder="书名" maxlength="30" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主要责任者" prop="mainResponsibility" label-width="100px">
                <el-input v-model.trim="form.mainResponsibility" placeholder="主要责任者" maxlength="200" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="资源分类" prop="resourceClassesId" label-width="100px">
                <el-cascader style="margin-top: -10px; width: 100%;" :options="typeList" ref="resourceRef"
                  :props="{ emitPath: false, checkStrictly: true }" v-model="form.resourceClassesId"
                  :show-all-levels="true" @change="classChange"></el-cascader>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="出版日期" prop="publishDate" label-width="100px">
                <el-input v-model.trim="form.publishDate" placeholder="出版日期" maxlength="200" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="主题词" prop="subjectWord" label-width="100px">
                <el-input v-model.trim="form.subjectWord" placeholder="主题词" maxlength="500" show-word-limit />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="版次" prop="revision" label-width="100px">
                <el-input v-model.trim="form.revision" placeholder="版次" maxlength="100" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="丛编" label-width="100px">
                <!-- <el-select v-model="form.dbId" placeholder="丛编" prop="dbId" style=" width: 100%;">
                <el-option
                  v-for="item in dbList"
                  :key="item.dbId"
                  :value="item.dbId"
                  :label="item.dbName"
                  >{{ item.dbName }}
                </el-option>
              </el-select> -->
                <el-input v-model.trim="form.conglomeration" placeholder="丛编" maxlength="100" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出版社" prop="publisher" label-width="100px">
                <el-input v-model.trim="form.publisher" placeholder="出版社" maxlength="100" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="30" class="mb8"> </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="封面高清图" prop="coverUrl">
                <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUpload"
                  :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                  :limit="1" :on-exceed="handleExceed" :file-list="fileList" class="upload-image">
                  <i class="el-icon-plus"></i>
                  <div class="el-upload__tip" slot="tip">
                    <span style="color: #f56c6c">可以上传1张,建议尺寸不能超过640×320像素。</span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="封面缩略图" prop="thumbCoverUrl">
                <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUploadThumbCoverUrl"
                  :before-upload="beforeUpload" :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemovethumbCoverUrl" :limit="1" :on-exceed="handleExceedthumbCoverUrlList"
                  :file-list="thumbCoverUrlList" class="upload-image">
                  <i class="el-icon-plus"></i>
                  <div class="el-upload__tip" slot="tip">
                    <span style="color: #f56c6c">可以上传1张,建议尺寸不能超过640×320像素。</span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <!-- <el-form-item label="中图分类法" prop="classiMethodCode" label-width="100px">
                <el-cascader style=" width: 100%;" :options="classList" v-model="form.classiMethodCode"
                  :show-all-levels="true" @change="changeClass" :props="{ emitPath: false }" ref="cascaderAddr">
                </el-cascader>
              </el-form-item> -->
              <el-form-item label="版本" label-width="100px">
                <el-input v-model.trim="form.edition" placeholder="版本" maxlength="100" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="display" label-width="100px">
                <el-input v-model.trim="form.display" placeholder="排序" maxlength="50" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="图文类型" prop="imageTextType" label-width="100px">
                <!-- <el-select v-model="form.imageTextType" style=" width: 100%;">
                  <el-option label="图片" value="P"></el-option>
                  <el-option label="文字" value="T"></el-option>
                  <el-option label="版式还原" value="SR"></el-option>
                </el-select> -->

                <el-select v-model="form.imageTextType" placeholder="请选择图文类型" style="width:100%">
                  <el-option v-for="item in imagetextTypeArray" :key="item.dictValue" :label="item.dictLabel"
                    :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="册数" prop="numberCopies" label-width="100px">
                <el-input v-model.trim="form.numberCopies" placeholder="册数" maxlength="10" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="字数" prop="wordNum" label-width="100px">
                <el-input v-model.trim="form.wordNum" placeholder="字数" maxlength="10" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图片数" prop="picNum" label-width="100px">
                <el-input v-model.trim="form.picNum" placeholder="图片数" maxlength="10" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12">
              <el-form-item label="数据库分类" label-width="100px">
                <el-select v-model="form.dbId" placeholder="数据库分类" prop="dbId" style=" width: 100%;">
                  <el-option v-for="item in dbList" :key="item.dbId" :value="item.dbId" :label="item.dbName">{{
                    item.dbName
                  }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="馆藏单位" prop="collection" label-width="100px">
                <el-input v-model.trim="form.collection" placeholder="馆藏单位" maxlength="40" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30" class="mb8">
            <el-col :span="12" v-if="form.imageTextType === 'SR'">
              <el-form-item label="OCR版本" label-width="100px">
                <el-input v-model.trim="form.ocrVersion" placeholder="OCR版本" maxlength="1" show-word-limit />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="开本信息"
                label-width="100px"
              >
                <el-input
                  v-model.trim="form.kaibenInfo"
                  placeholder="开本信息"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="12">
              <el-form-item
                label="印次"
                prop="impression"
                label-width="100px"
              >
                <el-input
                  v-model.trim="form.impression"
                  placeholder="印次"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="24">
              <el-form-item label="简介" prop="bookDesc" class="endItem">
                <!-- <Editor v-model="form.bookDesc" /> -->
                <el-input v-model.trim="form.bookDesc" :autosize="{ minRows: 1, maxRows: 10 }" type="textarea"
                  maxlength="2000" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>

    <!-- //详情展示 -->
    <el-dialog append-to-body customClass="customWidth" :title="title" :visible.sync="bookDetail"
      :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="18">
            <el-row>
              <el-col :span="8">
                <span class="catalog_title">题名:</span><br />
                <div class="catalog_other"></div>
                <span class="catalog_value">{{ form.bookName }}</span>
              </el-col>
              <el-col :span="8">
                <span class="catalog_title">作者:</span><br />
                <div class="catalog_other"></div>
                <span class="catalog_value">{{ form.mainResponsibility }}</span>
              </el-col>
              <el-col :span="8">
                <span class="catalog_title">图文类型:</span><br />
                <div class="catalog_other"></div>
                {{ imagetextTypeFormat(form) }}
                <!-- <span v-if="form.imageTextType == 'SR'" class="catalog_value">版式阅读</span>
                <span v-if="form.imageTextType == 'T'" class="catalog_value">全文阅读</span>
                <span v-if="form.imageTextType == 'P'" class="catalog_value">图片阅读</span> -->
              </el-col>
            </el-row>

            <el-row style="margin-top: 50px;">
              <el-col :span="8">
                <span class="catalog_title">资源分类:</span><br />
                <div class="catalog_other"></div>
                <span class="catalog_value">{{ form.resourceClasses }}</span>
              </el-col>
              <el-col :span="8">
                <span class="catalog_title">丛编:</span><br />
                <div class="catalog_other"></div>
                <span class="catalog_value">{{ form.conglomeration }}</span>
              </el-col>
              <el-col :span="8">
                <span class="catalog_title">版次:</span><br />
                <div class="catalog_other"></div>
                <span class="catalog_value">{{ form.revision }}</span>
              </el-col>
            </el-row>
          </el-col>

          <el-col :span="6">
            <span class="catalog_title">封面图:</span><br />
            <div class="catalog_other"></div>
            <el-image style="width: 85px; height: auto " :src="form.coverUrl">
            </el-image>
          </el-col>
        </el-row>

        <el-row style="margin-top: 50px;">
          <el-col :span="6">
            <span class="catalog_title">出版日期:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.publishDate }}</span>
          </el-col>
          <el-col :span="6">
            <span class="catalog_title">版本:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.edition }}</span>
          </el-col>
          <el-col :span="6">
            <span class="catalog_title">主题词:</span><br />
            <div class="catalog_other"></div>
            <span class="catalog_value">{{ form.subjectWord }}</span>
          </el-col>
          <el-col :span="6">
            <span class="catalog_title">出版者:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.publisher }}</span>
          </el-col>

        </el-row>

        <el-row style="margin-top: 50px;">
          <el-col :span="6">
            <span class="catalog_title">图片数:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.picNum }}</span>
          </el-col>
          <el-col :span="6">
            <span class="catalog_title">字数:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.wordNum }}</span>
          </el-col>
          <el-col :span="6">
            <span class="catalog_title">馆藏单位:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.collection }}</span>
          </el-col>
          <el-col :span="6" v-if="form.imageTextType === 'SR'">
            <span class="catalog_title">OCR版本:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ formatterOcrVersion(form.ocrVersion) }}</span>
          </el-col>
        </el-row>
        <!-- <el-row style="margin-top: 50px;">
          <el-col :span="6">
            <span class="catalog_title">印次:</span><br />
            <div class="catalog_other"></div>

            <span class="catalog_value">{{ form.impression }}</span>
          </el-col>
        </el-row> -->

        <el-row style="margin-top: 50px;">
          <el-col :span="24">
            <!-- <el-form-item label="简介" prop="bookDesc">
                <el-input v-model="form.bookDesc" type="textarea" :rows="5" placeholder="请输入简介" maxlength="2000" show-word-limit/>
              </el-form-item> -->

            <span class="catalog_title">简介:</span><br />
            <div class="catalog_other"></div>
            <p class="catalog_value jianjie" style="    font-size: 15px;
    line-height: 23px;
   ">
              <span>{{ form.bookDesc }}</span>
            </p>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="bookDetail = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

import { classicTYpe, classicType } from "@/api/product/classic/tree";
import {
  addBooks,
  check,
  dbNames,
  getBooks,
  importTemplate,
  listBooks,
  status,
  statusUp,
  updatebook,
  updatebook1,
  updateBooks,
  uploadAvatar,
} from "@/api/product/book/books";
import Editor from "@/components/Editor";

export default {
  name: "Books",
  components: {
    Editor,
  },

  data() {
    const checkBookName = (rule, value, callback) => {
      check().then((response) => {
        let a = false;
        response.forEach((e) => {
          if (e.bookName == value && this.form.id != e.id) {
            a = true;
          }
        });
        if (!value) {
          return callback(new Error("书名不能为空"));
        } else {
          return callback();
        }
        // else if (a) {
        //   return callback(new Error("书名已存在"));
        // }
      });
    };
    return {
      bookDetail: false,

      mainPage: window.innerHeight - 200,
      openBook: false,
      formBook: {},
      // 遮罩层
      loading: true,
      l: [],
      // 选中数组
      ids: [],
      recs: [],
      recOptions: [],
      // 非单个禁用
      file: "",
      single: true,
      // 非多个禁用
      multiple: true,
      scrollHeight: "0px",
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,

      // 产品资源管理表格数据
      booksList: [{ value: "id", label: "treeName" }],
      typeList: [],
      classList: [],
      dbList: { dbName: "dbName", dbId: "dbId" },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      treeNodeID: null,
      // 查询参数
      queryParams: {
        dbId: null,
        pageNum: 1,
        pageSize: 10,
        bookName: null,
        resourceClasses: null,
        resourceType: null,
        classiMethodCode: null,
        classiMethod: null,
        classification: null,
        mainResponsibility: null,
        publishDate: null,
        publisher: null,
        publishland: null,
        publishYear: null,
        proStatus: null,
        delFlag: null,
        dbName: null,
      },

      // 查看的数据
      infoDetail: null,
      // 表单参数
      form: {},
      watch: {
        handlerValue() {
          if (this.$refs.refHandle) {
            this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
          }
        },
      },
      //上传参数

      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/product/books/importData",
      },
      // 表单校验
      rules: {
        // siClassification: [
        //   { required: true, message: "四部分类法不能为空", trigger: "blur" },
        // ],
        bookName: [
          { required: true, validator: checkBookName, trigger: "blur" },
        ],
        resourceClasses: [
          { required: true, message: "资源分类不能为空", trigger: "change" },
        ],
        dbName: [
          { required: true, message: "数据库不能为空", trigger: "blur" },
        ],
        mainResponsibility: [
          { required: true, message: "主要责任者不能为空", trigger: "blur" },
        ],
        // publisher: [
        //   { required: true, message: "出版社不能为空", trigger: "blur" },
        // ],
        // publishDate: [
        //   { required: true, message: "出版日期不能为空", trigger: "blur" },
        // ],
        // revision: [
        //   { required: true, message: "版次不能为空", trigger: "blur" },
        // ],

        display: [
          { required: true, message: "排序不能为空", trigger: "change" },
        ],
      },

      dialogImageUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传图片地址
      fileList: [],
      resourceClassesId: [],
      thumbCoverUrlList: [],
      classiMethodCode: "",
      resourceClasses: [],
      resourceClassesQuery: [],
      siClassification: [],
      siClassificationId: [],
      siClassificationQuery: [],
      selectData: [],
      tableHeight: window.innerHeight - 500,
      imagetextTypeArray: [],
    };
  },
  mounted() {
    this.scrollHeight = window.innerWidth * 1 + "px";
  },
  created() {
    this.getType();
    this.getList();
    this.getName();
    this.getClassic();
    //图文类型
    this.getDicts("res_type_1").then((response) => {
      this.imagetextTypeArray = response.data;
    });
  },

  watch: {
    treeNodeID(v) {
      this.getList({ id: v });
    },
  },

  methods: {
    formatterOcrVersion(e) {
      if (e === "2") {
        return "旧版本";
      } else if (e === "3") {
        return "新版本";
      } else {
        return "未知";
      }
    },
    selectInfo(row) {
      const id = row.id || this.ids;
      getBooks(id).then((response) => {
        this.form = response.data;
        console.log(this.infoDetail);
      });

      this.bookDetail = true;
      this.title = "资源详情";
    },
    imagetextTypeFormat(row, column) {
      return this.selectDictLabel(this.imagetextTypeArray, row.imageTextType);
    },
    resetBooks() {
      this.resetForm("formBook");
      this.getList();
    },

    cancelBook() {
      this.openBook = false;
      this.resetForm("formBook");
      this.getList();
    },
    changeProduct(v) {
      // this.ProductActive = event.target.value; //获取商品ID，即option对应的ID值
    },

    beforeAvatarUpload(file) {
      // //debugger;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      if (testmsg == "xlsx" || testmsg == "xls") {
        return true;
      } else {
        this.msgError("文件上传格式有误，请上传后缀为xlsx，xls的文件");
        return false;
      }
    },

    //图片上穿
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.coverUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },
    requestUploadThumbCoverUrl(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.thumbCoverUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },

    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
    },

    handleExceed(files, fileList) {
      this.msgError("最多上传1张图片");
    },
    handleExceedthumbCoverUrlList(files, thumbCoverUrlList) {
      this.msgError("最多上传1张图片");
    },

    handleRemove(file, fileList) {
      this.form.coverUrl = "";
    },
    handleRemovethumbCoverUrl(file, thumbCoverUrlList) {
      this.form.thumbCoverUrl = "";
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    selectPlatform() {
      this.$forceUpdate(); //强制更新
    },

    /**
     * 资源类型加载
     */

    getType() {
      this.loading = true;
      classicTYpe().then((response) => {
        console.log("a");
        this.typeList = this.getTreeData(response.rows);
      });
    },
    //四部分类法
    getClassic() {
      this.loading = true;
      classicType().then((response) => {
        this.classList = this.getTreeData(response.rows);

        console.log(this.classList);
      });
    },
    getTreeData(data) {
      // 循环遍历json数据
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    getName() {
      this.loading = true;
      dbNames().then((response) => {
        this.dbList = response;
      });
    },
    // changrCatalog(value) {
    //   if (value.length == 0) {
    //     this.queryParams.resourceClasses = null;
    //   } else {
    //     this.queryParams.resourceClasses = value[0];
    //   }
    // },
    changrCat(value) {
      if (value.length == 0) {
        this.queryParams.siClassification = null;
      } else {
        this.queryParams.siClassification = value[0];
      }
    },

    changeCatalog(value) {
      if (value.length == 0) {
        this.queryParams.dbId = null;
      } else {
        this.queryParams.dbId = value;
      }
    },
    getList() {
      this.loading = true;
      listBooks(this.queryParams).then((response) => {
        this.booksList = response.rows;
        console.log(1 + 1);
        console.log(this.booksList);
        this.total = response.total;
        this.loading = false;
      });
    },

    handleImport() {
      this.upload.title = "资源导入";
      this.upload.open = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.resourceClasses = [];
      this.reset();
    },
    // 表单重置
    reset() {
      this.resourceClassesId = "";
      this.classiMethodCode = [];
      this.resourceClasses = null;
      this.siClassificationId = [];
      this.siClassification = null;
      this.fileList = [];
      this.thumbCoverUrlList = [];
      this.siClassificationQuery = [];
      this.form = {
        display: null,
        classiMethodCode: "",
        bookName: null,
        resourceClasses: null,
        siClassificationId: null,
        resourceClassesId: "",
        publishDate: null,
        dbId: null,
        proStatus: null,
        bookDesc: null,
        coverUrl: null,
      };
      if (this.$refs.upload != undefined) {
        this.$refs.upload.clearFiles();
      }

      this.resetForm("form");
    },
    classChange(v) {
      if (v.length > 0) {
        this.form.resourceClassesId = v;
        this.form.resourceClasses = this.$refs["resourceRef"]
          .getCheckedNodes()[0]
          .pathLabels.join("/");
      }
    },

    changeClass(v) {
      if (v.length > 0) {
        //debugger;
        this.form.classiMethod = this.$refs["cascaderAddr"]
          .getCheckedNodes()[0]
          .pathLabels.join("/");
        this.form.classiMethodCode = v;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.resourceClassesId = this.resourceClassesQuery.join("/");
      this.queryParams.siClassification = this.siClassificationQuery.join("/");
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resourceClassesQuery = [];
      this.siClassificationQuery = [];
      // 查询参数
      (this.queryParams = {
        dbId: null,
        pageNum: 1,
        pageSize: 10,
        bookName: null,
        resourceClasses: null,
        resourceType: null,
        classiMethodCode: null,
        classiMethod: null,
        classification: null,
        mainResponsibility: null,
        publishDate: null,
        publisher: null,
        publishland: null,
        publishYear: null,
        proStatus: null,
        delFlag: null,
        dbName: null,
        conglomeration: null,
        dbId: null,
      }),
        this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectData = selection;

      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.resourceClasses = [];
      this.form.classiMethod = this.classiMethod;
      this.open = true;

      this.title = "添加产品资源管理";
    },
    handleQueryById(row) {
      this.reset();
      const id = row.id || this.ids;
      getBooks(id).then((response) => {
        this.formBook = response.data;
        this.openBook = true;
        this.title = "查看";
        let obj = new Object();
        //obj.url = process.env.VUE_APP_BASE_API + this.formBook.coverUrl;
        obj.url = this.formBook.coverUrl;
        this.file = obj.url;
        // obj.url1 = process.env.VUE_APP_BASE_API + this.formBook.thumbCoverUrl;
        // this.file = obj.url1;
        this.openBook = true;
        this.title = "查看";
      });
    },

    //  上架

    updateStatus(row) {
      this.reset();
      let text = "";
      let boo = false;
      const id = row.id || this.ids;
      // getBooks(id).then((response) => {
      let len = this.selectData.filter((f) => f.proStatus == 1).length;
      const up = this.selectData.filter((f) => f.proStatus == 0);
      const id_ = up.map((item) => item.id);
      if (len > 0 && this.selectData.length > 1) {
        text = "您选中的资源中存在已上架的图书，确认是否上架其他未上架的图书？";
        boo = false;
      } else {
        text = "确认上架吗？";
        boo = true;
      }
      this.selectData.forEach((e) => {
        //debugger;
        if (e.proStatus == 0) {
          this.$confirm(text, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(function () {
              if (id_.length > 0) {
                return statusUp(id_);
              } else {
                return statusUp(id);
              }
            })
            .then((response) => {
              //debugger;
              this.selectData = [];
              this.getList();
              this.$messageSingle.successTime(response.msg, 2000);
            });

          //  this.msgSuccess("上架成功");
        } else {
          this.selectData = [];
          if (boo) {
            this.$messageSingle.warningTime("已上架，请勿重复操作", 1000);
          }
          this.getList();
        }

        // this.msgSuccess("已上架，请勿重复操作");
      });
      // });
    },

    updateStatusSoldOut(row) {
      //debugger;
      this.reset();

      let text = "";
      let boo = false;
      const id = row.id || this.ids;
      // getBooks(id).then((response) => {
      let len = this.selectData.filter((f) => f.proStatus == 0).length;
      const up = this.selectData.filter((f) => f.proStatus == 1);
      const id_ = up.map((item) => item.id);
      if (len > 0 && this.selectData.length > 1) {
        text = "您选中的资源中存在已下架的图书，确认是否下架其他未下架的图书？";
        boo = false;
      } else {
        text = "确认下架吗？";
        boo = true;
      }
      // getBooks(id).then((response) => {
      this.selectData.forEach((e) => {
        if (e.proStatus == 1) {
          this.$confirm(text, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(function () {
              if (id_.length > 0) {
                return status(id_);
              } else {
                return status(id);
              }
            })
            .then(() => {
              //debugger;
              this.selectData = [];
              this.getList();
              this.$messageSingle.successTime("下架成功", 1000);
            })
            .catch(function () { });
        } else {
          this.getList();
          if (boo) {
            this.$messageSingle.warningTime("已下架，请勿重复操作", 1000);
          }
        }
      });
      // });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBooks(id).then((response) => {
        this.form = response.data;
        console.log(this.form.classiMethodCode);
        console.log(this.form);
        /*   if (this.form.resourceClasses != null) {
           this.resourceClasses = this.form.resourceClasses.split("/");
         }*/
        /*  if (this.form.resourceClassesId != null) {
          this.resourceClassesId = this.form.resourceClassesId.split("/");
        }*/
        // //debugger
        // if (this.form.classiMethod != null) {
        //   this.classiMethod = this.form.classiMethod.split("/");
        // }
        /*  if (this.form.classiMethodCode != null) {
          this.classiMethodCode = this.form.classiMethodCode.split("/");
        }*/
        if (this.form.coverUrl != null) {
          let obj = new Object();
          obj.url = this.form.coverUrl;
          this.fileList.push(obj);
        }
        if (!this.form.coverUrl) {
          this.fileList = [];
        }
        if (this.form.thumbCoverUrl != null) {
          let obj1 = new Object();
          obj1.url = this.form.thumbCoverUrl;
          this.thumbCoverUrlList.push(obj1);
        }
        if (!this.form.thumbCoverUrl) {
          this.thumbCoverUrlList = [];
        }
        /* if (!this.form.display) {
          this.$set(this.form, 'display', 1);//解决计数器加1后点不动的问题
        }
        */
        this.open = true;
        this.title = "修改产品资源管理";
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        //debugger;
        if (valid) {
          if (this.form.id != null) {
            console.log(this.form);
            updateBooks(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("编辑成功");
                this.open = false;

                this.getList();
              }
            });
          } else if (this.form.id == null) {
            addBooks(this.form).then((response) => {
              console.log(this.form);
              //debugger;
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.getList();
                this.getName();
                this.fileList = [];
                this.form.coverUrl = null;
                this.form.thumbCoverUrl = null;
                this.thumbCoverUrlList = [];
                this.open = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      if (row.proStatus == 1) {
        this.msgError("您选中的资源还未下架，请先下架后再删除");
      } else {
        this.$confirm("确认删除吗", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          updatebook1(row, id)
            .then(() => {
              this.msgSuccess("删除成功");
              this.getList();
            })
            .catch(function () { });
        });
      }
    },

    handleDeletes() {
      try {
        for (let e of this.selectData) {
          //debugger;
          if (e.proStatus == 1) {
            console.log(e.proStatus + ",");
            this.msgError("您选中的资源存在未下架的资源，请先下架!");
            this.selectData = [];

            this.getList();
            return false;
          }
        }
        this.$confirm("确认删除吗", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          updatebook(this.selectData)
            .then((response) => {
              this.msgSuccess("删除成功");
              this.getList();
            })
            .catch(function () { });
        });
      } catch (e) { }
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        if (!response) {
          return;
        }
        let url = window.URL.createObjectURL(new Blob([response]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "资源模板.xls");
        document.body.appendChild(link);
        link.click();
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
  },

  handleClose(done) {
    this.$confirm("确认关闭？")
      .then((_) => {
        done();
      })
      .catch((_) => { });
  },
};
</script>
<style lang="scss" scoped>
::v-deep .table {
  .el-table__body-wrapper {
    z-index: 2;
  }

  .el-table__fixed::before,
  .el-table__fixed-right::before {
    display: none;
  }
}

/*去除upload组件过渡效果*/
.el-upload-list__item {
  transition: none !important;
}

.image {
  width: 200px;
  display: block;
}

.div-border {
  border: 1px solid #bfbfbf;
}

.customWidth {
  width: 70%;
}

.el-scrollbar {
  height: 90%;
}

.scrollbar-wrap {
  overflow-x: hidden;
}

.border {
  width: 700px;
  height: 150px;
}

/* .outside {
  width: 100%;
  height: 90%;
} */

.top {
  margin-top: 20px;
}

.middletb {
  width: 100%;
  margin-top: 30px;
}

.middle {
  margin-left: 20px;
  width: 90%;
  margin-top: 10px;
}

.borderNone input.el-input__inner {
  border: none;
}

/* .bottom {
  margin-bottom: -5px;
  float: right;
} */

.el-select-dropdown .el-scrollbar .el-scrollbar__wrap {
  overflow: scroll;
}

.pagination-container {
  float: right;
}

.el-textarea__inner {
  height: 150px;
}

.pagination-container {
  float: right;
}

.mb8 {
  margin-bottom: -20px;
}

.el-row {
  margin-bottom: 20px;
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.catalog_title {
  font-size: 15px;

  /* font-family: "楷体", sans-serif; */
  font-weight: 400;
  font-style: normal;
  text-align: left;
  text-transform: lowercase;
}

.catalog_value {
  margin-top: 10px;
  font-size: 15px;
  color: #222653;

  /* font-family: "楷体", sans-serif; */
  font-weight: 400;
  font-style: normal;
  text-align: left;
  text-transform: lowercase;
}

.catalog_other {
  height: 10px;
}

.endItem .el-textarea__inner {
  padding-bottom: 36px;
}

.dialog-footer {
  margin-top: -30px;
}

.el-textarea .el-input__count {
  background-color: transparent;
  bottom: -27px;
}

.mb8 {
  margin-bottom: -10px !important;
}

.tableS {
  margin-top: 0px;
}

.formDiv {
  height: calc(100vh - 300px);
  overflow-y: auto;
  overflow-x: hidden;
}

.div_row {
  margin-bottom: -10px !important;
}
</style>

<style lang="scss" scoped>
::v-deep .upload-image li {
  width: 148px;
  height: 200px;
}

::v-deep .el-upload--picture-card {
  height: 200px !important;

  i {
    margin-top: 73px;
  }
}
</style>
