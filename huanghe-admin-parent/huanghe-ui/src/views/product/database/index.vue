<template>
  <div class="app-container">
    <div style="padding-right: 15px">
      <!-- :style="{ width: treeShow ? '50%' : '100%' }" -->
      <div class="top">
        <!-- <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
          <el-form-item label="数据库名称" prop="dbName" label-width="60">
            <el-input v-model="queryParams.dbName" placeholder="请输入" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="dbStatus">
            <el-select v-model="queryParams.dbStatus" placeholder="请选择" clearable size="small">
              <el-option v-for="dict in databaseStatus" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form> -->
      </div>
      <!-- <div style="margin-left: 10px" class="div_row">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['database:database:add']">新增</el-button>
          </el-col>
        </el-row>
      </div> -->
      <!-- <el-scrollbar
        style="width: 100%; float: left; height: 80%"
        wrapClass="scrollbar-wrap"
        ref="scrollbarContainer"
      > -->
      <div class="middletbsss">
        <el-table v-loading="loading1" :data="databaseList" @selection-change="handleSelectionChange"
          :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium'
          :header-row-style="{ height: '20px' }" height="calc(100vh - 300px)"
          :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column min-width="30" align="center" label="序号" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="数据库编号" align="center" prop="dbCode" /> -->
          <el-table-column label="数据库名称" align="center" prop="dbName" />
          <!--          <el-table-column
            label="数据库英文名称"
            align="center"
            prop="dbNameEn"
          />-->
          <el-table-column label="资源量" align="center" prop="bookCount">
          </el-table-column>
          <!-- <el-table-column label="排序" align="center" prop="dataDisplay" /> -->
          <!-- <el-table-column label="定价(元/年)" align="center" prop="price" /> -->
          <el-table-column label="创建时间" align="center" prop="createTime" min-width="150px">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.dbStatus" active-value="1" inactive-value="0"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column> -->

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="150px">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['database:database:edit']">编辑</el-button>
              <!-- <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['database:database:remove']">删除</el-button> -->

              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleView(scope.row)"
                v-hasPermi="['product:books:query']">查看资源</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- </el-scrollbar> -->
      <!-- <div class="bottom"> -->
      <pagination v-show="total1 > 0" :total="total1" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
      <!-- </div> -->
      <!-- 添加或修改数据库管理对话框 -->
      <el-dialog :title="title" :close-on-click-modal="false" :visible.sync="open" width="40%" append-to-body>
        <div>
          <el-form ref="form" :model="form" :rules="rules" label-width="130px">
            <!-- <el-form-item label="数据库编号" prop="dbCode">
              <el-input
                v-model="form.dbCode"
                placeholder="请输入数据库编号"
                maxlength="30"
                show-word-limit
              />
            </el-form-item> -->

            <el-form-item label="数据库名称" prop="dbName">
              <el-input v-model="form.dbName"  style="width:99%" disabled />
            </el-form-item>
            <!--          <el-form-item label="数据库英文名称"  prop="dbNameEn" required="true">
              <el-input
                v-model="form.dbNameEn"

                placeholder="请输入数据库英文名称"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>-->
            <!-- <el-form-item label="排序" prop="dataDisplay" required>
              <el-input-number v-model="form.dataDisplay" controls-position="right" :min="0" :max="20000"
                style="width:99%"></el-input-number>
            </el-form-item>

            <el-form-item label="定价(元/年)" prop="price" required="true">
              <el-input-number v-model="form.price" controls-position="right" :min="0" :max="1000000000"
                style="width:99%"></el-input-number>
            </el-form-item> -->
            <!-- <el-form-item label="简介" prop="dbDesc">
              <el-input
                maxlength="1000"
                type="textarea"
                v-model="form.dbDesc"
                show-word-limit
                placeholder="请输入数据库简介"
              />
            </el-form-item> -->
            <!-- <el-form-item label="状态" prop="dbStatus">
              <el-switch v-model="form.dbStatus" active-value="1" inactive-value="0" active-color="#13ce66"
                inactive-color="#000000" style="width:99%">
              </el-switch>
            </el-form-item> -->

            <!-- <el-form-item label="数据库封面" prop="coverUrl">
              <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUpload"
                :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :limit="1"
                :on-exceed="handleExceed" :on-change="handleChange" :file-list="fileListA">
                <i class="el-icon-plus"></i>
                <div class="el-upload__tip" slot="tip">
                  <span style="color: #f56c6c">可以上传1张，建议尺寸640×640像素。</span>
                </div>
              </el-upload>
            </el-form-item> -->

            <el-form-item label="数据库封面" prop="coverUrl" style="margin-top: 30px;">
              <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUpload"
                :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                :on-exceed="handleExceed"  :limit=1 :file-list="fileListA"  :on-change="handleChange"  :on-success="uploadSuccess" class="upload-image">
                <i class="el-icon-plus"></i>
                <div class="el-upload__tip" slot="tip">
                  <span style="color: #f56c6c">可以上传1张,建议尺寸不能超过135*135像素。</span>
                </div>
              </el-upload>
            </el-form-item>

          </el-form>

        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="dialogVisible" >
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
      <!--查看资源-->
    </div>
    <!-- <el-scrollbar
        style="width: 99%; float: left; height: 800px"
        wrapClass="scrollbar-wrap"
        ref="scrollbarContainer"
        v-show="treeShow"
      > -->
    <el-dialog append-to-body :title="title" :visible.sync="openRecources" :close-on-click-modal="false">


      <!-- <div style="width: 98%; float: left; height: 98%"> -->
      <!-- <div>
          <el-button
            style="float: right"
            type="text"
            icon="el-icon-close"
            @click="closeTable"
          >
            关闭
          </el-button>
        </div> -->
      <!--  <div style="">

        </div>-->
      <el-form :model="queryBookParam" ref="bookForm" :inline="true" v-if="searchFormShow" label-width="68px">
        <!-- <el-row :gutter="40" class="mb8">
            <el-col :span="6"> -->
        <el-form-item label="书名" prop="bookName" label-width="70">
          <el-input style="width: 200px" v-model="queryBookParam.bookName" placeholder="请输入" clearable size="small"
            @keyup.enter.native="queryBooks" />
        </el-form-item>
        <!-- </el-col>

            <el-col :span="6"> -->
        <el-form-item label="资源分类" label-width="70">
          <el-cascader style="width: 150px" @change="changrCatalog" :options="typeList" v-model="resourceClasses"
            clearable></el-cascader>
        </el-form-item>
        <!-- </el-col>
            <el-col :span="6"> -->
        <el-form-item label-width="70" prop="publishDate" label="出版日期">
          <el-input v-model="queryBookParam.publishDate" size="small" @keyup.enter.native="queryBooks"
            placeholder="请输入"></el-input>
        </el-form-item>
        <!-- </el-col>
          </el-row> -->
        <!--
          <el-row :gutter="40" class="mb8">
            <el-col :span="6"> -->
        <el-form-item label="主要责任者" prop="mainResponsibility" label-width="80">
          <el-input v-model="queryBookParam.mainResponsibility" placeholder="请输入" clearable size="small"
            @keyup.enter.native="queryBooks" />
        </el-form-item>
        <el-form-item label="状态" label-width="70">
          <el-select placeholder="请选择" v-model="queryBookParam.proStatus" clearable>
            <el-option label="下架" value="0"></el-option>
            <el-option label="上架" value="1"></el-option>
          </el-select>
        </el-form-item>
        <!-- </el-col>

            <el-col :span="6"> -->

        <!-- </el-col>
            <el-col :span="6"> -->

        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="queryBooks">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetBook">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="margin-top: -40px">
        <right-toolbar :showSearch.sync="searchFormShow"></right-toolbar>
      </div>

      <el-table style="width: 100%; margin-top: 30px" v-loading="loading" :data="booksList"
        :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium'
        :header-row-style="{ height: '20px' }" height="calc(100vh - 500px)"
        :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
        <el-table-column label="序号" align="center" type="index" width="60px" />
        <el-table-column label="书名" align="center" prop="bookName" :show-overflow-tooltip="true" />
        <el-table-column label="资源分类" align="center" prop="resourceClasses" />
        <el-table-column label="主要责任者" align="center" prop="mainResponsibility" :show-overflow-tooltip="true" />
        <el-table-column :show-overflow-tooltip="true" label="出版日期" align="center" prop="publishDate" />
        <el-table-column label="出版地" align="center" prop="publishland" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="proStatus">
          <template slot-scope="scope">
            <p v-if="booksList[scope.$index].proStatus == 1">上架</p>
            <p v-if="booksList[scope.$index].proStatus == 0">下架</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" type="text" @click="handleQueryById(scope.row)">查看</el-button>

            <!-- <el-button size="mini" icon="el-icon-edit" type="text" @click="deleteBook(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryBookParam.pageNum"
        :limit.sync="queryBookParam.pageSize" @pagination="handleViews" />
      <div slot="footer" class="dialog-footer" style="margin-top: -10px;">
        <el-button @click="cancelRecourse">关闭</el-button>
      </div>
    </el-dialog>
    <!-- </div> -->
    <!-- </el-scrollbar> -->
    <el-dialog :title="title" :visible.sync="openBook" width="50%" append-to-body>
      <!-- <el-form ref="formBook" :model="formBook" label-width="100px">
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="书名" prop="bookName" label-width="100px">
                <div class="div-border">{{ formBook.bookName }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="作者" prop="publisher" label-width="100px">
                <div class="div-border">{{ formBook.publisher }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item
                label="资源类型"
                prop="resourceClasses"
                label-width="100px"
              >
                <div class="div-border">{{ formBook.resourceClasses }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                label="出版日期"
                prop="publishDate"
                label-width="100px"
              >
                <div class="div-border">{{ formBook.publishDate }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item
                label="主题词"
                prop="subjectWord"
                label-width="100px"
              >
                <div class="div-border">{{ formBook.subjectWord }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="版次" prop="revision" label-width="100px">
                <div class="div-border">{{ formBook.revision }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="丛编" label-width="100px">
                <div class="div-border">{{ formBook.dbName }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                label="出版地"
                prop="publishland"
                label-width="100px"
              >
                <div class="div-border">{{ formBook.publishland }}</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="10">
              <el-form-item label="海报" prop="coverUrl" label-width="100px">
                <div>
                  <el-image :src="fileList" class="image" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="20">
              <el-form-item label="简介" prop="bookDesc " label-width="100px">
                <el-scrollbar
                  wrapClass="scrollbar-wrap"
                  :style="{ width: scrollHeight }"
                  ref="scrollbarContainer"
                >
                  <div class="borderSo">{{ formBook.bookDesc }}</div>
                </el-scrollbar>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form> -->
      <div class="formDiv">
        <el-form ref="form" :model="formBook" label-width="100px">
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="书名" prop="bookName" label-width="100px">
                <el-input v-model="formBook.bookName" placeholder="书名" disabled="true" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主要责任者" prop="mainResponsibility" label-width="100px">
                <el-input disabled="true" v-model="formBook.mainResponsibility" placeholder="主要责任者" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="资源类型" prop="resourceClasses" label-width="100px">
                <el-input v-model="formBook.resourceClasses" disabled="true"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="出版日期" prop="publishDate" label-width="100px">
                <el-input v-model="formBook.publishDate" disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="主题词" prop="subjectWord" label-width="100px">
                <el-input disabled="true" v-model="formBook.subjectWord" placeholder="主题词" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="版次" prop="revision" label-width="100px">
                <el-input disabled="true" v-model="formBook.revision" placeholder="版次" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="丛编" label-width="100px">
                <el-input disabled="true" v-model="formBook.dbId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出版社" prop="publishland" label-width="100px">
                <el-input disabled="true" v-model="formBook.publishland" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="封面高清图" prop="coverUrl" label-width="100px">
                <div>
                  <el-image :src="fileList" class="image" />
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="封面缩略图" prop="thumbCoverUrl" label-width="100px">
                <div>
                  <el-image :src="thumbCoverUrlList" class="image" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="12">
              <el-form-item label="四部分类法" prop="siClassification" label-width="100px">
                <el-input disabled="true" v-model="formBook.siClassification"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40" class="mb8">
            <el-col :span="30">
              <el-form-item label="简介" prop="bookDesc">
                <el-input style="width: 850px" disabled="true" type="textarea" v-model="formBook.bookDesc"
                  :aria-readonly="true" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancelBook">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import Editor from "@/components/aa/editor";
import { getBooks, uploadAvatar } from "@/api/product/book/books";
import {
  check,
  listDatabase,
  getDatabase,
  delDatabase,
  addDatabase,
  updateDatabase,
  exportDatabase,
  listBook,
  changeDatabaseStatus,
} from "@/api/product/database/database";
import { classicTYpe } from "@/api/product/classic/tree";
import { dbNames, deleteFromDb } from "@/api/product/book/books";

export default {
  name: "Database",
  components: {
    Editor,
  },
  data() {
    const checkName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("名称不能为空"));
      }
      check().then((response) => {
        let a = false;
        response.forEach((e) => {
          if (e.dbName == value && this.form.dbId != e.dbId) {
            a = true;
          }
        });
        if (a) {
          return callback(new Error("名称已存在"));
        } else {
          return callback();
        }
      });
    };

    const isNum = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("价格不能小于0"));
      }
      const age = /^[0-9]*$/;
      if (!age.test(value)) {
        callback(new Error("只能为数字"));
      } else {
        callback();
      }
    };

    const checkCode = (rule, value, callback) => {
      check().then((response) => {
        let b = false;
        response.forEach((e) => {
          if (e.dbCode == value && this.form.dbId != e.dbId) {
            b = true;
          }
        });
        if (b) {
          return callback(new Error("编号已存在"));
        } else {
          return callback();
        }
      });
    };

    const isCover = (rule, value, callback) => {
      if (this.form.coverUrl == null || this.form.coverUrl == "") {
        callback(new Error("封面不能为空"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      loading1: true,

      num: 0,
      // 选中数组
      ids: [],
      openRecources: false,
      searchFormShow: true,
      typeList: { treeName: "treeName" },
      dbList: { dbName: "dbName" },
      // 非单个禁用
      single: true,
      treeShow: false,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      scrollHeight: "0px",

      total1: 0,
      // 数据库管理表格数据
      databaseList: [],
      booksList: [],
      dialogImageUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传图片地址
      fileList: [],
      thumbCoverUrlList: [],
      // 弹出层标题
      title: "",
      currentClickObj: {
        id: "",
      },
      tableHeight: window.innerHeight - 510,
      // 是否显示弹出层
      open: false,
      openView: false,
      openBook: false,
      databaseStatus: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dbCode: null,
        dbName: null,
        bookCount: null,
        dbStatus: null,
        price: null,
        discount: null,
        finalPrice: null,
        coverUrl: null,
        dbDesc: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      },

      queryBookParam: {
        pageNum: 1,
        pageSize: 10,
        bookName: null,
        resourceClasses: null,
        publishDate: null,
        proStatus: null,
        mainResponsibility: null,
        dbId: "",
      },
      // 表单参数
      form: {},
      formBook: {},
      fileListA: [],
      resourceClasses: [],
      // 表单校验
      rules: {
        /*     dbNameEn: [
          { required: true, message: "数据库英文名称不能为空", trigger: "blur" },
        ],*/
        // dbCode: [{ validator: checkCode, trigger: "blur" }],
        // dbName: [{required: true, validator: checkName, trigger: "blur" }],

        dbName: [{ required: true, message: "名称不能为空", trigger: "blur" },
        ],


        dataDisplay: [
          { required: true, message: "排序不能为空", trigger: "blur" },
        ],

        price: [{ validator: isNum, trigger: "blur" }],
        coverUrl: [
          {
            validator: isCover,
            required: true,
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.scrollHeight = window.innerWidth * 0.8 + "px";
  },

  created() {
    this.getType();
    this.getDicts("database_status").then((response) => {
      this.databaseStatus = response.data;
      console.log(this.databaseStatus);
    });
    this.getList();
  },
  methods: {
    //
    handleStatusChange(row) {
      let text = row.dbStatus === "0" ? "下架" : "上架";
      this.$confirm(
        "确认要" + text + "'" + row.dbName + "'" + "数据库吗?",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          console.log(row.dbId);

          return changeDatabaseStatus(row.dbId, row.dbStatus);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.dbStatus = row.dbStatus === "0" ? "1" : "0";
        });
    },
    /**
     * 资源分类下拉框
     */
    getType() {
      this.loading = true;
      let that = this;
      classicTYpe().then((response) => {
        this.typeList = this.getTreeData(response.rows);
      });
    },
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {

        if (response.code === 200) {
          this.form.coverUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }else{
          this.$refs.upload.uploadFiles.splice(
            this.$refs.upload.uploadFiles.indexOf(val.file)
          )
          this.fileList=[];
          this.msgError(response.msg);
          
        }
      });
    },

    cancelRecourse() {
      this.openRecources = false;
      this.resetQuery();
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handleExceed(files, fileListA) {
      this.msgError("最多上传1张图片");
    },

    handleRemove(file, fileListA) {
      this.form.coverUrl = "";
    },
    // handleChange(files, fileList) {
    //   this.hideUpload = fileList.length >= 1;
    // },
    beforeUpload(file) {
      //console.log(file)
    

      const isJPG =
        file.type === "image/jpg" ||
        file.type === "image/png" ||
        file.type === "image/jpeg";
      //图片大小
      if (!isJPG) {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
      else{
        return this.checkImageWH(file, 524, 400);
      }
    },

    

     // 配置图片像素
     checkImageWH(file, width, height) {
      let self = this;
      return new Promise(function (resolve, reject) {
        let filereader = new FileReader();
        filereader.onload = (e) => {
          let src = e.target.result;
          const image = new Image();
          image.onload = function () {
            // if (width && this.width > width) {
            //   self.$message.error(
            //     "图片尺寸不能超过524*400像素!"
            //   );
            //   reject();
            // } else if (height && this.height > height) {
            //   self.$message.error(
            //     "图片尺寸不能超过524*400像素!"
            //   );
            //   reject();
            // } else {
            //   resolve();
            // }
            resolve();
          };
          image.onerror = reject;
          image.src = src;
        };
        filereader.readAsDataURL(file);
      });
    },

    handleChange(file, fileList){
        console.log("1===="+file)
        console.log(fileList)
    },
    uploadSuccess(response, file, fileList){
    },

    //关闭资源
    closeTable() {
      this.treeShow = false;
    },
    getTreeData(data) {
      // 循环遍历json数据
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    // 数据库下拉框

    /** 查询数据库管理列表 */
    getList() {
      this.loading1 = true;
      listDatabase(this.queryParams).then((response) => {
        this.databaseList = response.rows;
        this.total1 = response.total;
        this.loading1 = false;
      });
    },

    resetBook() {
      this.queryBookParam.resourceClasses = null;
      this.queryBookParam.proStatus = [];
      this.resetForm("bookForm");
      //debugger;
      this.getBookRe();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelBook() {
      this.openBook = false;
      this.resetForm("formBook");
      this.getBook();
    },

    changrCatalog(value) {
      // //debugger;
      // if (value.length == 0) {
      //   this.queryBookParam.resourceClasses = null;
      // } else {
      //   this.queryBookParam.resourceClasses = value[0];
      // }
    },

    //查看资源的搜索/
    queryBooks() {
      this.queryBookParam.pageNum = 1;
      this.getBook();
    },
    handleQueryById(row) {
      //debugger;
      const id = row.id;
      getBooks(id).then((response) => {
        this.formBook = response.data;
        if (this.formBook.coverUrl != null) {
          let obj = new Object();

          obj.url = this.formBook.coverUrl;

          this.fileList = obj.url;
        }
        if (this.formBook.thumbCoverUrl != null) {
          let obj1 = new Object();
          obj1.url = this.formBook.thumbCoverUrl;
          console.log(this.formBook.thumbCoverUrl);
          this.thumbCoverUrlList = obj1.url;
        }

        this.openBook = true;
        this.title = "查看";
      });
    },

    /**
     * 把书从数据库剔除
     */
    deleteBook(row) {

      const ids = row.id || this.ids;
      this.$confirm("是否确认删除数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })

        .then(function () {
          return deleteFromDb(ids);
        })
        .then(() => {
          this.getBook();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },

    // 查看资源获取数据/
    getBook() {

      if (this.resourceClasses == null) {
        this.queryBookParam.resourceClasses == null;
      } else {
        this.queryBookParam.resourceClasses = this.resourceClasses.join("/");
      }

      this.loading = true;

      listBook(this.queryBookParam, this.queryBookParam.dbId).then(
        (response) => {
          console.log(response);
          console.log(response.data);
          this.total = response.total;
          this.booksList = response.rows;
          this.loading = false;
        }
      );
    },

    getBookRe() {

      if ((this.resourceClasses = [])) {
        this.queryBookParam.resourceClasses = null;
      } else {
        this.queryBookParam.resourceClasses = resourceClasses[0];
      }
      this.loading = true;

      listBook(this.queryBookParam, this.queryBookParam.dbId).then(
        (response) => {
          this.booksList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    //查看资源按钮
    handleView(row) {

      this.queryBookParam.dbId = row.dbId;
      const id = row.dbId;
      const dbName = row.dbName;
      listBook(this.queryBookParam, id).then((response) => {
        this.total = response.total;
        this.booksList = response.rows;
        this.loading = false;
        this.title = "查看资源";
        this.openRecources = true;
      });
    },
    handleViews(row) {

      const id = this.queryBookParam.dbId;
      const dbName = row.dbName;
      listBook(this.queryBookParam, id).then((response) => {
        this.total = response.total;
        this.booksList = response.rows;
        this.loading = false;
        this.openRecources = true;
      });
    },
    // 表单重置
    reset() {
      this.fileListA = [];
      this.resetForm("form");
      this.form = {
        dataDisplay: 0,
        dbCode: null,
        dbName: null,
        price: null,
      };
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.fileListA = [];
      this.reset();
      this.open = true;
      this.title = "添加数据库管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.dbId || this.ids;

      getDatabase(id).then((response) => {
        this.form = response.data;
        //   obj.url = process.env.VUE_APP_BASE_API + this.form.coverUrl;
        let obj = new Object();

        //obj.url = process.env.VUE_APP_BASE_API + this.form.coverUrl;
        obj.url = this.form.coverUrl;
        this.fileListA.push(obj);
        console.log(this.form.coverUrl);
        if (!this.form.coverUrl) {
          this.fileListA = [];
        }
        this.open = true;
        this.title = "修改数据库管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.form)
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.dbId != null) {
            updateDatabase(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addDatabase(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.dbId || this.dbids;
      this.$confirm("是否确认删除", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delDatabase(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有数据库管理数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportDatabase(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () { });
    },
  },
};
</script>
<style lang="scss" scoped>
.image {
  width: 200px;
  display: block;
}

.div-border {
  border: 1px solid #bfbfbf;
}

.border {
  width: 700px;
  border: 1px solid #bfbfbf;
  height: 100px;
}

.borderSo {
  width: 700px;
  height: 100px;
}

.borderNone input.el-input__inner {
  border: none;
}

.el-pagination {
  margin-bottom: 0px;
  position: relative;
  bottom: 0px;
  right: 10px;
}

.pagination-container .el-pagination {
  margin-bottom: 0px;
  position: relative;
  bottom: 0px;
  right: 10px;
}

/* .bottom {
  margin-bottom: 10px;
  margin-top: 0px;
} */
.outside {
  width: 100%;
  height: 90%;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.middletb {
  margin-left: 5px;
  width: 100%;
  height: 600px;
  margin-top: 10px;
}

.middle {
  margin-top: 10px;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}

.el-textarea__inner {
  height: 200px;
}

.pagination-container .el-pagination {
  margin-bottom: 0px;
  bottom: 0px;
  float: right;
  right: 10px;
}


.mb8 {
  margin-bottom: -10px !important;
}

.tableS {
  margin-top: 0px;
}

.formDiv {
  height: calc(100vh - 290px);
  overflow-y: auto;
}

.div_row {
  margin-bottom: 30px !important;
}

::v-deep .upload-image li {
  width: 148px;
  height: 200px;
}

::v-deep .el-upload--picture-card {
  height: 200px !important;

  i {
    margin-top: 73px;
  }
}
::v-deep .el-upload-list__item {
  transition: none !important;
}
</style>


