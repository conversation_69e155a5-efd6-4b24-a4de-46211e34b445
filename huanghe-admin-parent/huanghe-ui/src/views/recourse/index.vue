<template>
  <div :style="{ height: mainPage + 'px' }">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="资源名称" prop="bookName">
          <el-input v-model="queryParams.bookName" placeholder="请输入" clearabled size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- <el-scrollbar
        wrapClass="scrollbar-wrap"
        style="width: 110%; height: 95%"
        ref="scrollbarContainer"
      > -->


      <el-table
        v-loading="loading"
        :data="searchList"
        :header-cell-style="headClass"
        :resizable="false"
        :height="tableHeight"
        @sort-change="handleSortChange"
      >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="60px"
          />
        <el-table-column
          label="资源名称"
          align="center"
          prop="bookName"
          width="300px"
        />
        <el-table-column label="所属丛编" align="center" prop="dbName" />
        <el-table-column
          label="所属分类"
          align="center"
          prop="resourceClasses"
        />
        <el-table-column label="笔记次数" align="center" prop="noteCount" sortable="custom" width="120"/>
        <el-table-column label="访问次数" align="center" prop="visitCount"  sortable="custom" width="120"/>
         <el-table-column label="复制次数" align="center" prop="copyCount"  sortable="custom" width="120"/>
      <el-table-column label="引用次数" align="center" prop="quoteCount" sortable="custom" width="120"/>
      </el-table>
    <!-- </el-scrollbar> -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { recourseCount } from "@/api/homePage/homePage";
import "@/icon/more/iconfont.css";
export default {
  name: "recourseCount",
  data() {
    return {
      tableHeight: window.innerHeight - 300,
      mainPage: window.innerHeight - 200,
      loading: false,
      searchList: [],
      total: 0,
      scrollHeight: "0px",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dbName: "",
        selectData: [],
      },
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.scrollHeight = window.innerWidth * 0.8 + "px";
  },
  methods: {
    handleSortChange({ column, prop, order }) {
      this.queryParams.orderByColumn = prop;
      this.queryParams.isAsc = order === 'ascending' ? 'asc' : 'desc';
      this.getList();
    },
    getList() {
      this.loading = true;
      recourseCount(this.queryParams).then((response) => {
        this.searchList = response.rows;
           response.rows.forEach((e) => {
          if (e.copyCount == null) {
            e.copyCount = 0;
          }
          if (e.quoteCount == null) {
            e.quoteCount = 0;
          }if(e.searchCount==null){
            e.searchCount=0
          }if(e.visitCount==null){
            e.visitCount=0
          }if(e.noteCount==null){
            e.noteCount=0
          }
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.searchContent = "";
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectData = selection;

      this.multiple = !selection.length;
    },
    //批量删除
    handleDelete() {
      this.$confirm("确认删除吗", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delSearchContent(this.selectData)
          .then((response) => {
            this.msgSuccess("删除成功");
            this.getList();
          })
          .catch(function () { });
      });
    },
    //删除
    handleDeleteBy(row) {
      const id = row.id;
      this.$confirm("确认删除吗", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteSearch(id)
          .then(() => {
            this.msgSuccess("删除成功");
            this.getList();
          })
          .catch(function () { });
      });
    },
  },
};
</script>
<style scoped>
.top {
  margin-top: 20px;

}

.bottom {
  margin-bottom: 10px;
  float: right;
}

/* .el-pagination{
  //margin-bottom: 100px;
} */
.el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
