<template>
  <div class="app-container">
    <div style="margin-left:0px">
      <el-form
        label-width="68px"
        ref="queryForm"
        :inline="true"
        :model="queryParams"
      >
        <!-- <div style="width:500px"> -->
        <!-- <div style="float:left; witdh: 50%;margin-top:20px"> -->
        <el-form-item prop="bookName" label="资源名称" label-width="100px">
          <el-input
            v-model="queryParams.bookName"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- </div> -->
        <!-- <div style="margin-left:10px;folat:right;margin-top:20px"> -->
        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>

        <!-- </div> -->
        <!-- </div> -->
      </el-form>
    </div>
    <div style="width: 100%; height: 98%">
      <el-table
        v-loading="loading"
        :data="searchList"
        :header-cell-style="headClass"
        :resizable="false"
        :height="tableHeight"
      >
          <el-table-column
            label="序号"
            align="center"
            type="index"
            width="60px"
          />
        <el-table-column
          label="资源名称"
          align="center"
          prop="bookName"
          width="300px"
        />
        <el-table-column label="所属丛编" align="center" prop="dbName" />
        <el-table-column
          label="所属分类"
          align="center"
          prop="resourceClasses"
        />
        <el-table-column label="检索次数" align="center" prop="searchCount" sortable width="120"/>
        <el-table-column label="访问次数" align="center" prop="visitCount"  sortable width="120"/>
         <el-table-column label="复制次数" align="center" prop="copyCount"  sortable width="120"/>
      <el-table-column label="引用次数" align="center" prop="quoteCount" sortable width="120"/>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import { recourseCount } from "@/api/homePage/homePage";
import "@/icon/more/iconfont.css";
export default {
  name: "recourseCount",
  data() {
    return {
      tableHeight: window.innerHeight - 315,
      searchList: [],
      loading: false,
      total: 0,
      queryParams: {
        bookName: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.bookName = "";
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      recourseCount(this.queryParams).then((response) => {
        this.searchList = response.rows;
           response.rows.forEach((e) => {
          if (e.copyCount == null) {
            e.copyCount = 0;
          }
          if (e.quoteCount == null) {
            e.quoteCount = 0;
          }if(e.searchCount==null){
            e.searchCount=0
          }if(e.visitCount==null){
            e.visitCount=0
          }
        });
        this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped>
::v-deep .el-button el-button--cyan el-button--mini {
  margin-left: 10px;
}
</style>
