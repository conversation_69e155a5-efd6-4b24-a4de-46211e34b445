import {
  listBanner,
  getBanner,
  delBanner,
  addBanner,
  updateBanner,
  exportBanner,
  uploadAvatar,
  changeStatus
} from "@/api/tsystem/banner";

export default {
  name: "Banner",
  data() {
    return {
      // 遮罩层
      loading: true,
      uploadLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 轮播图表格数据
      bannerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        fileType: null,
        area: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        groupName: [
          { required: true, message: "分组名称不能为空", trigger: "change" },
        ],
        filePath: [
          { required: true, message: "文件不能为空", trigger: "change" },
        ],
        fileType: [
          { required: true, message: "文件类型不能为空", trigger: "change" },
        ],
        display: [
          { required: true, message: "排序不能为空", trigger: "blur" },
        ],
      },
      // 分组选项
      groupOptions: [
        { value: 'G1', label: '视频' },
        { value: 'G2', label: '书卷' },
        { value: 'G3', label: '专题' },
        { value: 'G4', label: '地图' }
      ],
      dialogFileUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传文件地址
      fileList: [],
      // 是否为图片
      isImage: true,
    };
  },
  computed: {
    fileTypeLabel() {
      return this.form.fileType === 'MP4' ? '视频文件:' : '图片文件:';
    },
    uploadTip() {
      if (this.form.fileType === 'MP4') {
        return '可以上传1个视频文件，建议大小不超过50MB，支持格式 MP4。';
      } else {
        return '可以上传1张图片，建议图片尺寸不能超过1920*700像素，支持格式 PNG，JPG，GIF。';
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询轮播图列表 */
    getList() {
      this.loading = true;
      listBanner(this.queryParams).then((response) => {
        this.bannerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取分组名称
    getGroupName(groupName) {
      const group = this.groupOptions.find(item => item.value === groupName);
      return group ? group.label : groupName;
    },
    // 获取分组标签类型
    getGroupTagType(groupName) {
      const typeMap = {
        'G1': 'primary',
        'G2': 'success', 
        'G3': 'warning',
        'G4': 'info'
      };
      return typeMap[groupName] || '';
    },
    // 分组变化处理
    handleGroupChange(value) {
      // 根据分组设置默认文件类型
      if (value === 'G1') {
        this.form.fileType = 'MP4'; // 视频分组默认MP4
      } else {
        this.form.fileType = 'JPG'; // 其他分组默认JPG
      }
    },
    // 文件类型变化处理
    handleFileTypeChange(value) {
      // 清空已上传的文件
      this.fileList = [];
      this.form.filePath = null;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 选择框是否可选
    selectable(row, index) {
      return row.status !== '0'; // 上架状态不可选择删除
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加轮播图";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.filePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.filePath;
          this.fileList.push(obj);
        }
        if (!this.form.filePath) {
          this.fileList = [];
        }
        this.open = true;
        this.title = "修改轮播图";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBanner(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBanner(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除轮播图编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delBanner(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有轮播图数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportBanner(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () {});
    },
    //文件上传
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      this.uploadLoading = true;
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.uploadLoading = false;
          this.form.filePath = response.filePath;
          this.form.fileType = response.fileType;
          this.msgSuccess("上传成功");
        }
      });
    },
    handleRemove(file, fileList) {
      this.form.filePath = "";
      this.fileList = [];
    },
    handlePictureCardPreview(file) {
      this.dialogFileUrl = file.url;
      this.isImage = this.form.fileType !== 'MP4';
      this.dialogVisible = true;
    },
    // 上传预处理
    beforeUpload(file) {
      const isValidType = file.type.indexOf("image/") !== -1 || file.type.indexOf("video/") !== -1;
      if (!isValidType) {
        this.msgError("文件格式错误，请上传图片或视频文件。");
        return false;
      }
      
      // 检查文件大小
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.msgError('上传文件大小不能超过 50MB!');
        return false;
      }
      
      return true;
    },
    //上传文件超出数量
    handleExceed(files, fileList) {
      this.msgError("最多上传1个文件");
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "上架" : "下架";
      this.$confirm('确认要"' + text + '""' + row.id + '"吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return changeStatus(row.id, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
          this.getList();
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
  },
};
</script>
