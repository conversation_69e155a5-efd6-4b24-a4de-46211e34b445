<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分组名称" prop="groupName">
        <el-select v-model="queryParams.groupName" placeholder="请选择分组" clearable size="small">
          <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择文件类型" clearable size="small">
          <el-option label="图片(JPG)" value="JPG" />
          <el-option label="视频(MP4)" value="MP4" />
        </el-select>
      </el-form-item>
      <el-form-item label="地市" prop="area">
        <el-input v-model="queryParams.area" placeholder="请输入地市" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="上架" value="0" />
          <el-option label="下架" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['tsystem:banner:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['tsystem:banner:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['tsystem:banner:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['tsystem:banner:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <el-table-column min-width="30" align="center" label="序号">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分组名称" align="center" prop="groupName" width="120">
        <template slot-scope="scope">
          <el-tag :type="getGroupTagType(scope.row.groupName)">{{ getGroupName(scope.row.groupName) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" align="center" prop="fileType" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.fileType === 'MP4' ? 'warning' : 'success'">{{ scope.row.fileType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容介绍" align="center" prop="content" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="地市" align="center" prop="area" width="100" />
      <el-table-column label="排序" align="center" prop="display" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createbyName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['tsystem:banner:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['tsystem:banner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分组名称" prop="groupName">
              <el-select v-model="form.groupName" placeholder="请选择分组" style="width: 100%" @change="handleGroupChange">
                <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地市" prop="area">
              <el-input v-model="form.area" placeholder="请输入地市" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="display">
              <el-input-number v-model="form.display" :min="1" :max="999" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件类型" prop="fileType">
              <el-select v-model="form.fileType" placeholder="请选择文件类型" style="width: 100%" @change="handleFileTypeChange">
                <el-option label="图片(JPG)" value="JPG" />
                <el-option label="视频(MP4)" value="MP4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="内容介绍" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="3" placeholder="请输入内容介绍" maxlength="500" show-word-limit />
        </el-form-item>
        <el-form-item label="跳转链接" prop="linkUrl">
          <el-input v-model="form.linkUrl" placeholder="请输入跳转链接" maxlength="255" />
        </el-form-item>
        <el-form-item :label="fileTypeLabel" prop="filePath">
          <el-upload v-loading="uploadLoading" ref="upload" action="#" list-type="picture-card" :http-request="requestUpload" :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :limit="1" :on-exceed="handleExceed" :file-list="fileList" class="upload-file">
            <i class="el-icon-plus"></i>
            <div class="el-upload__tip" slot="tip">
              <span style="color: #f56c6c">{{ uploadTip }}</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <el-dialog :visible.sync="dialogVisible">
      <img v-if="isImage" width="100%" :src="dialogFileUrl" alt="" />
      <video v-else width="100%" controls :src="dialogFileUrl" />
    </el-dialog>
  </div>
</template>

<script>
import { listBanner, getBanner, delBanner, addBanner, updateBanner, exportBanner, uploadAvatar, changeStatus } from "@/api/tsystem/banner";

export default {
  name: "Banner",
  data() {
    return {
      loading: true,
      uploadLoading: false,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      bannerList: [],
      title: "",
      open: false,
      queryParams: { pageNum: 1, pageSize: 10, groupName: null, fileType: null, area: null, status: null },
      form: {},
      rules: {
        groupName: [{ required: true, message: "分组名称不能为空", trigger: "change" }],
        filePath: [{ required: true, message: "文件不能为空", trigger: "change" }],
        fileType: [{ required: true, message: "文件类型不能为空", trigger: "change" }],
        display: [{ required: true, message: "排序不能为空", trigger: "blur" }],
      },
      groupOptions: [
        { value: 'G1', label: '视频' },
        { value: 'G2', label: '书卷' },
        { value: 'G3', label: '专题' },
        { value: 'G4', label: '地图' }
      ],
      dialogFileUrl: "",
      dialogVisible: false,
      fileList: [],
      isImage: true,
    };
  },
  computed: {
    fileTypeLabel() { return this.form.fileType === 'MP4' ? '视频文件:' : '图片文件:'; },
    uploadTip() {
      if (this.form.fileType === 'MP4') {
        return '可以上传1个视频文件，建议大小不超过50MB，支持格式 MP4。';
      } else {
        return '可以上传1张图片，建议图片尺寸不能超过1920*700像素，支持格式 PNG，JPG，GIF。';
      }
    }
  },
  created() { this.getList(); },
  methods: {
    getList() {
      this.loading = true;
      listBanner(this.queryParams).then((response) => {
        this.bannerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getGroupName(groupName) {
      const group = this.groupOptions.find(item => item.value === groupName);
      return group ? group.label : groupName;
    },
    getGroupTagType(groupName) {
      const typeMap = { 'G1': 'primary', 'G2': 'success', 'G3': 'warning', 'G4': 'info' };
      return typeMap[groupName] || '';
    },
    handleGroupChange(value) {
      if (value === 'G1') { this.form.fileType = 'MP4'; } else { this.form.fileType = 'JPG'; }
    },
    handleFileTypeChange(value) { this.fileList = []; this.form.filePath = null; },
    cancel() { this.open = false; this.reset(); },
    reset() {
      this.form = { id: null, groupName: null, filePath: null, fileType: null, content: null, linkUrl: null, display: 1, area: null, status: null, createbyId: null, createbyName: null, createTime: null, updatebyId: null, updatebyName: null, updateTime: null, delFlag: null };
      this.fileList = [];
      this.resetForm("form");
    },
    handleQuery() { this.queryParams.pageNum = 1; this.getList(); },
    resetQuery() { this.resetForm("queryForm"); this.handleQuery(); },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    selectable(row, index) { return row.status !== '0'; },
    handleAdd() { this.reset(); this.open = true; this.title = "添加轮播图"; },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.filePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.filePath;
          this.fileList.push(obj);
        }
        if (!this.form.filePath) { this.fileList = []; }
        this.open = true;
        this.title = "修改轮播图";
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBanner(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBanner(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除轮播图编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(function () { return delBanner(ids); }).then(() => { this.getList(); this.msgSuccess("删除成功"); }).catch(() => {});
    },
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有轮播图数据项?", "警告", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(function () { return exportBanner(queryParams); }).then((response) => { this.download(response.msg); }).catch(function () {});
    },
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      this.uploadLoading = true;
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.uploadLoading = false;
          this.form.filePath = response.filePath;
          this.form.fileType = response.fileType;
          this.msgSuccess("上传成功");
        }
      });
    },
    handleRemove(file, fileList) { this.form.filePath = ""; this.fileList = []; },
    handlePictureCardPreview(file) {
      this.dialogFileUrl = file.url;
      this.isImage = this.form.fileType !== 'MP4';
      this.dialogVisible = true;
    },
    beforeUpload(file) {
      const isValidType = file.type.indexOf("image/") !== -1 || file.type.indexOf("video/") !== -1;
      if (!isValidType) { this.msgError("文件格式错误，请上传图片或视频文件。"); return false; }
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) { this.msgError('上传文件大小不能超过 50MB!'); return false; }
      return true;
    },
    handleExceed(files, fileList) { this.msgError("最多上传1个文件"); },
    handleStatusChange(row) {
      let text = row.status === "0" ? "上架" : "下架";
      this.$confirm('确认要"' + text + '""' + row.id + '"吗?', "警告", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(function () { return changeStatus(row.id, row.status); }).then(() => { this.msgSuccess(text + "成功"); this.getList(); }).catch(function () { row.status = row.status === "0" ? "1" : "0"; });
    },
  },
};
</script>

<style scoped>
.upload-file .el-upload--picture-card { width: 100px; height: 100px; line-height: 100px; }
.upload-file .el-upload-list--picture-card .el-upload-list__item { width: 100px; height: 100px; }
.el-tag { margin-right: 5px; }
.app-container { padding: 20px; }
.mb8 { margin-bottom: 8px; }
</style>
