<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="Group Name" prop="groupName">
        <el-select v-model="queryParams.groupName" placeholder="Select Group" clearable size="small">
          <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="File Type" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="Select File Type" clearable size="small">
          <el-option label="Image(JPG)" value="JPG" />
          <el-option label="Video(MP4)" value="MP4" />
          <el-option label="GIF" value="GIF" />
        </el-select>
      </el-form-item>
      <el-form-item label="City" prop="area">
        <el-input v-model="queryParams.area" placeholder="Enter City" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="Status" prop="status">
        <el-select v-model="queryParams.status" placeholder="Select Status" clearable size="small">
          <el-option label="Online" value="0" />
          <el-option label="Offline" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">Search</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">Reset</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['tsystem:banner:add']">Add</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['tsystem:banner:edit']">Edit</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['tsystem:banner:remove']">Delete</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['tsystem:banner:export']">Export</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <el-table-column min-width="30" align="center" label="No.">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Group" align="center" prop="groupName" width="120">
        <template slot-scope="scope">
          <el-tag :type="getGroupTagType(scope.row.groupName)">{{ getGroupName(scope.row.groupName) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="File Type" align="center" prop="fileType" width="100">
        <template slot-scope="scope">
          <el-tag :type="getFileTypeTagType(scope.row.fileType)">{{ scope.row.fileType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Content" align="center" prop="content" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div v-html="scope.row.content" style="max-height: 50px; overflow: hidden;"></div>
        </template>
      </el-table-column>
      <el-table-column label="City" align="center" prop="area" width="100" />
      <el-table-column label="Sort" align="center" prop="display" width="80" />
      <el-table-column label="Status" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="Creator" align="center" prop="createbyName" width="100" />
      <el-table-column label="Create Time" align="center" prop="createTime" width="160" />
      <el-table-column label="Actions" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['tsystem:banner:edit']">Edit</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['tsystem:banner:remove']">Delete</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- Dynamic Form Dialog -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="dynamicRules" label-width="100px">
        <!-- Group Selection -->
        <el-form-item label="Group Name" prop="groupName">
          <el-select v-model="form.groupName" placeholder="Select Group" style="width: 300px" @change="handleGroupChange">
            <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">{{ getGroupDescription(form.groupName) }}</span>
        </el-form-item>

        <!-- Sort Field - Required for all groups -->
        <el-form-item label="Sort" prop="display">
          <el-input-number v-model="form.display" :min="1" :max="999" style="width: 200px" />
        </el-form-item>

        <!-- G4-Map Group: City field required -->
        <el-form-item v-if="form.groupName === 'G4'" label="City" prop="area">
          <el-input v-model="form.area" placeholder="Enter City" maxlength="50" style="width: 300px" />
        </el-form-item>

        <!-- G2-Book, G4-Map Groups: Rich text content required -->
        <el-form-item v-if="form.groupName === 'G2' || form.groupName === 'G4'" label="Content" prop="content">
          <RichEditor v-model="form.content" placeholder="Enter content description..." />
        </el-form-item>

        <!-- File Upload - Different components based on group -->
        <el-form-item :label="getUploadLabel()" prop="filePath">
          <!-- G1-Video: Upload video files -->
          <div v-if="form.groupName === 'G1'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              :http-request="requestUpload"
              :before-upload="beforeVideoUpload"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="video/mp4">
              <el-button size="small" type="primary">Select Video File</el-button>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">Only MP4 format video files, max 100MB</span>
              </div>
            </el-upload>
          </div>

          <!-- G2-Book, G4-Map: Upload images -->
          <div v-else-if="form.groupName === 'G2' || form.groupName === 'G4'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              list-type="picture-card"
              :http-request="requestUpload"
              :before-upload="beforeImageUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="image/jpeg,image/jpg,image/png">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">Only JPG/PNG format images, recommended 1920*700px, max 5MB</span>
              </div>
            </el-upload>
          </div>

          <!-- G3-Topic: Upload GIF images -->
          <div v-else-if="form.groupName === 'G3'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              list-type="picture-card"
              :http-request="requestUpload"
              :before-upload="beforeGifUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="image/gif">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">Only GIF format images, max 10MB</span>
              </div>
            </el-upload>
          </div>

          <!-- No group selected prompt -->
          <div v-else style="text-align: center; padding: 20px; border: 1px dashed #d9d9d9; color: #999;">
            Please select a group type first
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">OK</el-button>
        <el-button @click="cancel">Cancel</el-button>
      </div>
    </el-dialog>
    
    <el-dialog :visible.sync="dialogVisible">
      <img v-if="isImage" width="100%" :src="dialogFileUrl" alt="" />
      <video v-else width="100%" controls :src="dialogFileUrl" />
    </el-dialog>
  </div>
</template>
