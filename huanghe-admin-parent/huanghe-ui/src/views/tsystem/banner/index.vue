<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分组名称" prop="groupName">
        <el-select v-model="queryParams.groupName" placeholder="请选择分组" clearable size="small">
          <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择文件类型" clearable size="small">
          <el-option label="图片(JPG)" value="JPG" />
          <el-option label="视频(MP4)" value="MP4" />
          <el-option label="动图(GIF)" value="GIF" />
        </el-select>
      </el-form-item>
      <el-form-item label="地市" prop="area">
        <el-input v-model="queryParams.area" placeholder="请输入地市" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="上架" value="0" />
          <el-option label="下架" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['tsystem:banner:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['tsystem:banner:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['tsystem:banner:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['tsystem:banner:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <el-table-column min-width="30" align="center" label="序号">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分组名称" align="center" prop="groupName" width="120">
        <template slot-scope="scope">
          <el-tag :type="getGroupTagType(scope.row.groupName)">{{ getGroupName(scope.row.groupName) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" align="center" prop="fileType" width="100">
        <template slot-scope="scope">
          <el-tag :type="getFileTypeTagType(scope.row.fileType)">{{ scope.row.fileType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容介绍" align="center" prop="content" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div v-html="scope.row.content" style="max-height: 50px; overflow: hidden;"></div>
        </template>
      </el-table-column>
      <el-table-column label="地市" align="center" prop="area" width="100" />
      <el-table-column label="排序" align="center" prop="display" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createbyName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['tsystem:banner:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['tsystem:banner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 动态表单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="dynamicRules" label-width="100px">
        <!-- 分组选择 -->
        <el-form-item label="分组名称" prop="groupName">
          <el-select v-model="form.groupName" placeholder="请选择分组" style="width: 300px" @change="handleGroupChange">
            <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">{{ getGroupDescription(form.groupName) }}</span>
        </el-form-item>

        <!-- 排序字段 - 所有分组都需要 -->
        <el-form-item label="排序" prop="display">
          <el-input-number v-model="form.display" :min="1" :max="999" style="width: 200px" />
        </el-form-item>

        <!-- G4-地图分组：需要地市字段 -->
        <el-form-item v-if="form.groupName === 'G4'" label="地市" prop="area">
          <el-input v-model="form.area" placeholder="请输入地市" maxlength="50" style="width: 300px" />
        </el-form-item>

        <!-- G2-书卷、G4-地图分组：需要富文本内容介绍 -->
        <el-form-item v-if="form.groupName === 'G2' || form.groupName === 'G4'" label="内容介绍" prop="content">
          <RichEditor v-model="form.content" placeholder="请输入内容介绍..." />
        </el-form-item>

        <!-- 文件上传 - 根据分组显示不同的上传组件 -->
        <el-form-item :label="getUploadLabel()" prop="filePath">
          <!-- G1-视频：上传视频文件 -->
          <div v-if="form.groupName === 'G1'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              :http-request="requestUpload"
              :before-upload="beforeVideoUpload"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="video/mp4">
              <el-button size="small" type="primary">选择视频文件</el-button>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">只能上传MP4格式视频文件，且不超过100MB</span>
              </div>
            </el-upload>
          </div>

          <!-- G2-书卷、G4-地图：上传图片 -->
          <div v-else-if="form.groupName === 'G2' || form.groupName === 'G4'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              list-type="picture-card"
              :http-request="requestUpload"
              :before-upload="beforeImageUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="image/jpeg,image/jpg,image/png">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">只能上传JPG/PNG格式图片，建议尺寸1920*700像素，不超过5MB</span>
              </div>
            </el-upload>
          </div>

          <!-- G3-专题：上传GIF图片 -->
          <div v-else-if="form.groupName === 'G3'">
            <el-upload
              v-loading="uploadLoading"
              ref="upload"
              action="#"
              list-type="picture-card"
              :http-request="requestUpload"
              :before-upload="beforeGifUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
              class="upload-file"
              accept="image/gif">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: #f56c6c">只能上传GIF格式图片，不超过10MB</span>
              </div>
            </el-upload>
          </div>

          <!-- 未选择分组时的提示 -->
          <div v-else style="text-align: center; padding: 20px; border: 1px dashed #d9d9d9; color: #999;">
            请先选择分组类型
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <el-dialog :visible.sync="dialogVisible">
      <img v-if="isImage" width="100%" :src="dialogFileUrl" alt="" />
      <video v-else width="100%" controls :src="dialogFileUrl" />
    </el-dialog>
  </div>
</template>
