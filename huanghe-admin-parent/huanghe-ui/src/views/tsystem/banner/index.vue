<template>
  <div class="app-container">
    <div class="top">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="70px"
      >
        <el-form-item
          label="名称"
          prop="imageName"
        >
          <el-input
            v-model="queryParams.imageName"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row
      :gutter="10"
      class="mb8"
    >
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tsystem:banner:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tsystem:banner:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="bannerList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :selectable="selectable"
      />
      <el-table-column
        min-width="30"
        align="center"
        label="序号"
      >
        <template slot-scope="scope">
          <span>{{
              (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
            }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="名称"
        align="center"
        prop="imageName"
        width="500"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        width="200"
      >
      <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)" :disabled="scope.row.status === '0'"></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tsystem:banner:edit']"
            :disabled="scope.row.status === '0'||scope.row.imageName==='默认banner'"
          >修改</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tsystem:banner:remove']"
            :disabled="scope.row.status === '0'||scope.row.imageName==='默认banner'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改轮播图对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="名称:"
          prop="imageName"
          style="width: 400px;"
        >
          <el-input
            placeholder="请输入"
            v-model="form.imageName"
            maxlength="20"
          show-word-limit
          />
        </el-form-item>
        <el-form-item
          label="banner:"
          prop="imagePath"
        >
          <el-upload
           v-loading="uploadLoading"
            ref="upload"
            action="#"
            list-type="picture-card"
            :http-request="requestUpload"
            :before-upload="beforeUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            class="upload-image"
          >
            <i class="el-icon-plus"></i>
            <div
              class="el-upload__tip"
              slot="tip"
            >
              <span style="color: #f56c6c">可以上传1张,建议图片尺寸不能超过1920*700像素，支持格式 PNG，JPG，GIF。</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="submitForm"
        >确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 点击图片查看详情 -->
    <el-dialog :visible.sync="dialogVisible">
      <img
        width="100%"
        :src="dialogImageUrl"
        alt
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listBanner,
  getBanner,
  delBanner,
  addBanner,
  updateBanner,
  exportBanner,
  uploadAvatar,
  changeStatus
} from "@/api/tsystem/banner";

export default {
  name: "Banner",
  data() {
    return {
      // 遮罩层
      loading: true,
      uploadLoading:false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 轮播图表格数据
      bannerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,

      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        imagePath: [
          { required: true, message: "图片不能为空", trigger: "change" },
        ],
        imageName: [
          { required: true, message: "名称不能为空", trigger: "change" },
        ],
      },
      dialogImageUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传图片地址
      fileList: [],
    };
  },
  created() {
    this.getList();
    this.getDicts("publish_status").then((response) => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    /** 查询轮播图列表 */
    getList() {
      this.loading = true;
      listBanner(this.queryParams).then((response) => {
        this.bannerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        imageName: null,
        imagePath: null,
        display: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加轮播图";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.imagePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.imagePath;
          console.log(obj.url)
          this.fileList.push(obj);
        }
        if (!this.form.imagePath) {
          this.fileList = [];
        }
        this.open = true;
        this.title = "修改轮播图";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBanner(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addBanner(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                this.fileList = [];
                this.form.imagePath = null;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除所选数据项？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delBanner(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有轮播图数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportBanner(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () {});
    },
    //图片上传
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      this.uploadLoading=true;
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.uploadLoading=false;
          this.form.imagePath = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },
    // 校验上传类型
    beforeUpload(file) {
      const fileName = file.name;
      var fileExtension = fileName.split(".").pop().toLowerCase();
      if (fileExtension !== "jpg" && fileExtension !== "png"&&fileExtension !== "gif") {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG，GIF后缀的文件。");
        return false;
      }
      // const isLt2M = file.size / 1024 / 1024 < 10; // 判断图片大小是否小于2MB
      // if (!isLt2M) {
      //   this.$message.error("图片大小不能超过10MB！");
      //   return false; // 阻止上传
      // }
    },
    // 显示图片详情
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 清空图片
    handleRemove(file, fileList) {
      this.form.coverUrl = "";
    },
    //图片个数校验
    handleExceed(files, fileList) {
      this.msgError("最多上传1张图片");
    },

    //修改状态
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$confirm("确认更换banner样式?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return changeStatus(row.id, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
          this.getList();
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
          this.getList();
        });
    },
    selectable(row,index) {

      if(row.status === '0'||row.imageName==='默认banner'){
        return false
      }else{
        return true
      }
    }
  },
};
</script>

<style scoped>
::v-deep .el-upload-list__item {
  transition: none !important;
}
</style>
