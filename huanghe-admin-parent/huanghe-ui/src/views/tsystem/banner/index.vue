<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分组名称" prop="groupName">
        <el-select v-model="queryParams.groupName" placeholder="请选择分组" clearable size="small">
          <el-option v-for="item in groupOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="上架" value="0"/>
          <el-option label="下架" value="1"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['tsystem:banner:add']">{{ buttonText.add }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                   v-hasPermi="['tsystem:banner:edit']">{{ buttonText.edit }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                   v-hasPermi="['tsystem:banner:remove']">{{ buttonText.delete }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['tsystem:banner:export']">{{ buttonText.export }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="selectable"/>
      <el-table-column min-width="20" align="center" label="序号">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分组名称" align="center" prop="groupName" width="120">
        <template slot-scope="scope">
          <el-tag :type="getGroupTagType(scope.row.groupName)">{{ getGroupName(scope.row.groupName) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容介绍" align="center" prop="content" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div class="ellipsis-content">{{ stripTags(scope.row.content) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="display" width="80"/>
      <el-table-column label="状态" align="center" prop="status" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                     :loading="scope.row.loading"
                     @change="handleStatusChange(scope.row)"
                     active-text="上架" inactive-text="下架"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createbyName" width="100"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['tsystem:banner:edit']">{{ buttonText.edit }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['tsystem:banner:remove']">{{ buttonText.delete }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="dynamicRules" label-width="100px">
        <el-form-item label="分组名称" prop="groupName">
          <el-select v-model="form.groupName" placeholder="请选择分组" style="width: 300px" @change="handleGroupChange">
            <el-option v-for="item in groupOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
          </el-select>
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">{{ getGroupDescription(form.groupName) }}</span>
        </el-form-item>

        <el-form-item label="排序" prop="display">
          <el-input-number v-model="form.display" :min="1" :max="999" style="width: 200px"/>
        </el-form-item>

        <el-form-item v-if="form.groupName === 'G4'" label="地市" prop="area">
          <el-input v-model="form.area" placeholder="请输入地市" maxlength="50" style="width: 300px"/>
        </el-form-item>

        <el-form-item v-if="form.groupName === 'G2' || form.groupName === 'G4'" label="内容介绍" prop="content">
          <div style="border: 1px solid #dcdfe6;">
            <Toolbar
              style="border-bottom: 1px solid #dcdfe6"
              :editor="editor"
              :defaultConfig="toolbarConfig"
              mode="default"
            />
            <Editor
              style="height: 300px; overflow-y: hidden;"
              v-model="editorContent"
              :defaultConfig="editorConfig"
              mode="default"
              @onCreated="handleEditorCreated"
              @onChange="handleEditorChange"
            />
          </div>
        </el-form-item>

        <el-form-item :label="getUploadLabel()" prop="filePath">
          <div v-if="form.groupName === 'G1' || form.groupName === 'G2' || form.groupName === 'G3' || form.groupName === 'G4'">
            <el-upload
                v-loading="uploadLoading"
                ref="upload"
                action="#"
                :http-request="requestUpload"
                :before-upload="form.groupName === 'G1' ? beforeVideoUpload : (form.groupName === 'G3' ? beforeGifUpload : beforeImageUpload)"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                :limit="1"
                :on-exceed="handleExceed"
                :file-list="fileList"
                list-type="picture-card"
                accept="image/jpeg,image/jpg,image/png,video/mp4,image/gif">
              <template #file="{file}">
                <div style="width:100px;height:100px;display:flex;align-items:center;justify-content:center;">
                  <template v-if="file.url && file.url.endsWith('.mp4')">
                    <i class="el-icon-video-camera" style="font-size:48px;color:#409EFF;"></i>
                  </template>
                  <template v-else>
                    <img :src="file.url" style="max-width:100%;max-height:100%;object-fit:cover;" />
                  </template>
                </div>
              </template>
              <i v-if="form.groupName === 'G1'" class="el-icon-video-camera" style="font-size: 32px; color: #409EFF;"></i>
              <i v-else class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span v-if="form.groupName === 'G1'" style="color: #f56c6c">只能上传MP4格式视频文件，且不超过100MB</span>
                <span v-else-if="form.groupName === 'G3'" style="color: #f56c6c">只能上传GIF格式图片，不超过10MB</span>
                <span v-else style="color: #f56c6c">只能上传JPG/PNG格式图片，建议尺寸1920*700像素，不超过5MB</span>
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">{{ buttonText.confirm }}</el-button>
        <el-button @click="handleCancel">{{ buttonText.cancel }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible">
      <img v-if="isImage" width="100%" :src="dialogFileUrl" alt=""/>
      <video v-else width="100%" controls :src="dialogFileUrl"/>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBanner,
  getBanner,
  delBanner,
  addBanner,
  updateBanner,
  exportBanner,
  uploadAvatar,
  changeStatus
} from "@/api/tsystem/banner";
import { debounce } from 'lodash';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "Banner",
  components: { Editor, Toolbar },
  data() {
    return {
      loading: true,
      uploadLoading: false,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      bannerList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
        status: undefined
      },
      form: {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: "0",
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null
      },
      groupOptions: [],
      dialogFileUrl: "",
      dialogVisible: false,
      fileList: [],
      isImage: true,
      buttonText: {
        confirm: '确定',
        cancel: '取消',
        delete: '删除',
        export: '导出',
        add: '新增',
        edit: '修改'
      },
      // 编辑器实例
      editor: null,
      // 编辑器内容
      editorContent: '',
      // 编辑器配置
      editorConfig: {
        placeholder: '请输入内容介绍...',
        MENU_CONF: {
          uploadImage: {
            server: process.env.VUE_APP_BASE_API + '/common/upload',
            fieldName: 'uploadfile',
            maxFileSize: 5 * 1024 * 1024,
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            customInsert(res, insertFn) {
              if (res.code === 200) {
                insertFn(res.url, '', res.url);
              } else {
                this.$message.error('图片上传失败');
              }
            }
          }
        }
      },
      // 工具栏配置
      toolbarConfig: {
        excludeKeys: [
          'uploadVideo',
          'insertTable',
          'codeBlock',
          'todo'
        ]
      }
    };
  },
  computed: {
    dynamicRules() {
      const baseRules = {
        groupName: [
          { required: true, message: "请选择分组名称", trigger: "change" }
        ],
        display: [
          { required: true, message: "请输入排序号", trigger: "blur" },
          { type: 'number', message: "排序号必须为数字", trigger: "blur" }
        ]
      };

      // 根据分组类型添加不同的验证规则
      if (this.form.groupName === 'G4') {
        baseRules.area = [
          { required: true, message: "请输入地市", trigger: "blur" },
          { min: 2, max: 50, message: "地市长度必须在2到50个字符之间", trigger: "blur" }
        ];
      }

      if (this.form.groupName === 'G2' || this.form.groupName === 'G4') {
        baseRules.content = [
          { required: true, message: "请输入内容介绍", trigger: "blur" },
          { min: 10, message: "内容介绍不能少于10个字符", trigger: "blur" }
        ];
      }

      return baseRules;
    }
  },
  watch: {
    'form.content': {
      handler(val) {
        this.editorContent = val || '';
      },
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getDicts("sys_banner_group").then(response => {
      this.groupOptions = response.data;
    });
  },
  methods: {
    getList() {
      this.loading = true;
      console.log('查询参数:', this.queryParams);
      listBanner(this.queryParams)
        .then((response) => {
          console.log('返回数据:', response);
          this.bannerList = response.rows;
          this.total = response.total;
        })
        .catch(error => {
          console.error('获取数据失败:', error);
          this.msgError("获取数据失败：" + (error.message || "未知错误"));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getGroupName(groupName) {
      const group = this.groupOptions.find(item => item.dictValue === groupName);
      return group ? group.dictLabel : groupName;
    },
    getGroupTagType(groupName) {
      const typeMap = {'G1': 'primary', 'G2': 'success', 'G3': 'warning', 'G4': 'info'};
      return typeMap[groupName] || '';
    },
    getGroupDescription(groupName) {
      const group = this.groupOptions.find(item => item.dictValue === groupName);
      return group ? group.remark : '请选择分组';
    },
    getUploadLabel() {
      const labels = {'G1': '视频:', 'G2': '图片文件:', 'G3': 'GIF文件:', 'G4': '图片文件:'};
      return labels[this.form.groupName] || '文件上传:';
    },
    handleGroupChange(value) {
      this.fileList = [];
      this.form.filePath = null;
      this.form.content = null;
      this.form.area = null;
    },
    handleContentChange: debounce(function(event) {
      this.form.content = event.target.innerHTML;
    }, 300),
    handleCancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: "0",
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null
      };
      this.fileList = [];
      this.editorContent = '';
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    selectable(row, index) {
      return true;
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加轮播图";
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.filePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.filePath;
          this.fileList.push(obj);
        }
        if (!this.form.filePath) {
          this.fileList = [];
        }
        this.open = true;
        this.title = "修改轮播图";
        this.$nextTick(() => {
          if ((this.form.groupName === 'G2' || this.form.groupName === 'G4') && this.$refs.editor) {
            this.$refs.editor.innerHTML = this.form.content || '';
          }
        });
      });
    },
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) {
          this.msgError("请完善必填信息");
          return;
        }

        // 校验文件上传
        if (!this.form.filePath) {
          this.msgError("请上传文件");
          return;
        }

        // 校验内容介绍
        if ((this.form.groupName === 'G2' || this.form.groupName === 'G4') && !this.form.content) {
          this.msgError("请填写内容介绍");
          return;
        }

        // 校验地市
        if (this.form.groupName === 'G4' && !this.form.area) {
          this.msgError("请填写地市");
          return;
        }

        try {
          this.loading = true;
          if (this.form.id != null) {
            await updateBanner(this.form);
            this.msgSuccess("修改成功");
          } else {
            await addBanner(this.form);
            this.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        } catch (error) {
          this.msgError("操作失败：" + (error.message || "未知错误"));
        } finally {
          this.loading = false;
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('确认删除选中的数据项?', "警告", {
        confirmButtonText: this.buttonText.confirm,
        cancelButtonText: this.buttonText.cancel,
        type: "warning"
      }).then(async () => {
        await delBanner(ids);
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(error => {
        if (error !== 'cancel') {
          this.msgError("删除失败：" + (error.message || "未知错误"));
        }
      });
    },
    handleExport() {
      this.$confirm("确认导出所有数据?", "警告", {
        confirmButtonText: this.buttonText.confirm,
        cancelButtonText: this.buttonText.cancel,
        type: "warning"
      }).then(async () => {
        const response = await exportBanner(this.queryParams);
        this.download(response.msg);
      }).catch(error => {
        if (error !== 'cancel') {
          this.msgError("导出失败：" + (error.message || "未知错误"));
        }
      });
    },
    requestUpload(val) {
      const formData = new FormData();
      formData.append("uploadfile", val.file);
      this.uploadLoading = true;
      
      uploadAvatar(formData)
        .then((response) => {
          if (response.code === 200) {
            this.form.filePath = response.filePath;
            // 根据分组自动设置fileType
            if (this.form.groupName === 'G1') {
              this.form.fileType = 'MP4';
            } else if (this.form.groupName === 'G3') {
              this.form.fileType = 'GIF';
            } else {
              this.form.fileType = 'JPG';
            }
            this.msgSuccess("上传成功");
          } else {
            this.msgError(response.msg || "上传失败");
          }
        })
        .catch(error => {
          this.msgError("上传失败：" + (error.message || "未知错误"));
        })
        .finally(() => {
          this.uploadLoading = false;
        });
    },
    handleRemove() {
      this.form.filePath = "";
      this.fileList = [];
    },
    handlePictureCardPreview(file) {
      let url = file.url;
      if (url && !/^https?:\/\//.test(url)) {
        url = process.env.VUE_APP_BASE_API + url;
      }
      if (url && url.endsWith('.mp4')) {
        this.dialogFileUrl = url;
        this.isImage = false;
      } else {
        this.dialogFileUrl = url;
        this.isImage = true;
      }
      this.dialogVisible = true;
    },
    beforeVideoUpload(file) {
      const isMP4 = file.type === 'video/mp4';
      const isLt100M = file.size / 1024 / 1024 < 100;
      
      if (!isMP4) {
        this.msgError('只能上传MP4格式的视频文件！');
        return false;
      }
      if (!isLt100M) {
        this.msgError('视频大小不能超过100MB！');
        return false;
      }
      return true;
    },
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;
      
      if (!isJPG) {
        this.msgError('只能上传JPG/PNG格式的图片文件！');
        return false;
      }
      if (!isLt5M) {
        this.msgError('上传图片大小不能超过5MB！');
        return false;
      }
      return true;
    },
    beforeGifUpload(file) {
      const isGIF = file.type === 'image/gif';
      const isLt10M = file.size / 1024 / 1024 < 10;
      
      if (!isGIF) {
        this.msgError('只能上传GIF格式的图片文件！');
        return false;
      }
      if (!isLt10M) {
        this.msgError('上传GIF大小不能超过10MB！');
        return false;
      }
      return true;
    },
    handleExceed() {
      this.msgError("最多上传1个文件");
    },
    async handleStatusChange(row) {
      try {
        row.loading = true;
        await changeStatus(row.id, row.status);
        this.msgSuccess(row.status === "0" ? "上架成功" : "下架成功");
      } catch (error) {
        this.msgError("操作失败：" + (error.message || "未知错误"));
        row.status = row.status === "0" ? "1" : "0";
      } finally {
        row.loading = false;
      }
    },
    // 编辑器创建完成时的回调
    handleEditorCreated(editor) {
      this.editor = editor;
    },

    // 编辑器内容变化时的回调
    handleEditorChange(editor) {
      this.form.content = editor.getHtml();
    },

    // 组件销毁时，也及时销毁编辑器
    beforeDestroy() {
      if (this.editor) {
        this.editor.destroy();
        this.editor = null;
      }
    },
    stripTags(html) {
      if (!html) return '';
      return html.replace(/<[^>]+>/g, '').replace(/\n/g, ' ');
    },
  },
};
</script>

<style scoped>
.upload-file .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.upload-file .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.el-tag {
  margin-right: 5px;
}

.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

/* 编辑器样式覆盖 */
.w-e-text-container {
  height: 300px !important;
}
.w-e-toolbar {
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

.ellipsis-content {
  max-width: 100%;
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
</style>
