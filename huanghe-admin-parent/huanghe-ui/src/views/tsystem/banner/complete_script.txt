import { listBanner, getBanner, delBanner, addBanner, updateBanner, exportBanner, uploadAvatar, changeStatus } from "@/api/tsystem/banner";

export default {
  name: "Banner",
  data() {
    return {
      loading: true,
      uploadLoading: false,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      bannerList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        fileType: null,
        area: null,
        status: null
      },
      form: {},
      groupOptions: [
        { value: 'G1', label: '视频' },
        { value: 'G2', label: '书卷' },
        { value: 'G3', label: '专题' },
        { value: 'G4', label: '地图' }
      ],
      dialogFileUrl: "",
      dialogVisible: false,
      fileList: [],
      isImage: true,
    };
  },
  computed: {
    dynamicRules() {
      const baseRules = {
        groupName: [{ required: true, message: "分组名称不能为空", trigger: "change" }],
        filePath: [{ required: true, message: "文件不能为空", trigger: "change" }],
        display: [{ required: true, message: "排序不能为空", trigger: "blur" }],
      };
      
      if (this.form.groupName === 'G4') {
        baseRules.area = [{ required: true, message: "地市不能为空", trigger: "blur" }];
      }
      
      if (this.form.groupName === 'G2' || this.form.groupName === 'G4') {
        baseRules.content = [{ required: true, message: "内容介绍不能为空", trigger: "blur" }];
      }
      
      return baseRules;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listBanner(this.queryParams).then((response) => {
        this.bannerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getGroupName(groupName) {
      const group = this.groupOptions.find(item => item.value === groupName);
      return group ? group.label : groupName;
    },
    getGroupTagType(groupName) {
      const typeMap = {
        'G1': 'primary',
        'G2': 'success',
        'G3': 'warning',
        'G4': 'info'
      };
      return typeMap[groupName] || '';
    },
    getFileTypeTagType(fileType) {
      const typeMap = {
        'MP4': 'primary',
        'JPG': 'success',
        'PNG': 'success',
        'GIF': 'warning'
      };
      return typeMap[fileType] || '';
    },
    getGroupDescription(groupName) {
      const descriptions = {
        'G1': '上传视频文件',
        'G2': '上传图片 + 编写内容介绍',
        'G3': '上传GIF图片',
        'G4': '上传图片 + 编写内容介绍 + 填写地市'
      };
      return descriptions[groupName] || '请选择分组';
    },
    getUploadLabel() {
      const labels = {
        'G1': '视频文件:',
        'G2': '图片文件:',
        'G3': 'GIF文件:',
        'G4': '图片文件:'
      };
      return labels[this.form.groupName] || '文件上传:';
    },
    handleGroupChange(value) {
      this.fileList = [];
      this.form.filePath = null;
      this.form.content = null;
      this.form.area = null;
      
      if (value === 'G1') {
        this.form.fileType = 'MP4';
      } else if (value === 'G3') {
        this.form.fileType = 'GIF';
      } else {
        this.form.fileType = 'JPG';
      }
    },
    handleContentChange(event) {
      this.form.content = event.target.innerHTML;
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        groupName: null,
        filePath: null,
        fileType: null,
        content: null,
        linkUrl: null,
        display: 1,
        area: null,
        status: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.fileList = [];
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    selectable(row, index) {
      return row.status !== '0';
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加轮播图";
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBanner(id).then((response) => {
        this.form = response.data;
        if (this.form.filePath != null) {
          let obj = new Object();
          obj.url = process.env.VUE_APP_BASE_API + this.form.filePath;
          this.fileList.push(obj);
        }
        if (!this.form.filePath) {
          this.fileList = [];
        }
        this.open = true;
        this.title = "修改轮播图";
        
        this.$nextTick(() => {
          if ((this.form.groupName === 'G2' || this.form.groupName === 'G4') && this.$refs.editor) {
            this.$refs.editor.innerHTML = this.form.content || '';
          }
        });
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBanner(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBanner(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除轮播图编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        return delBanner(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有轮播图数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        return exportBanner(queryParams);
      }).then((response) => {
        this.download(response.msg);
      }).catch(function () {});
    },
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      this.uploadLoading = true;
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.uploadLoading = false;
          this.form.filePath = response.filePath;
          this.form.fileType = response.fileType;
          this.msgSuccess("上传成功");
        }
      });
    },
    handleRemove(file, fileList) {
      this.form.filePath = "";
      this.fileList = [];
    },
    handlePictureCardPreview(file) {
      this.dialogFileUrl = file.url;
      this.isImage = this.form.fileType !== 'MP4';
      this.dialogVisible = true;
    },
    beforeVideoUpload(file) {
      const isMP4 = file.type === 'video/mp4';
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isMP4) {
        this.msgError('只能上传MP4格式的视频文件!');
        return false;
      }
      if (!isLt100M) {
        this.msgError('上传视频大小不能超过 100MB!');
        return false;
      }
      return true;
    },
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isJPG) {
        this.msgError('只能上传JPG/PNG格式的图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.msgError('上传图片大小不能超过 5MB!');
        return false;
      }
      return true;
    },
    beforeGifUpload(file) {
      const isGIF = file.type === 'image/gif';
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isGIF) {
        this.msgError('只能上传GIF格式的图片文件!');
        return false;
      }
      if (!isLt10M) {
        this.msgError('上传GIF大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    handleExceed(files, fileList) {
      this.msgError("最多上传1个文件");
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "上架" : "下架";
      this.$confirm('确认要"' + text + '""' + row.id + '"吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        return changeStatus(row.id, row.status);
      }).then(() => {
        this.msgSuccess(text + "成功");
        this.getList();
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
  },
};
</script>

<style scoped>
.upload-file .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.upload-file .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.el-tag {
  margin-right: 5px;
}

.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
