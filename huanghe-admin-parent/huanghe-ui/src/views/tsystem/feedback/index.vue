<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
        <el-form-item label="反馈内容" prop="recContent">
          <el-input v-model="queryParams.recContent" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="昵称" prop="userName" label-width="40px">
          <el-input v-model="queryParams.userName" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="手机号" prop="userTel" label-width="55px">
          <el-input v-model="queryParams.userTel" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="手机号" prop="userTel" label-width="55px">
          <el-input v-model="queryParams.userTel" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->

        <!-- <el-form-item label="反馈时间">
          <el-date-picker
            v-model="dateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item> -->
        <el-form-item label="处理进度" prop="handleStatus"  label-width="68px">
          <el-select v-model="queryParams.handleStatus" placeholder="请选择" clearable size="small" style="width: 150px">
            <el-option v-for="dict in handleStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['tsystem:feedback:remove']">删除</el-button>
      </el-col>
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table style="margin-top: 30px" v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange"
    :row-style="{ height: '50px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 300px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="30" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column> 
      <el-table-column label="反馈内容" align="center" prop="recContent" show-overflow-tooltip>

        <!-- <template slot-scope="scope">
          <span>{{ scope.row.recContent | ellipsis }}</span>
        </template> -->

      </el-table-column>>

      <el-table-column label="昵称" align="center" prop="userName" />
      <!-- <el-table-column
        label="反馈内容"
        align="center"
        prop="recContent"
        min-width="250"
      >
        <template slot-scope="{ row }">
          <span v-if="row.recContent && row.recContent.length > 30">
            <el-popover
              placement="top-start"
              title="反馈内容"
              width="800"
              trigger="hover"
            >
              <div>{{ row.recContent }}</div>
              <span slot="reference">
                {{ row.recContent.substr(0, 30) + "..." }}
              </span>
            </el-popover>
          </span>
          <span v-else>{{ row.recContent }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="手机号" align="center" prop="userTel" />

      <el-table-column label="反馈时间" align="center" prop="createTime" width="180" sortable>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="邮箱"
        align="center"
        prop="userEmail"
        :show-overflow-tooltip="true"
      /> -->
      <!-- <el-table-column
        label="反馈来源"
        align="center"
        prop="dataFrom"
        :formatter="dataFromFormat"
      /> -->
      <el-table-column label="处理进度" align="center" prop="handleStatus" sortable>
        <template slot-scope="scope">
          <el-tag :type="scope.row.handleStatus == '0' ? '' : 'success'">
            {{ scope.row.handleStatus == "0" ? "未回复" : "已回复" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="处理人" align="center" prop="handlebyName" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.handleStatus === 0" type="text" icon="el-icon-chat-line-round"
            @click="handleReply(scope.row)" v-hasPermi="['tsystem:feedback:reply']">回复
          </el-button>
          <el-button v-if="scope.row.handleStatus === 1" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
            v-hasPermi="['tsystem:feedback:query']">查看
          </el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['tsystem:feedback:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 反馈回复对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="700px" append-to-body>
      <div class="formDiv">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="24">
              <!-- <span class="catalog_title">反馈内容:</span><br />
              <span class="catalog_value">{{ form.recContent }}</span> -->
              <el-form-item label="反馈内容" prop="recContent">
                <el-input type="textarea" :rows="5" resize='none' v-model="form.recContent" disabled />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="用户名" prop="userName">
                <el-input v-model="form.userName" disabled />
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="12">
              <span class="catalog_title">反馈时间:</span>
              <span class="catalog_value">{{ form.createTime }}</span>

              <!-- <el-form-item label="反馈时间" prop="createTime">
                <el-input v-model="form.createTime" disabled />
              </el-form-item> -->
            </el-col>
            <el-col :span="12">
              <!-- <span class="catalog_title">来源:</span>
              <span class="catalog_value">{{ form.bookName }}&nbsp;&nbsp;{{ form.bookMenuName }}</span> -->

              <!-- <el-form-item label="来源" prop="dataFrom">
                <el-row v-if="this.form.dataFrom == 'P'">
                  <div>平台</div>
                </el-row>
                <el-row v-else>
                  <div>小程序</div>
                </el-row>
              </el-form-item> -->
            </el-col>
          </el-row>

          <el-row>
            <el-form-item v-if="this.form.handleStatus === 0" label="回复内容" prop="feedbackContent">
              <el-input type="textarea" :rows="6" resize='none' :maxlength="200" show-word-limit
                v-model="form.feedbackContent" placeholder="请输入回复内容" />
            </el-form-item>
            <el-form-item v-else label="回复内容">
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" :rows="6" :maxlength="150"
                v-model="form.feedbackContent" disabled />
            </el-form-item>
          </el-row>
          <el-row v-if="this.form.handleStatus === 1">
            <el-col :span="12">
              <span class="catalog_title">回复时间:</span>
              <span class="catalog_value">{{ form.handleTime }}</span>

              <!-- <el-form-item label="回复人" prop="handlebyName">
                <el-input v-model="form.handlebyName" disabled />
              </el-form-item> -->
            </el-col>
            <el-col :span="12">
              <span class="catalog_title">回复人:</span>
              <span class="catalog_value">{{ form.handlebyName }}</span>

              <!-- <el-form-item label="回复时间" prop="handleTime">
                <el-input v-model="form.handleTime" disabled />
              </el-form-item> -->
            </el-col>
          </el-row>
        </el-form>

      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="this.form.handleStatus === 0" type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFeedback,
  listFeedback,
  updateFeedback,
  delFeedback,
} from "@/api/tsystem/feedback";

export default {
  name: "Feedback",
  data() {
    return {
      tableHeight: window.innerHeight - 395,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 意见反馈表格数据
      feedbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        handleStatus: null,
        userPhone: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        feedbackContent: [
          { required: true, message: "回复内容不能为空", trigger: "blur" },
        ],
      },
      // 处理状态数据字典
      handleStatusOptions: [],
      //反馈来源数据字典
      dataFromOptions: [],
      // 日期范围
      dateRange: [],
    };
  },
  created() {
    this.getList();
    this.getDicts("handle_status").then((response) => {
      this.handleStatusOptions = response.data;
    });
    this.getDicts("data_from").then((response) => {
      this.dataFromOptions = response.data;
    });
  },
  filters: {
    ellipsis(value) {
      if (!value) return "";
      if (value.length > 30) {
        return value.slice(0, 30) + "...";
      }
      return value;
    }
  },
  methods: {
    /** 删除 */
    /** 删除按钮操作 */
    handleDelete(row) {
      console.log(row.id);
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除所选数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delFeedback(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    /** 查询意见反馈列表 */
    getList() {
      this.loading = true;
      listFeedback(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.feedbackList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 数据状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.handleStatusOptions, row.handleStatus);
    },
    //反馈来源字典翻译
    dataFromFormat(row, column) {
      return this.selectDictLabel(this.dataFromOptions, row.dataFrom);
    },
    dataFromFormatForm(row, column) {
      return this.selectDictLabel(this.dataFromOptions, row.dataFrom);
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        recContent: null,
        userId: null,
        handleStatus: null,
        handlebyId: null,
        handlebyName: null,
        handleTime: null,
        dataFrom: null,
        feedbackContent: null,
        createTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 回复按钮操作 */
    handleReply(row) {
      //debugger;
      const id = row.id || this.ids;
      getFeedback(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "反馈回复";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFeedback(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("回复成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /**详情按钮**/
    handleDetail(row) {
      this.reset();
      const id = row.id || this.ids;
      getFeedback(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "反馈详情";
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-tooltip__popper {
  max-width: 1000px;
  line-height: 30px;
}

/*.el-tooltip__popper{
  display: none;
}
.el-tooltip{
  border: 1px solid #333;
  max-height: 150px!important;
  overflow-y: scroll!important;
  overflow-x:hidden!important;
  box-sizing: border-box;

}*/

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}

.catalog_value {
  margin-top: 30px;
  font-size: 14px;
  text-align: right;
    vertical-align: middle;
 
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-weight: bold;
}

.catalog_title {
  margin: 5px;
  border: 0;
  padding: 0;
  font-size: 14px;
  text-align: right;
    vertical-align: middle;
    font-weight: bold;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}


// .dialog-footer {
//   padding-top: 6px;
//   margin-top: -140px

// }
::v-deep .el-dialog__body {
    padding: 10px 20px;
}

.el-dialog-footer {
  padding:10px 20px;
  //padding-top: 5px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.el-textarea .el-input__count {
  background-color: transparent;
  bottom: -27px;
}
.mb8 {
    margin-bottom: -10px !important;
}
.tableS{
  margin-top: 0px;
}
.formDiv{
  height: calc(100vh - 500px);
  overflow-y: hidden;
}
</style>
