<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="标题" prop="newsTitle">
          <el-input v-model="queryParams.newsTitle" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="publishStatus" label-width="40px">
          <el-select v-model="queryParams.publishStatus" size="small" placeholder="请选择" clearable style="width: 240px">
            <el-option v-for="dict in publishStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['tsystem:news:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['tsystem:news:remove']">删除</el-button>
      </el-col>
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table style="margin-top: 30px" v-loading="loading" :data="newsList" @selection-change="handleSelectionChange"
    :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 310px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="60" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column> <el-table-column label="标题" align="center" prop="newsTitle" :show-overflow-tooltip="true"
        width="250" />
      <!-- <el-table-column label="发布时间" align="center" prop="publishTime" width="180" sortable>
        <template slot-scope="scope">

            <span>{{
                parseTime(scope.row.publishTime, "{y}-{m}-{d}")
              }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="阅读（次数）" align="center" prop="readCount">
        <template slot-scope="scope">
          <span>{{scope.row.readCount}}次</span>
        </template>
      </el-table-column> -->
      <el-table-column label="发布状态" align="center" prop="publishStatus" sortable>
        <template slot-scope="scope">
          <el-tag v-if="scope.row.publishStatus == '0'"> 下架 </el-tag>
          <el-tag v-if="scope.row.publishStatus == '1'" :type="'success'">
            上架
          </el-tag>
          <el-tag v-if="scope.row.publishStatus == '2'" :type="'warning'">
            已撤销
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="排序" align="center" prop="display" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.publishStatus === 0" type="text" icon="el-icon-top" @click="handlePublish(scope.row)"
            v-hasPermi="['tsystem:news:publish']">发布</el-button>
          <el-button v-if="scope.row.publishStatus === 2" type="text" icon="el-icon-top" @click="handlePublish(scope.row)"
            v-hasPermi="['tsystem:news:publish']">再次发布</el-button>
          <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['tsystem:news:edit']">编辑</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['tsystem:news:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改资讯管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="40%" append-to-body
      :before-close="cancel">
      <!-- <div class="formDiv"> -->
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="newsTitle" style="width: 400px;">
              <el-input maxlength="50" show-word-limit v-model="form.newsTitle" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="排序" prop="display" required="true">
              <el-input-number v-model="form.display" controls-position="right" :min="0" :max="20000" style="width: 250px;"/>
            </el-form-item>
          </el-col> -->
        </el-row>

        <el-form-item label="封面" prop="iconUrl" style="margin-top: 30px;">
          <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUpload"
            :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
            :on-exceed="handleExceed" :on-change="handleChange" :limit=1 :file-list="fileList"
            >
            <i class="el-icon-plus"></i>
            <div class="el-upload__tip" slot="tip">
              <span style="color: #f56c6c">可以上传1张，建议尺寸442*332像素。</span>
            </div>
          </el-upload>
        </el-form-item>


        <!-- <el-form-item label="来源" prop="source">
          <el-input maxlength="200" show-word-limit v-model="form.source" placeholder="请输入来源" />
        </el-form-item> -->

        <el-form-item label="描述" prop="newsContent" class="ms_lable">
          <Editor style="height: 300px;margin-top: -10px;" v-model="form.newsContent" maxlength="5000" show-word-limit />
        </el-form-item>

        <!-- <el-form-item label="状态" prop="publishStatus">
          <el-switch v-model="form.publishStatus" active-color="#13ce66" inactive-color="#000000" active-value="1"
            inactive-value="0">
          </el-switch>
        </el-form-item> -->

        <el-form-item label="状态" prop="publishStatus">

          <el-switch v-model="form.publishStatus" active-value="1" inactive-value="0" active-color="#13ce66"
            inactive-color="#000000">
          </el-switch>

        </el-form-item>
        <!-- <el-form-item label="发布时间" prop="publishTime"   style="padding-top: 40px;">
          <el-date-picker
          v-model="form.publishTime"
            align="right"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            >
          </el-date-picker>
        </el-form-item> -->
      </el-form>

      <!-- </div> -->
     
      <div slot="footer" class="dialog-footer" style="padding-top: 35px">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listNews,
  getNews,
  delNews,
  addNews,
  updateNews,
  exportNews,
  uploadAvatar,
  publishNews,
} from "@/api/tsystem/news";
import Editor from "@/components/Editor";

export default {
  name: "News",
  components: {
    Editor,
  },
  data() {
    return {
      tableHeight: window.innerHeight - 400,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资讯管理表格数据
      newsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        newsTitle: null,
        publishStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        newsTitle: [
          { required: true, message: "标题不能为空", trigger: "blur" },
        ],
        iconUrl: [
          { required: true, message: "封面不能为空", trigger: "blur" },
        ],
        newsContent: [
          { required: true, message: "描述不能为空", trigger: "blur" },
        ],
        // publishTime: [
        //   { required: true, message: "发布时间不能为空", trigger: "change" },
        // ],
        // source: [
        //   { required: true, message: "发布来源不能为空", trigger: "blur" },
        // ],
      },
      // 发布状态数据字典
      publishStatusOptions: [],
      //查看大图图片地址
      dialogImageUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传图片地址
      fileList: [],
      hideUpload: false,
    };
  },
  created() {
    this.getList();
    this.getDicts("publish_status").then((response) => {
      this.publishStatusOptions = response.data;
    });
  },
  methods: {
    /** 查询资讯管理列表 */
    getList() {
      this.loading = true;
      listNews(this.queryParams).then((response) => {
        this.newsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 数据状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.publishStatusOptions, row.publishStatus);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.hideUpload = false;
    },
    // 表单重置
    reset() {
      this.fileList = [];
      this.form = {
        display: 0,
        newsTitle: null,
        newsContent: null,
        iconUrl: null,
        publishStatus: 0,
      };
      this.resetForm("form");
      if (this.$refs.upload != undefined) {
        this.$refs.upload.clearFiles();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加资讯";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getNews(id).then((response) => {
        this.form = response.data;
        console.log(this.form)
        this.open = true;
        this.title = "编辑资讯";
        let obj = new Object();
        obj.url = process.env.VUE_APP_BASE_API + this.form.iconUrl;
        this.fileList.push(obj);
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.form)
      //提交表单
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateNews(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.reset();
                this.open = false;
                this.getList();
                this.hideUpload = false;
              }
            });
          } else {
            addNews(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.reset();
                this.open = false;
                this.getList();
                this.hideUpload = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除所选数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delNews(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    handlePublish(row) {
      this.$confirm("是否确认发布当前资讯?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return publishNews(row.id);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("发布成功");
        })
        .catch(function () { });
    },
    // 覆盖默认的上传行为
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.iconUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.form.iconUrl = "";
      this.hideUpload = fileList.length >= 1;
    },
    handlePictureCardPreview(file) {
      console.log(file);
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
    },
    //上传文件超出数量
    handleExceed(files, fileList) {
      this.msgError("最多上传1张图片");
    },
    handleChange(files, fileList) {
      this.hideUpload = fileList.length >= 1;
    },
  },
};
</script>
<style lang="scss" scoped>
/*去除upload组件过渡效果*/
.el-upload-list__item {
  transition: none !important;
}

.hide .el-upload--picture-card {
  display: none;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}





.el-form-item__label {
  line-height: 40px;
}
.mb8 {
    margin-bottom: -10px !important;
}
.tableS{
  margin-top: 0px;
}
.dialog-footer{
  margin-top: -95px;
}
// .formDiv{
//   height: calc(100vh - 300px);
//   overflow-y: hidden;
//   width: 100%;
 
// }
</style>
