<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="平台名称" prop="webName">
          <el-input v-model="queryParams.webName" placeholder="请输入平台名称" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="平台链接" prop="webUrl">
          <el-input
            v-model="queryParams.webUrl"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
            style="width: 350px; "
          />
        </el-form-item> -->
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['tsystem:link:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['tsystem:link:remove']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table style="margin-top:30px" v-loading="loading" :data="linkList" @selection-change="handleSelectionChange"
      :height="tableHeight">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="70" />
      <el-table-column label="平台名称" align="center" prop="webName" />
      <el-table-column label="平台链接" align="center" prop="appPath" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['tsystem:link:edit']">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['tsystem:link:remove']">删除</el-button>
          <el-button size="medium" type='text' style="font-size:20px;"
            @click.stop="configUp(scope.$index, scope.row)">↑</el-button>
          <el-button size="medium" type='text' style="font-size:20px;"
            @click.stop="configDown(scope.$index, scope.row)">↓</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改友情链接对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="平台名称" prop="webName">
          <el-input maxlength="100" show-word-limit v-model="form.webName" placeholder="请输入平台名称" />
        </el-form-item>

      
        <el-form-item label="appId" prop="appId">
          <el-input maxlength="100" show-word-limit v-model="form.appId" placeholder="请输入appId" />
        </el-form-item>
        <el-form-item label="appPath" prop="appPath">
          <el-input maxlength="100" show-word-limit v-model="form.appPath" placeholder="请输入appPath" />
        </el-form-item>
        <el-form-item label="webUrl" prop="webUrl">
          <el-input maxlength="100" show-word-limit v-model="form.webUrl" placeholder="请输入h5连接" />
        </el-form-item>


        <el-form-item label="图片封面" prop="iconUrl" style="margin-top: 30px;">
              <el-upload ref="upload" action="#" list-type="picture-card" :http-request="requestUpload"
                :before-upload="beforeUpload" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                :on-exceed="handleExceed" :on-change="handleChange" :limit="1" :file-list="fileListA" class="upload-image">
                <i class="el-icon-plus"> </i>
                <!-- <div class="el-upload__tip" slot="tip">
                  <span style="color: #f56c6c">可以上传1张,建议尺寸不能超过640×320像素。</span>
                </div> -->
              </el-upload>
            </el-form-item>
        <!-- <el-form-item label="平台链接" prop="webUrl">
          <el-input maxlength="200" show-word-limit type="textarea" :rows="3" v-model="form.webUrl"
            placeholder="请输入网站链接" />
        </el-form-item> -->
      </el-form>


      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLink, getLink, delLink, addLink, updateLink, exportLink, updateWebDisplay,uploadAvatar } from "@/api/tsystem/link";
import Vue from 'vue'

export default {
  name: "Link",
  data() {
    const isCover = (rule, value, callback) => {
      if (this.form.iconUrl == null || this.form.iconUrl == "") {
        callback(new Error("封面不能为空"));
      } else {
        callback();
      }
    };
    return {
      tableHeight: window.innerHeight - 315,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 友情链接表格数据
      linkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        webName: null,
        webUrl: null,
      },
      // 表单参数
      form: {},
      fileListA:[],
      // 表单校验
      rules: {
        // appId: [
        //   { required: true, message: "appId不能为空", trigger: "blur" }
        // ],
        // appPath: [
        //   { required: true, message: "appPath不能为空", trigger: "blur" }
        // ],
        webName: [
          { required: true, message: "网站名称不能为空", trigger: "blur" }
        ],
        // webUrl: [
        //   { required: true, message: "网站链接不能为空", trigger: "blur" }
        // ],
        iconUrl: [
          {
            validator: isCover,
            required: true,
            trigger: "blur",
          },
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询友情链接列表 */
    getList() {
      this.loading = true;
      listLink(this.queryParams).then(response => {
        this.linkList = response.rows;
        this.total = response.total;
        this.loading = false;
        console.log(response.rows);
      });

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.fileListA = [];
      this.form = {
        id: null,
        webName: null,
        webUrl: null,
        webDisplay: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
        iconUrl:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加合作平台";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLink(id).then(response => {
        this.form = response.data;
        if(this.form.iconUrl!=null){
        let obj = new Object();

        //obj.url = process.env.VUE_APP_BASE_API + this.form.coverUrl;
        if(this.form.iconUrl.includes("http")){
        obj.url =this.form.iconUrl;
        }else{
        obj.url = process.env.VUE_APP_BASE_API +this.form.iconUrl;
        }
        this.fileListA.push(obj);
      }
        console.log(this.form.iconUrl);
        // if (!this.form.iconUrl) {
        //   this.fileListA = [];
        // }
        this.open = true;
        this.title = "修改合作平台";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLink(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addLink(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除选中的友情链接?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delLink(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(function () { });
    },
    // 上移
    configUp(index, row) {
      if (index === 0) {
        this.$message({
          message: '已经是列表中第一行!',
          type: 'warning'
        })
      } else {
        const temp = this.linkList[index - 1];
        Vue.set(this.linkList, index - 1, this.linkList[index]);
        Vue.set(this.linkList, index, temp);
        //更新后台数据
        let param = {};
        param.display = row.id + ":" + row.webDisplay;//该行数据id
        param.displayAfter = temp.id + ":" + temp.webDisplay;  //上一行数据的id
        console.log(param);
        this.updateWebDisplay(param);
      }
    },
    // 下移
    configDown(index, row) {
      if (index === (this.linkList.length - 1)) {
        this.$message({
          message: '已经是列表中最后一行!',
          type: 'warning'
        })
      } else {
        const temp = this.linkList[index + 1];
        Vue.set(this.linkList, index + 1, this.linkList[index]);
        Vue.set(this.linkList, index, temp);
        //更新后台数据
        let param = {};
        param.display = row.id + ":" + row.webDisplay;//该行数据id
        param.displayAfter = temp.id + ":" + temp.webDisplay;  //下一行数据的id
        console.log(param);
        this.updateWebDisplay(param);
      }
    },
    updateWebDisplay(displayArray) {
      updateWebDisplay(displayArray).then(response => {
        console.log(response);
        if (response.code === 200) {
          this.msgSuccess("移动成功");
          this.getList();
        }
      });
    },



    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.iconUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },

    cancelRecourse() {
      this.openRecources = false;
      this.resetQuery();
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handleExceed(files, fileListA) {
      this.msgError("最多上传1张图片");
    },

    handleRemove(file, fileListA) {
      this.form.iconUrl = "";
      this.hideUpload = fileListA.length >= 1;
    },
    handleChange(files, fileListA) {
      this.hideUpload = fileListA.length >= 1;
    },
    beforeUpload(file) {
  
      const isJPG =
        file.type === "image/jpg" ||
        file.type === "image/png" ||
        file.type === "image/jpeg";
      //图片大小
      if (!isJPG) {     
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
    },
  }
};
</script>
<style type="text/css">
/*去除upload组件过渡效果*/
.el-upload-list__item {
  transition: none !important;
}
.hide .el-upload--picture-card {
  display: none;
}
.top {
  margin-left: 10px;
  margin-top: 20px;
}
</style>
