<template>
  <div class="app-container home" v-if="open">
    <div class="top-info-wrap">
      <div class="welcome-wrap">
        <div class="welcome-logo"></div>
        <div style="padding-left: 13px; flex: 1">
          <div style="font-size: 16px">
            <b>欢迎回来,{{ userName }}</b>
          </div>
        </div>
      </div>

    </div>


  </div>

</template>

<script>
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
import { checkPassword } from "@/api/login";
export default {
  name: "Index",
  dicts: ["DIGITAL_STATUS"],
  data() {
    return {
      // 版本号
      version: "1.0.0",
      userName: null,
      open: false
    };
  },
  created() {
    const name = this.$route.query.name;
    this.userName = Cookies.get("username")
    if (name == 'login') {
      this.password();
      setTimeout(() => {
        this.userName = Cookies.get("username")
        this.open = true;
      }, 500);
    }
  },

  methods: {
    password() {
      checkPassword().then(response => {
        let data = response.data;
        if (558 === data) {
          this.$notify.info({
            title: '消息',
            message: response.msg,
            duration: 0,
            position: 'bottom-right'
          });
        }
      })
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  .top-info-wrap {
    display: flex;

    .welcome-wrap {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 268px;
      height: 76px;
      padding: 14px 12px;
      background: #e7f7ff;
      border-radius: 8px;

      .welcome-logo {
        width: 41px;
        height: 41px;
        background-image: url(../assets/image/welcome.png);
        background-repeat: no-repeat;
        background-size: contain;
      }

      .amount {
        position: relative;

        &::after {
          position: absolute;
          left: -3px;
          bottom: -8px;
          content: "";
          display: inline-block;
          width: 15px;
          height: 5px;
          //background: #ee6d6d;
          border-radius: 3px;
        }
      }

      .icon-right {
        display: inline-block;
        width: 14px;
        height: 14px;
        //background-image: url(../assets/image/icon-right-arrow.png);
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
        top: 3px;
      }
    }

    .task-status-wrap {
      margin-left: 50px;
      display: flex;

      .task-item {
        position: relative;
        padding: 0 20px;
        font-size: 14px;
        font-weight: 500;

        &::after {
          position: absolute;
          right: 0;
          top: 6px;
          content: "";
          display: inline-block;
          width: 1px;
          height: 46px;
          border: 1px dashed #e5e5e5;
        }

        .header {
          color: #6d94ee;
          font-weight: bold;
        }

        .amount {
          text-align: center;
          font-weight: bold;
        }
      }
    }
  }

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  // color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 6px;

    b {
      font-weight: 700;
    }
  }

  .el-table {
    overflow-y: auto;
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .table-content {
    display: flex;

    .title-wrap {
      width: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      writing-mode: vertical-lr;
      font-size: 18px;
      background-color: #f2f6fc;
      margin-right: 10px;
    }

    .table-wrap {
      flex: 1;
    }
  }

  .table-content2 {
    .table-title {
      position: relative;
      color: #409eff;
      font-size: 14px;
      padding-left: 12px;
      margin-bottom: 20px;

      &::after {
        position: absolute;
        left: 0;
        bottom: -8px;
        content: "";
        display: inline-block;
        width: 103px;
        height: 5px;
        //background-image: url(../assets/image/icon-line.png);
        background-repeat: no-repeat;
      }
    }
  }
}
</style>
