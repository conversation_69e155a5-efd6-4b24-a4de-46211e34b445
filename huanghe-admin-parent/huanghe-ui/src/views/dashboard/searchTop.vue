<template>
  <div style="height:100%;">
    <div class="topFont">
      <span>PC端搜索词排行</span>
       <router-link to="/search/more" class="link-type">
        <div class="icon-color">查看更多</div>
      </router-link>
    </div>
   <!-- :height="tableHeight" -->
    <el-table
      v-loading="loading"
      :data="searchList"
      class="customer-table tablePad"
      :header-cell-style="headClass"
      :resizable="false"
      
      size="mini"
    
    >
      <el-table-column label="搜索词" align="center" prop="searchContent" />
      <el-table-column label="搜索次数" align="center" prop="searchCount" />
    </el-table>
  </div>
</template>
<script>
import { searchTop } from "@/api/homePage/homePage";

import Router from "vue-router";
import "@/icon/7/iconfont.css";
import "@/icon/more/iconfont.css";
export default {
  name: "searchTopPage",
  data() {
    return {
      tableHeight: window.innerHeight - 360,
      loading: false,
      searchList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    headClass() {
      return "text-align: center;background:	#E8E8E8;";
    },
    getList() {
      this.loading = true;
      searchTop(this.queryParams).then((response) => {
        this.searchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.borderSize {
  width: 98%;
  margin-left: 5px;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
  border-color: rgba(0, 0, 0, 0.05);
}
.topFont {
  color: rgb(59, 171, 166);
  font-size: 15px;
  font-weight: bold;
  float: left;
  margin:10px 0px 0px 5px;
  width: 100%;
}
.tablePad {
  float: left;
  width: 100%;
 // margin-top: 10px;
 
}
::v-deep .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    background-color: #e8e8e8;
    
 }
 ::v-deep.el-table--scrollable-y .el-table__body-wrapper {
    overflow-y: hidden;
}
.icon-color {
  color: rgb(59, 171, 166);
  font-size: 14px;
  float: right;
  margin:0px 10px 0px 0px;
  
  /* width: 50%px; */
}

.tablePad{ height:95% !important;}


</style>
<style>
.customer-table th {
  border: none;
}
.customer-table td,
.customer-table th.is-leaf {
  border: none;
}
/* // 表格最外边框 */
.el-table--border{}
.el-table--group {
  border: none;
}
.el .customer-table thead tr th:nth-last-of-type(2) {
  border-right: 1px solid #ebeef5;
}
.el-table--border::after,
.el-table--group::after {
  width: 0;
}
.customer-table::before {
  width: 0;
  
}
.customer-table .el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
 
}
/* // 表格有滚动时表格头边框 */
.el-table--border th.gutter:last-of-type {
  border: 1px solid #ebeef5;
  border-left: none;
}


</style>