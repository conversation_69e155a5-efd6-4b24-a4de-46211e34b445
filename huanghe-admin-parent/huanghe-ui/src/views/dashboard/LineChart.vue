<template>
  <div style="height:100%; width: 100%; ">
    <div style="height:100%; width: 100%; " :class="className"></div>
  </div>
</template>



<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";
import { aloneVisits,aloneVisits1, aloneVisitsCount1s,aloneVisitsCounts } from "@/api/homePage/homePage";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },

    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "73.5%",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      options1: [
        {
          value1: 0,
          label1: "今天",
        },
        {
          value1: 1,
          label1: "昨天",
        },
        {
          value1: 30,
          label1: "最近30天",
        },
        {
          value1: 7,
          label1: "最近7天",
        },
      ],
      value1: 0,
      servicedata: [],
      nameDatas: [],
      servicedata1: [],
      nameDatas1: [],
      chart: null,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.rec();
      this.aa();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },

  methods: {
    changeValue1(value1) {
      this.servicedata = [];
      this.nameDatas = [];
      this.servicedata1 = [];
      this.nameDatas1 = [];
        if(value1==0||value1==1){
     aloneVisits(value1).then((res) => {
        
        if (res.code == "200") {
          var aaa = res.data;
          for (var i = 0; i < aaa.length; i++) {
            this.servicedata.push(aaa[i].count);
            this.nameDatas.push(aaa[i].timeForVisits);
   
          }
           console.log(this.nameDatas,this.servicedata)
        }
        aloneVisitsCounts(value1).then((res) => {
          if (res.code == "200") {
            var xqo = res.data;
            for (var i = 0; i < xqo.length; i++) {
              this.servicedata1.push(xqo[i].visitCount);
              this.nameDatas1.push(xqo[i].timeForVisits);
            }
            xqo.forEach((e) => {});
            this.initChart();
          }
        });
      });
        }else{
             aloneVisits1(value1).then((res) => {
        //debugger
        if (res.code == "200") {
          var aaa = res.data;
          for (var i = 0; i < aaa.length; i++) {
            this.servicedata.push(aaa[i].count);
            this.nameDatas.push(aaa[i].timeForVisits);
   
          }
             console.log(this.nameDatas,this.servicedata)
           this.initChart();
        }
           });
         aloneVisitsCount1s(value1).then((res) => {
          if (res.code == "200") {
            var xqo = res.data;
            for (var i = 0; i < xqo.length; i++) {
              this.servicedata1.push(xqo[i].visitCount);
              this.nameDatas1.push(xqo[i].timeForVisits);
            }
            xqo.forEach((e) => {});
                       console.log(this.nameDatas1,this.servicedata1)
            this.initChart();
          }
        });
   
        }
   
    },
    rec() {

      var day = this.value1;
      if(day==0||day==1){
     aloneVisits(day).then((res) => {
        //debugger
        if (res.code == "200") {
          var aaa = res.data;
          for (var i = 0; i < aaa.length; i++) {
            this.servicedata.push(aaa[i].count);
            this.nameDatas.push(aaa[i].timeForVisits);
          }

          this.initChart();
        }
      });
      }else{
           aloneVisits1(day).then((res) => {
    
        if (res.code == "200") {
          var aaa = res.data;
          for (var i = 0; i < aaa.length; i++) {
            this.servicedata.push(aaa[i].count);
            this.nameDatas.push(aaa[i].timeForVisits);
          }

          this.initChart();
        }
      });
      }

    },

    aa() {
      var day = this.value1;
      if(day==0||day==1){
      aloneVisitsCounts(day).then((res) => {
        if (res.code == "200") {
          var xqo = res.data;
          for (var i = 0; i < xqo.length; i++) {
            this.servicedata1.push(xqo[i].visitCount);
      //      this.nameDatas1.push(xqo[i].timeForVisits);
          }
          xqo.forEach((e) => {});
          this.initChart();
        }
      });
      

      }
      else{
          aloneVisitsCount1s(day).then((res) => {
        if (res.code == "200") {
          var xqo = res.data;
          for (var i = 0; i < xqo.length; i++) {
            this.servicedata1.push(xqo[i].visitCount);
      //      this.nameDatas1.push(xqo[i].timeForVisits);
          }
          xqo.forEach((e) => {});
          this.initChart();
        }
      });
      
      }

    },
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions({ expectedData, actualData } = {}) {
      var colors = ["#5793f3", "#d14a61", "#675bba"];

      this.chart.setOption({
        legend: {
          data: ["独立访客", "登录次数"],
        },
          tooltip: {
            trigger: 'axis'//当trigger为’item’时只会显示该点的数据,为’axis’时显示该列下所有坐标轴所对应的数据
        },
        grid: {
          top: 70,
          bottom: 50,
        },
        xAxis: [
          {
               type: 'category',
                //数值起始和结束两端空白策略
            axisTick: {
              alignWithLabel: true,
            },
            axisLine: {
              onZero: false,
              lineStyle: {
                color: colors[1],
              },
            },
            axisPointer: {
              label: {
                formatter: function (params) {
                  return (

                    params.value
                  );
                },
              },
            },
            data: this.nameDatas,
          },
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            axisLine: {
              onZero: false,
              lineStyle: {
                color: colors[0],
              },
            },
            axisPointer: {
              label: {
                formatter: function (params) {
                  return (
                    "独立访客" +
                    params.value +
                    (params.seriesData.length
                      ? "：" + params.servicedata1[0].data
                      : "")
                  );
                },
              },
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "登录次数",
            type: "line",
            // xAxisIndex: 1,
            data: this.servicedata,
          },
          {
            name: "独立访客",
            type: "line",
            data: this.servicedata1,
          },
        ],
      });
    },
  },
};
</script>
<style scoped>
.icon-color {
  width: 60px;
  height: 60px;
  color: rgb(59, 171, 166);
  margin-top: 0%;
  margin-right: 0%;
}
</style>
