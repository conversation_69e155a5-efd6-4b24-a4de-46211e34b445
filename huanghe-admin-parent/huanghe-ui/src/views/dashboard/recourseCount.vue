<template>
  <div class="borderSize_1">
    <el-row>
      <div class="topFont">
        <span>资源统计</span>
      </div>
      <div style="width: 80%; float: right; ">
        <router-link to="/recourse/moreRecourse" class="link-type" style="width: 20%; float: right">
          <div class="icon-c">查 看 更 多</div>
        </router-link>
        <el-form :inline="true" style="float: right; ">
          <el-form-item prop="bookName" label="资源名称" label-width="100px">
            <el-input v-model="queryParams.bookName" placeholder="请输入" clearable size="mini" style="width: 100%"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="text" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-row>
    <el-row>
      <el-table v-loading="loading" :data="searchList" size="mini" class="customer-table" :header-cell-style="headClass"
      :resizable="false" :height="tableHeight">
        <el-table-column label="资源名称" align="center" prop="bookName" width="150" />
        <el-table-column label="所属丛编" align="center" prop="dbName" width="80" />
        <el-table-column label="所属分类" align="center" prop="resourceClasses" width="100" />
        <el-table-column label="笔记次数" align="center" prop="noteCount" />

        <!-- <el-table-column label="检索次数" align="center" prop="searchCount" /> -->
        <el-table-column label="复制次数" align="center" prop="copyCount" />
        <el-table-column label="引用次数" align="center" prop="quoteCount">
        </el-table-column>
        <el-table-column label="访问次数" align="center" prop="visitCount" />
      </el-table>
      <pagination v-show="total > 0"  :total="total" :page-sizes="[5, 10, 15, 20]" :page.sync="queryParams.pageNum" class="bottom"
        :limit.sync="queryParams.pageSize" @pagination="getList" small  style="padding: 0px !important;"/>
    </el-row>

    <!--  -->
    <!-- <el-row>
      <pagination v-show="total > 0"  :total="total" :page-sizes="[5, 10, 15, 20]" :page.sync="queryParams.pageNum" class="bottom"
        :limit.sync="queryParams.pageSize" @pagination="getList" small  style="padding: 0px !important;"/>
    </el-row> -->
    <!-- <div class="aaa"></div> -->
  </div>
</template>
<script>
import Router from "vue-router";
import { recourseCount } from "@/api/homePage/homePage";
import "@/icon/more/iconfont.css";
export default {
  data() {
    return {
      searchList: [],
      loading: false,
      total: 0,
      queryParams: {
        bookName: "",
        pageNum: 1,
        pageSize: 5,
      },
      tableHeight: window.innerHeight / 2 - 200,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    headClass() {
      return "text-align: center;background:#E8E8E8;";
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      recourseCount(this.queryParams).then((response) => {
        this.searchList = response.rows;
        response.rows.forEach((e) => {
          if (e.copyCount == null) {
            e.copyCount = 0;
          }
          if (e.quoteCount == null) {
            e.quoteCount = 0;
          }
          if (e.noteCount == null) {
            e.noteCount = 0;
          }
        });
        this.total = response.total;
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped>

::v-deep  .bottom {
 margin-top: 0px;
  margin-right:15px ;
  float: right;
  
}

::v-deep  .el-input--mini .el-input__inner {
    height: 20px;
    line-height: 20px;
 }
.icon-c {
  color: rgb(59, 171, 166);
  font-size: 14px;
  float: right;
  margin: 10px 0px 0px 0px;
  width: 100px;
}

.aaa {
  float: left;
  margin-left: 80%;
  margin-top: 15%;
}

.tablePad {
  float: left;
  height: 80%;
  margin-top: 0%;
}

.borderSize_1 {
  width: 100%;
  background: #fff;
  box-shadow: 3px 3px 20px hsl(0, 0%, 85%);
  border-color: rgba(0, 0, 0, 0.05);
  
  
}


.topFont {
  color: hsl(207, 82%, 47%);
  font-size: 15px;
  font-weight: bold;
  float: left;
  margin: 10px 0px 0px 5px;
}

.icon-color {
  float: right;
  margin: 5px 5px 0px 0px;
}

</style>
<style>

.customer-table th {
  border: none;
}

.customer-table td,
.customer-table th.is-leaf {
  border: none;
}

/* // 表格最外边框 */
.el-table--border,
.el-table--group {
  border: none;
}

.customer-table thead tr th:nth-last-of-type(2) {
  border-right: 1px solid #ebeef5;
}

.el-table--border::after,
.el-table--group::after {
  width: 0;
}

.customer-table::before {
  width: 0;
}

.customer-table .el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
}

/* // 表格有滚动时表格头边框 */
.el-table--border th.gutter:last-of-type {
  border: 1px solid #ebeef5;
  border-left: none;
}
</style>
