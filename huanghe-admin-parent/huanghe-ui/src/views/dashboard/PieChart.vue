<template>
  <div :class="className" style="height:100%; width: 100%; " />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";
import { recoursePercent } from "@/api/homePage/homePage";
export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    /* width: {
      type: String,
      default: "100%",
    }, */
    /* height: {
      type: String,
      default: "73.5%",
    }, */
  },
  data() {
    return {
      chart: null,
      servicedata: [],
      nameDatas: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.rec();
    });
  },

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        title: {
          text: "资源数据库占比",
          textStyle: {
            fontSize: 15,
            color: "#3baba6",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: " <br/>{b} : {c} ({d}%)",
        },

        legend: {
          left: "center",
          x: "left", //可设定图例在左、右、居中

          bottom: 0,

          data: this.nameDatas,
        },

        series: [
          {
            type: "pie",
            hoverAnimation: true,
            minAngle: 5,
            radius: "55%",
            center: ["45%", "50%"],
            data: this.servicedata,
            label: {
              normal: {
                textStyle: {
                  fontWeight: "normal",
                  fontSize: 15,
                },
              },
            },
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
            animationEasing: "cubicInOut",
            animationDuration: 2600,
          },
        ],
      });
    },
    rec() {
      recoursePercent().then((res) => {
        if (res.code == "200") {
          var xqo = res.data;
          for (var i = 0; i < xqo.length; i++) {
            this.servicedata.push({ value: xqo[i].dbCount, name: xqo[i].dbName });
            this.nameDatas.push({ name: xqo[i].dbName });
          }
          console.log(this.servicedata);
          this.initChart();
        }
      });
    },
  },
};
</script>
