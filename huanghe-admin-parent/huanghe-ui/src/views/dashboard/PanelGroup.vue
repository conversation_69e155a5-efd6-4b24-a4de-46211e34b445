<template>
  <el-row :gutter="30" class="panel-group">
    <el-col
      :xs="8"
      :sm="8"
      :lg="8"
      class="card-panel-col"
      style="padding:0px 5px 0px 5px;"
    >
      <div class="card-panel">
        <div class="card-panel-icon-wrapper icon-pcColor">
          <div width="300px">PC端访问量（近7天）</div>
        </div>
        <div class="iconfont icon-rili icon-pc"></div>
        <div class="bor"></div>
        <div class="card-panel-description">
          <div class="card-panel-text">
              <count-to
                :startVal="0"
                :endVal="visitCount"
                :duration="3000"
                class="card-panel-num"
              />
            <div class="card-panel-total">
              <span>累计:</span>
               <count-to
                :startVal="0"
                :endVal="totalVisitCount"
                :duration="3000"
                class="card-panel-num-total"
              />
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col
      :xs="8"
      :sm="8"
      :lg="8"
      class="card-panel-col"
      style="padding:0px 5px 0px 5px;"
    >
      <div class="card-panel">
        <div class="card-panel-icon-wrapper icon-shopping">
          <div width="300px">PC端新增用户（近30天）</div>
        </div>
        <div class="iconfont icon-rili-copy icon-new"></div>
        <div class="bor"></div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            
              <count-to
                :startVal="startVal"
                :endVal="endVal"
                :duration="3000"
                class="card-panel-num"
              />
              
            <div class="card-panel-total">
              <span>累计:</span>
              <count-to
                :startVal="startVal"
                :endVal="totalCount"
                :duration="3000"
                class="card-panel-num-total"
              />
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col
      :xs="8"
      :sm="8"
      :lg="8"
      class="card-panel-col"
      style="padding:0px 5px 0px 5px;"
    >
      <div class="card-panel">
        <div class="card-panel-icon-wrapper icon-shopping">
          <div width="300px">PC端新增机构（近30天）</div>
        </div>
        <div class="iconfont icon-rili-copy icon-new"></div>
        <div class="bor"></div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            <count-to
              :startVal="startVal"
              :endVal="newPlat"
              :duration="3000"
              class="card-panel-num"
            />
            <div class="card-panel-total ">
              <span>累计:</span>
              <count-to
                :startVal="startVal"
                :endVal="newPlatTotalCount"
                :duration="3000"
                class="card-panel-num-total"
              />
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from "vue-count-to";
import "@/icon/7/iconfont.css";
import "@/icon/30/iconfont.css";
import { newUser, newPlatCount, visits } from "@/api/homePage/homePage";

export default {
  data() {
    return {
      startVal: 0,
      endVal: 0,
      newPlat: 0,
      totalCount: "",
      newPlatTotalCount: "",
      visitCount: "",
      loginCount: "",
      loginTotalCount:"",
      totalVisitCount: "",
    };
  },

  components: {
    CountTo,
  },
  created() {
    this.newUserCount();
    this.newPlatCounts();
    this.recentlyVisits();
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit("handleSetLineChartData", type);
    },
    newUserCount() {
      newUser().then((response) => {
        this.endVal = response.data.count;
        this.totalCount = response.data.totalCount;
      });
    },
    recentlyVisits() {
      visits().then((response) => {
        this.visitCount = response.data.visitCount;
        this.loginCount = response.data.loginCount;
        this.loginTotalCount = response.data.totalCountLogin;
        this.totalVisitCount = response.data.totalCount;
      });
    },
    newPlatCounts() {
      newPlatCount().then((response) => {
        this.newPlat = response.data.monthAddCount;
        this.newPlatTotalCount = response.data.totalCount;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.bor {
  background: hsla(0, 4%, 85%, 0.959);
  height: 0.5px;
  margin:42px 2px 0px 2px;
}
.panel-group {
  margin-top: 10px;
  width:100%;
  .card-panel-col {
    margin-bottom: 8px;
  }

  .card-panel {
    height: 140px;
    margin-left: 10px;
    width: 100%;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      // .card-panel-icon-wrapper {
      //   color: #fff;
      // }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }
    .icon-pc {
      color: rgb(59, 171, 166);
      font-size: 30px;
      float: right;
      margin:5px 30px 0px 0px;
      width: 3px;
    }
    .icon-shopping {
      color: hsl(199, 49%, 45%);
      font-size: 15px;
      height: 20%;
      width: 80%;
    }
    .icon-pcColor {
      color: rgb(59, 171, 166);
      font-size: 15px;
      height: 20%;
      width: 80%;
    }
    .icon-new {
      color: hsl(199, 49%, 45%);
      font-size: 30px;
      float: right;
      margin:5px 10px 0px 0px;
    }

    .card-panel-icon-wrapper {
      float: left;
      padding: 10px;
      margin-top: 5px;
    }

    .card-panel-description {
      height: 200px;
      width: 100%;
      float: left;
      margin-left: 5px;

      .card-panel-text {
        width: 100%;
        color: rgba(0, 0, 0, 0.45);
        font-size: 25px;
        float: left;
        margin:25px 0px 0px 5px;
      }

      .card-panel-num {
        font-size: 25px;
        color: #000;
        float: left;
        margin-top: 5px;
      }

      .card-panel-num-total {
        font-size: 18px;
        float: right;
        margin:0px 10px 0px 0px;
        color: grey;
        
      }
      .card-panel-total {
        width: 100;
        font-size: 18px;
        float: right;
        color: grey;
        margin:13px 5px 0px 0px;
      }
    }
  }
}

@media (max-width: 700px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
