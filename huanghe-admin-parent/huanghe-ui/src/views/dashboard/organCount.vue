<template>
  <div class="borderSize">
    <div class="topFont">
      <span style="margin-top: 5px;display: inline-block;">
        授权机构
      </span>
      <div class="icon-color">
        <el-select v-model="value" @change="changeValue" size="mini">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <!-- height="calc(100vh - 520px)" -->
    <el-table
      v-loading="loading"
      :data="searchList"
      class="customer-table tablePad"
      :header-cell-style="headClass"
      :resizable="false"
      size="mini"
      max-height="310px"
      height="calc(100vh - 520px)"
    >
      <el-table-column
        label="机构名称"
        align="center"
        prop="orgName"
        width="300px"
      />
      <el-table-column label="访问量" align="center" prop="visitCount" />
      <el-table-column label="检索次数" align="center" prop="searchCount" />
      <el-table-column label="登录次数" align="center" prop="loginCount" />
    </el-table>
    <div >
       <pagination
       class="bottom"
        v-show="total > 0"
        :total="total"
        :page-sizes="[5,10,15,20]"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        small
        style="padding: 0px !important;"
      /> 
    </div>
  </div>
</template>
<script>
import { organCount } from "@/api/homePage/homePage";
export default {
  data() {
    return {
      loading: false,
      searchList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 5,
      },
      options: [
        {
          value: "30",
          label: "最近30天",
        },
        {
          value: "7",
          label: "最近7天",
        },
      ],
      value: "7",
      tableHeight:window.innerHeight/2-235
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changeValue(value) {
      this.loading = true;
      var day = this.value;
      console.log(this.value);
      organCount(this.queryParams, day).then((response) => {
        this.searchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getList() {
      this.loading = true;
      var day = this.value;
      organCount(this.queryParams, day).then((response) => {
        this.searchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    headClass() {
      return "text-align: center;background:#E8E8E8;";
    },
  },
};
</script>
<style lang="scss" scoped>
.borderSize {
  width: 100%;
  height: 400px;
  background: #fff;
  box-shadow: 2px 2px 20px hsl(0, 0%, 85%);
  border-color: rgba(0, 0, 0, 0.05);
  
}
.aaa {
  float: right;
  margin-left: 80%;
  margin-top: 10px;
}

.topFont {
  color: hsl(207, 82%, 47%);
  font-size: 15px;
  font-weight: bold;
  float: left;
  margin:5px 0px 0px 5px;
  width: 100%;
}
// .tablePad {
//   float: left;
//   margin-top: 5px;
// }
// .bottom {
//   margin-top: 0px !important;
//   margin-right:15px ;
//   float: right;
//   ::v-deep .el-input--mini .el-input__inner {
//     height: 20px;
//     line-height: 20px;
//  }
  
// }
.icon-color {
  float: right;
  margin-right: 10px;
}
::v-deep  .bottom {
 margin-top: 0px;
  margin-right:15px ;
  float: right;
  
}

::v-deep  .el-input--mini .el-input__inner {
    height: 20px;
    line-height: 20px;
 }
</style>
<style>
.customer-table th {
  border: none;
}
.customer-table td,
.customer-table th.is-leaf {
  border: none;
}
/* // 表格最外边框 */
.el-table--border,
.el-table--group {
  border: none;
}

.customer-table thead tr th:nth-last-of-type(2) {
  border-right: 1px solid #ebeef5;
}
.el-table--border::after,
.el-table--group::after {
  width: 0;
}
.customer-table::before {
  width: 0;
}
.customer-table .el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
}
/* // 表格有滚动时表格头边框 */
.el-table--border th.gutter:last-of-type {
  border: 1px solid #ebeef5;
  border-left: none;
}


</style>
