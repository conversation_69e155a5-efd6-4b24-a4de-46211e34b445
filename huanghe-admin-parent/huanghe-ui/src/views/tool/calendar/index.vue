<template>
  <el-container>
    <el-header>
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="568px" style="margin-top:20px">
        <el-form-item label="纪年换算:" prop="webName">
          <el-input
            v-model="queryParams.keyword"
            placeholder="输入您要检索的关键词：如：212"
            clearable
            size="medium "
            @keyup.enter.native="handleQuery"
            style="width: 400px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-header>
    <el-container>
      <el-aside width="400px">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClickCard">
          <el-tab-pane label="干支" name="first" >
            <el-row v-for="(ganzhi, index) in ganzhiList" :key="ganzhi.index" style="margin-top:20px">
              <el-button type="info" plain
                 v-for="(domains, rowIndex) in ganzhi.ganzhiRow.domains"
                 :key="domains.rowIndex"
              @click="getList(domains.value)">
                {{domains.value}}
              </el-button>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="时期" name="second" >
            <el-table
              v-loading="calendarPeriodLoading"
              :data="calendarPeriodList"
              @cell-click="getRegimeList"
              :show-header="false"
              highlight-current-row
            >
              <el-table-column align="center" prop="period"  />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="政权" name="third" >
            <el-table
              v-loading="regimeByConditionLoading"
              :data="regimeByConditionList"
              @cell-click="clickRegimeList"
              :show-header="false"
              highlight-current-row
            >
              <el-table-column label="序号" align="center" type="index" v-if="false"/>
              <el-table-column align="center" prop="regime"/>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-aside>
      <el-main>
        <div class="app-container">
          <h3>
            <span :style="{'color':'#F56C6C'}">“
              {{keyword.calendarAD}}
              {{keyword.power}}
              {{keyword.period}}
              {{keyword.regime}}
              {{keyword.emperorNo}}
              {{keyword.emperorName}}
              {{keyword.yearNo}}
              {{keyword.other}}
              ”
            </span>
            <span style="margin-left:20px">检索结果</span>
          </h3>
          <el-divider></el-divider>
          <!--公元列表 -->
          <template v-if="calendarADtableShow">
            <el-table  v-loading="loading" :data="calendarADList" @cell-click="clickCalendarADList" :show-header="false">
              <el-table-column label="序号" align="center" type="index" v-if="false"/>
              <el-table-column align="center" prop="calendarAD" :formatter="formatCalendarAD" />
            </el-table>
          </template>
         <!-- 政权列表-->
          <template v-if="regimeTableShow">
            <el-table v-loading="regimeLoading" :data="regimeList" @cell-click="clickPeriodRegimeList" :show-header="false">
              <el-table-column label="序号" align="center" type="index" v-if="false"/>
              <el-table-column align="center" :formatter="formatRegime"/>
            </el-table>
          </template>
          <!--帝号，帝名列表-->
          <template v-if="emperorNameTableShow">
            <el-table  v-loading="emperorNameLoading" :data="emperorNameList" @cell-click="clickEmperorNameList" :show-header="false">
              <el-table-column label="序号" align="center" type="index" v-if="false"/>
              <el-table-column align="center" :formatter="formatEmperorName"/>
            </el-table>
          </template>
          <!--纪年分页列表-->
          <template  v-if="calendarTableShow">
            <el-table v-loading="calendarLoading" :data="calendarList" @cell-click="clickCalendarTable">
              <el-table-column label="序号" align="center"type='index'
                               :index='(index)=>{return (index+1) + (queryParams.pageNum-1)*queryParams.pageSize  }'/>
              <el-table-column label="公元" align="center" prop="calendarAD"  :formatter="formatCalendarAD"/>
              <el-table-column label="干支" align="center" prop="power" />
              <el-table-column label="时期" align="center" prop="period" />
              <el-table-column label="政权" align="center" prop="regime" />
              <el-table-column label="帝号" align="center" prop="emperorNo" />
              <el-table-column label="帝名" align="center" prop="emperorName" />
              <el-table-column label="年号" align="center" prop="yearNo" />
              <el-table-column label="年份" align="center" prop="year" />
            </el-table>

            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getCalendarPageList"
            />
          </template>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
  import {
    calendarADList,
    periodList,
    regimeList,
    calendarList,
    regimeListByCondition,
    emperorNameList,
    getQueryParamsType,
    regimeListByLike,
    calendarListByLike
  } from "@/api/utils/util";

  export default {
    name: "Util",
    data() {
      return {
        // 遮罩层
        loading: true,
        calendarPeriodLoading: true,
        calendarADtableShow:true,
        regimeLoading:true,
        regimeTableShow:false,
        calendarLoading:true,
        calendarTableShow:false,
        regimeByConditionLoading: false,
        emperorNameTableShow:false,
        emperorNameLoading:true,
        getPageType: null,
        // 表格数据
        calendarADList: [],
        calendarPeriodList:[],
        regimeList:[],
        calendarList:[],
        regimeByConditionList:[],
        emperorNameList:[],
        // 总条数
        total: 0,
        activeName: 'first',
        queryParams:{
          calendarAD: null,
          power: null,
          period: null,
          regime: null,
          emperorNo: null,
          emperorName: null,
          yearNo: null,
          pageNum: 1,
          pageSize: 10,
          keyword: null
        },
        ganzhiList: [
          {
          ganzhiRow: {
              domains:[
                {value: '甲子'},
                {value: '乙丑'},
                {value: '丙寅'},
                {value: '丁卯'}
              ]
            }
          },
          {
          ganzhiRow: {
              domains:[
                {value: '戊辰'},
                {value: '己巳'},
                {value: '庚午'},
                {value: '辛未'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '壬申'},
                {value: '癸酉'},
                {value: '甲戌'},
                {value: '乙亥'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '丙子'},
                {value: '丁丑'},
                {value: '戊寅'},
                {value: '己卯'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '庚辰'},
                {value: '辛巳'},
                {value: '壬午'},
                {value: '癸未'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '甲申'},
                {value: '乙酉'},
                {value: '丙戌'},
                {value: '丁亥'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '戊子'},
                {value: '己丑'},
                {value: '庚寅'},
                {value: '辛卯'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '壬辰'},
                {value: '癸巳'},
                {value: '甲午'},
                {value: '乙未'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '丙申'},
                {value: '丁酉'},
                {value: '戊戌'},
                {value: '己亥'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '庚子'},
                {value: '辛丑'},
                {value: '壬寅'},
                {value: '癸卯'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '甲辰'},
                {value: '乙巳'},
                {value: '丙午'},
                {value: '丁未'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '戊申'},
                {value: '己酉'},
                {value: '庚戌'},
                {value: '辛亥'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '壬子'},
                {value: '癸丑'},
                {value: '甲寅'},
                {value: '乙卯'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '丙辰'},
                {value: '丁巳'},
                {value: '戊午'},
                {value: '己未'}
              ]
            }
          },
          {
            ganzhiRow: {
              domains:[
                {value: '庚申'},
                {value: '辛酉'},
                {value: '庚午'},
                {value: '辛未'}
              ]
            }
          }
        ],
        keyword:{
          calendarAD: '',
          power: '',
          period: '',
          regime: '',
          emperorNo: '',
          emperorName: '',
          yearNo: '',
          other:''
        }
      };
    },
    created() {
      this.getList(0);
      this.getPeriodList();
    },
    methods: {
      /** 查询列表 */
      getList(item) {
        this.reset();
        this.keywordRest();
        this.regimeByConditionList = [];
        this.calendarADtableShow = true;
        this.regimeTableShow = false;
        this.calendarTableShow = false;
        this.regimeTableShow = false;
        if(item === 0){
          item = this.ganzhiList[0].ganzhiRow.domains[0].value;
        }
        this.loading = true;
        this.queryParams.power = item;
        let queryParams = {"power":item};
        calendarADList(queryParams).then(response => {
          this.calendarADList = response.rows;
          this.loading = false;
        });
        this.keyword.power = item;
      },
      //查询时期列表
      getPeriodList(item) {
        this.calendarPeriodLoading = true;
        periodList().then(response => {
          this.calendarPeriodList = response.rows;
          this.calendarPeriodLoading = false;
        });
      },
      // 表单重置
      reset() {
        this.queryParams = {
          calendarAD: null,
            power: null,
            period: null,
            regime: null,
            emperorNo: null,
            emperorName: null,
            yearNo: null,
            pageNum: 1,
            pageSize: 10
        };
      },
      keywordRest(){
        this.keyword = {
          calendarAD: '',
          power: '',
          period: '',
          regime: '',
          emperorNo: '',
          emperorName: '',
          yearNo: '',
          other:''
        };
      },
      /** 搜索按钮操作 */
      handleQuery() {
        let keyword = this.queryParams.keyword;
        if(keyword == '' || keyword == undefined){
          return false;
        }
        keyword = keyword.replace("公元前","-");
        keyword = keyword.replace("前","-");
        let reg = /^([^0][0-9]+|0)$/;
        //如果查询条件是整数，按照公元条件查询
        if(reg.test(keyword)){
          let row = {"calendarAD":keyword};
          this.reset();
          this.keywordRest();
          this.clickCalendarADList(row);
        }else{
          //判断查询条件是什么类型
          let queryParams = {"keyword":keyword};
          let type;
          getQueryParamsType(queryParams).then(response => {
            type = response.data.queryParamsType;
            console.log(type);
            //干支
            if(type == 'power'){
              this.activeName ='first';
              this.getList(keyword);
            }
            //时期
            else if(type == 'period'){
              this.activeName = 'second';
              const row = {"period":keyword};
              this.getRegimeList(row);
            }
            //其他模糊查询
            else{
              this.reset();
              this.keywordRest();
              this.emperorNameTableShow = false;
              this.calendarADtableShow = false;
              this.regimeTableShow = false;
              this.calendarTableShow = true;
              this.keyword.other = keyword;
              this.regimeByConditionLoading = true;
              this.activeName ='third';
              regimeListByLike(queryParams).then(response => {
                this.regimeByConditionList = response.rows;
                this.regimeByConditionLoading = false;
              });
              this.queryParams.keyword = keyword;
              this.getPageType = 'other';
              this.getCalendarPageList();
            }
          });
        }
      },
      /** 重置按钮操作 */
      resetQuery() {
      },
      handleClickCard(tab, event) {
      },
      //格式化公元
      formatCalendarAD(row){
        if(row.calendarAD < 0){
          return "公元前"+Math.abs(row.calendarAD)+"年";
        }else{
          return "公元"+row.calendarAD+"年";
        }
      },
      //格式化政权
      formatRegime(row){
        let before = row.minad;
        let after = row.maxad;
        if(row.minad < 0){
          before = "公元前"+Math.abs(row.minad)+"年";
        }else{
          before = "公元"+ row.minad +"年";
        }
        if(row.maxad < 0){
          after = "公元前"+Math.abs(row.maxad)+"年";
        }else{
          after = "公元"+ row.maxad +"年";
        }
        return row.regime + "("+before + "~" + after +")";
      },
      //点击公元
      clickCalendarADList(row){
        console.log(row);
        this.emperorNameTableShow = false;
        this.calendarADtableShow = false;
        this.regimeTableShow = false;
        this.calendarTableShow = true;
        this.queryParams.calendarAD = row.calendarAD;
        this.queryParams.power = this.queryParams.power;
        let num = parseInt(this.queryParams.calendarAD);
        this.keyword.calendarAD = (num < 0? "公元前"+Math.abs(num)+"年": "公元"+ num +"年");
        this.getPageType = null;
        this.getCalendarPageList();
        this.getRegimeListByCondition();
      },
      //查询纪年列表
      getCalendarPageList(){
        this.calendarLoading = true;
        if(this.getPageType == "other"){
          calendarListByLike(this.queryParams).then(response => {
            this.calendarList = response.rows;
            this.calendarLoading = false;
            this.total = response.total;
          });
        }else{
          calendarList(this.queryParams).then(response => {
            this.calendarList = response.rows;
            this.calendarLoading = false;
            this.total = response.total;
          });
        }
      },
      //根据条件查询政权列表
      getRegimeListByCondition(){
        this.regimeByConditionLoading = true;
        this.activeName ='third';
        let queryParams = {
          "calendarAD":this.queryParams.calendarAD,
          "power":this.queryParams.power,
          "regime":this.queryParams.regime,
          "emperorName":this.queryParams.emperorName,
          "emperorNo":this.queryParams.emperorNo,
          "yearNo":this.queryParams.yearNo
        };
        regimeListByCondition(queryParams).then(response => {
          this.regimeByConditionList = response.rows;
          this.regimeByConditionLoading = false;
        });
      },
      //点击时期，查询政权列表
      getRegimeList(row){
        this.reset();
        this.keywordRest();
        this.regimeByConditionList = [];
        this.keyword.period = row.period;
        this.calendarTableShow = false;
        this.calendarADtableShow = false;
        this.emperorNameTableShow = false;
        this.regimeTableShow = true;
        this.regimeLoading = true;
        this.queryParams.period = row.period;
        let queryParams = {"period":row.period};
        regimeList(queryParams).then(response => {
          this.regimeList = response.rows;
          this.regimeLoading = false;
        });
      },
      //点击时期，政权列表
      clickPeriodRegimeList(row){
        this.keyword.regime = row.regime;
        this.emperorNameTableShow = true;
        this.calendarTableShow = false;
        this.calendarADtableShow = false;
        this.regimeTableShow = false;
        this.queryParams.regime = row.regime;
        this.getEmperorNameList(this.queryParams.period,row.regime);
      },
      //查询帝名列表
      getEmperorNameList(period,regime){
        this.emperorNameLoading = true;
        let queryParams = {"period":period,"regime":regime};
        emperorNameList(queryParams).then(response => {
          this.emperorNameList = response.rows;
          this.emperorNameLoading = false;
        });
      },
      //格式化帝名，帝号列表
      formatEmperorName(row){
        let str = (row.emperorNo != null? row.emperorNo:"")+ " " +(row.emperorName != null? row.emperorName:"")
        return str;
      },
      //点击帝名，帝号列表
      clickEmperorNameList(row){
        this.queryParams.emperorName = row.emperorName;
        this.queryParams.emperorNo = row.emperorNo;
        this.keyword.emperorNo = row.emperorNo != null? row.emperorNo:"";
        this.keyword.emperorName = row.emperorName != null? row.emperorName:"";
        this.emperorNameTableShow = false;
        this.calendarADtableShow = false;
        this.regimeTableShow = false;
        this.calendarTableShow = true;
        this.getPageType = null;
        this.getCalendarPageList();
        this.getRegimeListByCondition();
      },
      //点击政权查询
      clickRegimeList(row){
        this.keyword.regime = row.regime;
        this.queryParams.regime = row.regime;
        this.getRegimeListByCondition();
        this.getPageType = null;
        this.getCalendarPageList();
      },
      //点击纪年分页列表
      clickCalendarTable(row, column){
        //点击的是公元列
        if(column.property == "calendarAD"){
          if(row.calendarAD == null || row.calendarAD == ''){
            return false;
          }
          this.reset();
          this.keywordRest();
          this.clickCalendarADList(row);
        }
        //点击的是干支列
        if(column.property == "power"){
          if(row.power == null || row.power == ''){
            return false;
          }
          this.activeName ='first';
          this.getList(row.power);
        }
        //点击的是时期列
        if(column.property == "period"){
          if(row.period == null || row.period == ''){
            return false;
          }
          this.activeName = 'second';
          this.getRegimeList(row);
        }
        //点击的是政权列
        if(column.property == "regime"){
          if(row.regime == null || row.regime == ''){
            return false;
          }
          this.reset();
          this.keywordRest();
          this.clickRegimeList(row);
        }
        //点击的是帝号列
        if(column.property == "emperorNo"){
          if(row.emperorNo == null || row.emperorNo == ''){
            return false;
          }
          this.reset();
          this.keywordRest();
          this.emperorNameTableShow = false;
          this.calendarADtableShow = false;
          this.regimeTableShow = false;
          this.calendarTableShow = true;
          this.queryParams.emperorNo = row.emperorNo;
          this.keyword.emperorNo = row.emperorNo;
          this.getPageType = null;
          this.getCalendarPageList();
          this.getRegimeListByCondition();
        }
        //点击的是帝名列
        if(column.property == "emperorName"){
          if(row.emperorName == null || row.emperorName == ''){
            return false;
          }
          this.reset();
          this.keywordRest();
          this.emperorNameTableShow = false;
          this.calendarADtableShow = false;
          this.regimeTableShow = false;
          this.calendarTableShow = true;
          this.queryParams.emperorName = row.emperorName;
          this.keyword.emperorName = row.emperorName;
          this.getPageType = null;
          this.getCalendarPageList();
          this.getRegimeListByCondition();
        }
        //点击的是年号列
        if(column.property == "yearNo"){
          if(row.yearNo == null || row.yearNo == ''){
            return false;
          }
          this.reset();
          this.keywordRest();
          this.emperorNameTableShow = false;
          this.calendarADtableShow = false;
          this.regimeTableShow = false;
          this.calendarTableShow = true;
          this.queryParams.yearNo = row.yearNo;
          this.keyword.yearNo = row.yearNo;
          this.getPageType = null;
          this.getCalendarPageList();
          this.getRegimeListByCondition();
        }
      }
    }
  };
</script>
