<template>

  <div
    class="div-editable"
    :contenteditable="editPunDisabled"
    v-html="innerText"
    @input="changeText"
    @focus="focusFunc"
    @blur="blurFunc"
    tabindex="0"></div>
</template>

<script>
import {VISIT_URL,SYS_INFO,INTERVAL_TIME} from '@/api/libs/js/config';
  export default {
    /* name: 'DivEditable', */
    props: {
      value: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        innerText: this.value,
        isChange: true,
        editPunDisabled:false
      }
    },
    watch: {
      value() {
        if(this.isChange){
          this.innerText = this.value;
        }
      }
    },
    methods: {
      changeText() {
        this.$emit('input', this.$el.innerHTML)
      },
      blurFunc() {
        this.isChange = true;
        this.$emit('blurFunc')
      },
      focusFunc(){
        this.isChange = false;
      },
      //div是否开启编辑模式
      divEdit(v){
        this.editPunDisabled=v; 
      }
    }
  }
</script>

<style lang="less">
   @import "../home/<USER>";
  .div-editable{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    word-break: break-all;
    outline: none;
    user-select: text;
    white-space: pre-wrap;
    text-align: left;
    padding: 0px 10px;
    &[contenteditable=true]{
      user-modify: read-write-plaintext-only;
      &:empty:before {
        content: attr(placeholder);
        display: block;
        color: #ccc;
      }
    }
  }
</style>