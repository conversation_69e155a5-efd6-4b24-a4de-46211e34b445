<style lang="less" scoped>
  @import "../home/<USER>";
   .evaluate{
       /deep/ .ivu-btn-primary {
           /*  color: #fff; */
            background: linear-gradient(45deg,#A5040D,#2874AA);box-shadow:0px 7px 27px 0px rgba(49,129,255,0.51);
            
        }
   }
</style>
<template>
   <div class="evaluate">
       <!-- <Icon type="ios-list-box-outline" /> -->
       <Button size="small" icon="ios-list-box-outline" type="primary" @click="clickApproved">提交</Button>
       <Modal v-model="isShow" title="意见反馈" :mask-closable="false" width="50%"  :transfer="true" v-if="initSuccess">    
            <Row :style="{ height: contentHeight+'px'}" style="overflow: auto;">
               <Row>
                   意见描述（非必填）:
               </Row>
               <Row >
                  <Input v-model="workRemark" type="textarea"  :autosize="{minRows: 5,maxRows: 8}" placeholder="" />
               </Row>
            </Row> 
            <div slot="footer">
                <Button type="primary" @click="submit">提交</Button>
                <Button type="text" @click="cancel">取消</Button>
            </div>  
       </Modal>   
   </div>
</template>
<script>
import {INTERVAL_TIME} from '@/api/libs/js/config';
import { 
   getPunctuateassessPunctuarion
} from "@/api/autoPun/index";
export default {
    props: {
        id: {
           type: String
        },
        grade:{
            type:Number
        }
    },
    data(){
        return{
           initSuccess:false,
           isShow:false,
           contentHeight:window.innerHeight-760,
           workRemark:""
          
        }
    },
    methods:{
       clickApproved(){
           if(!this.id){
                this.$Message.warning("需要评价的内容为空！暂不能评价！" );
                return;
           }
           this.initSuccess=true;
           this.isShow=true;
       },
       submit(){
           this.$Modal.confirm({
              title: "确认提交？",
              content: "您确认要提交？",
              onOk: () => {
                let parmas={
                    "id":this.id,
                    "result":this.grade+"",
                    "content":this.workRemark
                }
                getPunctuateassessPunctuarion(JSON.stringify(parmas)).then(res=>{
                    if(res.success===true){
                        this.$Message.success( res.msg );
                        this.isShow=false;
                    }
                })
              }
            });
          
       },
       cancel(){
           this.isShow=false;
       }
    }
}
</script>