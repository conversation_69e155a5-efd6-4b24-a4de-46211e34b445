<template>
    <div>
        <!--  <button @click="senMessage">测试</button>   -->
    </div>
</template>

 <script>
import SockJS from 'sockjs-client'

import {VISIT_URL} from '@/api/libs/js/config';
export default {
  data(){
            return {
        
            }
        },
        methods:{
            //初始化
            initWebSocket(){
              this.connection();
            },
            senMessage(){
              let params= {
                  "total":14
              }
              this.stompClient.send('/user/message',JSON.stringify(params))
            },
            //建立连接
            connection() {
                // 建立连接对象
                //连接服务端提供的通信接口，连接以后才可以订阅广播消息和个人消息
                //后台服务ip和port
                let accessToken = window.sessionStorage.getItem("userKey");
                if(accessToken){
                    this.socket = new SockJS(VISIT_URL.web_scoket_url+'?_accessTokenG='+accessToken,null,{ timeout: 15000});
                    // 获取STOMP子协议的客户端对象
                    this.stompClient = Stomp.over(this.socket);
                    // 向服务器发起websocket连接
                    this.stompClient.connect({}, (frame) => {
                        this.stompClient.subscribe('/user/message', (val) => {
                            let obj=JSON.parse(val.body);
                            let msg = obj.unreadNumber;
                            this.$emit("cancelWebSocket",msg);
                        })
                    })
                }
                
            },
            disconnect() {
                if (this.stompClient != null) {
                    this.stompClient.disconnect();
                }
            }
        },
        //销毁页面之前，断开连接
        beforeDestroy: function () {
            //页面离开时断开连接,清除定时器
            this.disconnect();
            clearInterval(this.timer);
        },
        mounted(){
            //调用初始化websocket方法
            this.initWebSocket();
        },
} 
</script> 

<style>

</style>