<style lang="less" scoped>
  @import "../home/<USER>";
    .upload-button{
        /deep/ .ivu-btn {
            color: #fff;
            background: linear-gradient(45deg,#6FA0EA, #c39aef);
            border-color: #b39bed;
        }
    }
    .import{
        /deep/ .ivu-btn {
            color: #fff;
            background: linear-gradient(45deg,#6FA0EA,#C39AEF);
            border-color: #b39bed;
        }
    }
    .header{
        img{
            margin-right:5px;
            vertical-align: middle;
        }
        span{
            color:#201D81;
            font-size: 14px;
            font-weight: 700;
            letter-spacing:3px;
        }

    }
    .model{
        /deep/ .ivu-modal-header {
            border-bottom: 1px dashed #979797;
        }
        /deep/ .ivu-modal-footer {
            border-top: 1px solid #979797;
        }
        .middle-content{
            padding: 10px 0px;
            color:#858585;
            line-height: 26px;
        }
        .footer-content{
            text-align: center;
        }
    }

</style>
<template>
    <div class="import">
        <Button  shape="circle"   @click="operationImport" icon="md-cloud-upload">{{title}}</Button>
        <Modal v-model="importModel" :mask-closable="false" :closable="false" width="40%" v-if="initSuccess" class="model">
            <div slot="header" class="header">
                <img src="../assets/info-Icon.png" height="22" width="22" />
                <span>上传</span>
            </div>
            <Row >
                <Row><!-- #252284 -->
                    邮箱：  <Input v-model="mailAddress" type="email" placeholder="请输入邮箱" style="width: 75%;border-color: #252284;margin-right:10px;" />
                    <!-- <Button style="float: left;border-color: #252284;color: #252284;margin-right:10px;" shape="circle" @click="downloadExamples">查看样例</Button>  -->
                    <!--  -->
                    <RadioGroup v-model="status" size="small" @on-change="radioChange">
                        <Radio label="1">句读</Radio>
                        <Radio label="2">标点</Radio>
                    </RadioGroup>
                </Row>
                <Row style="margin-top: 10px;">
                    <Upload
                        class="upload-button"
                        style="float: left;"
                        ref="upload"
                        :headers="accessToken"
                        :action="importXls"
                        :name="name"
                        :before-upload="handleUpload"
                        :show-upload-list="true"
                        :on-format-error="handleFormatError"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                        :data="importData"
                        :max-size="102400"
                        :on-exceeded-size="handleMaxSize"
                        accept=".txt"
                        :format ="['txt']">
                            <Button type="info" long>请上传txt文件</Button>
                    </Upload>
                    <!-- :max-size="102400" -->
                </Row>
                <Row v-if="file !== null">
                    <span style="color:#3A3790;font-size: 14px;">已选择 {{ file.name }} </span>
                    <span style="float: right;margin-right: 20px;color: red;cursor:pointer;" @click="remove"><Icon type="ios-trash" size="16" />删除文件</span>
                </Row>
                <Row class="middle-content">
                    <h3>上传规则说明</h3>
                    <p >（1）请上传.txt格式文件；</p>
                    <p >（2）单个文件大小在100M以下；</p>
                    <p >（3）文本内容格式，具体<a  @click="downloadExamples" style="color:#d0021b;">参见样例</a>；</p>
                    <p >（4）上传后，2个工作日内，会以邮件的方式反馈结果；</p>
                </Row>
            </Row>
            <div slot="footer" class="footer-content">
              <Button type="text" size="large"  @click="upload" style="color:#D0021B;"  :disabled="importDisabled">
                  {{ importDisabled ? '上传中，请稍后' : '完成' }}
              </Button>
              <span>|</span>
              <Button type="text" size="large"  @click="importModel=false">取消</Button>
            </div>
        </Modal>
    </div>

</template>

<script>
import {IMPORT_TYPE} from '@/api/libs/js/config';
import {
       getFileDownloadExamples
    }
from "@/api/autoPun/index";
export default {
    name: "importChoose",
        props: {
          uploadUrl: {
            type: String,
            required:true
          },
          uploadName:{//后台接收名称
              type:String,
              required:true
          },
          buttonTitle:{//按钮标题
              type:String,
              required:true
          },
          data:{//参数
              type:Object
          }

    },
    data(){
        return{
          accessToken:{},
          importXls:"",
          initSuccess:false,
          importModel:false,
          name:"",
          file: null,
          title:"",
          importData:{
              "id":"",
              "status":"2"
          },
          status:"2",
          importIcon:"",
          importSize:"default",
          importDisabled:false,
          mailAddress:""
        }
    },
    methods:{
        handleUpload (file) {
            this.file = file;
            return false;
        },
        //导入
        operationImport(){
            this.initSuccess=true;
            this.accessToken = {
               accessToken: this.getStore("accessToken")
            };
            this.file=null;
            this.$nextTick(function () {
                 this.$refs.upload.clearFiles();
            })
            this.importModel=true;
            this.importXls=this.uploadUrl;
            this.name=this.uploadName;
            this.title=this.buttonTitle;
            this.mailAddress="";
            this.importData=this.data;
            this.$set(this.importData,"mailAddress","");
            this.$set(this.importData,"status","2");
            this.importDisabled=false;
        },
        radioChange(v){
           this.importData.status=v;
        },
        upload () {
            if(this.file==null){
                this.$Message.warning("请选择上传的文件！")
                return;
            }
            if(!this.mailAddress){
                this.$Message.warning("请填写邮箱！")
                return;
            }
            /* if(this.file.size>1048576){
                this.$Message.warning("文件大小不能超过1G")
                return;
            }
            console.log(this.file); */
            var checkEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
            /* let checkEmail = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/ */
            if(checkEmail.test(this.mailAddress)){
                this.importDisabled=true;
                this.importData.mailAddress=this.mailAddress;
                this.$refs.upload.post(this.file);
                this.file = null;
            }else{
               this.$Message.warning("请填写正确的邮箱格式！")
            }

        },
        handleMaxSize (file) {
            this.$Message.warning('文件 ' + file.name + ' 大小不能超过100M!');
            this.importDisabled=false;
        },
        downloadExamples(){
            this.publicDownloadExamples("样例.txt");
        },
        publicDownloadExamples(name){
           //导入
            let params={
                filename:name
            }
            getFileDownloadExamples(JSON.stringify(params)).then(res => {
                const content = res
                const blob = new Blob([content],{type: 'application/octet-stream'})
                const fileName = name;
                if('msSaveOrOpenBlob' in navigator){
                    window.navigator.msSaveOrOpenBlob(blob, fileName);
                }else{
                    const elink = document.createElement('a')
                    elink.download = fileName
                    elink.style.display = 'none'
                    elink.href = URL.createObjectURL(blob)
                    document.body.appendChild(elink)
                    elink.click()
                    URL.revokeObjectURL(elink.href) // 释放URL 对象
                    document.body.removeChild(elink)
                    this.currentRow="";
                }
            });
        },
        remove(){
            this.file = null;
        },
        handleFormatError(file){
            this.$Notice.warning({
                title: '文件格式不正确',
                desc: '文件 ' + file.name + ' 格式不正确，请上传.txt文件。'
            })
            this.importDisabled=false;
        },
        handleSuccess(res,file){
            if(res.success===true){
                this.$Message.success({
                    content: res.message,
                    closable: true,
                    duration:30
                });
                this.importModel=false;
            }else{
                this.$Message.error(res.message);
            }
            this.$refs.upload.clearFiles();
            this.importDisabled=false;
        },
        handleError(error,file){
            this.importDisabled=false;
            this.$Message.error({
                    content: '数据导入失败！',
                    closable: true,
                    duration:30
                })
        },
        importCloseModel(){
            this.file=null;
            this.$refs.upload.clearFiles();
            this.importModel=false;
        },
    },
    mounted() {
        this.title=this.buttonTitle;
    },
}
</script>
