<template>
    <div>
        <a @click="viewRules" style="color:#ffffff;" id="apu-one">帮助</a>
        <Modal v-model="ruleDescriptionModel" width="50%" :mask-closable="false" class="rule-desc-modal" >
          <div slot="header" class="header">
            <img src="../assets/info-Icon.png" height="22" width="22" />
            <span>自动标点说明</span>
          </div>
          <div class="middle-content" :style="{ height: contentHeight+'px'}" style="overflow: auto;">
            <h3>1、功能介绍：</h3>
            <p >（1）自动标点目前有两套模型，分别为古联北师大联合实验室和古联智能数据研究室。用户根据需要进行选择，通过复制文本进行标点或断句（文本中请勿带序号、注号），单次上限为5000字，使用纠错功能，可对其修改，仅支持全角符号录入。如需全文标点，可使用上传文件功能，2个工作日内，我们将通过邮件的方式反馈结果。</p>
            <h3>2、可用字数规则说明：</h3>
            <p >（1）首次访问平台，每户赠送30万字，字数可用于该平台功能使用；</p>
            <p >（2）使用“标点”后消减相应字数（以标点字数为准）；</p>
            <p >（3）使用“纠错”后赠送500字；</p>
            <p >（4）使用“满意度”提交后赠送100字；</p>
            <!-- <p >（5）使用“反馈”提交后赠送100字；</p> -->
            <h3>3、颜色说明：</h3>
            <p >（1）句读结果，新增断句位置，以橙色句号“<span style="color:#FFA500;">。</span>”分割语句；</p>
            <p >（2）标点结果，正确以黑色字体呈现，漏标以橙色字体呈现，错标以红色字体呈现，包含七种现代标点，分别为，。、？！；：</p>
            <p >（3）纠错结果，正确以黑色字体呈现，漏标以橙色字体呈现，错标以红色字体呈现，半角符号以蓝色字体呈现；</p>
            <h3>4、浏览器说明：</h3>
            <p>建议您使用如下浏览器，体验效果会更好！</p>
            <p >（1）谷歌浏览器：推荐63版本及以上；</p>
            <p >（2）火狐浏览器：推荐78版本及以上；</p>
            <p >（3）Edge浏览器：推荐42版本以上；</p>
            <p >（4）360浏览器：推荐极速模式，且为8.1版本及以上；</p>
            <p >（5）IE浏览器：推荐11版本及以上；</p>
          </div>
          <div slot="footer" class="footer-content">
              <Button type="text" size="large"  @click="ok" style="color:#D0021B;">OK，我知道了</Button>
          </div>
        </Modal>
    </div>
</template>
<script>
export default {
    data(){
        return{
            ruleDescriptionModel:false,
            contentHeight:window.innerHeight-300
        }
    },
    methods:{
        viewRules(){
           this.ruleDescriptionModel=true;
        },
        ok(){
           this.ruleDescriptionModel=false;
        }
    }
}
</script>
<style lang="less" scoped>
  @import "../home/<USER>";
    .rule-desc-modal{
        /deep/ .ivu-modal-header {
            border-bottom: 1px dashed #979797;
        }
        /deep/ .ivu-modal-footer {
            border-top: 1px solid #979797;
        }
        .header{
            img{
                margin-right:5px;
                vertical-align: middle;
            }
            span{
                color:#201D81;
                font-size: 14px;
                font-weight: 700;
                letter-spacing:3px;
            }
        }
        .middle-content{
            padding: 10px 0px;
            color:#000000;
            line-height: 26px;
            p{
                text-indent: 2em;
                text-align: justify;
                 color: #858585;
            }
            h3{
                color: #979797;
            }
        }
        .footer-content{
           text-align: center;
        }
    }
</style>