.home-main{
    width: 100%;
    
    background-image: url('../assets/back.png');
    .content{
        width: 80%;
        margin-left: 20%;
    }
    &-info{
      float: right;
      span{
        margin-right: 10px;
      }
    }
    &-operation{
        height: 60px;
        background: #ffffff;
        .operation-left{
            width: 50%;
            float: left;
            .operation-left-span{
                float: left;
                margin-left: 10px;
                margin-top: 14px;
                .title{
                    margin-right: 20px;
                }
                img{
                    vertical-align: middle;
                    margin-right: 15px;
                }
            }
        }
        .operation-right{
            width: 50%;
            float: right;
            .operation-right-span{
                float: right;
                margin-top: 14px;
                button{
                    margin-right: 10px;
                }
                /deep/ .ivu-btn-primary {
                    color: #fff;
                    background: linear-gradient(45deg,#6FA0EA,#C39AEF);
                    border-color: #b39bed;
                }
                .clear-data{
                    color: #fff;
                    background: linear-gradient(45deg,#A3071E,#EF4C5B);
                    border-color: #EF4C5B;
                }
            }
        }
    }
    &-edit{
        width: 100%;
        margin-top: 10px;
        .edit-left{
            width:49%;
            background:#FFFFFF;
            float:left;
            border-radius: 5px;
            box-shadow: 5px 7px 2px #eee;
            overflow: auto;
            height: 100%;
            /deep/ textarea.ivu-input {
                height: 100%;
                border: 0px;
                border-radius: 5px;
                overflow: auto;
                resize: none;
            }
        }
        .edit-right{
            width:49%;
            height: 100%;
            background:#FFFFFF;
            float:left;
            margin-left:2%;
            border-radius: 5px; 
            box-shadow: 5px 7px 2px #eee;
            /deep/ .ProseMirror{
                width: 100%;
                height: 100%;
            }
           
        }
       
    }
    .text{
        letter-spacing: 2px;
        font-family: SourceHanSansCN-Normal;
        font-size: 14px;
    }
}
.confirm-model{
 
    /deep/ .el-dialog-footer {
      border-top: 1px solid #979797;
    }
    /deep/ .el-dialog{
       width: 600px;
       height: 350px;
      }
}