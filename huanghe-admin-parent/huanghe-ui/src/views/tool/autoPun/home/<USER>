<style lang="less" scoped>
@import "./home.less";
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
/deep/.ivu-rate-text {
  margin-left: 0.04rem;
  vertical-align: middle;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  height: 30px;
}
/deep/ div#driver-page-overlay {
  background: #00000073;
}
/deep/ .ivu-radio-small .ivu-radio-inner {
  width: 16px;
  height: 16px;
}
/deep/ .ivu-radio-small .ivu-radio-inner:after {
  width: 10px;
  height: 10px;
}
/deep/ .ivu-radio-inner:after {
  left: 2px;
  top: 2px;
}
.back {
  background-image: url("../assets/back.png");
  width: 100%;
  height: 100%;
}
</style>

<template>
  <div class="home-main" :style="{ height: mainPage + 'px' }">
    <el-row class="content">
      <Spin fix v-if="spinShow">
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>{{ spinText }}</div>
      </Spin>
      <el-row>
        <div class="home-main-info">
          <div style="float: left">
            <span @click="notice" id="apu-ten"
              ><el-badge :count="count"
                ><Icon type="md-notifications-outline" size="23" /></el-badge
            ></span>
            <span
              >可用字数：<span style="color: #d0021b; font-size: 22px">{{
                available
              }}</span></span
            >
          </div>
          <span
            ><img src="../assets/question-line.png" height="20" width="20"
          /></span>
        </div>
      </el-row>
      <el-row class="home-main-operation">
        <div class="operation-left">
          <span class="operation-left-span" id="apu-two"> </span>
        </div>
        <div class="operation-right">
          <div class="operation-right-span">
            <div style="float: right; margin-left: 10px">
              <span id="apu-four">
                <el-button
                  type="primary"
                  round
                  @click="autoPunctuation"
                  v-if="autoPunIsShow"
                >
                  <img
                    src="../assets/punctuation.png"
                    height="14"
                    width="14"
                    style="margin-right: 4px; vertical-align: middle"
                  />
                  自动标点
                </el-button>
              </span>
              <el-button
                type="primary"
                round
                @click="punctuationSubmit"
                v-if="punDisabled == false"
                id="apu-six"
              >
                <img
                  src="../assets/error-correction.png"
                  height="14"
                  width="14"
                  style="margin-right: 4px; vertical-align: middle"
                />
                开始纠错
              </el-button>

              <el-button
                type="primary"
                round
                @click="downloadText"
                id="apu-nine"
                Icon="md-arrow-down"
              >
                下载</el-button
              >
              <el-button type="primary" round class="clear-data" @click="clear">
                <img
                  src="../assets/clear.png"
                  height="14"
                  width="14"
                  style="margin-right: 4px; vertical-align: middle"
                />
                清空内容
              </el-button>
            </div>
          </div>
        </div>
      </el-row>
      <el-row>
        <div class="home-main-edit" :style="{ height: mainPageCon + 'px' }">
          <div class="edit-left text">
            <el-row
              style="width: 70%; border-right: 1px solid #979797"
              id="apu-three"
            >
              <el-input
                v-model="punForm.innerText"
                @change="fontLenght"
                :style="{ height: contentHeight + 'px' }"
                maxlength="5000"
                type="textarea"
                placeholder=""
                :readonly="isShowDisabled"
                style="width: 100%; height: 600px; font-size: 20px"
              />
            </el-row>
            <el-row
              style="
                margin-top: 15px;
                height: 50px;
                border-top: 1px solid #979797;
              "
            >
              <span style="margin-left: 4px"
                >字数：{{ punForm.fontCount }}/5000</span
              >
            </el-row>
          </div>
          <div class="edit-right text">
            <!-- v-ban-save  禁用右键 复制 粘贴 剪切  @copy.native.capture.prevent='disableCopy' @paste.native.capture.prevent='disablePaste'-->
            <el-row
              style="width: 99%; border-right: 1px solid #979797"
              id="apu-seven"
            >
              <div-editable
                :style="{ height: contentHeight + 'px' }"
                v-model="punForm.contentPun"
                @blurFunc="blurHighLight"
                ref="divEditable"
              >
              </div-editable>
            </el-row>
            <el-row
              style="
                margin-top: 15px;
                height: 50px;
                border-top: 1px solid #979797;
              "
            >
              <div
                id="apu-eight"
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  float: right;
                  margin-right: 10px;
                  height: 44px;
                "
              >
                <span style="margin-left: 10px">
                  满意度
                  <el-rate
                    :colors="colors"
                    v-model="punForm.grade"
                    @change="changeGrade()"
                  />
                  <!-- <el-rate v-model="punForm.grade"></el-rate> -->
                </span>
              </div>
            </el-row>
          </div>
        </div>
      </el-row>
    </el-row>

    <!-- <el-row>
      <div
        class="page-con footer"
        style="max-height: 120px; text-align: center; margin-top: 40px"
      >
        <p>金陵全书</p>
      </div>
    </el-row> -->
    <websocket @cancelWebSocket="webSocketNumber"></websocket>
    <el-dialog
      :visible.sync="confirmModel"
      :close-on-click-modal="false"
      class="confirm-model"
    >
      <div slot="header">
        <img
          src="../assets/info-Icon.png"
          height="22"
          width="22"
          style="margin-right: 5px; vertical-align: middle"
        />
        <span
          style="
            color: #201d81;
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 3px;
          "
          >清空内容</span
        >
      </div>
      <div style="padding: 38px 0px">
        <p style="text-align: center">
          <img src="../assets/delete.png" height="20" width="20" />
        </p>
        <p style="text-align: center; color: #000000; font-size: 20px">
          确认要清空本次内容吗？
        </p>
      </div>
      <div slot="footer" style="text-align: center">
        <el-button type="text" size="large" @click="ok" style="color: #d0021b"
          >确定</el-button
        >
        <span>|</span>
        <el-button type="text" size="large" @click="confirmModel = false"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <el-dialog v-model="radioInfo" footer-hide class="confirm-model">
      <div slot="header">
        <img
          src="../assets/info-Icon.png"
          height="22"
          width="22"
          style="margin-right: 5px; vertical-align: middle"
        />
        <span
          style="
            color: #201d81;
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 3px;
          "
          >{{ modelTitle }}</span
        >
      </div>
      <div style="text-indent: 2em; text-align: justify">
        <span>{{ modelContent }}</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { VISIT_URL, SYS_INFO, INTERVAL_TIME } from "@/api/libs/js/config";
import DivEditable from "../main-components/divEditable.vue";
import websocket from "../main-components/web-scoket/websocket.vue";
import importChoose from "../main-components/import.vue";
import Cookies from "js-cookie";
// import Driver from "driver.js";
// import "driver.js/dist/driver.min.css";
import steps from "@/api/libs/js/defineSteps.js"; //用来存放引导的步骤
import {
  getPunctuateAuthentication,
  getPunctuateAutoPunctuation,
  getPunctuateDownloadText,
  getPunctuatecorrectPunctuation,
  getPunctuateassessPunctuarion,
  getPunctuateApplyQuota,
  getPunctuateAvailableWords,
  getPunctuateSignOut,
  getPunctuateNoticeUnreadNumber,
  uploadUrl,
  getPunctuateGuide,
} from "@/api/autoPun/index";
export default {
  name: "home",
  data() {
    return {
      available: 0,
      //innerText:"郭熙⼭⽔畫論云春⼭艷冶⽽如笑夏⼭蒼翠 如滴秋⼭明净⽽如妝冬⼭慘淡⽽如睡⼜云海⼭微茫⽽隱⾒江⼭嚴厲⽽峭卓溪⼭窈窕⽽幽深塞⼭童頳⽽堆⾩操觚當作如是觀",
      mainPage: window.innerHeight - 200,
      mainPageCon: window.innerHeight - 600,
      contentHeight: window.innerHeight - 600,
      punDisabled: false,
      punForm: {
        id: "",
        content: "",
        contentPun: "",
        innerText: "",
        grade: 0,
        fontCount: 0,
      },
      content: "",
      count: 0,
      // grade:0,
      confirmModel: false,
      spinShow: false,
      nikeName: "",
      autoPunIsShow: true,
      uploadData: {},
      uploadUrl: uploadUrl,
      radioInfo: false,
      modelTitle: "",
      modelContent: "",
      automaticSentenceDisabled: true,
      isShowDisabled: false,
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      steps: [
        {
          element: "#apu-one",
          popover: {
            title: "自动标点说明",
            description:
              "主要介绍自动标点工具使用、字数规则、颜色标识等，帮助您快速熟悉工具。",
            position: "left",
          },
        },
        {
          element: "#apu-two",
          popover: {
            title: "选择模型",
            description:
              "目前有两个模型供您选择，您可根据需要，选择任意一个自动标点模型，用于句读或标点。",
            position: "right",
          },
          padding: 5,
        },
        {
          element: "#apu-three",
          popover: {
            title: "输入内容",
            description:
              "您可将文字内容，直接复制到文本框中，单次上限5000字，请勿带序号、注号等符号。",
            position: "right",
          },
        },
        {
          element: "#apu-four",
          popover: {
            title: "句读或标点",
            description:
              "根据您的需要，点击句读或标点功能，右侧文本框将出现结果。",
            position: "top",
          },
        },
        {
          element: "#apu-five",
          popover: {
            title: "上传文件",
            description:
              "文本量过大，需全文标点时，您也可通过上传功能提交内容，我们将在2个工作日内，以邮箱方式反馈结果",
            position: "bottom",
          },
        },
        {
          element: "#apu-six",
          popover: {
            title: "开始纠错",
            description:
              "对句读或标点后的结果，您可以对其进行纠错并保存，为我们提供宝贵的纠错意见。",
            position: "left",
          },
        },
        {
          element: "#apu-seven",
          popover: {
            title: "修改内容",
            description: "纠错模式下，您可以对结果进行修改，直至纠错完成。",
            position: "left",
          },
        },
        {
          element: "#apu-eight",
          popover: {
            title: "满意度评价",
            description:
              "您可对句读或标点的结果给予星级评价，以激励我们提供更好的服务。",
            position: "left",
          },
        },
        {
          element: "#apu-nine",
          popover: {
            title: "下载",
            description:
              "句读或标点的结果，可通过下载功能，为您生成txt文件，包含了原文、结果及纠错，方便您的使用和查看。",
            position: "left",
          },
        },
        {
          element: "#apu-ten",
          popover: {
            title: "通知中心",
            description: "通知中心主要展示您纠错、满意度评价获得的奖励。",
            position: "left",
          },
        },
      ],
      Driver: "",
      openDriver: true,
      spinText: "处理中，请稍后。。。",
      userInfo: "",
    };
  },
  components: {
    DivEditable,
    websocket,
    importChoose,
  },
  created() {
    this.wordsList();
    this.aa();
  },

  methods: {
   
    clickRadio(v) {
      this.radioInfo = true;
      if (v == "1") {
        this.modelTitle = "古联北师大联合实验室";
        this.modelContent =
          "2019年11月，古联公司与北京师范大学中文信息处理研究所成立“古汉语信息处理联合实验室”，联合实验室定位于研究型实验室，结合北京师范大学建设世界一流大学目标，开展古汉语信息处理技术研究，推动研究成果转化，并培养既熟悉古籍文献又具备信息处理能力的复合型人才。2020年1月，联合实验室的第一个正式合作项目 “古籍文献自动断句标点模型研发项目”启动，在北师大原有技术基础上，以古联公司精加工结构化古籍整理本和古籍原始文献语料资源为基础数据，训练古汉语深层语言模型和自动标点模型，进一步研发高精度的自动标点技术。";
      } else {
        this.modelTitle = "古联智能数据研究室";
        this.modelContent =
          "古联智能数据研究室成立于2019年10月，致力于利用大数据技术和人工智能技术辅助古籍的教学、科研、整理工作。自动标点是研究室的研究方向之一，借助深度学习在自然语言处理领域成为最新的工具，为古籍断句或添加现代标点提高效率。古联以中华书局经典古籍库15亿字，涉及经史子集四部类共2250本古籍为基础训练语料训练自动标点模型。";
      }
    },
    disableCopy() {
      return false;
    },
    disablePaste() {
      return false;
    },
    notice() {
      if (!this.nikeName) {
        this.$Message.warning({
          content: "请先登录！",
        });
        return;
      }
      this.$router.push({
        name: "notice_index",
        params: {
          id: this.punForm.id,
          content: this.punForm.content,
          contentPun: this.punForm.contentPun,
          type: this.punForm.type,
          innerText: this.punForm.innerText,
          grade: this.punForm.grade,
          fontCount: this.punForm.fontCount,
        },
      });
    },
    radioChange(v) {
      this.autoPunIsShow = true;
      this.automaticSentenceDisabled = true;
    },
    automaticSentence() {
      if (!this.punForm.innerText) {
        this.msgInfo("需要自动句读的内容不能为空！");
        return;
      }
      if (this.punForm.innerText.length > this.available) {
        this.$Message.warning(
          "需要自动句读的内容的字数大于可用字数，请先申请字数！"
        );
        return;
      }
      this.$refs.divEditable.divEdit(false);
      this.punForm.contentPun = "";
      this.getAutomaticSentence();
    },
    getAutomaticSentence() {
      let params = {
        content: this.punForm.innerText,
        type: this.punForm.type,
        status: "1",
      };
      this.spinShow = true;
      this.spinText = "自动句读中，请稍后。。。";
      getPunctuateAutoPunctuation(JSON.stringify(params)).then((res) => {
        this.spinShow = false;
        if (res.success === true) {
          /* if(res.message!="认证失败"){ */
          this.punForm.id = res.result.id;
          this.punForm.contentPun = res.result.content;
          this.automaticSentenceDisabled = false;
          this.isShowDisabled = true;
          this.autoPunIsShow = true;
          this.wordsList();
          this.punForm.grade = 0;
          /* } */
        } else {
          if (res.message == "重复请求，请稍后再试") {
            this.automaticSentenceDisabled = true;
            this.autoPunIsShow = true;
          }
        }
      });
    },
    //自动标点
    autoPunctuation() {

      console.log(this.punForm.innerText);
      if (this.punForm.innerText.length == 0) {
        this.msgInfo("需要自动标点的内容不能为空！");
        return;
      }
      if (this.punForm.innerText.length > this.available) {
        this.msgInfo("需要自动标点的内容的字数大于可用字数，请先申请字数！");
        return;
      }
      this.punDisabled = false;
      this.$refs.divEditable.divEdit(false);
      this.punForm.contentPun = "";
      this.getPun();
    },
    getPun() {
      let params = {
        content: this.punForm.innerText,
        type: "1",
        status: "2",
      };
      this.spinShow = true;
      this.spinText = "自动标引中，请稍后。。。";
      getPunctuateAutoPunctuation(params).then((res) => {
        if (res.code == 403) {
            this.msgInfo("请先登录！");
            return;
        }
        this.spinShow = false;
        if (res.code === 200) {
          this.punForm.id = res.data.id;
          this.punForm.contentPun = res.data.content;
          this.autoPunIsShow = false;
          this.automaticSentenceDisabled = true;
          this.isShowDisabled = true;
          this.wordsList();
          this.punForm.grade = 0;
          /* } */
        } else {
          if (res.message == "重复请求，请稍后再试") {
            this.automaticSentenceDisabled = true;
            this.autoPunIsShow = true;
          }
        }
      });
    },
    //纠错
    errorCorrection() {
     
      if (this.punForm.id == null) {
        this.msgInfo("清先自动标点！");
        return;
      }
      if (!this.punForm.contentPun) {
        this.msgInfo("内容为空，不能纠错！");
        return;
      }
      this.punDisabled = true;
      this.$refs.divEditable.parentHandleclick("嘿嘿嘿");
      this.$refs.divEditable.divEdit(true);
    },
    //完成纠错
    punctuationSubmit() {

      var reg = /<[^<>]+>/g;
      let completeContent = this.punForm.contentPun.replace(reg, "");
      if (!completeContent) {
        this.msgInfo("请输入纠错内容！");
        return;
      }
      this.punForm.content = this.punForm.contentPun;
      console.log("传给后台的值" + this.punForm.content);
      console.log(this.punForm);
      getPunctuatecorrectPunctuation(this.punForm).then((res) => {
          if (res.code == 403) {
            this.msgInfo("请先登录！");
            return;
        }
        
        if (res.code == 200) {
          if (res.code == 500) {
            this.msgInfo(res.msg);
            this.punForm.contentPun = res.data.content;
          } else {
            this.punForm.contentPun = res.data.content;
            this.msgSuccess(res.msg);
            this.punDisabled = false;
            this.$refs.divEditable.divEdit(false);
          }
          console.log("后台返回的值" + res.data.content);
          this.wordsList();
        }
      });
    },
    //清除
    clear() {
      this.confirmModel = true;
    },
    ok() {
      this.punForm.innerText = "";
      this.punForm.id = "";
      this.punForm.content = "";
      this.punForm.contentPun = "";
      this.punForm.fontCount = 0;
      this.punForm.grade = 0;
      this.punDisabled = false;
      this.confirmModel = false;
      this.autoPunIsShow = true;
      this.automaticSentenceDisabled = true;
      this.isShowDisabled = false;
    },
    downloadText() {
      this.spinShow = true;
      this.spinText = "下载中，请稍后。。。";
      
      this.spinShow = false;

      if (!this.punForm.id) {
        this.msgInfo("内容为空，不能下载！");
        return;
      }
      let parmas = {
        id: this.punForm.id,
      };
      console.log(parmas);
      getPunctuateDownloadText(parmas).then((res) => {
          if (res.data == null) {
            this.msgInfo("请先登录！");
            return;
        }
        
        const content = res;
        const blob = new Blob([content], {
          type: "application/octet-stream",
        });
        let time = this.getTime();
        const fileName = this.nikeName + "_" + time + ".txt";
        if ("msSaveOrOpenBlob" in navigator) {
          window.navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          const elink = document.createElement("a");
          elink.download = fileName;
          elink.style.display = "none";
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href); // 释放URL 对象
          document.body.removeChild(elink);
        }
      });
    },
    //获取当前时间
    getTime() {
      var date1 = new Date();
      var year = date1.getFullYear();
      var month = date1.getMonth() + 1;
      var day = date1.getDate();
      var hours = date1.getHours();
      var minutes = date1.getMinutes();
      var seconds = date1.getSeconds();
      return year + "-" + month + "-" + day + "_" + hours + minutes + seconds;
    },
    wordsList() {
      getPunctuateAvailableWords().then((res) => {
        if (res.code == 200) {
          this.available = res.data.available;
        }
      });
    },
    fontLenght(v) {
      
      // alert("sdfsdf");
      this.autoPunIsShow = true;
      this.automaticSentenceDisabled = true;
      if (v) {
        let str = v.length;
        this.punForm.fontCount = str;

        if (str.length > 5000) {
          this.msgInfo("您本次复制内容超过5000，已自动截取规定范围内的字数！");
        }
      }
    },
    blurHighLight() {
      // 这里做数据过滤或样式变更操作
      //const pattern = /[，、·~。？！；：【】〖〗〔〕﹝﹞《〈〉》〈〉＜＞（）｛｝“”〝〞‘’＇「」『』［］]{1}(?!<\/span>)/g;
      //this.punForm.contentPun = this.punForm.contentPun.replace(pattern,(val)=>`<span class="edit">${val}</span>`);
      // console.log(this.punForm.contentPun);
    },
    webSocketNumber(v) {
      this.count = v;
    },

    init() {
      this.wordsList();
    },
    changeGrade(v) {
      
      if (!this.punForm.id) {
        this.msgInfo("需要评价的内容为空！暂不能评价！");
        this.$nextTick(function () {
          this.punForm.grade = 0;
        });
        return;
      }
      let parmas = {
        id: this.punForm.id,
        result: this.punForm.grade + "",
        content: "",
      };
      getPunctuateassessPunctuarion(parmas).then((res) => {
        if (res.code == 200) {
          this.msgSuccess("评价成功");
        }
      });
    },
    initDriverPlugin() {
      // 初始化引导页
      this.Driver = new Driver({
        doneBtnText: "知道了", // 结束按钮的文字
        allowClose: false, // 是否可以通过点击遮罩层关闭指引
        stageBackground: "#dcdee242", //突出显示元素的背景颜色
        nextBtnText: "下一步", // 下一步按钮的文字
        prevBtnText: "上一步", // 上一步按钮的文字
        closeBtnText: "关闭指引", // 关闭按钮的文字
        keyboardControl: false, // 是否允许键盘操控
        xCloseButton: false, // 将关闭按钮作为X放在弹出
      });
      // 判断是否需要打开新手指引
      if (this.openDriver) {
        this.Driver.defineSteps(this.steps);
        this.Driver.start();
      }
    },
  },
  mounted() {
    // $("ul").on("click","li",function () {
    //   alert("事件委托");
    // });
    /* this.$nextTick(() => {
        $('body').on('input','.ivu-input', function(){
          alert('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa')
        })
        }); */
    //    this.$nextTick(() => {
    //     var input = document.getElementsByClassName('ivu-input');
    //     if (input.addEventListener) {
    //         input.addEventListener('change', function () {
    //             console.log('aaaaaa');
    //         });
    //     }
    //     else {
    //         input.attachEvent('onchange', function () {
    //             console.log('1111');
    //         });
    //     }
    //     });
    this.punForm.fontCount = 0;
    getPunctuateAuthentication().then((res) => {
      if (res.success === true) {
        this.userInfo = res.result;
        this.nikeName = this.userInfo.nikeName;
        this.init();
        let data = this.$route.params;
        var arr = Object.keys(data);
        if (arr.length > 0) {
          this.punForm = data;
        }
        if (this.userInfo._guideG == "0") {
          this.$nextTick(() => {
            this.initDriverPlugin();
          });
          //取消引导
          getPunctuateGuide().then((res) => {});
        }
      } else {
        this.userInfo = "";
        Cookies.set("apuUserInfo", "");
        sessionStorage.removeStore("userKey");
      }
    });
  },
};
</script>
