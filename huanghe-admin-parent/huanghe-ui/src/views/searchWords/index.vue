<template>
  <div :style="{ height: mainPage + 'px' }" >
    <div class="top">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="搜索词" prop="orderCode">
        <el-input
          v-model="queryParams.searchContent"
          placeholder="请输入"
          clearabled
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    </div>
      <!-- <el-scrollbar
        wrapClass="scrollbar-wrap"
        style="width: 110%; height: 95%"
        ref="scrollbarContainer"
      > -->

          <el-table
              :height="tableHeight"
           style="width: 100%"
            v-loading="loading"
            :data="searchList"
            :resizable="false"
           
          >
            <el-table-column
              label="序号"
              align="center"
              type="index"
              width="60px"
            />
            <el-table-column
              label="搜索词"
              align="center"
              prop="searchContent"
            />
            <el-table-column label="搜索次数" align="center" prop="searchCount" />
          </el-table>
      <!-- </el-scrollbar> -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
        </div>
</template>

<script>
import {
  searchTop,
  delSearchContent,
  deleteSearch,
} from "@/api/homePage/homePage";
export default {
  name: "searchTopPage",
  data() {
    return {
        tableHeight: window.innerHeight - 300,
      mainPage: window.innerHeight - 200,
      loading: false,
      searchList: [],
      total: 0,
      scrollHeight: "0px",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchContent: "",
        selectData: [],
      },
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.scrollHeight = window.innerWidth * 0.8 + "px";
  },
  methods: {
    getList() {
      this.loading = true;
      searchTop(this.queryParams).then((response) => {
        this.searchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.searchContent = "";
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.selectData = selection;

      this.multiple = !selection.length;
    },
    //批量删除
    handleDelete() {
      this.$confirm("确认删除吗", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delSearchContent(this.selectData)
          .then((response) => {
            this.msgSuccess("删除成功");
            this.getList();
          })
          .catch(function () {});
      });
    },
    //删除
    handleDeleteBy(row) {
      const id = row.id;
      this.$confirm("确认删除吗", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteSearch(id)
          .then(() => {
            this.msgSuccess("删除成功");
            this.getList();
          })
          .catch(function () {});
      });
    },
  },
};
</script>
<style scoped>
.top {
  margin-top: 20px;

}
.bottom {
  margin-bottom: 10px;
  float: right;
}
/* .el-pagination{
  //margin-bottom: 100px;
} */
.el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>