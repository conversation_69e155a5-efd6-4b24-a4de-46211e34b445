<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="机构名称" prop="orgName">
          <el-input v-model="queryParams.orgName" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" style="width:150px"/>
        </el-form-item>
        <el-form-item label="机构编号" prop="orgCode">
          <el-input v-model="queryParams.orgCode" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" style="width:150px" />
        </el-form-item>

        <el-form-item label="机构类型" prop="orgType">
          <el-select v-model="queryParams.orgType" placeholder="请选择" clearable size="small" style="width:150px">
            <el-option v-for="item in orgTypeOptions" :key="item.id" :label="item.typeName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="机构联系人" prop="userName" label-width="82px">
          <el-input v-model="queryParams.userName" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" style="width:150px"/>
        </el-form-item>
        <el-form-item label="状态" prop="orgStatus" label-width="40px">
          <el-select v-model="queryParams.orgStatus" placeholder="请选择" clearable size="small" style="width:120px">
            <el-option v-for="dict in orgStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['plat:organ:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['plat:organ:export']">导出
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plat:organ:remove']"
          >删除</el-button
        >
      </el-col> -->

      <el-col :span="1.5">
        <el-button type="danger" size="mini" @click="routeOrganType" v-hasPermi="['plat:organ:route']">机构类型管理</el-button>
      </el-col>
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table style="margin-top: 30px" v-loading="loading" :data="organList" @selection-change="handleSelectionChange"
    :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 300px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="50" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column> <el-table-column label="机构编号" align="center" prop="orgCode" />
      <el-table-column label="机构名称" align="center" prop="orgName" sortable show-overflow-tooltip="true" min-width="250"/>
      <el-table-column label="机构类型" align="center" prop="typeName" sortable min-width="100"/>
      <el-table-column label="机构IP" align="center" prop="ipRange" min-width="200" />
      <!-- <el-table-column
        label="机构状态"
        align="center"
        prop="orgStatus"
        sortable
      >
        <template slot-scope="scope">
          <el-tag :type="scope.row.orgStatus == '0' ? 'success' : 'danger'">
            {{ scope.row.orgStatus == "0" ? "在用" : "停用" }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="机构联系人" align="center" prop="userName" sortable min-width="120"/>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.orgStatus" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <!-- <el-button
            v-if="scope.row.orgStatus === '0'"
            size="small"
            type="text"
            icon="el-icon-circle-close"
            v-hasPermi="['plat:organ:edit']"
            @click="handleUpdateStatus(scope.row, scope.row.orgStatus)"
            >禁用</el-button
          >
          <el-button
            v-else
            size="small"
            type="text"
            icon="el-icon-circle-check"
            v-hasPermi="['plat:organ:edit']"
            @click="handleUpdateStatus(scope.row, scope.row.orgStatus)"
            >启用</el-button
          > -->
          <el-button size="small" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['plat:organ:edit']">编辑</el-button>
          <!-- <el-button
            size="small"
            type="text"
            icon="el-icon-setting"
            @click="handleRestPass(scope.row)"
            v-hasPermi="['plat:organ:edit']"
            >重置密码</el-button
          > -->

          <el-button size="small" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['plat:organ:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改平台机构对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" append-to-body width="800px">
      <el-container>
        <!-- <span style="font-size: larger;font-weight: 700">新增机构</span> -->
        <el-main>
          <el-form ref="formData" :model="formData" :rules="rules" size="medium" label-width="100px"
            style="margin-top: 2vh">
            <el-row>
              <el-col :span="10">
                <el-form-item label="机构名称" prop="orgName">
                  <el-input v-model="formData.orgName" placeholder="请输入内容" clearable maxlength="50" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="联系人" prop="userName">
                  <el-input v-model="formData.userName" placeholder="请输入内容" clearable maxlength="10" show-word-limit />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">
                <el-form-item label="机构编号" prop="orgCode">
                  <el-input v-model="formData.orgCode" placeholder="请输入机构编号" show-word-limit clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="手机号" prop="userTel">
                  <el-input v-model="formData.userTel" placeholder="请输入" show-word-limit clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">
                <el-form-item label="机构类型" prop="orgType">
                  <el-select v-model="formData.orgType" placeholder="请输入机构类型" clearable :style="{ width: '100%' }">
                    <el-option v-for="(item, index) in orgTypeOptions" :key="index" :label="item.typeName"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col span="10">
                <el-form-item label="状态" prop="orgStatus">

                  <el-switch v-model="formData.orgStatus" active-value="0" inactive-value="1" active-color="#13ce66"
                    inactive-color="#000000">
                  </el-switch>

                </el-form-item>
              </el-col>
            </el-row>


            <el-col :span="20">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="formData.remark" placeholder="请输入内容" clearable show-word-limit type="textarea"
                  :rows="5" resize='none' :style="{ width: '100%' }" />
              </el-form-item>
            </el-col>



            <el-col :span="20">
              <el-form-item label="机构IP" v-for="(domain, index) in formData.orgIps"
                :label="index == 0 ? '机构IP' : '机构IP' + (index + 1)" :key="domain.key" :prop="'orgIps.' + index + '.value'"
                :rules="{
                  required: true,
                  validator: checkOrgIp,
                  trigger: 'blur',
                }">
                <el-input v-model="domain.value" placeholder="请输入机构IP" clearable :style="{ width: '80%' }"></el-input>
                <el-button v-if="index === 0" type="primary" icon="el-icon-plus" style="margin-left: 10px"
                  @click="addDomain"></el-button>
                <el-button v-else type="primary" icon="el-icon-minus" style="margin-left: 10px"
                  @click.prevent="removeDomain(domain)"></el-button>
              </el-form-item>
            </el-col>


            <!-- <el-col :span="22">
            <el-form-item label="机构地址" prop="orgAddress">
              <el-input
                v-model="formData.orgAddress"
                placeholder="请输入机构地址"
                clearable
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col> -->
          </el-form>
        </el-main>
      </el-container>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- <el-divider></el-divider>
      <el-row :gutter="15">
        <span style="font-size: larger; font-weight: 700">新增机构管理员</span>
        <el-form
          ref="userFormData"
          :model="userFormData"
          :rules="rules"
          size="medium"
          label-width="100px"
          style="margin-top: 3vh"
        >
          <el-col :span="11">
            <el-form-item label="登录名" prop="loginName">
              <el-input
                v-model="userFormData.loginName"
                placeholder="登录名"
                clearable
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="管理员姓名" prop="userName">
              <el-input
                v-model="userFormData.userName"
                placeholder="请输入管理员姓名"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="userFormData.userId === null" :span="11">
            <el-form-item label="登录密码" prop="loginPass">
              <el-input
                show-password
                v-model="userFormData.loginPass"
                placeholder="请输入登录密码"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="userFormData.userId === null" :span="11">
            <el-form-item label="确认密码" prop="confirmLoginPass">
              <el-input
                show-password
                v-model="userFormData.confirmLoginPass"
                placeholder="请输入确认密码"
                clearable
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="联系人电话" prop="userTel">
              <el-input
                v-model="userFormData.userTel"
                placeholder="请输入联系人电话"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="联系人邮箱" prop="userEmail">
              <el-input
                v-model="userFormData.userEmail"
                placeholder="请输入联系人邮箱"
                clearable
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="22">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="userFormData.remark"
                type="textarea"
                placeholder="请输入备注"
                :maxlength="2000"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row> -->
    <!-- <div slot="footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog> -->

    <el-dialog title="重置密码" :visible.sync="passWordOpen" :close-on-click-modal="false" width="700px" append-to-body>
      <el-form ref="passWordFormData" :model="passWordFormData" :rules="passWordRules" label-width="80px">
        <el-form-item label="登录名">
          <span>{{ passWordFormData.loginName }}</span>
        </el-form-item>
        <el-col :span="11">
          <el-form-item label="登录密码" prop="loginPass">
            <el-input show-password v-model="passWordFormData.loginPass" placeholder="请输入登录密码" clearable
              :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="确认密码" prop="confirmLoginPass">
            <el-input show-password v-model="passWordFormData.confirmLoginPass" placeholder="请输入确认密码" clearable
              :style="{ width: '100%' }"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer">
        <el-button @click="passWordCancel">取消</el-button>
        <el-button type="primary" @click="passWordSubmitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listOrgan,
  getOrgan,
  delOrgan,
  addOrgan,
  updateOrgan,
  exportOrgan,
  updateOrganStatus,
  changeOrgStatus,
} from "@/api/plat/organ";
import { listType } from "@/api/plat/type";
import { getUser, updateUser } from "@/api/plat/user";

export default {
  name: "Organ",
  data() {
    let validateConfirmPassword = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入确认密码"));
      } else if (value !== this.userFormData.loginPass) {
        callback(new Error("两次密码输入不一致"));
      } else {
        callback();
      }
    };
    let restPassValidateConfirmPassword = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入确认密码"));
      } else if (value !== this.passWordFormData.loginPass) {
        callback(new Error("两次密码输入不一致"));
      } else {
        callback();
      }
    };
    return {
      tableHeight: window.innerHeight - 400,
      title: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      names: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 平台机构表格数据
      organList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出层
      passWordOpen: false,
      // 类型状态字典
      orgStatusOptions: [],
      // 查询参数
      queryParams: {
        ids: null,
        pageNum: 1,
        pageSize: 10,
        orgCode: null,
        orgName: null,
      },
      // 表单参数
      formData: {
        orgIps: [
          {
            key: Date.now(),
            value: "",
          },
        ],
      },
      //用户表单
      userFormData: {},
      //重置密码表单
      passWordFormData: {},
      // 表单校验
      rules: {
        orgCode: [
          {
            required: true,
            message: "请输入机构编号",
            trigger: "blur",
          },
        ],
        orgName: [
          {
            required: true,
            message: "请输入机构名称",
            trigger: "blur",
          },
        ],
        orgType: [
          {
            required: true,
            message: "请输入机构类型",
            trigger: "change",
          },
        ],
        orgIP: [
          {
            required: true,
            message: "请输入机构IP",
            trigger: "blur",
          },
        ],
        orgAddress: [
          {
            required: true,
            message: "请输入机构地址",
            trigger: "blur",
          },
        ],
        loginName: [
          {
            required: true,
            message: "请输入登录名",
            trigger: "blur",
          },
        ],
        userName: [
          {
            required: true,
            message: "请输入管理员姓名",
            trigger: "blur",
          },
        ],
        loginPass: [
          {
            required: true,
            message: "请输入登录密码",
            trigger: "blur",
          },
        ],
        confirmLoginPass: [
          {
            required: true,
            validator: validateConfirmPassword,
            trigger: "blur",
          },
        ],
        userTel: [
          {
            required: true,
            message: "请输入联系人电话",
            trigger: "blur",
          },
          {
            pattern: /^1(3|4|5|7|8|9)\d{9}$/,
            message: "手机号格式错误",
            trigger: "blur",
          },
        ],
        userEmail: [
          {
            required: true,
            message: "请输入联系人邮箱",
            trigger: "blur",
          },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
            message: "邮箱格式错误",
            trigger: "blur",
          },
        ],
      },
      passWordRules: {
        loginPass: [
          {
            required: true,
            message: "请输入登录密码",
            trigger: "blur",
          },
          {
            min: 6,
            message: "长度不能小于6个字符",
          },
        ],
        confirmLoginPass: [
          {
            required: true,
            validator: restPassValidateConfirmPassword,
            trigger: "blur",
          },
        ],
      },
      orgTypeOptions: [],
      orgTypeParams: {
        typeStatus: null,
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then((response) => {
      this.orgStatusOptions = response.data;
    });
    this.getOrgTypeList();
  },
  methods: {
    //路由到机构类型管理
    routeOrganType() {
      this.$router.push({ path: "/res/organ-organType" });
    },
    //修改状态
    handleStatusChange(row) {
      let text = row.orgStatus === "0" ? "启用" : "停用";
      this.$confirm("确认要" + text + "该机构吗?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return changeOrgStatus(row.id, row.orgStatus);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.orgStatus = row.orgStatus === "0" ? "1" : "0";
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const ids = this.ids.join(",");
      const queryParams = this.queryParams;
      queryParams.ids = ids;

      if (ids.length == 0) {
        this.$confirm("是否确认导出所有平台数据项?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return exportOrgan(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          })
          .catch(function () { });
      } else {
        this.$confirm("是否确认导出所选择的数据项?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return exportOrgan(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          })
          .catch(function () { });
      }

    },
    /** 查询平台机构列表 */
    getList() {
      this.loading = true;
      listOrgan(this.queryParams).then((response) => {
        this.organList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 类型状态 -1 不可用  0-可用
    orgStatusFormat(row, column) {
      return this.selectDictLabel(this.orgStatusOptions, row.orgStatus);
    },
    getOrgTypeList() {
      listType(this.orgTypeParams).then((response) => {
        this.orgTypeOptions = response.rows;
        console.log(this.orgTypeOptions);
      });
    },
    // 类型状态字典翻译
    orgStatusFormat(row, column) {
      return this.selectDictLabel(this.orgStatusOptions, row.orgStatus);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    passWordCancel() {
      this.passWordOpen = false;
      this.restPassFormReset();
    },
    // 表单重置
    reset() {
      this.formData = {
        orgId: null,
        orgCode: null,
        orgName: null,
        orgType: null,
        orgAddress: null,
        orgStatus: 1,
        orgIps: [
          {
            key: Date.now(),
            value: "",
          },
        ],
        userName: null,
        userTel: null,
      };
      this.resetForm("formData");
      // this.userFormData = {
      //   userId: null,
      //   loginName: null,
      //   userName: null,
      //   loginPass: null,
      //   confirmLoginPass: null,
      //   userTel: null,
      //   userEmail: null,
      //   remark: null,
      // };
      // this.resetForm("userFormData");
    },
    restPassFormReset() {
      this.passWordFormData = {
        id: null,
        loginName: null,
        loginPass: null,
        confirmLoginPass: null,
      };
      this.resetForm("passWordFormData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.names = selection.map((item) => item.orgName);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.orgTypeParams.typeStatus = "0";
      this.title = "新增机构";
      this.getOrgTypeList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;

      getOrgan(id).then((response) => {
        console.log(response);
        this.formData = response.data;
        this.getOrgTypeList();
        this.open = true;
        this.title = "修改机构";
      });
      console.log(1, this.formData);
    },
    /** 提交按钮 */
    submitForm() {
      const p1 = new Promise((resolve, reject) => {
        this.$refs["formData"].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject();
          }
        });
      });
      // const p2 = new Promise((resolve, reject) => {
      //   this.$refs["userFormData"].validate((valid) => {
      //     if (valid) {
      //       resolve();
      //     } else {
      //       reject();
      //     }
      //   });
      // });
      // Promise.all([p1, p2])
      //   .then((res) => {
      //     //都通过了
      //     console.log("验证通过,提交表单");
      //     //合并表单
      //     Object.assign(this.formData, this.userFormData);
      //     console.log(this.formData);
      //     if (this.formData.orgId != null) {
      //       updateOrgan(this.formData).then((response) => {
      //         if (response.code === 200) {
      //           this.msgSuccess("修改成功");
      //           this.open = false;
      //           this.getList();
      //         }
      //       });
      //     } else {
      //       this.formData.userType = "S";
      //       addOrgan(this.formData).then((response) => {
      //         if (response.code === 200) {
      //           this.msgSuccess("新增成功");
      //           this.open = false;
      //           this.getList();
      //         } else {
      //           this.msgError(response.msg);
      //         }
      //       });
      //     }
      //   })
      //   .catch((e) => {
      //     console.log(e);
      //     return;
      //   });

      Promise.all([p1])
        .then((res) => {
          //都通过了
          console.log("验证通过,提交表单");
          //合并表单
          console.log(this.formData);
          if (this.formData.orgId != null) {
            updateOrgan(this.formData).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            this.formData.userType = "S";
            addOrgan(this.formData).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        })
        .catch((e) => {
          console.log(e);
          return;
        });
    },
    /***重置密码提交操作*/
    passWordSubmitForm() {
      this.$refs["passWordFormData"].validate((valid) => {
        if (valid) {
          if (this.passWordFormData.id != null) {
            updateUser(this.passWordFormData).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("重置成功");
                this.passWordOpen = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const names = row.orgName || this.names;
      this.$confirm('是否确认删除机构名称为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delOrgan(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    /** 重置密码按钮操作 */
    handleRestPass(row) {
      this.restPassFormReset();
      const id = row.userId;
      getUser(id).then((response) => {
        this.passWordFormData.loginName = response.data.loginName;
        this.passWordFormData.id = id;
        this.passWordOpen = true;
      });
    },
    /** 禁用启用按钮操作 */
    handleUpdateStatus(row, status) {
      const id = row.id;
      let msg = "";
      if (status === "1") {
        status = 0;
        msg = "启用";
      } else {
        status = 1;
        msg = "禁用";
      }
      this.$confirm("是否" + msg + '机构"' + row.orgName + '"?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          const form = {
            id: id,
            orgStatus: status,
          };
          return updateOrganStatus(form);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(msg + "成功");
        })
        .catch(function () { });
    },
    removeDomain(item) {
      var index = this.formData.orgIps.indexOf(item);
      if (index !== -1) {
        this.formData.orgIps.splice(index, 1);
      }
    },
    addDomain() {
      this.formData.orgIps.push({
        value: "",
        key: Date.now(),
      });
    },
    checkOrgIp(rule, value, callback) {
      let arr = value.split("-");
      if (arr.length < 2) {
        this.checkIp(rule, value, callback);
      } else {
        this.checkIpRange(rule, value, callback);
      }
    },
    checkIp(rule, value, callback) {
      let reg =
        /^(?:(?:2[0-4][0-9]\.)|(?:25[0-5]\.)|(?:1[0-9][0-9]\.)|(?:[1-9][0-9]\.)|(?:[0-9]\.)){3}(?:(?:2[0-4][0-9])|(?:25[0-5])|(?:1[0-9][0-9])|(?:[1-9][0-9])|(?:[0-9]))$/;
      let re = new RegExp(reg);
      if (re.test(value)) {
        callback();
      } else {
        callback(new Error("请填写正确的IP地址！"));
      }
    },
    checkIpRange(rule, value, callback) {
      let reg =
        /^(?:(?:2[0-4][0-9]\.)|(?:25[0-5]\.)|(?:1[0-9][0-9]\.)|(?:[1-9][0-9]\.)|(?:[0-9]\.)){3}(?:(?:2[0-4][0-9])|(?:25[0-5])|(?:1[0-9][0-9])|(?:[1-9][0-9])|(?:[0-9]))$/;
      let re = new RegExp(reg);
      if (!value) {
        callback(new Error("请填写IP区间内容！"));
        return;
      }
      let arr = value.split("-");
      if (!re.test(arr[0])) {
        callback(new Error("请填写正确的IP起始地址！"));
        return;
      }
      if (!re.test(arr[1])) {
        callback(new Error("请填写正确的IP结束地址！"));
        return;
      }
      if (arr.length !== 2 || !this.compareIp(arr[0], arr[1])) {
        callback(new Error("请填写正确的IP区间范围！"));
        return;
      }
      callback();
    },
    compareIp(start, end) {
      let status = false;
      let startArr = start.split(".");
      let endArr = end.split(".");
      for (let i = 0; i < startArr.length; i++) {
        const startItem = startArr[i];
        const endItem = endArr[i];
        if (parseInt(endItem) > parseInt(startItem)) {
          status = true;
          break;
        } else if (parseInt(endItem) < parseInt(startItem)) {
          status = false;
          break;
        }
      }
      return status;
    },
  },
};
</script>
<style type="text/css" scoped>
.top {
  margin-left: 10px;
  margin-top: 20px;
}

.dialog-footer {
  margin-top: -60px;
}

.mb8 {
  margin-bottom: -25px;
}

::v-deep .el-dialog-footer {
  padding: 20px;
  padding-top: 0px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mb8 {
  margin-bottom: -10px !important;
}
.tableS{
  margin-top: 0px;
}
</style>
