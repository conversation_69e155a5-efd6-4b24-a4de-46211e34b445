<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-row>
          <el-form-item label="订单编号" prop="orderNo">
            <el-input v-model="queryParams.orderNo" placeholder="请输入" clearable size="small"
              @keyup.enter.native="handleQuery" style="width:150px"/>
          </el-form-item>
          <el-form-item label="昵称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入" clearable size="small"
              @keyup.enter.native="handleQuery" style="width:150px"/>
          </el-form-item>
          <el-form-item label="订单状态" prop="orderValid">
            <el-select v-model="queryParams.orderValid" placeholder="请选择" clearable size="small" style="width:120px">
              <el-option v-for="dict in orderStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>


          <!-- <el-form-item label="授权开始时间" prop="authStartTime" label-width="98px">
            <el-date-picker clearable size="small" style="width: 150px" v-model="queryParams.authStartTime" type="date"
              value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权结束时间" prop="authEndTime" label-width="98px">
            <el-date-picker clearable size="small" style="width: 200px" v-model="queryParams.authEndTime" type="date"
              value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          </el-form-item> !-->
          <el-form-item label="时间查询" prop="searchTime" label-width="98px">
            <el-date-picker value-format="yyyy-MM-dd" v-model="searchTime" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="width:240px">
            </el-date-picker>

          </el-form-item>



          <el-form-item>
            <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div style="margin-left: 10px; width: 98%">
      <el-row :gutter="10" class="mb8">

        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['order:wxorder:export']">导出
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['order:wxorder:remove']">删除</el-button>
        </el-col>


        <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>
    </div>

    <el-table style="width: 100%; margin-top: 30px" v-loading="loading" :data="orderList"
      @selection-change="handleSelectionChange" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
      size='medium' :header-row-style="{ height: '20px' }" height="calc(100vh - 320px)"
      :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column min-width="40" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column> <el-table-column :show-overflow-tooltip="true" label="订单编号" align="center" prop="orderNo"
      min-width="100" />
      <el-table-column :show-overflow-tooltip="true" label="昵称" align="center" prop="userName" min-width="100"/>
      <el-table-column :show-overflow-tooltip="true" label="订单状态" align="center" prop="orderValid"
        :formatter="orderStatusFormat" sortable>
        <template slot-scope="scope">
          <el-tag :type="scope.row.orderValid == '1'
            ? 'success'
            : scope.row.orderValid == '0'
              ? 'warning'
              : 'danger'
            ">
            {{
              scope.row.orderValid == "1"
              ? "已授权"
              : scope.row.orderValid == "0"
                ? "已停止"
                : "已关闭"
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="price" sortable min-width="80">
      </el-table-column>
      <el-table-column label="授权开始时间" align="center" prop="validStartTime" width="160" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.validStartTime!=null">{{ scope.row.validStartTime }}</span>
          <span v-else>{{ "永久" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权结束时间" align="center" prop="validExpireTime" width="160" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.validExpireTime!=null">{{ scope.row.validExpireTime }}</span>
          <span v-else>{{ "永久" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" align="center" prop="createTime" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.orderStatus === 0 || scope.row.orderStatus === 2" @click="handleStatus(scope.row)"
            size="mini" type="text" icon="el-icon-circle-plus-outline" v-hasPermi="['order:order:role']">授权</el-button>
          <el-button v-if="scope.row.orderStatus === 1" size="mini" type="text" icon="el-icon-circle-close"
            @click="handleStatus(scope.row)">停止</el-button>

          <el-button size="mini" type="text" @click="handleFind(scope.row)" v-hasPermi="['order:wxorder:view']"
            icon="el-icon-view">查看</el-button>
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)" v-hasPermi="['order:wxorder:edit']"
            icon="el-icon-edit">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['order:order:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- <pagination
        v-show="totalLog > 0"
        :total="totalLog"
        :page.sync="queryLogParams.pageNum"
        :limit.sync="queryLogParams.pageSize"
        @pagination="getLog"
      /> -->

    <!-- 添加或修改数据库订单管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="12" class="mb8">
          <el-form-item label="昵称" prop="userName" required="true">
            <el-input v-model="form.userName" placeholder="请输入订单创建人" disabled />
          </el-form-item>
          <el-form-item label="订单金额" prop="price" required="true">
            <el-input v-model="form.price" placeholder="请输入订单创建人" disabled />
          </el-form-item>
          <el-form-item label="子库" prop="" required="true">
            <el-input v-model="form.databaseName" placeholder="" disabled />
          </el-form-item>
          <el-form-item label="时长" prop="quantity" required="true">
            <el-input v-model="form.quantity"  placeholder="" disabled />

          </el-form-item>
          <el-form-item label="状态" prop="orderValid">
            <el-switch v-model="form.orderValid" active-value="1" inactive-value="0" active-color="#13ce66"
              inactive-color="#000000">
            </el-switch>
          </el-form-item>
          <el-form-item label="授权开始时间" prop="validStartTime">

            <el-input v-model="form.validStartTime" v-if="form.validStartTime=='永久'"  
              disabled>
            </el-input>
            <el-date-picker v-model="form.validStartTime" v-else type="date" value-format="yyyy-MM-dd" placeholder="授权开始时间"
              disabled>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="授权结束时间" prop="validExpireTime">
            <el-input v-model="form.validExpireTime"  v-if="form.validExpireTime=='永久'"  
              disabled>
            </el-input>
            <el-date-picker v-model="form.validExpireTime" v-else type="date" value-format="yyyy-MM-dd" placeholder="授权结束时间"
              disabled>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="备注" prop="content">
            <el-input v-model="form.content" placeholder="请输入内容" clearable show-word-limit type="textarea" :rows="5"
              resize='none' :style="{ width: '90%' }"  />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 查看 -->
    <el-dialog :title="title" :visible.sync="FindOpen" width="750px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="120px">
        <el-row class="">
          <el-col span="10">
            <span>昵称：</span>
            <span>{{ form.userName }}</span>
          </el-col>
          <el-col span="10">
            <span>订单金额：</span>
            <span>{{ form.price }}</span>
          </el-col>
        </el-row>

        <el-row class="row_value">
          <el-col span="10">
            <span>子库：</span>
            <span>{{ form.databaseName }}</span>
          </el-col>
          <el-col span="10">
            <span>时长：</span>
            <span  v-if="form.quantity!=0">{{ form.quantity }}</span>
            <span  v-else>{{ "永久" }}</span>

          </el-col>
        </el-row>

        <el-row class="row_value">
          <el-col span="10">
            <span>订单状态：</span>
            <span v-if="form.orderValid == 1">已授权</span>
            <span v-if="form.orderValid == 0">未授权</span>
          </el-col>
          <el-col span="12">
            <span>下单时间：</span>
            <span>{{ form.createTime }}</span>
          </el-col>
        </el-row>

        <el-row class="row_value">
          <el-col span="10">
            <span>授权开始时间：</span>
            <span v-if="form.validStartTime!=null">{{ form.validStartTime }}</span>
            <span v-else>{{ "永久" }}</span>

          </el-col>
          <el-col span="10">
            <span>授权结束时间：</span>
            <span v-if="form.validExpireTime!=null">{{ form.validExpireTime }}</span>
            <span v-else>{{ "永久" }}</span>
          </el-col>
        </el-row>

        <el-row class="row_value">
          <el-col span="24">
            <span>备注:</span>
            <span>{{ form.content }}</span>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="findCancel">确 定</el-button>
        <el-button @click="findCancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  updateStaus,
  updateStaus1,
  checkOrder,
  listOrder,
  getOrder,
  delOrder,
  addOrder,
  updateOrder,
  exportOrder,
} from "@/api/order/wxorder";
import { dbNames, formatDate } from "@/api/product/book/books";
import Cookies from "js-cookie";
import { listOrgan, getOrgan } from "@/api/plat/organ";
export default {
  name: "wxOrder",
  data() {
    const daba = (rule, value, callback) => {
      //debugger;
      console.log(!value);
      if (this.ktDatabase.length == 0) {
        callback(new Error("数据库不能为空"));
      } else {
        callback();
      }
    };
    const validateAuthEndTime = (rule, value, callback) => {
      //debugger;

      let endTime = new Date(value);

      let date = this.form.authStartTime

      let startTime = new Date(date)

      // console.log(startTime)
      // console.log(endTime)


      if (value == null) {
        callback(new Error("授权结束不能为空"));
      } else if (endTime >= startTime) {
        callback();
      } else {
        callback(new Error("应大于开始时间"));
      }
    };

    const final = (rule, value, callback) => {
      const finalPrice = /^\d+(?:\.\d{1,2})?$/;
      if (!value) {
        callback(new Error("价格不能为空"));
      } else if (!finalPrice.test(value)) {
        callback(new Error("只能为数字"));
      } else {
        callback();
      }
    };
    const pri = (rule, value, callback) => {
      //debugger;
      if (value==null) {
        callback(new Error("价格不能为空"));
      }
      const price = /^\d+(?:\.\d{1,2})?$/;
      if (!price.test(value)) {
        callback(new Error("只能为数字"));
      } else {
        callback();
      }
    };

    const org = (rule, value, callback) => {
      //debugger;
      if (!this.form.orgId) {
        callback(new Error("机构名称不能为空"));
      } else {
        callback();
      }
    };

    const dis = (rule, value, callback) => {
      const discount = /^100$|^(\d|[1-9]\d)(\.\d{1,4})*$/;
      if (!value) {
        callback(new Error("折扣不能为空"));
      } else if (!discount.test(value) || value == "0") {
        callback(new Error("只能输入1-100的数字"));
      } else {
        callback();
      }
    };

    return {
      tableHeight: window.innerHeight - 400,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      readonly: false,
      // 数据库订单管理表格数据
      orderList: [],

      // ktDatabase: [],
      // 弹出层标题
      title: "",
      dbList: { dbName: "dbName", dbId: "dbId" },
      orgList: [{ value: "id", label: "orgName" }],
      // 是否显示弹出层
      open: false,
      FindOpen:false,
      // 订单状态字典
      orderStatusOptions: [],
      // 订单类型字典
      orderTypeOptions: [],
      // 授权方式字典
      authMethodOptions: [],
      // 查询参数
      queryParams: {
        orgName: null,
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        orgId: null,
        orderStatus: null,
        authStartTime: null,
        authEndTime: null,
        searchTime: null,
      },
      // 表单参数
      form: {},
      searchTime: [],
      // 表单校验
      rules: {
        price: [{ required: true, validator: pri, trigger: "change" }],
        discount: [{ required: true, validator: dis, trigger: "blur" }],

        // ktDatabase: [
        //   {
        //     required: true,
        //     validator: daba,
        //     trigger: "change",
        //   },
        // ],
        authMethod: [
          {
            required: true,
            message: "授权方式不能为空",
            trigger: "change",
          },
        ],
        orgName: [
          {
            required: true,

            validator: org,
            trigger: "change",
          },
        ],

        finalPrice: [{ required: true, validator: final, trigger: "blur" }],

        orderCreateName: [
          { required: true, message: "订单创建人不能为空", trigger: "blur" },
        ],
        orderType: [
          { required: true, message: "类型不能为空", trigger: "blur" },
        ],
        authStartTime: [
          {
            required: true,
            message: "授权开始时间不能为空",
            trigger: "change",
          },
        ],

        authEndTime: [
          {
            required: true,

            validator: validateAuthEndTime,
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getName();
    this.getList();
    this.getDbName();
    this.getDicts("order_status").then((response) => {
      this.orderStatusOptions = response.data;
    });
    this.getDicts("auth_method").then((response) => {
      this.authMethodOptions = response.data;
    });
    this.getDicts("order_type").then((response) => {
      this.orderTypeOptions = response.data;
    });
  },

  methods: {
    forceUpdate(v) {
      //debugger;
      let name = "";
      let price = 0;
      v.forEach((a) => {
        this.dbList.forEach((e) => {
          if (a == e.dbName) {
            name += a + ",";
            price += e.price;
          }
        });
        // this.form
      });
      this.form.ktDatabase = name.substring(0, name.length - 1);
      this.form.price = price;
      /*  console.log(v); */
    },
    /**获取数据库 */
    getDbName() {
      this.loading = true;
      dbNames().then((response) => {
        this.dbList = response;
      });
    },
    convertToDate(date) {
      var date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      m = m < 10 ? "0" + m : m; //月小于10，加0
      d = d < 10 ? "0" + d : d; //day小于10，加0
      return y + "-" + m + "-" + d;
    },
    changeTime() {
      if (this.form.authMethod == "LONG") {
        this.readonly = true;
        this.form.authStartTime = this.convertToDate(new Date());
        this.form.authEndTime = this.convertToDate(
          new Date(this.form.authStartTime).getTime() +
          24 * 60 * 60 * 1000 * 200000
        );
      } else {
        this.readonly = false;
      }
    },

    /*计算总金额 */
    final() {
      if (this.form.price != null && this.form.discount != null) {
        if (this.form.price >= 0 && this.form.discount >= 0)
          this.form.finalPrice = parseFloat(
            Number(this.form.discount) * Number(this.form.price) * Number(0.01)
          ).toFixed(2);
      }
    },

    /** 查询数据库订单管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.searchTime = this.searchTime.join(",")
      this.queryParams.orderByColumn="createTime";
      this.queryParams.isAsc="desc";
      listOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 订单状态字典翻译
    orderStatusFormat(row, column) {
      return this.selectDictLabel(this.orderStatusOptions, row.orderStatus);
    },
    // 授权方式字典翻译
    authMethodFormat(row, column) {
      return this.selectDictLabel(this.authMethodOptions, row.authMethod);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.ktDatabase = [];
      this.reset();
    },
    findCancel() {
      this.FindOpen = false;
      // this.ktDatabase = [];
      this.reset();
    },
    /**机构名称 */
    getName() {
      this.loading = true;
      listOrgan().then((response) => {
        this.orgList = response.rows;

        this.orgList.forEach(function (item, index, arr) {
          console.log(item)
          if (item.orgStatus == "1") {
            arr.splice(index, 1);

          }
        })
        console.log(this.orgList)


      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderCode: null,
        orgId: null,
        // ktDatabase: [],
        orderStatus: "0",
        price: null,
        discount: null,
        finalPrice: null,
        authMethod: null,
        authStartTime: null,
        authEndTime: null,
        orderDesc: null,
        // orgName:null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        orgName: null,
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        orgId: null,
        orderStatus: null,
        authStartTime: null,
        authEndTime: null,
        searchTime: null,
      },
        this.searchTime = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();

      this.open = true;

      // this.ktDatabase = [];
      this.title = "添加数据库订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {

      this.reset();
      const id = row.id || this.ids;
      this.form = JSON.parse(JSON.stringify(row));
      if(this.form.quantity==0){
        this.form.quantity="永久"
      }
      if(this.form.validStartTime==null){
        this.form.validStartTime="永久"
      }
      if(this.form.validExpireTime==null){
        this.form.validExpireTime="永久"
      }
      // this.form.authEndTime =   new Date(row.authEndTime);
      // this.form.authStartTime =   new Date(row.authStartTime);


      // //debugger;
      // this.ktDatabase = this.form.ktDatabase.split(",");
      // console.log(this.ktDatabase);
      this.open = true;

      if (this.form.authMethod == "LONG") {
        this.readonly = true;
      }
      this.title = "修改个人订单";
    },

    /** 查看 */
    handleFind(row) {

      this.reset();
      const id = row.id || this.ids;
      this.form = JSON.parse(JSON.stringify(row));
      // this.form.authEndTime =   new Date(row.authEndTime);
      // this.form.authStartTime =   new Date(row.authStartTime);


      // //debugger;
      // this.ktDatabase = this.form.ktDatabase.split(",");
      // console.log(this.ktDatabase);
      this.FindOpen = true;

      if (this.form.authMethod == "LONG") {
        this.readonly = true;
      }
      this.title = "查看个人订单";
    },


    getLog() {
      this.logloading = true;
      listLog(this.queryLogParams, this.queryLogParams.taskId).then(
        (response) => {
          this.logList = response.rows;
          this.totalLog = response.total;
          this.logloading = false;
        }
      );
    },
    /** 提交按钮 */
    submitForm() {
      console.log(1, this.form)
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if(this.form.quantity=="永久")
          {
            this.form.quantity = 0
          }
          if(this.form.validStartTime=="永久"){
            this.form.validStartTime=null
          }
          if(this.form.validExpireTime=="永久"){
            this.form.validExpireTime=null
          }
          if (this.form.id != null) {
            updateOrder(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            checkOrder().then((response) => {
              var a = false;

              response.data.forEach((e) => {
                if (
                  e.orgId == this.form.orgId &&
                  e.authEndTime > this.form.authStartTime &&
                  // e.ktDatabase == this.form.ktDatabase &&
                  e.delFlag != 1
                ) {
                  a = true;
                }
              });

              if (this.b) {
                this.msgInfo("机构已停用，无法添加订单");
                this.open = false;
              }

              if (a) {
                this.msgInfo("在授权中");
                this.open = false;
              } else {
                addOrder(this.form).then((response) => {
                  if (response.code === 200) {
                    this.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  }
                });
              }
            });
          }
        }
      });
    },

    handleStatus(row) {
      const id = (this.id = row.id);
      if (row.orderStatus == 0 || row.orderStatus == 2) {
        updateStaus(id).then((response) => {
          this.msgSuccess("开通成功");
          this.getList();
        });
      } else {
        updateStaus1(id).then((response) => {
          this.msgSuccess("关闭成功");
          this.getList();
        });
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delOrder(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      const ids = this.ids.join(",");
      queryParams.ids = ids;


      if (ids.length == 0) {
        this.$confirm("是否确认导出所有平台数据项?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return exportOrder(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          })
          .catch(function () { });
      } else {
        this.$confirm("是否确认导出所选择的数据项?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return exportOrder(queryParams);
          })
          .then((response) => {
            this.download(response.msg);
          })
          .catch(function () { });
      }

      // this.$confirm("是否确认导出所有数据库订单管理数据项?", "警告", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      //   .then(function () {
      //     return exportOrder(queryParams);
      //   })
      //   .then((response) => {
      //     this.download(response.msg);
      //   })
      //   .catch(function () { });
    },
  },
};
</script>
<style scoped>
/* .bottom {
  margin-bottom: 10px;
} */
.outside {
  width: 100%;
  height: 90%;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.middletb {
  width: 100%;
  margin-top: 30px;
}

.middle {
  margin-top: 10px;
  width: 99%;
}

/* .dialog-footer {
  margin-top: -100px;
} */

/* .el-dialog-footer {
  padding: 20px;
  padding-top: 0px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
} */
.mb8 {
  margin-bottom: -10px !important;
}
.row_value{
  margin-top: 8%;
}
/* .tableS{
  margin-top: 0px;
} */
</style>
