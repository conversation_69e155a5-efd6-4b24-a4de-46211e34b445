<template>
  <div class="app-container">
    <div style="margin-left: 10px"></div>
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
        <el-form-item label="昵称" prop="wxName">
          <el-input v-model="queryParams.wxName" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="手机号" prop="userTel">
          <el-input v-model="queryParams.userTel" placeholder="请输入" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item label="状态" prop="userStatus">
          <el-select v-model="queryParams.userStatus" placeholder="请选择" clearable size="small">
            <el-option v-for="dict in userStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="邮箱" prop="userEmail">
          <el-input
            v-model="queryParams.userEmail"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="开通状态" prop="orderStatus">
          <el-select
            v-model="queryParams.orderStatus"
            placeholder="请选择"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          >
            <el-option label="开通授权" value="1"></el-option>
            <el-option label="未开通" value="0"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['plat:user:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['plat:user:export']">导出
        </el-button>
      </el-col>
    </el-row>
    <el-table style="margin-top: 30px" v-loading="loading" :data="userList" @selection-change="handleSelectionChange"
    :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 310px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column min-width="60" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="序号" align="center" type="index" width="70" /> -->
      <!-- <el-table-column label="微信昵称" align="center" prop="wxName" sortable/> -->
      <el-table-column label="昵称" align="center" prop="userName" sortable />
      <!-- <el-table-column label="邮箱" align="center" prop="userEmail"/> -->
      <el-table-column label="手机号" align="center" prop="userTel" />
      <el-table-column label="IP" align="center" prop="clientIp" />
      <!-- <el-table-column label="用户来源" align="center" prop="userSource" :formatter="userSourceFormat"/> -->
      <!-- <el-table-column label="状态" align="center" prop="userStatus" sortable>
        <template slot-scope="scope">
          <el-tag :type="(scope.row.userStatus == '0' ? 'success':'danger')">
            {{ scope.row.userStatus == '0' ? '在用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="开通状态" align="center" prop="orderStatus" sortable>
        <template slot-scope="scope">
          <el-tag :type="(scope.row.orderStatus == '1' ? 'success':'danger')">
            {{ scope.row.orderStatus == '1' ? '开通授权' : '未开通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" sortable width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.userStatus" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <!-- <el-button
            v-if="scope.row.userStatus === '0'"
            size="small"
            type="text"
            icon="el-icon-circle-close"
            @click="handleUpdateStatus(scope.row, '1')"
            v-hasPermi="['plat:user:edit']"
            >禁用
          </el-button>
          <el-button
            v-else
            size="small"
            type="text"
            icon="el-icon-circle-check"
            @click="handleUpdateStatus(scope.row, '0')"
            v-hasPermi="['plat:user:edit']"
            >启用
          </el-button> -->
          <el-button
            v-if="scope.row.orderStatus === '0'"
            size="small"
            type="text"
            icon="el-icon-circle-close"
            @click="handleOrderStatus(scope.row, '1')"
            v-hasPermi="['plat:user:edit']"
            >开通授权
          </el-button>
          <el-button
            v-else
            size="small"
            type="text"
            icon="el-icon-circle-close"
            @click="handleOrderStatus(scope.row, '0')"
            v-hasPermi="['plat:user:edit']"
            >关闭授权
          </el-button>
          <!-- <el-button
            size="small"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['plat:user:query']"
            >详情
          </el-button> -->
          <el-button
            size="small"
            type="text"
            icon="el-icon-view"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plat:user:update']"
            >修改
          </el-button>
          <el-button size="small" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['plat:user:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="title" :visible.sync="openAdd" :close-on-click-modal="false" width="450px" append-to-body>
      <el-container>
        <el-main>
          <el-form ref="formAdd" :model="formAdd" :rules="rules" label-width="80px">
            <el-form-item label="昵称" prop="userName">
              <el-input show-word-limit v-model="formAdd.userName" placeholder="请输入内容" maxlength="12" />
            </el-form-item>
             <el-form-item label="密码" prop="loginPass">
              <el-input

              placeholder="请输入密码"
                type="password"
                v-model="formAdd.loginPass"
                autocomplete="off"
              ></el-input>
            </el-form-item>
           <!-- <el-form-item label="确认密码" prop="checkPass">
              <el-input
                type="password"
                v-model="formAdd.checkPass"
                autocomplete="off"
              ></el-input>
            </el-form-item> -->

            <el-form-item label="手机号" prop="userTel" label-width="80px">
              <el-input v-model="formAdd.userTel" placeholder="请输入手机号" />
            </el-form-item>

            <!-- <el-form-item label="邮箱" prop="userEmail">
              <el-input v-model="formAdd.userEmail" />
            </el-form-item> -->
            <el-form-item label="状态" prop="userStatus">
              <el-switch v-model="formAdd.userStatus" active-value="0" inactive-value="1" active-color="#13ce66"
                inactive-color="#000000">
              </el-switch>
            </el-form-item>
          </el-form>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelAdd">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="titleUpdate" :visible.sync="openUpdate" :close-on-click-modal="false" width="600px" append-to-body>
      <el-container>
        <el-main>
          <el-form ref="formUpdate" :model="formUpdate" :rules="rulesUpdate" label-width="80px">
            <el-form-item label="昵称" prop="userName">
              <el-input v-model="formUpdate.userName" />
            </el-form-item>
            <el-form-item label="密码" prop="loginPass">
              <el-input type="password" v-model="formUpdate.loginPass" readonly disabled></el-input>
            </el-form-item>
            <!-- <el-form-item label="确认密码" prop="checkPass">
              <el-input type="password" v-model="formUpdate.checkPass" autocomplete="off"></el-input>
            </el-form-item> -->

            <el-form-item label="手机号" prop="userTel">
              <el-input v-model="formUpdate.userTel" />
            </el-form-item>

            <!-- <el-form-item label="邮箱" prop="userEmail">
              <el-input v-model="formUpdate.userEmail" />
            </el-form-item> -->
          </el-form>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPass">确 定</el-button>
        <el-button @click="cancelUpade">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改平台用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="900px" append-to-body>
      <el-container>
        <el-aside>
          <el-image :src="form.wxCoverUrl">
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-aside>
        <el-main>
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="用户ID">
                  <el-tooltip class="item" effect="dark" placement="top-end">
                    <div slot="content">{{ form.id }}</div>
                    <el-input v-model="form.id" disabled />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册时间">
                  <el-input v-model="form.createTime" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="昵称">
                  <el-input v-model="form.userName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="微信昵称">
                  <el-input v-model="form.wxName" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input v-model="form.userTel" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.userEmail" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="状态">
                  <el-input v-model="form.userStatus" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户来源">
                  <el-input v-model="form.userSource" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="所在IP">
                  <el-input v-model="form.clientIp" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属区域">
                  <el-input v-model="form.ipArea" disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addUser,
  delUser,
  getUser,
  listUser,
  updateUser,
  changeUserStatus,
  exportUser,
} from "@/api/plat/user";

export default {
  name: "User",
  data() {
    var validatePass = (rule, value, callback) => {
      //debugger;
      if (value == null) {
        callback(new Error("请输入密码"));
      } else {
        if (this.formAdd.checkPass !== "") {
          this.$refs.formAdd.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (this.formAdd.loginPass == null) {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.formAdd.loginPass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var validatePassUpdate = (rule, value, callback) => {
      //debugger;
      if (this.formAdd.checkPass !== "") {
        this.$refs.formAdd.validateField("checkPass");
      }
      callback();
    };
    var validatePass2Update = (rule, value, callback) => {
      //debugger;
      if (
        this.formUpdate.loginPass !== "" &&
        this.formUpdate.loginPass !== this.loginPass
      ) {
        if (value == null) {
          callback(new Error("请再次输入密码"));
        }
        if (value !== this.formUpdate.loginPass) {
          callback(new Error("两次输入密码不一致!"));
        }
      } else {
        callback();
      }
    };
    return {
      tableHeight: window.innerHeight - 460,
      loginPass: "",
      // 遮罩层
      loading: true,
      openAdd: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      titleUpdate: "",
      // 平台用户表格数据
      userList: [],
      rulesUpdate: {
        userName: [
          { required: true, message: "昵称不能为空",  trigger: "blur" },
          {
            pattern: /^[\u4E00-\u9FA5A-Za-z0-9]{0,12}$/,
            message: "请输入含有有数字、英文、汉字三种的昵称",
            trigger: "blur",
          },
        ],
        // loginPass: [
        //       //  {validator: validatePass, trigger: 'blur'}
        //       {
        //     pattern: /^(?!^(\\d+|[a-zA-Z]+|[~!@#$%^&*?]+)$)^[\\w~!@#$%^&*?]{6,12}$/,
        //     message: "密码需包含英文字符、数字、特殊字符至少两种并且(6-12位数)",
        //     trigger: "blur",
        //   },
        // ],
        // checkPass: [{ validator: validatePass2Update, trigger: "blur" }],
        // userEmail: [
        //   { required: true, message: "邮箱不能为空", trigger: "blur" },
        // ],
        userTel: [
          { required: true, message: "电话不能为空", trigger: "blur" },
          {
            pattern: /^1(3|4|5|7|8|9)\d{9}$/,
            message: "手机号格式错误",
            trigger: "blur",
          },
        ],
      },
      // 弹出层标题
      title: "",
      formUpdate: {},
      // 是否显示弹出层
      open: false,
      openUpdate: false,
      // 用户来源字典
      userSourceOptions: [],
      // 用户状态字典
      userStatusOptions: [],
      // 查询参数
      queryParams: {
        orderStatus: null,
        pageNum: 1,
        pageSize: 10,
        userName: null,
        userTel: null,
        userEmail: null,
      },
      formAdd: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // userName: [
        //   { required: true, message: "昵称不能为空", trigger: "blur" },
        // ],
        // loginPass: [
        //   { validator: validatePass, trigger: "blur", required: true },
        // ],
        userName: [
          { required: true, message: "昵称不能为空",  trigger: "blur" },
          {
            pattern: /^[\u4E00-\u9FA5A-Za-z0-9]{0,12}$/,
            message: "请输入含有有数字、英文、汉字三种的昵称",
            trigger: "blur",
          },
        ],
        loginPass: [
        { required: true, message: "密码不能为空",  trigger: "blur" },
              //  {validator: validatePass, trigger: 'blur'}
              {
            pattern: /^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$).{6,20}$/,
            message: "需包含英文字符、数字、特殊字符至少两种6-20位",
            trigger: "blur",
          },
        ],
        // checkPass: [
        //   { validator: validatePass2, trigger: "blur", required: true },
        // ],
        // userEmail: [
        //   { required: true, message: "邮箱不能为空", trigger: "blur" },
        // ],
        userTel: [{ required: true, message: "电话不能为空", trigger: "blur" },

        {
          pattern: /^1(3|4|5|7|8|9)\d{9}$/,
          message: "手机号格式错误",
          trigger: "blur",
        }],
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("data_from").then((response) => {
      this.userSourceOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then((response) => {
      this.userStatusOptions = response.data;
    });
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      console.log(11111);
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有用户数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportUser(queryParams);
        })
        .then((response) => {
           this.download(response.msg);
        })
        .catch(function () { });
    },
    //修改状态

    handleStatusChange(row) {
      let text = row.userStatus === "0" ? "启用" : "停用";
      this.$confirm("确认要" + text + "该用户吗?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return changeUserStatus(row.id, row.userStatus);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.userStatus = row.userStatus === "0" ? "1" : "0";
        });
    },

    handleUpdate(row) {
      this.resetUpdate();
      this.formUpdate = row;
      console.log(this.formUpdate)
      /*   const { loginPass} = row.loginPass
            this.formAdd.checkPass =   this.formAdd.loginPass*/
      this.openUpdate = true;
      this.loginPass = row.loginPass;
      this.titleUpdate = "修改用户";
    },

    submitForm() {
      this.$refs["formAdd"].validate((valid) => {
        //debugger;
        //debugger;
        if (valid) {
          if (this.form.id != null) {
            updateUser(this.formAdd).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("编辑成功");
                this.open = false;
                this.getList();
              }
            });
          } else if (this.formAdd.id == null) {
            addUser(this.formAdd).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.getList();
                this.openAdd = false;
              }
            });
          }
        }
      });
    },
    submitPass() {
      //debugger;
      this.$refs["formUpdate"].validate((valid) => {
        if (valid) {
          if (this.formUpdate.id != null) {
            updateUser(this.formUpdate).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("编辑成功");
                this.openUpdate = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 查询平台用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.userType = "P";
      listUser(this.queryParams).then((response) => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleAdd() {
      this.resetAdd();
      this.openAdd = true;

      this.title = "添加用户";
    },
    // 用户来源字典翻译
    userSourceFormat(row, column) {
      return this.selectDictLabel(this.userSourceOptions, row.userSource);
    },
    // 用户状态字典翻译
    userStatusFormat(row, column) {
      return this.selectDictLabel(this.userStatusOptions, row.userStatus);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelAdd() {
      this.openAdd = false;
      this.reset();
    },
    cancelUpade() {
      this.openUpdate = false;
      this.reset();
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        orderStatus: null,
        id: null,
        orgId: null,
        loginName: null,
        loginPass: null,
        userName: null,
        wxName: null,
        wxCoverUrl: null,
        userTel: null,
        userEmail: null,
        userType: null,
        remark: null,
        userSource: null,
        userStatus: null,
        lastLoginTime: null,
        lastModpassTime: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    resetAdd() {
      this.formAdd = {
        orderStatus: null,
        id: null,
        orgId: null,
        loginName: null,
        loginPass: null,
        checkPass: null,
        userName: null,
        wxName: null,
        wxCoverUrl: null,
        userTel: null,
        userEmail: null,
        userType: null,
        remark: null,
        userSource: null,
        userStatus: null,
        lastLoginTime: null,
        lastModpassTime: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("formAdd");
    },
    resetUpdate() {
      this.formUpdate = {
        orderStatus: null,
        id: null,
        orgId: null,
        loginName: null,
        loginPass: null,
        checkPass: null,
        userName: null,
        wxName: null,
        wxCoverUrl: null,
        userTel: null,
        userEmail: null,
        userType: null,
        remark: null,
        userSource: null,
        userStatus: null,
        lastLoginTime: null,
        lastModpassTime: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("formUpdate");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /* 开通授权*/
    handleOrderStatus(row, status) {
      const id = row.id;
      let msg = "";
      if (status === "1") {
        msg = "开通授权";
      } else {
        msg = "关闭授权";
      }
      this.$confirm("是否给" + "该用户" + msg + "?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          const form = {
            updateStatus:true,
            id: id,
            orderStatus: status,
          };
          return updateUser(form);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(msg + "成功");
        })
        .catch(function () { });
    },
    /** 禁用启用按钮操作 */
    handleUpdateStatus(row, status) {
      const id = row.id;
      let msg = "";
      if (status === "0") {
        msg = "启用";
      } else {
        msg = "禁用";
      }
      this.$confirm("是否" + msg + "该用户?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          const form = {
            id: id,
            userStatus: status,
          };
          return updateUser(form);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(msg + "成功");
        })
        .catch(function () { });
    },
    passChange(v) {
      // if(this.formUpdate.loginPass!=null){
      //   if (value !== this.formUpdate.loginPass) {
      //     callback(new Error('两次输入密码不一致!'));
      //   } else {
      //     callback();
      //   }
      // }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const id = row.id || this.ids;
      getUser(id).then((response) => {
        this.form = response.data;
        this.form.userSource = this.selectDictLabel(
          this.userSourceOptions,
          row.userSource
        );
        this.form.userStatus = this.selectDictLabel(
          this.userStatusOptions,
          row.userStatus
        );
        this.open = true;
        this.title = "个人用户详情";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除该用户?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delUser(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () { });
    },
  },
};
</script>
<style type="text/css" scoped>
.top {
  margin-left: 10px;
  margin-top: 20px;
}

.dialog-footer {
  margin-top: -60px;
}
.mb8 {
    margin-bottom: -10px !important;
}
.tableS{
  margin-top: 0px;
}
</style>
