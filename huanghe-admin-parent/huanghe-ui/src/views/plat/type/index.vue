<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="类型名称" prop="typeName">
          <el-input
            v-model="queryParams.typeName"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['plat:type:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['plat:type:remove']"
        >删除</el-button>
      </el-col>
	  <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table style="margin-top:30px" v-loading="loading" :data="typeList" @selection-change="handleSelectionChange" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 310px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS"> 
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="60" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>      <!-- <el-table-column label="类型编号" align="center" prop="typeCode" /> -->
      <el-table-column label="类型名称" align="center" prop="typeName" />
      <el-table-column label="描述" align="center" prop="content" />

      <!-- <el-table-column label="类型状态" align="center" prop="typeStatus">
        <template slot-scope="scope">
          <el-tag :type="(scope.row.typeStatus == '0' ? 'success':'danger')">
            {{ scope.row.typeStatus == '0' ? '在用' :'停用'}}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            type="text" icon="el-icon-edit" size="mini"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['plat:type:edit']"
          >编辑</el-button>
          <el-button
            type="text" icon="el-icon-delete" size="mini"
            @click="handleDelete(scope.row)"
            v-hasPermi="['plat:type:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改平台机构类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="类型编号" prop="typeCode">
          <el-input v-model="form.typeCode" placeholder="请输入类型编号" />
        </el-form-item> -->
        <el-form-item label="类型" prop="typeName">
                <el-input
                show-word-limit
                maxlength="10"
                v-model="form.typeName"
                placeholder="请输入内容"
              />
        </el-form-item>
         <el-form-item label="描述" prop="content">
           <el-input
                show-word-limit
                maxlength="100"
                v-model="form.content"
                placeholder="请输入内容"
                type="textarea"
                :rows="5" resize='none'
              />
        </el-form-item>
        <!-- <el-form-item label="类型状态" prop="typeStatus">
          <el-switch
            v-model="form.typeStatus"
            active-value="0"
            inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listType, getType, delType, addType, updateType, exportType } from "@/api/plat/type";

export default {
  name: "Type",
  data() {
    return {
      tableHeight: window.innerHeight - 315,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 平台机构类型表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型状态 -1 不可用  0-可用字典
      typeStatusOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        typeCode: [
          { required: true, message: "类型编号不能为空", trigger: "blur" }
        ],
        typeName: [
          { required: true, message: "类型名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.typeStatusOptions = response.data;
    });
  },
  methods: {
    /** 查询平台机构类型列表 */
    getList() {
      this.loading = true;
      listType(this.queryParams).then(response => {
        this.typeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 类型状态 -1 不可用  0-可用字典翻译
    typeStatusFormat(row, column) {
      return this.selectDictLabel(this.typeStatusOptions, row.typeStatus);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        typeCode: null,
        typeName: null,
        typeStatus: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加平台机构类型";
      this.form.typeStatus = "0";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getType(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改平台机构类型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateType(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addType(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除所选平台机构类型的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delType(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有平台机构类型数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportType(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>
<style type="text/css" scoped >
  .top {
    margin-left: 10px;
    margin-top: 20px;
  }
  .dialog-footer{
  margin-top: -45px;
}
.mb8 {
    margin-bottom: -10px !important;
}
.tableS{
  margin-top: 0px;
}
</style>
