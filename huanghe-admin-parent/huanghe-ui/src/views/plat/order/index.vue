<template>
  <div class="app-container">
    <div class="top">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-row>
          <el-form-item label="订单编号" prop="orderCode">
            <el-input v-model="queryParams.orderCode" placeholder="请输入" clearable size="small"
              @keyup.enter.native="handleQuery" style="width:150px"/>
          </el-form-item>
          <el-form-item label="机构名称" prop="orgName">
            <el-input v-model="queryParams.orgName" placeholder="请输入" clearable size="small"
              @keyup.enter.native="handleQuery" style="width:150px"/>
          </el-form-item>
          <el-form-item label="订单状态" prop="orderStatus">
            <el-select v-model="queryParams.orderStatus" placeholder="请选择" clearable size="small">
              <el-option v-for="dict in orderStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" style="width:120px"/>
            </el-select>
          </el-form-item>
          <el-form-item label="订单类型" prop="orderType">
            <el-select v-model="queryParams.orderType" placeholder="请选择" clearable size="small">
              <el-option v-for="dict in orderTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" style="width:120px"/>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="授权开始时间" prop="authStartTime" label-width="98px">
            <el-date-picker clearable size="small" style="width: 150px" v-model="queryParams.authStartTime" type="date"
              value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          </el-form-item>
          <el-form-item label="授权结束时间" prop="authEndTime" label-width="98px">
            <el-date-picker clearable size="small" style="width: 200px" v-model="queryParams.authEndTime" type="date"
              value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          </el-form-item> !-->
          <el-form-item label="时间查询" prop="searchTime" label-width="98px">
            <el-date-picker value-format="yyyy-MM-dd" v-model="searchTime" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>

          </el-form-item>



          <el-form-item>
            <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div style="margin-left: 10px; width: 98%">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['order:order:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['order:order:export']">导出
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['order:order:remove']">删除</el-button>
        </el-col>


        <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>
    </div>
    <el-table style="width: 100%; margin-top: 30px" v-loading="loading" :data="orderList"
      @selection-change="handleSelectionChange" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
      size='medium' :header-row-style="{ height: '20px' }" height="calc(100vh - 370px)"
      :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" class="tableS">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column min-width="50" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column> 
      <el-table-column :show-overflow-tooltip="true" label="订单编号" align="center" prop="orderCode"
        min-width="120px" />
      <el-table-column :show-overflow-tooltip="true" label="机构名称" align="center" prop="orgName" sortable min-width="250px"/>
      <el-table-column :show-overflow-tooltip="true" label="订单状态" align="center" prop="orderStatus"
        :formatter="orderStatusFormat" sortable min-width="100px">
        <template slot-scope="scope">
          <el-tag :type="scope.row.orderStatus == '1'
            ? 'success'
            : scope.row.orderStatus == '0'
              ? 'warning'
              : 'danger'
            ">
            {{
              scope.row.orderStatus == "1"
              ? "已授权"
              : scope.row.orderStatus == "0"
                ? "已停止"
                : "已关闭"
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单类型" align="center" prop="orderType" sortable min-width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.orderType == 0">试用</span>
          <span v-if="scope.row.orderType == 1">正式</span>
        </template></el-table-column>

      <!-- <el-table-column
        label="开通数据库"
        align="center"
        prop="ktDatabase"
        :show-overflow-tooltip="true"
      /> -->
      <!-- <el-table-column
        label="授权方式"
        align="center"
        prop="authMethod"
        :formatter="authMethodFormat"
      /> -->
      <el-table-column label="授权开始时间" align="center" prop="authStartTime" min-width="120px" sortable>
        <template slot-scope="scope">
          <span>{{ scope.row.authStartTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="授权结束时间" align="center" prop="authEndTime" min-width="120px" sortable>
        <template slot-scope="scope">
          <span>{{ scope.row.authEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" align="center" prop="createTime" min-width="150px">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.orderStatus === 0 || scope.row.orderStatus === 2" @click="handleStatus(scope.row)"
            size="mini" type="text" icon="el-icon-circle-plus-outline" v-hasPermi="['order:order:role']">授权</el-button>
          <el-button v-if="scope.row.orderStatus === 1" size="mini" type="text" icon="el-icon-circle-close"
            @click="handleStatus(scope.row)">停止</el-button>
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)" v-hasPermi="['order:order:edit']"
            icon="el-icon-edit">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['order:order:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- <pagination
        v-show="totalLog > 0"
        :total="totalLog"
        :page.sync="queryLogParams.pageNum"
        :limit.sync="queryLogParams.pageSize"
        @pagination="getLog"
      /> -->

    <!-- 添加或修改数据库订单管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-form-item label="订单创建人" prop="orderCreateName" required="true">
          <el-input
            v-model="form.orderCreateName"
            placeholder="请输入订单创建人"
          />
        </el-form-item> -->
        <el-row :gutter="12" class="mb8">

          <el-form-item label="机构名称" prop="orgName">
            <el-select v-model="form.orgId" placeholder="机构名称" :style="{ width: '90%' }">
              <el-option v-for="item in orgList" :key="item.id" :value="item.id" :label="item.orgName">{{ item.orgName
              }}</el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="订单类型" prop="orderType">
            <el-select v-model="form.orderType" placeholder="请选择" clearable :style="{ width: '90%' }">
              <el-option v-for="dict in orderTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="parseInt(dict.dictValue)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="orderStatus">
            <el-switch v-model="form.orderStatus" active-value="1" inactive-value="0" active-color="#13ce66"
              inactive-color="#000000">
            </el-switch>
          </el-form-item>
          <el-form-item label="授权开始时间" prop="authStartTime">
            <!-- <el-date-picker :readonly="readonly" clearable   :style="{width:'90%'}" v-model="form.authStartTime" 
              type="dateTime" value-format="yyyy-MM-dd hh:mm:ss" placeholder="授权开始时间"></el-date-picker> -->
            <el-date-picker v-model="form.authStartTime" type="date" value-format="yyyy-MM-dd" placeholder="授权开始时间">
            </el-date-picker>
          </el-form-item>


          <el-form-item label="授权结束时间" prop="authEndTime">
            <!-- <el-date-picker :readonly="readonly" clearable :style="{ width: '90%' }" v-model="form.authEndTime"
              type="dateTime" value-format="yyyy-MM-dd hh:mm:ss" placeholder="授权结束时间"></el-date-picker> -->
            <el-date-picker v-model="form.authEndTime" type="date" value-format="yyyy-MM-dd" placeholder="授权结束时间">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="备注" prop="orderDesc">
            <el-input v-model="form.orderDesc" placeholder="请输入内容" clearable show-word-limit type="textarea" :rows="5"
              resize='none' :style="{ width: '90%' }" />
          </el-form-item>

        </el-row>
        <!-- <el-form-item label="数据库" prop="ktDatabase">
          @change="$forceUpdate()"
          <el-checkbox-group v-model="ktDatabase" @change="forceUpdate">
            <el-checkbox
              v-for="(item, index) in dbList"
              :key="index"
              :label="item.dbName"
              >{{ item.dbName }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item> -->
        <!-- <el-row :gutter="40" class="mb8">
          <el-col :span="10"> </el-col>
          <el-col :span="12">
            <el-form-item label="订单金额" prop="price">
              <el-input
                readonly="true"
                v-model="form.price"
                placeholder="订单金额"
                @input="final()"
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >万元</i
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣" prop="discount">
              <el-input
                v-model="form.discount"
                placeholder="折扣"
                @input="final()"
              >
                <i slot="suffix" style="font-style: normal; margin-right: 10px"
                  >%</i
                >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-form-item label="合计金额" prop="finalPrice" required="true">
          <el-input v-model="form.finalPrice" placeholder="合计金额">
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >万元</i
            >
          </el-input>
        </el-form-item>
        <el-form-item label="授权方式" prop="authMethod">
          <el-select
            v-model="form.authMethod"
            placeholder="授权方式"
            @change="changeTime"
          >
            <el-option label="限时" value="LIMIT"></el-option>
            <el-option label="永久" value="LONG"></el-option>
          </el-select>
        </el-form-item> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateStaus,
  updateStaus1,
  checkOrder,
  listOrder,
  getOrder,
  delOrder,
  addOrder,
  updateOrder,
  exportOrder,
} from "@/api/order/order";
import { dbNames, formatDate } from "@/api/product/book/books";
import Cookies from "js-cookie";
import { listOrgan, getOrgan } from "@/api/plat/organ";
export default {
  name: "Order",
  data() {
    const daba = (rule, value, callback) => {
      //debugger;
      console.log(!value);
      if (this.ktDatabase.length == 0) {
        callback(new Error("数据库不能为空"));
      } else {
        callback();
      }
    };
    const validateAuthEndTime = (rule, value, callback) => {
      //debugger;

      let endTime = new Date(value);

      let date = this.form.authStartTime

      let startTime = new Date(date)

      console.log(startTime)
      console.log(endTime)






      if (value == null) {
        callback(new Error("授权结束不能为空"));
      } else if (endTime >= startTime) {
        callback();
      } else {
        callback(new Error("应大于开始时间"));
      }
    };

    const final = (rule, value, callback) => {
      const finalPrice = /^\d+(?:\.\d{1,2})?$/;
      if (!value) {
        callback(new Error("价格不能为空"));
      } else if (!finalPrice.test(value)) {
        callback(new Error("只能为数字"));
      } else {
        callback();
      }
    };
    const pri = (rule, value, callback) => {
      //debugger;
      if (!value) {
        callback(new Error("价格不能为空"));
      }
      const price = /^\d+(?:\.\d{1,2})?$/;
      if (!price.test(value)) {
        callback(new Error("只能为数字"));
      } else {
        callback();
      }
    };

    const org = (rule, value, callback) => {
      //debugger;
      if (!this.form.orgId) {
        callback(new Error("机构名称不能为空"));
      } else {
        callback();
      }
    };

    const dis = (rule, value, callback) => {
      const discount = /^100$|^(\d|[1-9]\d)(\.\d{1,4})*$/;
      if (!value) {
        callback(new Error("折扣不能为空"));
      } else if (!discount.test(value) || value == "0") {
        callback(new Error("只能输入1-100的数字"));
      } else {
        callback();
      }
    };

    return {
      tableHeight: window.innerHeight - 400,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      readonly: false,
      // 数据库订单管理表格数据
      orderList: [],

      // ktDatabase: [],
      // 弹出层标题
      title: "",
      dbList: { dbName: "dbName", dbId: "dbId" },
      orgList: [{ value: "id", label: "orgName" }],
      // 是否显示弹出层
      open: false,
      // 订单状态字典
      orderStatusOptions: [],
      // 订单类型字典
      orderTypeOptions: [],
      // 授权方式字典
      authMethodOptions: [],
      // 查询参数
      queryParams: {
        orgName: null,
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        orgId: null,
        orderStatus: null,
        authStartTime: null,
        authEndTime: null,
        searchTime: null,
      },
      // 表单参数
      form: {},
      searchTime: [],
      // 表单校验
      rules: {
        price: [{ required: true, validator: pri, trigger: "change" }],
        discount: [{ required: true, validator: dis, trigger: "blur" }],

        // ktDatabase: [
        //   {
        //     required: true,
        //     validator: daba,
        //     trigger: "change",
        //   },
        // ],
        authMethod: [
          {
            required: true,
            message: "授权方式不能为空",
            trigger: "change",
          },
        ],
        orgName: [
          {
            required: true,

            validator: org,
            trigger: "change",
          },
        ],

        finalPrice: [{ required: true, validator: final, trigger: "blur" }],

        orderCreateName: [
          { required: true, message: "订单创建人不能为空", trigger: "blur" },
        ],
        orderType: [
          { required: true, message: "类型不能为空", trigger: "blur" },
        ],
        authStartTime: [
          {
            required: true,
            message: "授权开始时间不能为空",
            trigger: "change",
          },
        ],

        authEndTime: [
          {
            required: true,

            validator: validateAuthEndTime,
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getName();
    this.getList();
    this.getDbName();
    this.getDicts("order_status").then((response) => {
      this.orderStatusOptions = response.data;
    });
    this.getDicts("auth_method").then((response) => {
      this.authMethodOptions = response.data;
    });
    this.getDicts("order_type").then((response) => {
      this.orderTypeOptions = response.data;
    });
  },

  methods: {
    forceUpdate(v) {
      //debugger;
      let name = "";
      let price = 0;
      v.forEach((a) => {
        this.dbList.forEach((e) => {
          if (a == e.dbName) {
            name += a + ",";
            price += e.price;
          }
        });
        // this.form
      });
      this.form.ktDatabase = name.substring(0, name.length - 1);
      this.form.price = price;
      /*  console.log(v); */
    },
    /**获取数据库 */
    getDbName() {
      this.loading = true;
      dbNames().then((response) => {
        this.dbList = response;
      });
    },
    convertToDate(date) {
      var date = new Date(date);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      m = m < 10 ? "0" + m : m; //月小于10，加0
      d = d < 10 ? "0" + d : d; //day小于10，加0
      return y + "-" + m + "-" + d;
    },
    changeTime() {
      if (this.form.authMethod == "LONG") {
        this.readonly = true;
        this.form.authStartTime = this.convertToDate(new Date());
        this.form.authEndTime = this.convertToDate(
          new Date(this.form.authStartTime).getTime() +
          24 * 60 * 60 * 1000 * 200000
        );
      } else {
        this.readonly = false;
      }
    },

    /*计算总金额 */
    final() {
      if (this.form.price != null && this.form.discount != null) {
        if (this.form.price >= 0 && this.form.discount >= 0)
          this.form.finalPrice = parseFloat(
            Number(this.form.discount) * Number(this.form.price) * Number(0.01)
          ).toFixed(2);
      }
    },

    /** 查询数据库订单管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.searchTime = this.searchTime.join(",")
      listOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 订单状态字典翻译
    orderStatusFormat(row, column) {
      return this.selectDictLabel(this.orderStatusOptions, row.orderStatus);
    },
    // 授权方式字典翻译
    authMethodFormat(row, column) {
      return this.selectDictLabel(this.authMethodOptions, row.authMethod);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      // this.ktDatabase = [];
      this.reset();
    },
    /**机构名称 */
    getName() {
      this.loading = true;
      listOrgan().then((response) => {
        this.orgList = response.rows;
        
        this.orgList.forEach(function(item,index,arr){
          console.log(item)
          if(item.orgStatus=="1"){
            arr.splice(index, 1);
            
          }
        })
        console.log(this.orgList)

        
    });
  },
  // 表单重置
  reset() {
    this.form = {
      id: null,
      orderCode: null,
      orgId: null,
      // ktDatabase: [],
      orderStatus: "0",
      price: null,
      discount: null,
      finalPrice: null,
      authMethod: null,
      authStartTime: null,
      authEndTime: null,
      orderDesc: null,
      // orgName:null,
      createbyId: null,
      createbyName: null,
      createTime: null,
      updatebyId: null,
      updatebyName: null,
      updateTime: null,
      delFlag: null,
    };
    this.resetForm("form");
  },
  /** 搜索按钮操作 */
  handleQuery() {
    this.queryParams.pageNum = 1;
    this.getList();
  },
  /** 重置按钮操作 */
  resetQuery() {
    this.queryParams = {
      orgName: null,
      pageNum: 1,
      pageSize: 10,
      orderCode: null,
      orgId: null,
      orderStatus: null,
      authStartTime: null,
      authEndTime: null,
      searchTime: null,
    },
      this.searchTime = [];
    this.handleQuery();
  },
  // 多选框选中数据
  handleSelectionChange(selection) {
    this.ids = selection.map((item) => item.id);
    this.single = selection.length !== 1;
    this.multiple = !selection.length;
  },

  /** 新增按钮操作 */
  handleAdd() {
    this.reset();

    this.open = true;

    // this.ktDatabase = [];
    this.title = "添加数据库订单";
  },
  /** 修改按钮操作 */
  handleUpdate(row) {

    this.reset();
    const id = row.id || this.ids;
    this.form = JSON.parse(JSON.stringify(row));
    // this.form.authEndTime =   new Date(row.authEndTime);
    // this.form.authStartTime =   new Date(row.authStartTime);


    // //debugger;
    // this.ktDatabase = this.form.ktDatabase.split(",");
    // console.log(this.ktDatabase);
    this.open = true;

    if (this.form.authMethod == "LONG") {
      this.readonly = true;
    }
    this.title = "修改数据库订单";
  },

  getLog() {
    this.logloading = true;
    listLog(this.queryLogParams, this.queryLogParams.taskId).then(
      (response) => {
        this.logList = response.rows;
        this.totalLog = response.total;
        this.logloading = false;
      }
    );
  },
  /** 提交按钮 */
  submitForm() {
    console.log(1, this.form)
    this.$refs["form"].validate((valid) => {
      if (valid) {
        console.log(this.form);
        if (this.form.id != null) {
          updateOrder(this.form).then((response) => {
            if (response.code === 200) {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }
          });
        } else {
          checkOrder().then((response) => {
            var a = false;

            response.data.forEach((e) => {
              if (
                e.orgId == this.form.orgId &&
                e.authEndTime > this.form.authStartTime &&
                // e.ktDatabase == this.form.ktDatabase &&
                e.delFlag != 1
              ) {
                a = true;
              }
            });

            if (this.b) {
              this.msgInfo("机构已停用，无法添加订单");
              this.open = false;
            }

            if (a) {
              this.msgInfo("在授权中");
              this.open = false;
            } else {
              addOrder(this.form).then((response) => {
                if (response.code === 200) {
                  this.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                }
              });
            }
          });
        }
      }
    });
  },

  handleStatus(row) {
    const id = (this.id = row.id);
    if (row.orderStatus == 0 || row.orderStatus == 2) {
      updateStaus(id).then((response) => {
        this.msgSuccess("开通成功");
        this.getList();
      });
    } else {
      updateStaus1(id).then((response) => {
        this.msgSuccess("关闭成功");
        this.getList();
      });
    }
  },

  /** 删除按钮操作 */
  handleDelete(row) {
    const ids = row.id || this.ids;
    this.$confirm("是否确认删除", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(function () {
        return delOrder(ids);
      })
      .then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
      .catch(function () { });
  },
  /** 导出按钮操作 */
  handleExport() {
    const queryParams = this.queryParams;
    const ids = this.ids.join(",");
    queryParams.ids = ids;


    if (ids.length == 0) {
      this.$confirm("是否确认导出所有平台数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportOrder(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () { });
    } else {
      this.$confirm("是否确认导出所选择的数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportOrder(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () { });
    }

    // this.$confirm("是否确认导出所有数据库订单管理数据项?", "警告", {
    //   confirmButtonText: "确定",
    //   cancelButtonText: "取消",
    //   type: "warning",
    // })
    //   .then(function () {
    //     return exportOrder(queryParams);
    //   })
    //   .then((response) => {
    //     this.download(response.msg);
    //   })
    //   .catch(function () { });
  },
},
};
</script>
<style scoped>
/* .bottom {
  margin-bottom: 10px;
} */
.outside {
  width: 100%;
  height: 90%;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.middletb {
  width: 100%;
  margin-top: 30px;
}

.middle {
  margin-top: 10px;
  width: 99%;
}

/* .dialog-footer {
  margin-top: -100px;
} */

/* .el-dialog-footer {
  padding: 20px;
  padding-top: 0px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
} */
.mb8 {
  margin-bottom: -10px !important;
}

/* .tableS{
  margin-top: 0px;
} */
</style>
