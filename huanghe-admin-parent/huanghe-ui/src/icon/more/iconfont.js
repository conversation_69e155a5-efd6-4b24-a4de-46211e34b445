!function(e){var t,n,o,i,d,c,l='<svg><symbol id="icon-gengduo" viewBox="0 0 1024 1024"><path d="M430.278767 430.278767h163.442466v163.442466h-163.442466v-163.442466z m364.894617 0h163.442466v163.442466h-163.442466v-163.442466z m-729.789234 0h163.442466v163.442466H65.38415v-163.442466z" fill="#2e6b9b" ></path></symbol></svg>',s=(s=document.getElementsByTagName("script"))[s.length-1].getAttribute("data-injectcss");if(s&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}function a(){d||(d=!0,o())}t=function(){var e,t,n,o;(o=document.createElement("div")).innerHTML=l,l=null,(n=o.getElementsByTagName("svg")[0])&&(n.setAttribute("aria-hidden","true"),n.style.position="absolute",n.style.width=0,n.style.height=0,n.style.overflow="hidden",e=n,(t=document.body).firstChild?(o=e,(n=t.firstChild).parentNode.insertBefore(o,n)):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(t,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),t()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(o=t,i=e.document,d=!1,(c=function(){try{i.documentElement.doScroll("left")}catch(e){return void setTimeout(c,50)}a()})(),i.onreadystatechange=function(){"complete"==i.readyState&&(i.onreadystatechange=null,a())})}(window);