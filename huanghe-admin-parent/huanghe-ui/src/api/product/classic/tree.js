import request from '@/utils/request'

// 查询分类树列表
export function listTree(query) {
  return request({
    url: '/product/tree/list',
    method: 'get',
    params: query
  })
}

// 查询分类树详细
export function getTree(id) {
  return request({
    url: '/product/tree' + id,
    method: 'get'
  })
}
export function treePCode(treePid) {
  return request({
    url: '/product/tree/q/' + treePid,
    method: 'get'
  })
}
// 新增分类树
export function addTree(data) {
  return request({
    url: '/product/tree',
    method: 'post',
    data: data
  })
}

// 修改分类树
export function updateTree(data) {
  return request({
    url: '/product/tree',
    method: 'put',
    data: data
  })
}

// 删除分类树
export function delTree(id,treePid) {
  return request({
    url: '/product/tree/' +treePid+'/'+ id,
    method: 'delete'
  })
}



  /**
   * 查找第一个树
   */

  export function first(data) {
    return request({
      url: '/poduct/tree/export',
      method: 'get',
      params: query
    })
  }
    export function classicTYpe(data) {
      return request({
        url: '/product/books/type',
        method: 'get',
        data: data
      })
    }
      export function classicType(data) {
        return request({
          url: '/product/books/typeSi',
          method: 'get',
          data: data
        })
      }
