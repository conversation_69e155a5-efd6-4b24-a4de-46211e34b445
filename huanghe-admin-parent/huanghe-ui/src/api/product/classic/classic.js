import request from '@/utils/request'

// 查询分类定义列表
export function listClassic(query) {
  return request({
    url: '/product/classic/list',
    method: 'get',
    params: query
  })
}

// 查询分类定义详细
export function getClassic(id) {
  return request({
    url: '/product/classic/' + id,
    method: 'get'
  })
}
//批量删除
export function batch(data) {
  return request({
    url: '/product/classic/batch' ,
    method: 'put',
    data: data
  })
}
// 新增分类定义
export function addClassic(data) {
  return request({
    url: '/product/classic',
    method: 'post',
    data: data
  })
}

// 修改分类定义
export function updateClassic(data) {
  return request({
    url: '/product/classic',
    method: 'put',
    data: data
  })
}

// 删除分类定义
export function delClassic(id) {
  return request({
    url: '/product/classic/' + id,
    method: 'delete'
  })
}

// 导出分类定义
export function exportClassic(query) {
  return request({
    url: '/product/classic/export',
    method: 'get',
    params: query
  })
}

export function treeCatalog(query) {
  return request({
    url: '/product/tree/treeselect',
    method: 'get',
    params: query
  })
}


// 状态修改
export function changeClassicStatus(id, classicStatus) {
  const data = {
    id,
    classicStatus
  };
  return request({
    url: '/product/classic/changeStatus',
    method: 'put',
    data: data
  })
}
