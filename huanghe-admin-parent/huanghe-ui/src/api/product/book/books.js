import request from '@/utils/request'

// 查询产品资源管理列表
export function listBooks(query) {
  return request({
    url: '/product/books/list',
    method: 'get',
    params: query
  })
}
export function importTemplate() {
  return request({
    url: '/product/books/importTemplate',
    method: 'get',
    responseType: "blob"
  })
}
// 查询产品资源管理详细
export function getBooks(id) {
  return request({
    url: '/product/books/' + id,
    method: 'get'
  })
}

// 新增产品资源管理
export function addBooks(data) {
  return request({
    url: '/product/books',
    method: 'post',
    data: data
  })
}

// 修改产品资源管理
export function updateBooks(data) {
  return request({
    url: '/product/books',
    method: 'put',
    data: data
  })
}
export function updatebook(data) {
  return request({
    url: '/product/books/delFlag' ,
    method: 'put',
    data: data
  })
}

export function updatebook1(data,id) {
  return request({
    url: '/product/books/pro/'+id ,
    method: 'put',
    data: data
  })
}



// 删除产品资源管理
export function delBooks(id) {
  return request({
    url: '/product/books/',
    method: 'delete'
  })
}

// 导出产品资源管理
export function find(query) {
  return request({
    url: '/product/books/type',
    method: 'get',
    params: query
  })
}

// 修改产品资源管理
export function status(id) {
  return request({
    url: '/product/books/status/' + id,
    method: 'put',
  })
}

//把书从数据库剔除
export function deleteFromDb(id) {
  return request({
    url: '/product/books/deleteFromDb/' + id,
    method: 'put',
  })
}

export function statusUp(id) {
  return request({
    url: '/product/books/statusUp/' + id,
    method: 'put',
  })
}
export function uploadAvatar(data) {
  return request({
    url: '/product/books/upload',
    method: 'post',
    data: data
  })
}

export function dbNames(data) {
  return request({
    url: '/product/books/dbName',
    method: 'get',
    data: data
  })
}

export function check() {
  return request({
    url: '/product/books/check',
    method: 'post',
  })
}