import request from '@/utils/request'

// 查询数据库管理列表
export function listDatabase(query) {
  return request({
    url: '/product/database/list',
    method: 'get',
    params: query
  })
}

// 查询数据库管理详细
export function getDatabase(id) {
  return request({
    url: '/product/database/' + id,
    method: 'get'
  })
}

// 新增数据库管理
export function addDatabase(data) {
  return request({
    url: '/product/database',
    method: 'post',
    data: data
  })
}

// 修改数据库管理
export function updateDatabase(data) {
  return request({
    url: '/product/database',
    method: 'put',
    data: data
  })
}

// 删除数据库管理
export function delDatabase(id) {
  return request({
    url: '/product/database/' + id,
    method: 'delete'
  })
}

// 导出数据库管理
export function exportDatabase(query) {
  return request({
    url: '/product/database/export',
    method: 'get',
    params: query
  })
}

export function listBook(query,id) {
  return request({
    url: '/product/database/recourse/'+id,
    method: 'get',
    params: query
  })
}


export function list(id) {
  return request({
    url: '/product/database/aaa/'+id,
    method: 'get',
   
  })
}


export function check() {
  return request({
    url: '/product/database/selectRe/',
    method: 'post',
   
  })
}



// 用户状态修改
export function changeDatabaseStatus(dbId, dbStatus) {
  const data = {
    dbId,
    dbStatus
  };
  return request({
    url: '/product/database/changeStatus',
    method: 'put',
    data: data
  })
}
