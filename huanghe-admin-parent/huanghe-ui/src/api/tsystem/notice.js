import request from '@/utils/request'

// 查询关于我们、使用帮助列表
export function listNotice(query) {
  return request({
    url: '/tsystem/notice/list',
    method: 'get',
    params: query
  })
}

// 查询关于我们、使用帮助详细
export function getNotice(id) {
  return request({
    url: '/tsystem/notice/' + id,
    method: 'get'
  })
}

// 新增关于我们、使用帮助
export function addNotice(data) {
  return request({
    url: '/tsystem/notice',
    method: 'post',
    data: data
  })
}

// 修改关于我们、使用帮助
export function updateNotice(data) {
  return request({
    url: '/tsystem/notice',
    method: 'put',
    data: data
  })
}

// 删除关于我们、使用帮助
export function delNotice(id) {
  return request({
    url: '/tsystem/notice/' + id,
    method: 'delete'
  })
}

// 导出关于我们、使用帮助
export function exportNotice(query) {
  return request({
    url: '/tsystem/notice/export',
    method: 'get',
    params: query
  })
}