import request from '@/utils/request'

// 查询友情链接列表
export function listLink(query) {
  return request({
    url: '/tsystem/link/list',
    method: 'get',
    params: query
  })
}

// 查询友情链接详细
export function getLink(id) {
  return request({
    url: '/tsystem/link/' + id,
    method: 'get'
  })
}

// 新增友情链接
export function addLink(data) {
  return request({
    url: '/tsystem/link',
    method: 'post',
    data: data
  })
}

// 修改友情链接
export function updateLink(data) {
  return request({
    url: '/tsystem/link',
    method: 'put',
    data: data
  })
}

// 删除友情链接
export function delLink(id) {
  return request({
    url: '/tsystem/link/' + id,
    method: 'delete'
  })
}

// 导出友情链接
export function updateWebDisplay(data) {
  return request({
    url: '/tsystem/link/updateWebDisplay',
    method: 'put',
    data: data
  })
}


// 资讯海报上传
export function uploadAvatar(data) {
  return request({
    url: '/tsystem/link/upload',
    method: 'post',
    data: data
  })
}

