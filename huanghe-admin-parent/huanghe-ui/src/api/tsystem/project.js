import request from '@/utils/request'

// 查询工程项目简介列表
export function listProject(query) {
  return request({
    url: '/system/project/list',
    method: 'get',
    params: query
  })
}

// 查询工程项目简介详细
export function getProject(id) {
  return request({
    url: '/system/project/' + id,
    method: 'get'
  })
}

// 新增工程项目简介
export function addProject(data) {
  return request({
    url: '/system/project',
    method: 'post',
    data: data
  })
}

// 修改工程项目简介
export function updateProject(data) {
  return request({
    url: '/system/project',
    method: 'put',
    data: data
  })
}

// 删除工程项目简介
export function delProject(id) {
  return request({
    url: '/system/project/' + id,
    method: 'delete'
  })
}

// 导出工程项目简介
export function exportProject(query) {
  return request({
    url: '/system/project/export',
    method: 'get',
    params: query
  })
}

// 资讯海报上传
export function uploadAvatar(data) {
  return request({
    url: '/system/project/upload',
    method: 'post',
    data: data
  })
}

// 上架项目工程
export function upProject(id) {
  return request({
    url: '/system/project/up/'+id,
    method: 'put'
  })
}


// 下架项目工程
export function downProject(id) {
  return request({
    url: '/system/project/down/'+id,
    method: 'put'
  })
}

// 发布资讯
export function publishProject(id) {
  return request({
    url: '/system/project/publish/'+id,
    method: 'put'
  })
}