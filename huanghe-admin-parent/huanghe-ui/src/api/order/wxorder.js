import request from '@/utils/request'

// 查询数据库订单管理列表
export function listOrder(query) {
  return request({
    url: '/order/wxorder/list',
    method: 'get',
    params: query
  })
}

// 查询数据库订单管理详细
export function getOrder(id) {
  return request({
    url: '/order/wxorder/' + id,
    method: 'get'
  })
}

// 新增数据库订单管理
export function addOrder(data) {
  return request({
    url: '/order/wxorder/',
    method: 'post',
    data: data
  })
}


export function checkOrder() {
  return request({
    url: '/order/wxorder/checkOrder',
    method: 'get'
  })
}
// 修改数据库订单管理
export function updateOrder(data) {
  return request({
    url: '/order/wxorder',
    method: 'put',
    data: data
  })
}
// 修改数据库订单管理
export function updateStaus(id) {
  return request({
    url: '/order/wxorder/updateStaus/'+id,
    method: 'put',

  })
}

// 修改数据库订单管理
export function updateStaus1(id) {
  return request({
    url: '/order/wxorder/updateStaus1/'+id,
    method: 'put',

  })
}
// 删除数据库订单管理
export function delOrder(id) {
  return request({
    url: '/order/wxorder/' + id,
    method: 'delete'
  })
}

// 导出数据库订单管理
export function exportOrder(query) {
  return request({
    url: '/order/wxorder/export',
    method: 'get',
    params: query
  })
}