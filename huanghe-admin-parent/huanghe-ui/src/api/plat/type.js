import request from '@/utils/request'

// 查询平台机构类型列表
export function listType(query) {
  return request({
    url: '/plat/type/list',
    method: 'get',
    params: query
  })
}

// 查询平台机构类型详细
export function getType(id) {
  return request({
    url: '/plat/type/' + id,
    method: 'get'
  })
}

// 新增平台机构类型
export function addType(data) {
  return request({
    url: '/plat/type',
    method: 'post',
    data: data
  })
}

// 修改平台机构类型
export function updateType(data) {
  return request({
    url: '/plat/type',
    method: 'put',
    data: data
  })
}

// 删除平台机构类型
export function delType(id) {
  return request({
    url: '/plat/type/' + id,
    method: 'delete'
  })
}

// 导出平台机构类型
export function exportType(query) {
  return request({
    url: '/plat/type/export',
    method: 'get',
    params: query
  })
}