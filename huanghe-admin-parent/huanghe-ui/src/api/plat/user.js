import request from '@/utils/request'

// 查询平台用户列表
export function listUser(query) {
  return request({
    url: '/plat/user/list',
    method: 'get',
    params: query
  })
}

// 查询平台用户详细
export function getUser(id) {
  return request({
    url: '/plat/user/' + id,
    method: 'get'
  })
}

// 新增平台用户
export function addUser(data) {
  return request({
    url: '/plat/user',
    method: 'post',
    data: data
  })
}

// 修改平台用户
export function updateUser(data) {
  return request({
    url: '/plat/user',
    method: 'put',
    data: data
  })
}

// 删除平台用户
export function delUser(id) {
  return request({
    url: '/plat/user/' + id,
    method: 'delete'
  })
}

// 导出平台用户
export function exportUser(query) {
  return request({
    url: '/plat/user/export',
    method: 'get',
    params: query
  })
}


// 状态修改
export function changeUserStatus(id, userStatus) {
  const data = {
    id,
    userStatus
  };
  return request({
    url: '/plat/user/changeStatus',
    method: 'put',
    data: data
  })
}
