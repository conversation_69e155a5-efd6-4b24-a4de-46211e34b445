import request from '@/utils/request'

// 查询平台机构列表
export function listOrgan(query) {
  return request({
    url: '/plat/organ/list',
    method: 'get',
    params: query
  })
}

// 查询平台机构详细
export function getOrgan(id) {
  return request({
    url: '/plat/organ/' + id,
    method: 'get'
  })
}

// 新增平台机构
export function addOrgan(data) {
  return request({
    url: '/plat/organ',
    method: 'post',
    data: data
  })
}

// 修改平台机构
export function updateOrgan(data) {
  return request({
    url: '/plat/organ',
    method: 'put',
    data: data
  })
}

// 修改平台机构状态
export function updateOrganStatus(data) {
  return request({
    url: '/plat/organ/updateStatus',
    method: 'put',
    data: data
  })
}

// 删除平台机构
export function delOrgan(id) {
  return request({
    url: '/plat/organ/' + id,
    method: 'delete'
  })
}

// 导出平台机构
export function exportOrgan(query) {
  return request({
    url: '/plat/organ/export',
    method: 'get',
    params: query
  })
}


// 状态修改
export function changeOrgStatus(id, orgStatus) {
  const data = {
    id,
    orgStatus
  };
  return request({
    url: '/plat/organ/changeStatus',
    method: 'put',
    data: data
  })
}

