import request from '@/utils/request'

// 查询产品资源管理列表
export function listBooks(query) {
  return request({
    url: '/product/books/list',
    method: 'get',
    params: query
  })
}

// 查询产品资源管理详细
export function getBooks(id) {
  return request({
    url: '/product/books/' + id,
    method: 'get'
  })
}

// 新增产品资源管理
export function addBooks(data) {
  return request({
    url: '/product/books',
    method: 'post',
    data: data
  })
}

// 修改产品资源管理
export function updateBooks(data) {
  return request({
    url: '/product/books',
    method: 'put',
    data: data
  })
}
export function updatebook(id) {
  return request({
    url: '/product/books/delFlag/'+id,
    method: 'put',

  })
}

// 删除产品资源管理
export function delBooks(id) {
  return request({
    url: '/product/books/' + id,
    method: 'delete'
  })
}

// 导出产品资源管理
export function find(query) {
  return request({
    url: '/product/books/type',
    method: 'get',
    params: query
  })
}

// 修改产品资源管理
export function status(id) {
  return request({
    url: '/product/books/status/'+id,
    method: 'put',
  })
}
