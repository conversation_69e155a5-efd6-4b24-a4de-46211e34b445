import request from '@/utils/request'

// 查询地方志地图信息列表
export function listMap(query) {
  return request({
    url: '/miniPrograms/map/list',
    method: 'get',
    params: query
  })
}

// 查询地方志地图信息详细
export function getMap(id) {
  return request({
    url: '/system/map/' + id,
    method: 'get'
  })
}

// 新增地方志地图信息
export function addMap(data) {
  return request({
    url: '/system/map',
    method: 'post',
    data: data
  })
}

// 修改地方志地图信息
export function updateMap(data) {
  return request({
    url: '/system/map',
    method: 'put',
    data: data
  })
}

// 删除地方志地图信息
export function delMap(id) {
  return request({
    url: '/system/map/' + id,
    method: 'delete'
  })
}

// 导出地方志地图信息
export function exportMap(query) {
  return request({
    url: '/system/map/export',
    method: 'get',
    params: query
  })
}
