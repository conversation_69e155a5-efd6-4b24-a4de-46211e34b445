import request from '@/utils/request'

// 查询地方志意见反馈列表
export function listOpinion(query) {
  return request({
    url: '/miniPrograms/opinion/list',
    method: 'get',
    params: query
  })
}

// 查询地方志意见反馈详细
export function getOpinion(id) {
  return request({
    url: '/miniPrograms/opinion/' + id,
    method: 'get'
  })
}

// 新增地方志意见反馈
export function addOpinion(data) {
  return request({
    url: '/miniPrograms/opinion',
    method: 'post',
    data: data
  })
}

// 修改地方志意见反馈
export function updateOpinion(data) {
  return request({
    url: '/miniPrograms/opinion',
    method: 'put',
    data: data
  })
}

// 删除地方志意见反馈
export function delOpinion(id) {
  return request({
    url: '/miniPrograms/opinion/' + id,
    method: 'delete'
  })
}

// 导出地方志意见反馈
export function exportOpinion(query) {
  return request({
    url: '/miniPrograms/opinion/export',
    method: 'get',
    params: query
  })
}
