import request from '@/utils/request'

// 查询地方志书库列表
export function listBooks(query) {
  return request({
    url: '/miniPrograms/books/list',
    method: 'get',
    params: query
  })
}

// 查询地方志书库详细
export function getBooks(id) {
  return request({
    url: '/miniPrograms/books/' + id,
    method: 'get'
  })
}

// 新增地方志书库
export function addBooks(data) {
  return request({
    url: '/miniPrograms/books',
    method: 'post',
    data: data
  })
}

// 修改地方志书库
export function updateBooks(data) {
  return request({
    url: '/miniPrograms/books',
    method: 'put',
    data: data
  })
}

// 删除地方志书库
export function delBooks(id) {
  return request({
    url: '/miniPrograms/books/' + id,
    method: 'delete'
  })
}

// 导出地方志书库
export function exportBooks(query) {
  return request({
    url: '/miniPrograms/books/export',
    method: 'get',
    params: query
  })
}
