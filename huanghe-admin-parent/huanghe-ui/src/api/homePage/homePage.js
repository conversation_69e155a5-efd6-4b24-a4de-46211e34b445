import request from '@/utils/request'
//新增用户量
export function newUser() {
  return request({
    url: '/homePage/newUser',
    method: 'get',
  })
}
//新增机构
export function newPlatCount() {
  return request({
    url: '/homePage/newPlat',
    method: 'get',
  })
}
//机构统计
export function organCount(query,day) {
  return request({
    url: '/homePage/organCount/'+day,
    method: 'get',
    params: query
  })
}
//机构统计
export function recourseCount(query) {
  return request({
    url: '/homePage/recourseCount/',
    method: 'get',
    params: query
  })
}
export function aloneVisits(day) {
  return request({
    url: '/homePage/aloneVisits/'+day,
    method: 'get',
  })
}
export function aloneVisits1(day) {
  return request({
    url: '/homePage/aloneVisits1/'+day,
    method: 'get',
  })
}
export function aloneVisitsCounts(day) {
  return request({
    url: '/homePage/aloneVisitsCounts/'+day,
    method: 'get',
  })
}
export function aloneVisitsCount1s(day) {
  return request({
    url: '/homePage/aloneVisitsCounts1/'+day,
    method: 'get',
  })
}



//近7天访问量
export function visits() {
  return request({
    url: '/homePage/visits',
    method: 'get',
  })
}
//资源占比
export function recoursePercent() {
  return request({
    url: '/homePage/recoursePercent',
    method: 'get',
  })
}
//搜索历史搜索词排行
export function searchTop(query) {
  return request({
    url: '/homePage/searchTop',
    method: 'get',
    params: query
  })
}
//批量删除

export function delSearchContent(data) {
  return request({
    url: '/homePage/delSearchContent/',
    method: 'put',
    data: data
  })
}

export function deleteSearch(id) {
  return request({
    url: '/homePage/delSearchContent/' + id,
    method: 'put',

  })
}


//机构统计
export function getOrganUserType(query) {
  return request({
    url: '/homePage/organUser',
    method: 'get',
    params: query
  })
}

//用户登录量统计
export function getUserLoginSum(type) {
  return request({
    url: '/homePage/userLoginSum/' + type,
    method: 'get',

  })
}



export function getResourceProportion(id) {
  return request({
    url: '/homePage/ResourceProportion/' + id,
    method: 'get',

  })
}



export function getdataStatisticsDeatil(id) {
  return request({
    url: '/homePage/dataStatistics/' + id,
    method: 'get',

  })
}

//各编资源统计
export function getResourceStatistic(query) {
  return request({
    url: '/homePage/ResourceStatistic',
    method: 'get',
    params: query
  })
}