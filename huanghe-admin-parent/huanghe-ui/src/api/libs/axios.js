import axios from 'axios';
import { getStore, setStore } from './storage';
// import { Message } from 'view-design';
import Cookies from 'js-cookie';
import {VISIT_URL} from '@/api/libs/js/config';
// 统一请求路径前缀
let base='';
let appKey="3c629d018cae4aed9a0a8e906b26dc06";
// 超时设定
axios.defaults.timeout = 6000000;


axios.interceptors.request.use(config => {
    return config;
}, err => {
    Message.error('请求超时');
    return Promise.resolve(err);
});

// http response 拦截器
axios.interceptors.response.use(response => {
    const data = response.data;
    // 根据返回的code值来做不同的处理(和后端约定)
    switch (data.code) {
        case 403:
            // 没有权限
            if (data.message !== null) {
                Message.error({
                    content: data.message,
                    closable: true,
                    duration:5
                });
            } else {
                Message.error({
                    content: "未知错误",
                    closable: true,
                    duration:5
                });
            }
            break;
        case 401:
             // 未登录 清除已登录状态
             Message.error({
                content: "登录已失效或未登录，请重新登录后再使用自动标点！",
                closable: true,
                duration:5
            });
            setTimeout(function (){
                window.location.href=VISIT_URL.login_url;
            },"1000"); 
            break;
        case 404:
             // 错误
             if (data.message !== null) {
                Message.error({
                    content: data.message,
                    closable: true,
                    duration:5
                });
            } else {
                Message.error({
                    content: "未知错误",
                    closable: true,
                    duration:5
                });
            }
            setTimeout(function (){
                window.location.href=VISIT_URL.login_url;
            },"1000");
            break;
        case 500:
            // 错误
            if (data.message !== null) {
                Message.error({
                    content: data.message,
                    closable: true,
                    duration:5
                });
            } else {
                Message.error({
                    content: "未知错误",
                    closable: true,
                    duration:5
                });
            }
            break;
        case -1:
            // 错误
            if (data.msg !== null) {
                Message.error({
                    content: data.msg,
                    closable: true,
                    duration:5
                });
            } else {
                Message.error({
                    content: "未知错误",
                    closable: true,
                    duration:5
                });
            }
            break;
        default:
            return data;
    }
    return data;
}, err => {
        // 返回状态码不为200时候的错误处理
        switch (err.response.status) {
             
            case 404:
                Message.error("请求资源不存在,请联系管理员!");
                break;
            case 401:
                // 未登录 清除已登录状态
                Message.error({
                    content: "登录已失效请联系管理员！",
                    closable: true,
                    duration:5
                });
                setTimeout(function (){
                    window.location.href=VISIT_URL.login_url;
                },"1000"); 
                break;
            default:
                Message.error(err.toString());
                return Promise.resolve(err);
                break;
        }
});

export const baseV = (url) => {
       /*  if(url.indexOf('/description-task/') >= 0 ){
            return '/dto';
        }else {
            return '/dto/api/wx-app';
        } */
      
       return '/pun';
}

export const getRequest = (url, params) => {
    let accessToken = getStore('accessToken');
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'get',
        url: `${base}${url}`,
        params: params 
    });
};

export const postRequest = (url, params) => {
    let accessToken = getStore("accessToken");
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'post',
        url: `${base}${url}`,
        data: params,
        transformRequest: [function (data) {
            let ret = '';
            for (let it in data) {
                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
            }
            return ret;
        }],
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
};

export const putRequest = (url, params) => {
    let accessToken = getStore("accessToken");
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'put',
        url: `${base}${url}`,
        data: params,
        transformRequest: [function (data) {
            let ret = '';
            for (let it in data) {
                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
            }
            return ret;
        }],
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
};

export const deleteRequest = (url, params) => {
    let accessToken = getStore('accessToken');
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'delete',
        url: `${base}${url}`,
        params: params 
    });
};
export const deleteJsonRequest = (url, params) => {
    let accessToken = getStore('accessToken');
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'delete',
        url: `${base}${url}`,
        params: params,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    });
};

export const uploadFileRequest = (url, params) => {
    let accessToken = getStore('accessToken');
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'post',
        url: `${base}${url}`,
        data: params,
        headers: {
            'Content-Type': 'multipart/form-data'
        } 
    });
};
//传json
export const postJsonRequest = (url, params) => {
    let accessToken = getStore("accessToken");
    if(accessToken==null){
        accessToken="";
    } 
    let base=baseV(url);
    return axios({
        method: 'post',
        url: `${base}${url}`,
        data: params,
        dataType:'json',
        transformRequest: [function (data) {
            return data;
        }],
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    });
};

//下载
export const getBlobRequest = (url, params) => {
    let accessToken = getStore("accessToken");
    if(accessToken==null){
        accessToken="";
    } 
     let base=baseV(url);
    return axios({
        method: 'post',
        url: `${base}${url}`,
        data: params,
        responseType: 'blob',
        transformRequest: [function (data) {
            return data;
        }],
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    });
};

