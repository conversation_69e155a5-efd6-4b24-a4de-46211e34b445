/**
 * 存储localStorage
 */
export const setStore = (name, content) => {
    if (!name) return;
    if (typeof content !== 'string') {
        content = JSON.stringify(content);
    }
   // window.localStorage.setItem(name, content);
   sessionStorage.setItem(name,content);
}

/**
 * 获取localStorage
 */
export const getStore = name => {
    if (!name) return;
    //return window.localStorage.getItem(name);
    return sessionStorage.getItem(name);
}

/**
 * 删除localStorage
 */
export const removeStore = name => {
    if (!name) return;
    sessionStorage.removeItem(name);
}
