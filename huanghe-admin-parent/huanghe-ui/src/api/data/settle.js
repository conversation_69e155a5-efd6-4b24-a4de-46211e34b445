import request from '@/utils/request'

// 查询数据入库配置列表
export function listSettle(query) {
  return request({
    url: '/data/settle/list',
    method: 'get',
    params: query
  })
}
//查看日志信息
export function listLog(query,id) {
  return request({
    url: '/data/log/list',
    method: 'post',
    data: query
  })
}
// 查询数据入库配置详细
export function getSettle(id) {
  return request({
    url: '/data/settle/' + id,
    method: 'get'
  })
}


// 新增数据入库配置
export function addSettle(data) {
  return request({
    url: '/data/settle',
    method: 'post',
    data: data
  })
}

// 修改数据入库配置
export function updateSettle(data) {
  return request({
    url: '/data/settle',
    method: 'put',
    data: data
  })
}

// 删除数据入库配置
export function delSettle(id) {
  return request({
    url: '/data/settle/updateSettle/' + id,
    method: 'put'
  })
}
//修改状态
export function upStatus(id) {
  return request({
    url: '/data/settle/upStatus/' + id,
    method: 'put'
  })
}



// 新增数据入库配置
export function startAll() {
  return request({
    url: '/data/settle/startAll',
    method: 'post'
  })
}