// 统一请求路径前缀在libs/axios.js中修改
import { getRequest, postRequest, putRequest, deleteRequest,deleteJsonRequest,
     uploadFileRequest,postJsonRequest ,getBlobRequest } from '@/api/libs/axios';
// import { param } from 'jquery';

import request from '@/utils/request'
//籍合网授权登录
export const getPunctuateAuthentication=(params)=>{
    return postJsonRequest('/punctuate/authentication',params);
}


//自动标点
export function getPunctuateAutoPunctuation(params) {
    return request({
      url: '/wapi/autoPun/AutoPunMethod',
      method: 'get',
      params: params
    })
  }
//下载数据
export function getPunctuateDownloadText(params) {
    return request({
      url: '/wapi/autoPun/download',
      method: 'get',
      params: params
    })
  }

//开始纠错
export function getPunctuatecorrectPunctuation(params) {
    return request({
      url: '/wapi/autoPun/correction',
      method: 'post',
      params: params
    })
  }
//标点评价

export function getPunctuateassessPunctuarion(params) {
    return request({
      url: '/wapi/autoPun/judge',
      method: 'get',
      params: params
    })
  }
//申请字
export const getPunctuateApplyQuota=(params)=>{
    return postJsonRequest("/punctuate/apply-quota",params);
}
//获取字数
export function getPunctuateAvailableWords() {
    return request({
      url: '/wapi/autoPun/availableWords',
      method: 'post',
    })
  }


//通知-列表
export  const getPunctuateNoticeList=(params)=>{
    return postJsonRequest('/punctuate/notice-list',params);
}
//通知-已读
export  const getPunctuateNoticeRead=(params)=>{
    return postJsonRequest('/punctuate/notice-read',params);
}
//通知-获取未读数量
export  const getPunctuateNoticeUnreadNumber=(params)=>{
    return postJsonRequest('/punctuate/notice-unread-number',params);
}
//退出
export const getPunctuateSignOut=(params)=>{
    return postJsonRequest('/punctuate/sign-out',params);
}
//上传
export const uploadUrl="/pun/punctuate/upload-text";

//样例
export const getFileDownloadExamples=(params)=>{
    return getBlobRequest('/punctuate/download-example',params);
}
//取消引导
export const getPunctuateGuide=()=>{
    return postJsonRequest('/punctuate/guide');
}
//上传
/* export const uploadFeedback="/pun/punctuate/feedback"; */
export const uploadFeedback=(params)=>{
    return uploadFileRequest('/punctuate/feedback',params);
} 