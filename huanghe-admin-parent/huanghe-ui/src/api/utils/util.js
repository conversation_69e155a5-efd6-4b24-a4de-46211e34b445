import request from '@/utils/request'

// 查询公元列表
export function calendarADList(query) {
  return request({
    url: '/tool/calendar/calendarADList',
    method: 'get',
    params: query
  })
}

//查询时期列表
export function periodList(query) {
  return request({
    url: '/tool/calendar/periodList',
    method: 'get',
    params: query
  })
}

//查询政权列表
export function regimeList(query) {
  return request({
    url: '/tool/calendar/regimeList',
    method: 'get',
    params: query
  })
}

//根据条件查询政权列表
export function regimeListByCondition(query) {
  return request({
    url: '/tool/calendar/regimeListByCondition',
    method: 'get',
    params: query
  })
}

//查询纪年列表
export function calendarList(query) {
  return request({
    url: '/tool/calendar/calendarList',
    method: 'get',
    params: query
  })
}
//查询帝号，帝名列表
export function emperorNameList(query) {
  return request({
    url: '/tool/calendar/emperorNameList',
    method: 'get',
    params: query
  })
}

//查询关键字类型
export function getQueryParamsType(query) {
  return request({
    url: '/tool/calendar/getQueryParamsType',
    method: 'get',
    params: query
  })
}

//模糊查询政权列表
export function regimeListByLike(query) {
  return request({
    url: '/tool/calendar/regimeListByLike',
    method: 'get',
    params: query
  })
}

//模糊查询获取纪年列表
export function calendarListByLike(query) {
  return request({
    url: '/tool/calendar/calendarListByLike',
    method: 'get',
    params: query
  })
}
