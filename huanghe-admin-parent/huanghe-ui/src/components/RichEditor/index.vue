<template>
  <div class="rich-editor-container">
    <div class="toolbar">
      <button type="button" @click="execCommand('bold')" :class="{ active: isActive('bold') }">
        <i class="el-icon-s-promotion"></i> 粗体
      </button>
      <button type="button" @click="execCommand('italic')" :class="{ active: isActive('italic') }">
        <i class="el-icon-s-flag"></i> 斜体
      </button>
      <button type="button" @click="execCommand('underline')" :class="{ active: isActive('underline') }">
        <i class="el-icon-minus"></i> 下划线
      </button>
      <select @change="execCommand('fontSize', $event.target.value)">
        <option value="">字号</option>
        <option value="1">小</option>
        <option value="3">中</option>
        <option value="5">大</option>
        <option value="7">特大</option>
      </select>
      <input type="color" @change="execCommand('foreColor', $event.target.value)" title="文字颜色">
      <button type="button" @click="execCommand('justifyLeft')">左对齐</button>
      <button type="button" @click="execCommand('justifyCenter')">居中</button>
      <button type="button" @click="execCommand('justifyRight')">右对齐</button>
      <button type="button" @click="execCommand('insertUnorderedList')">无序列表</button>
      <button type="button" @click="execCommand('insertOrderedList')">有序列表</button>
    </div>
    <div
      ref="editor"
      class="editor-content"
      contenteditable="true"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      v-html="value"
    ></div>
  </div>
</template>

<script>
export default {
  name: "RichEditor",
  props: {
    value: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: "请输入内容..."
    }
  },
  data() {
    return {
      focused: false
    };
  },
  mounted() {
    this.$refs.editor.innerHTML = this.value;
    if (this.placeholder && !this.value) {
      this.$refs.editor.setAttribute('data-placeholder', this.placeholder);
    }
  },
  watch: {
    value(newVal) {
      if (this.$refs.editor.innerHTML !== newVal) {
        this.$refs.editor.innerHTML = newVal;
      }
    }
  },
  methods: {
    handleInput() {
      const content = this.$refs.editor.innerHTML;
      this.$emit('input', content);
      this.$emit('change', content);
    },
    handleFocus() {
      this.focused = true;
      this.$emit('focus');
    },
    handleBlur() {
      this.focused = false;
      this.$emit('blur');
    },
    execCommand(command, value = null) {
      document.execCommand(command, false, value);
      this.$refs.editor.focus();
      this.handleInput();
    },
    isActive(command) {
      return document.queryCommandState(command);
    },
    getContent() {
      return this.$refs.editor.innerHTML;
    },
    setContent(content) {
      this.$refs.editor.innerHTML = content;
      this.handleInput();
    },
    clear() {
      this.$refs.editor.innerHTML = '';
      this.handleInput();
    }
  }
};
</script>

<style scoped>
.rich-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

.toolbar {
  border-bottom: 1px solid #dcdfe6;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  background: #f5f7fa;
}

.toolbar button {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  background: #fff;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.toolbar button:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.toolbar button.active {
  background: #409eff;
  color: #fff;
  border-color: #409eff;
}

.toolbar select {
  padding: 4px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  font-size: 12px;
}

.toolbar input[type="color"] {
  width: 30px;
  height: 26px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  cursor: pointer;
}

.editor-content {
  min-height: 200px;
  padding: 10px;
  outline: none;
  line-height: 1.6;
  font-size: 14px;
}

.editor-content:empty:before {
  content: attr(data-placeholder);
  color: #c0c4cc;
}

.editor-content:focus {
  border-color: #409eff;
}

.editor-content p {
  margin: 0 0 10px 0;
}

.editor-content ul, .editor-content ol {
  margin: 10px 0;
  padding-left: 20px;
}

.editor-content li {
  margin: 5px 0;
}
</style>
